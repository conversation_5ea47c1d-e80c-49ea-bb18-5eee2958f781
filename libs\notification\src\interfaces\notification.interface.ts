import { NotificationType, NotificationChannel, NotificationPriority, NotificationStatus } from '../enums/notification.enum';

export interface NotificationPayload {
  senderId: string;
  type: NotificationType;
  title: string;
  message: string;
  userId: string;
  userEmail?: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  priority?: NotificationPriority;
  isRouted?: boolean;
  metadata?: {
    taskId?: string;
    taskType?: string;
    collectionName?: string;
    recordCount?: number;
    failedCount?: number;
    downloadUrl?: string;
    errorMessage?: string;
    [key: string]: any;
  };
}

export interface NotificationTemplate {
  type: NotificationType;
  title: string;
  message: string;
  channels: NotificationChannel[];
  priority: NotificationPriority;
}

export interface NotificationRecord {
  id: string;
  userId: string;
  userEmail?: string;
  type: NotificationType;
  title: string;
  message: string;
  channels: NotificationChannel[];
  priority: NotificationPriority;
  status: NotificationStatus;
  data?: Record<string, any>;
  metadata?: Record<string, any>;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface WebSocketNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  data?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
}

export interface EmailNotificationData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export interface TaskNotificationData {
  taskId: string;
  taskType: 'import' | 'export';
  status: 'COMPLETED' | 'FAILED' | 'STARTED' | 'IN_PROGRESS';
  collectionName?: string;
  recordCount?: number;
  failedCount?: number;
  insertedCount?: number;
  updatedCount?: number;
  downloadUrl?: string;
  errorMessage?: string;
  userId: string;
  userEmail?: string;
}
