import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'user_process_assignments',
})
export class UserProcessAssignment extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => ID)
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  userId: Types.ObjectId;

  @Field(() => ID)
  @Prop({ type: Types.ObjectId, required: true, ref: 'Role' })
  roleId: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, required: false, ref: 'Organisation', default: null })
  organisationId?: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, required: false, ref: 'Organisation', default: null })
  subOrganisationId?: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, required: false, ref: 'Process', default: null })
  processId?: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, required: false, ref: 'User' })
  reportToUserId?: Types.ObjectId;

  @Field({ nullable: true })
  @Prop({ type: String })
  orgId?: string;
}

export const UserProcessAssignmentSchema = SchemaFactory.createForClass(UserProcessAssignment); 