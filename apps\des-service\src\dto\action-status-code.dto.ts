import { ArgsType, Field, InputType, ObjectType, Int, ID, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsBoolean, IsMongoId, IsNotEmpty, IsObject, ValidateNested } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { ActionCode, StatusCode, ActionStatusCodeMap } from '../entities/action-status-code.entity';
import { PaginationMeta } from './base.response.dto';
import { Type } from 'class-transformer';

// ===== GENERIC ARGS AND RESPONSES =====
@InputType()
export class ClientSortInput {
    @Field()
    @IsString()
    field: string;

    @Field({ defaultValue: true })
    @IsBoolean()
    ascending: boolean;
}
@ArgsType()
export class FindAllArgs {
    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    page?: number = 1;

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    search?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    filters?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    sortBy?: string;

    @Field(() => String, { nullable: true, description: "Should be 'asc' or 'desc'" })
    @IsOptional()
    @IsString()
    sortOrder?: 'asc' | 'desc';

    @Field(() => ClientSortInput, { nullable: true })
    @IsOptional()
    @ValidateNested()
    @Type(() => ClientSortInput)
    sort?: ClientSortInput;
    @Field(() => GraphQLJSON, { nullable: true })
    selectedFields?: Record<string, any>;
}
@ArgsType()
export class FindAllAactions {
    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    page?: number = 1;

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    search?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    filters?: Record<string, any>;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    sortBy?: string;

    @Field(() => String, { nullable: true, description: "Should be 'asc' or 'desc'" })
    @IsOptional()
    @IsString()
    sortOrder?: 'asc' | 'desc';

    @Field(() => ClientSortInput, { nullable: true })
    @IsOptional()
    @ValidateNested()
    @Type(() => ClientSortInput)
    sort?: ClientSortInput;
    @Field(() => GraphQLJSON, { nullable: true })
    selectedFields?: Record<string, any>;
}
// ===== ACTION CODE DTOS =====

@InputType()
export class CreateActionCodeInput {
    @Field(() => ID)
    @IsMongoId()
    statusCodeId: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    code: string;
}

@InputType()
export class UpdateActionCodeInput {
    @Field(() => ID)
    @IsMongoId()
    id: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsMongoId()
    statusCodeId?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    code?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

@ObjectType()
export class PaginatedActionCodesResponse {
    @Field(() => [ActionCode])
    items: ActionCode[];

    @Field(() => PaginationMeta)
    pagination: PaginationMeta;
}

// ===== STATUS CODE DTOS =====

@InputType()
export class CreateStatusCodeInput {
    @Field(() => ID)
    @IsMongoId()
    processId: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    code: string;

    // @Field()
    // @IsString()
    // @IsNotEmpty()
    // description: string;
}

@InputType()
export class UpdateStatusCodeInput {
    @Field(() => ID)
    @IsMongoId()
    id: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsMongoId()
    processId?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    code?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

@ObjectType()
export class PaginatedStatusCodesResponse {
    @Field(() => [StatusCode])
    items: StatusCode[];

    @Field(() => PaginationMeta)
    pagination: PaginationMeta;
}

// ===== ACTION STATUS CODE MAP DTOS =====

@InputType()
export class CreateActionStatusCodeMapInput {
    @Field(() => ID)
    @IsMongoId()
    processId: string;

    @Field(() => ID)
    @IsMongoId()
    actionCodeId: string;

    @Field(() => ID)
    @IsMongoId()
    statusCodeId: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    templateId?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    flattenedValues?: Record<string, any>;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    values?: Record<string, any>;
}

@InputType()
export class UpdateActionStatusCodeMapInput {
    @Field(() => ID)
    @IsMongoId()
    id: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsMongoId()
    processId?: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsMongoId()
    actionCodeId?: string;

    @Field(() => ID, { nullable: true })
    @IsOptional()
    @IsMongoId()
    statusCodeId?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    templateId?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    values?: Record<string, any>;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsObject()
    flattenedValues?: Record<string, any>;
}

@ObjectType()
export class PaginatedActionStatusCodeMapsResponse {
    @Field(() => [GraphQLJSON])
    items: any[];

    @Field(() => PaginationMeta)
    pagination: PaginationMeta;
} 