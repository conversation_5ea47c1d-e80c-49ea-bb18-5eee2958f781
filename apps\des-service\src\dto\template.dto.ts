import { InputType, Field, ID, Int, ArgsType, ObjectType } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsMongoId, IsBoolean, IsEnum, ValidateNested } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { ClientType, Template } from '../entities/template.entity';
import { EntryType } from '../entities/provideremail.entity'; // updated import path
import { Type } from 'class-transformer';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
@InputType()
export class CreateTemplateInput {
  @Field()
  @IsString()
  name: string;

  @Field({ nullable: true, defaultValue: true })
  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;


  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  organisationId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  subOrganisationId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  view_summary?: {
    in_grid?: string[];
    default?: string[];
    isImport?: string[];
  };

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  useTemplate?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  docType?: string;

 @Field({ nullable: true })
  @IsOptional()
  @IsString()
  key?: string; // Optional key field for the template
}

@InputType()
export class UpdateTemplateInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  view_summary?: {
    in_grid?: string[];
    default?: string[];
    isImport?: string[];
  };

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  fields: Record<string, any>;


  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  useTemplate?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  doctype?: string;
}

@ArgsType()
export class FindAllTemplatesArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  sort?: {
    field: string;
    ascending: boolean;
  };

  @Field(() => [String], { nullable: true })
  @IsOptional()
  selectedFields?: string[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: string;
}

// Response DTOs
@ObjectType()
export class TemplatePaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;

}

@ObjectType()
export class FindAllTemplatesResponse {
  @Field(() => GraphQLJSON)
  templates: any[];

  @Field(() => TemplatePaginationMeta)
  pagination: TemplatePaginationMeta;
}

@InputType()
export class TemplateFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  version?: number;



  @Field(() => ClientType, { nullable: true })
  @IsOptional()
  @IsEnum(ClientType)
  type?: ClientType;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  selectedFields?: Record<string, any>;
}

@InputType()
export class CloneTemplateInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field()
  @IsString()
  organisationId: string;

  @Field()
  @IsString()
  subOrganisationId: string;

  @Field({ nullable: true })
  name: string; // Optional name for the cloned template
}

@InputType()
export class CreateTemplateVersionInput {
  @Field(() => ID)
  @IsMongoId()
  currentId: string;

  @Field(() => ID)
  @IsMongoId()
  oldVersionId: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;
}


@InputType()
export class AttachmentInput {
  @Field()
  @IsString()
  fileName: string;

  @Field()
  @IsString()
  url: string;

  @Field()
  @IsString()
  uploadedAt: string; // or use Date if you're parsing to Date

  @Field()
  @IsString()
  type: string;
}
@InputType()
export class DesTicketMessageInput {
  @Field()
  @IsString()
  from: string;

  @Field()
  @IsString()
  to?: string;

  @Field()
  @IsString()
  body: string;

  @Field(() => Date)
  date: Date;

  @Field(() => [AttachmentInput], { nullable: true })
  @IsOptional()
  attachments?: AttachmentInput[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  messageId?: string;


}


@InputType()
export class ProviderEmailTicketInput {

  @Field(() => [String], { nullable: true })
  @Prop({ type: [String], default: [] })
  tickectId?: string[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  orgId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  main_orgId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  subOrgId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  processId?: string;  

}
