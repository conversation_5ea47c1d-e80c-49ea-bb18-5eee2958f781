import { InputType, Field, ID, ObjectType, ArgsType, Int } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsObject } from 'class-validator';
import { Graph<PERSON>JSON, GraphQLJSONObject } from 'graphql-type-json';

@InputType()
export class CreateSystemUserInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  employeeId: string;

  @Field()
  roleName: string;

  @Field({ nullable: true })
  profileImage?: string;
}

@InputType()
export class UpdateSystemUserInput {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  email?: string;

  @Field({ nullable: true })
  employeeId?: string;

  @Field({ nullable: true })
  roleName?: string;

  @Field({ nullable: true })
  profileImage?: string;
}

@ObjectType()
export class UserResponse {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  employeeId: string;

  @Field()
  roleName: string;

  @Field({ nullable: true })
  profileImage?: string;

  @Field()
  isActive: boolean;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  createdBy?: string;

  @Field({ nullable: true })
  updatedBy?: string;
}

@ArgsType()
export class PaginateUserArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional() 
  type?: string;
}

@InputType()
export class AssignUserRoleInput {
  @Field(() => ID)
  userId: string;

  @Field(() => ID)
  roleId: string;

  @Field()
  organisationId: string;

  @Field()
  subOrganisationId: string;

  @Field()
  processId: string;

  @Field(() => ID, { nullable: true })
  reportToUserId?: string;
}

@InputType()
export class PermissionInput {
  @Field()
  displayName: string;

  @Field()
  isEnabled: boolean;
}

@InputType()
export class SubModuleInput {
  @Field()
  displayName: string;

  @Field()
  moduleName: string;

  @Field()
  isEnabled: boolean;

  @Field(() => [PermissionInput])
  permissions: PermissionInput[];
}

@InputType()
export class ModulePermissionInput {
  @Field()
  moduleName: string;

  @Field()
  displayName: string;

  @Field()
  isEnabled: boolean;

  @Field(() => [SubModuleInput])
  subModules: SubModuleInput[];
}

@InputType()
export class UpdateRolePermissionsInput {
  @Field(() => ID)
  roleId: string;

  @Field(() => [ModulePermissionInput])
  permissions: ModulePermissionInput[];
}

@ArgsType()
export class RoleListArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number = 10;

  @Field({ nullable: true })
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  filters?: Record<string, any>;
}

@InputType()
export class AssignRoleHierarchyInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() managerId: string;
  @Field() supervisorId: string;
  @Field() agentId: string;
}

@InputType()
export class RemoveRoleMappingInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() managerId: string;
  @Field() supervisorId: string;
  @Field() agentId: string;
}

@InputType()
export class AssignOperationsRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() managerId: string;
  @Field() supervisorId: string;
  @Field() agentId: string;
  @Field({ nullable: true }) orgId?: string;
}

@InputType()
export class AssignAuditRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() qcManagerId: string;
  @Field() qcSupervisorId: string;
  @Field() qcAgentId: string;
  @Field({ nullable: true }) orgId?: string;
}

@InputType()
export class AssignManagementsRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() adminId: string;
  @Field() subAdminId: string;
  @Field({ nullable: true }) orgId?: string;
}

@ObjectType()
export class CategoryHierarchies {
  @Field()
  category: string;

  @Field(() => [GraphQLJSONObject])
  hierarchies: any[];
}

@ObjectType()
export class ProcessAssignmentsByCategoryResult {
  @Field()
  processId: string;

  @Field()
  isActive: boolean;

  @Field(() => [CategoryHierarchies])
  users: CategoryHierarchies[];
}

@InputType()
export class RemoveOperationsRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() managerId: string;
  @Field() supervisorId: string;
  @Field() agentId: string;
}

@InputType()
export class RemoveAuditRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() qcManagerId: string;
  @Field() qcSupervisorId: string;
  @Field() qcAgentId: string;
}

@InputType()
export class RemoveManagementsRoleInput {
  @Field() organisationId: string;
  @Field() subOrganisationId: string;
  @Field() processId: string;
  @Field() adminId: string;
  @Field() subAdminId: string;
}

@InputType()
export class GetUsersUnderManagerInput {
  // @Field() orgId: string;
}

@ObjectType()
export class UserBasicInfo {
  @Field()
  _id: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  employeeId?: string;

  @Field()
  roleName: string;
}

@ObjectType()
export class UsersUnderManagerResponse {
  @Field({ nullable: true })
  role?: string;

  @Field(() => [UserBasicInfo])
  users: UserBasicInfo[];
}

@InputType()
export class AssignGlobalRoleInput {
  @Field()
  userId: string;

  @Field()
  roleKey: 'admin' | 'sub-admin';
}

@ObjectType()
export class GlobalRoleResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field({ nullable: true })
  roleKey?: string;
}

@ObjectType()
export class GlobalAdminUser {
  @Field()
  _id: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  employeeId?: string;

  @Field()
  roleKey: string; // 'admin' or 'sub-admin'

  @Field()
  roleName: string; // 'Admin' or 'Sub Admin'

  @Field()
  assignedAt: Date;
}

@ObjectType()
export class ListGlobalAdminsResponse {
  @Field(() => [GlobalAdminUser])
  admins: GlobalAdminUser[];

  @Field(() => [GlobalAdminUser])
  subAdmins: GlobalAdminUser[];

  @Field()
  totalCount: number;
}