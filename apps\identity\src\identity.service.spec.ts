import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { IdentityService } from './identity.service';
import { Agent } from './entities/agent.entities';
import { CreateUserInput } from './dto/client.dto';
import { UpdateAgentInput, AgentSSOLoginInput } from './dto/agent.dto';
import * as jwt from 'jsonwebtoken';
import { authenticator } from 'otplib';
import { Client, ClientType } from './entities/client.entities';

// Mock external dependencies
jest.mock('jsonwebtoken');
jest.mock('otplib');
jest.mock('nodemailer');
jest.mock('fs');

describe('IdentityService', () => {
  let service: IdentityService;
  let userModel: Model<Client>;
  let agentModel: Model<Agent>;

  const mockObjectId = new Types.ObjectId();
  const mockUser = {
    _id: mockObjectId,
    name: 'Test User',
    email: '<EMAIL>',
    is2FAenabled: false,
    bypass2FA: false,
    secretKey: 'test-secret',
    qrcode: 'test-qr-code',
    token: 'test-token',
    attemp: 0,
    dailyOtpAttempts: 0,
    lastOtpAttemptDate: new Date(),
    save: jest.fn().mockResolvedValue(this),
    toObject: jest.fn().mockReturnValue({
      _id: mockObjectId,
      name: 'Test User',
      email: '<EMAIL>',
      is2FAenabled: false,
      bypass2FA: false,
    }),
  };

  const mockAgent = {
    _id: mockObjectId,
    name: 'Test Agent',
    email: '<EMAIL>',
    role: 'admin',
    clients: [],
    token: 'agent-token',
    save: jest.fn().mockResolvedValue(this),
  };

  const mockUserModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    deleteOne: jest.fn(),
    countDocuments: jest.fn(),
    exec: jest.fn(),
  };

  const mockAgentModel = {
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    updateOne: jest.fn(),
    deleteOne: jest.fn(),
    countDocuments: jest.fn(),
    exec: jest.fn(),
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IdentityService,
        {
          provide: getModelToken('User'),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken('Agent'),
          useValue: mockAgentModel,
        },
      ],
    }).compile();

    service = module.get<IdentityService>(IdentityService);
    userModel = module.get<Model<Client>>(getModelToken('User'));
    agentModel = module.get<Model<Agent>>(getModelToken('Agent'));

    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup default environment variables
    process.env.JWT_SECRET = 'test-secret';
    process.env.SMTP_USER = '<EMAIL>';
    process.env.SMTP_PASS = 'test-password';
    process.env.ADMIN_EMAIL = '<EMAIL>';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all users successfully', async () => {
      const mockUsers = [mockUser];
      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUsers),
      });

      const result = await service.findAll();

      expect(result).toEqual(mockUsers);
      expect(mockUserModel.find).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      await expect(service.findAll()).rejects.toThrow('Database error');
    });
  });

  describe('sendOtp - Positive Scenarios', () => {
    beforeEach(() => {
      // Mock authenticator methods
      (authenticator.generateSecret as jest.Mock).mockReturnValue('new-secret');
      (authenticator.keyuri as jest.Mock).mockReturnValue('otpauth://test');
      (authenticator.generate as jest.Mock).mockReturnValue('123456');

      // Mock sendEmail method
      jest.spyOn(service, 'sendEmail').mockResolvedValue();
    });

    it('should send OTP successfully for valid email', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);

      const result = await service.sendOtp('<EMAIL>');

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('is2FAenabled', false);
      expect(result.data).toHaveProperty('bypass2FA', false);
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(service.sendEmail).toHaveBeenCalledWith('<EMAIL>', '123456');
    });

    it('should generate new secret if user has no secret key', async () => {
      const userWithoutSecret = { ...mockUser, secretKey: null };
      mockUserModel.findOne.mockResolvedValue(userWithoutSecret);
      mockUserModel.updateOne.mockResolvedValue({});

      await service.sendOtp('<EMAIL>');

      expect(authenticator.generateSecret).toHaveBeenCalled();
      expect(authenticator.keyuri).toHaveBeenCalledWith('<EMAIL>', 'ASP-RCM', 'new-secret');
      expect(mockUserModel.updateOne).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        { $set: { secretKey: 'new-secret', qrcode: 'otpauth://test' } }
      );
    });

    it('should use existing secret if user already has one', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);

      await service.sendOtp('<EMAIL>');

      expect(authenticator.generateSecret).not.toHaveBeenCalled();
      expect(authenticator.generate).toHaveBeenCalledWith('test-secret');
    });
  });

  describe('sendOtp - Negative Scenarios', () => {
    it('should throw error for invalid email format', async () => {
      await expect(service.sendOtp('invalid-email')).rejects.toThrow();
      await expect(service.sendOtp('')).rejects.toThrow();
      await expect(service.sendOtp('test@')).rejects.toThrow();
      await expect(service.sendOtp('@example.com')).rejects.toThrow();
    });

    it('should throw error for null or undefined email', async () => {
      await expect(service.sendOtp(null as any)).rejects.toThrow();
      await expect(service.sendOtp(undefined as any)).rejects.toThrow();
    });

    it('should throw error when user not found', async () => {
      mockUserModel.findOne.mockResolvedValue(null);

      await expect(service.sendOtp('<EMAIL>')).rejects.toThrow();
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });

    it('should handle database errors during user lookup', async () => {
      mockUserModel.findOne.mockRejectedValue(new Error('Database connection failed'));

      await expect(service.sendOtp('<EMAIL>')).rejects.toThrow('Database connection failed');
    });

    it('should handle errors during secret generation and update', async () => {
      const userWithoutSecret = { ...mockUser, secretKey: null };
      mockUserModel.findOne.mockResolvedValue(userWithoutSecret);
      mockUserModel.updateOne.mockRejectedValue(new Error('Update failed'));
      (authenticator.generateSecret as jest.Mock).mockReturnValue('new-secret');

      await expect(service.sendOtp('<EMAIL>')).rejects.toThrow('Update failed');
    });
  });

  describe('verifyOtp - Positive Scenarios', () => {
    beforeEach(() => {
      (authenticator.check as jest.Mock).mockReturnValue(true);
    });

    it('should verify OTP successfully', async () => {
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(mockUser),
      });

      const result = await service.verifyOtp('<EMAIL>', '123456');

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('qrcode', 'test-qr-code');
      expect(result.data).toHaveProperty('secret_code', 'test-secret');
      expect(authenticator.check).toHaveBeenCalledWith('123456', 'test-secret');
    });

    it('should handle user with empty qrcode', async () => {
      const userWithoutQR = { ...mockUser, qrcode: null };
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(userWithoutQR),
      });

      const result = await service.verifyOtp('<EMAIL>', '123456');

      expect(result.data).toHaveProperty('qrcode', '');
    });
  });

  describe('verifyOtp - Negative Scenarios', () => {
    it('should throw error when user not found', async () => {
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(null),
      });

      await expect(service.verifyOtp('<EMAIL>', '123456')).rejects.toThrow();
    });

    it('should throw error when user has no secret key', async () => {
      const userWithoutSecret = { ...mockUser, secretKey: null };
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(userWithoutSecret),
      });

      await expect(service.verifyOtp('<EMAIL>', '123456')).rejects.toThrow();
    });

    it('should throw error for invalid OTP', async () => {
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(mockUser),
      });
      (authenticator.check as jest.Mock).mockReturnValue(false);

      await expect(service.verifyOtp('<EMAIL>', 'invalid-otp')).rejects.toThrow();
    });

    it('should handle database errors during verification', async () => {
      mockUserModel.findOne.mockReturnValue({
        lean: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      await expect(service.verifyOtp('<EMAIL>', '123456')).rejects.toThrow('Database error');
    });
  });

  describe('verifyTotp - Positive Scenarios', () => {
    beforeEach(() => {
      (authenticator.check as jest.Mock).mockReturnValue(true);
      (jwt.sign as jest.Mock).mockReturnValue('jwt-token');
    });

    it('should verify TOTP successfully', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockResolvedValue({});

      const result = await service.verifyTotp('<EMAIL>', '123456');

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('token', 'jwt-token');
      expect(result.data).toHaveProperty('user_id', mockObjectId.toString());
      expect(authenticator.check).toHaveBeenCalledWith('123456', 'test-secret');
    });

    it('should skip 2FA check when requested', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockResolvedValue({});

      const result = await service.verifyTotp('<EMAIL>', undefined, true);

      expect(result).toHaveProperty('code', 200);
      expect(authenticator.check).not.toHaveBeenCalled();
      expect(mockUserModel.updateOne).toHaveBeenCalledWith(
        { _id: mockUser._id },
        { token: 'jwt-token', is2FAenabled: false, bypass2FA: true }
      );
    });

    it('should generate JWT token with correct payload', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockResolvedValue({});

      await service.verifyTotp('<EMAIL>', '123456');

      expect(jwt.sign).toHaveBeenCalledWith(
        { userId: mockUser._id, role: 'admin' },
        'test-secret',
        { expiresIn: '1h' }
      );
    });
  });

  describe('verifyTotp - Negative Scenarios', () => {
    it('should throw error when user not found', async () => {
      mockUserModel.findOne.mockResolvedValue(null);

      await expect(service.verifyTotp('<EMAIL>', '123456')).rejects.toThrow();
    });

    it('should throw error when user has no secret key', async () => {
      const userWithoutSecret = { ...mockUser, secretKey: null };
      mockUserModel.findOne.mockResolvedValue(userWithoutSecret);

      await expect(service.verifyTotp('<EMAIL>', '123456')).rejects.toThrow();
    });

    it('should throw error for invalid TOTP', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      (authenticator.check as jest.Mock).mockReturnValue(false);

      await expect(service.verifyTotp('<EMAIL>', 'invalid-totp')).rejects.toThrow();
    });

    it('should handle database errors during token update', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockRejectedValue(new Error('Update failed'));
      (authenticator.check as jest.Mock).mockReturnValue(true);

      await expect(service.verifyTotp('<EMAIL>', '123456')).rejects.toThrow('Update failed');
    });
  });

  describe('createUser - Positive Scenarios', () => {
    it('should create user successfully', async () => {
      const createUserInput: CreateUserInput = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
        address: '123 Main St',
        phone_number: 1234567890,
      };

      mockUserModel.findOne.mockResolvedValue(null); // No existing user
      const mockNewUser = { ...mockUser, ...createUserInput, save: jest.fn().mockResolvedValue(mockUser) };

      // Mock the constructor
      const userModelSpy = jest.spyOn(service as any, 'userModel', 'get').mockReturnValue({
        ...mockUserModel,
        constructor: jest.fn().mockImplementation(() => mockNewUser),
      });

      const result = await service.createUser(createUserInput);

      expect(result).toHaveProperty('code', 201);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('user');
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: createUserInput.email });

      userModelSpy.mockRestore();
    });
  });

  describe('createUser - Negative Scenarios', () => {
    it('should throw error when email already exists', async () => {
      const createUserInput: CreateUserInput = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
      };

      mockUserModel.findOne.mockResolvedValue(mockUser); // Existing user

      await expect(service.createUser(createUserInput)).rejects.toThrow();
      expect(mockUserModel.findOne).toHaveBeenCalledWith({ email: createUserInput.email });
    });

    it('should handle database errors during user creation', async () => {
      const createUserInput: CreateUserInput = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
      };

      mockUserModel.findOne.mockResolvedValue(null);
      const mockNewUser = { save: jest.fn().mockRejectedValue(new Error('Save failed')) };

      const userModelSpy = jest.spyOn(service as any, 'userModel', 'get').mockReturnValue({
        ...mockUserModel,
        constructor: jest.fn().mockImplementation(() => mockNewUser),
      });

      await expect(service.createUser(createUserInput)).rejects.toThrow('Save failed');

      userModelSpy.mockRestore();
    });
  });

  describe('resendOtp - Positive Scenarios', () => {
    beforeEach(() => {
      (authenticator.generateSecret as jest.Mock).mockReturnValue('new-secret');
      (authenticator.keyuri as jest.Mock).mockReturnValue('otpauth://test');
      (authenticator.generate as jest.Mock).mockReturnValue('123456');
      jest.spyOn(service, 'sendEmail').mockResolvedValue();
    });

    it('should resend OTP successfully within daily limit', async () => {
      const userWithLowAttempts = {
        ...mockUser,
        dailyOtpAttempts: 2,
        attemp: 5,
        lastOtpAttemptDate: new Date()
      };
      mockUserModel.findOne.mockResolvedValue(userWithLowAttempts);
      mockUserModel.updateOne.mockResolvedValue({});

      const result = await service.resendOtp('<EMAIL>');

      expect(result).toHaveProperty('code', 200);
      expect(mockUserModel.updateOne).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        {
          $set: {
            attemp: 6,
            dailyOtpAttempts: 3,
            lastOtpAttemptDate: expect.any(Date),
          },
        }
      );
      expect(service.sendEmail).toHaveBeenCalledWith('<EMAIL>', '123456');
    });

    it('should reset daily attempts for new day', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const userWithOldAttempts = {
        ...mockUser,
        dailyOtpAttempts: 5,
        lastOtpAttemptDate: yesterday
      };
      mockUserModel.findOne.mockResolvedValue(userWithOldAttempts);
      mockUserModel.updateOne.mockResolvedValue({});

      await service.resendOtp('<EMAIL>');

      expect(mockUserModel.updateOne).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        {
          $set: {
            attemp: 1,
            dailyOtpAttempts: 1,
            lastOtpAttemptDate: expect.any(Date),
          },
        }
      );
    });

    it('should generate new secret if user has none', async () => {
      const userWithoutSecret = {
        ...mockUser,
        secretKey: null,
        qrcode: null,
        dailyOtpAttempts: 0
      };
      mockUserModel.findOne.mockResolvedValue(userWithoutSecret);
      mockUserModel.updateOne.mockResolvedValue({});

      await service.resendOtp('<EMAIL>');

      expect(authenticator.generateSecret).toHaveBeenCalled();
      expect(mockUserModel.updateOne).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        {
          $set: {
            secretKey: 'new-secret',
            qrcode: 'otpauth://test',
          },
        }
      );
    });
  });

  describe('resendOtp - Negative Scenarios', () => {
    it('should throw error when user not found', async () => {
      mockUserModel.findOne.mockResolvedValue(null);

      await expect(service.resendOtp('<EMAIL>')).rejects.toThrow();
    });

    it('should throw error when daily limit reached', async () => {
      const userWithMaxAttempts = {
        ...mockUser,
        dailyOtpAttempts: 5,
        lastOtpAttemptDate: new Date()
      };
      mockUserModel.findOne.mockResolvedValue(userWithMaxAttempts);

      await expect(service.resendOtp('<EMAIL>')).rejects.toThrow();
    });

    it('should handle database errors during resend', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockRejectedValue(new Error('Database error'));

      await expect(service.resendOtp('<EMAIL>')).rejects.toThrow('Database error');
    });
  });

  describe('findAllAgents - Positive Scenarios', () => {
    it('should return all agents with default pagination', async () => {
      const mockAgents = [mockAgent];
      const mockTotal = 1;

      mockAgentModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue(mockAgents),
            }),
          }),
        }),
      });
      mockAgentModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal),
      });

      const result = await service.findAllAgents();

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('agents', mockAgents);
      expect(result.data).toHaveProperty('pagination');
      expect(result.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it('should handle search functionality', async () => {
      const args = {
        page: 1,
        limit: 5,
        search: 'test',
      };

      mockAgentModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockAgent]),
            }),
          }),
        }),
      });
      mockAgentModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAllAgents(args);

      expect(mockAgentModel.find).toHaveBeenCalledWith({
        $or: [
          { name: { $regex: 'test', $options: 'i' } },
          { email: { $regex: 'test', $options: 'i' } },
          { role: { $regex: 'test', $options: 'i' } },
        ],
      });
    });

    it('should handle filters', async () => {
      const args = {
        page: 1,
        limit: 10,
        filters: { role: 'admin', name: 'test' },
      };

      mockAgentModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockAgent]),
            }),
          }),
        }),
      });
      mockAgentModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAllAgents(args);

      expect(mockAgentModel.find).toHaveBeenCalledWith({
        role: { $regex: 'admin', $options: 'i' },
        name: { $regex: 'test', $options: 'i' },
      });
    });

    it('should handle sorting', async () => {
      const args = {
        page: 1,
        limit: 10,
        sort: { field: 'name', ascending: false },
      };

      const mockSortChain = {
        skip: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            exec: jest.fn().mockResolvedValue([mockAgent]),
          }),
        }),
      };

      mockAgentModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue(mockSortChain),
      });
      mockAgentModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAllAgents(args);

      expect(mockAgentModel.find().sort).toHaveBeenCalledWith({ name: -1 });
    });
  });

  describe('findAllAgents - Negative Scenarios', () => {
    it('should handle database errors', async () => {
      mockAgentModel.find.mockImplementation(() => {
        throw new Error('Database error');
      });

      await expect(service.findAllAgents()).rejects.toThrow('Database error');
    });

    it('should handle invalid pagination parameters', async () => {
      const args = {
        page: -1,
        limit: 0,
      };

      // Should handle gracefully or throw appropriate error
      mockAgentModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([]),
            }),
          }),
        }),
      });
      mockAgentModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(0),
      });

      const result = await service.findAllAgents(args);

      // Should handle invalid parameters gracefully
      expect(result).toBeDefined();
    });
  });

  describe('agentSSOLogin - Positive Scenarios', () => {
    beforeEach(() => {
      (jwt.sign as jest.Mock).mockReturnValue('agent-jwt-token');
    });

    it('should login agent successfully', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'Test Agent',
        role: 'admin',
      };

      mockAgentModel.findOne.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockResolvedValue({});

      const result = await service.agentSSOLogin(loginInput);

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('token', 'agent-jwt-token');
      expect(result.data).toHaveProperty('agent_id', mockAgent._id.toString());
      expect(mockAgentModel.findOne).toHaveBeenCalledWith({ email: loginInput.email });
      expect(mockAgentModel.updateOne).toHaveBeenCalledWith(
        { _id: mockAgent._id },
        { token: 'agent-jwt-token' }
      );
    });

    it('should create new agent if not exists', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'New Agent',
        role: 'user',
      };

      mockAgentModel.findOne.mockResolvedValue(null); // No existing agent
      const mockNewAgent = { ...mockAgent, ...loginInput, save: jest.fn().mockResolvedValue(mockAgent) };

      const agentModelSpy = jest.spyOn(service as any, 'agentModel', 'get').mockReturnValue({
        ...mockAgentModel,
        constructor: jest.fn().mockImplementation(() => mockNewAgent),
      });

      const result = await service.agentSSOLogin(loginInput);

      expect(result).toHaveProperty('code', 200);
      expect(result.data).toHaveProperty('token', 'agent-jwt-token');
      expect(mockNewAgent.save).toHaveBeenCalled();

      agentModelSpy.mockRestore();
    });

    it('should generate JWT token with correct payload for agent', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'Test Agent',
        role: 'admin',
      };

      mockAgentModel.findOne.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockResolvedValue({});

      await service.agentSSOLogin(loginInput);

      expect(jwt.sign).toHaveBeenCalledWith(
        { userId: mockAgent._id, role: 'admin' },
        'test-secret',
        { expiresIn: '1h' }
      );
    });
  });

  describe('agentSSOLogin - Negative Scenarios', () => {
    it('should handle database errors during agent lookup', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'Test Agent',
        role: 'admin',
      };

      mockAgentModel.findOne.mockRejectedValue(new Error('Database error'));

      await expect(service.agentSSOLogin(loginInput)).rejects.toThrow('Database error');
    });

    it('should handle errors during agent creation', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'New Agent',
        role: 'user',
      };

      mockAgentModel.findOne.mockResolvedValue(null);
      const mockNewAgent = { save: jest.fn().mockRejectedValue(new Error('Save failed')) };

      const agentModelSpy = jest.spyOn(service as any, 'agentModel', 'get').mockReturnValue({
        ...mockAgentModel,
        constructor: jest.fn().mockImplementation(() => mockNewAgent),
      });

      await expect(service.agentSSOLogin(loginInput)).rejects.toThrow('Save failed');

      agentModelSpy.mockRestore();
    });

    it('should handle errors during token update', async () => {
      const loginInput: AgentSSOLoginInput = {
        email: '<EMAIL>',
        name: 'Test Agent',
        role: 'admin',
      };

      mockAgentModel.findOne.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockRejectedValue(new Error('Update failed'));

      await expect(service.agentSSOLogin(loginInput)).rejects.toThrow('Update failed');
    });
  });

  describe('updateAgent - Positive Scenarios', () => {
    it('should update agent successfully', async () => {
      const updateInput: UpdateAgentInput = {
        id: mockObjectId.toString(),
        name: 'Updated Agent',
        role: 'manager',
        clients: [new Types.ObjectId()],
      };

      mockAgentModel.findById.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockResolvedValue({});

      const result = await service.updateAgent(updateInput);

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('message', 'Agent updated successfully');
      expect(mockAgentModel.findById).toHaveBeenCalledWith(updateInput.id);
      expect(mockAgentModel.updateOne).toHaveBeenCalledWith(
        { _id: updateInput.id },
        {
          name: updateInput.name,
          role: updateInput.role,
          clients: updateInput.clients,
        }
      );
    });

    it('should update only provided fields', async () => {
      const updateInput: UpdateAgentInput = {
        id: mockObjectId.toString(),
        name: 'Updated Agent',
      };

      mockAgentModel.findById.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockResolvedValue({});

      await service.updateAgent(updateInput);

      expect(mockAgentModel.updateOne).toHaveBeenCalledWith(
        { _id: updateInput.id },
        {
          name: updateInput.name,
        }
      );
    });
  });

  describe('updateAgent - Negative Scenarios', () => {
    it('should throw error when agent not found', async () => {
      const updateInput: UpdateAgentInput = {
        id: 'nonexistent-id',
        name: 'Updated Agent',
      };

      mockAgentModel.findById.mockResolvedValue(null);

      await expect(service.updateAgent(updateInput)).rejects.toThrow();
      expect(mockAgentModel.findById).toHaveBeenCalledWith(updateInput.id);
    });

    it('should handle database errors during update', async () => {
      const updateInput: UpdateAgentInput = {
        id: mockObjectId.toString(),
        name: 'Updated Agent',
      };

      mockAgentModel.findById.mockResolvedValue(mockAgent);
      mockAgentModel.updateOne.mockRejectedValue(new Error('Update failed'));

      await expect(service.updateAgent(updateInput)).rejects.toThrow('Update failed');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing environment variables', async () => {
      delete process.env.JWT_SECRET;

      await expect(service.verifyTotp('<EMAIL>', '123456')).rejects.toThrow();
    });

    it('should handle invalid email formats in various methods', async () => {
      const invalidEmails = ['', 'invalid', '@domain.com', 'user@', 'user@domain'];

      for (const email of invalidEmails) {
        await expect(service.sendOtp(email)).rejects.toThrow();
        await expect(service.verifyOtp(email, '123456')).rejects.toThrow();
        await expect(service.verifyTotp(email, '123456')).rejects.toThrow();
        await expect(service.resendOtp(email)).rejects.toThrow();
      }
    });

    it('should handle null and undefined inputs gracefully', async () => {
      await expect(service.sendOtp(null as any)).rejects.toThrow();
      await expect(service.sendOtp(undefined as any)).rejects.toThrow();
      await expect(service.verifyOtp('<EMAIL>', null as any)).rejects.toThrow();
      await expect(service.verifyOtp('<EMAIL>', undefined as any)).rejects.toThrow();
    });

    it('should handle concurrent OTP requests', async () => {
      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUserModel.updateOne.mockResolvedValue({});
      (authenticator.generate as jest.Mock).mockReturnValue('123456');
      jest.spyOn(service, 'sendEmail').mockResolvedValue();

      // Simulate concurrent requests
      const promises = [
        service.resendOtp('<EMAIL>'),
        service.resendOtp('<EMAIL>'),
        service.resendOtp('<EMAIL>'),
      ];

      // All should complete without throwing
      await Promise.all(promises);
      expect(service.sendEmail).toHaveBeenCalledTimes(3);
    });
  });

  describe('deleteClient - Positive Scenarios', () => {
    it('should delete sub-client successfully', async () => {
      const subClientId = new Types.ObjectId().toString();
      const mockSubClient = {
        ...mockUser,
        _id: subClientId,
        type: ClientType.SUB_CLIENT,
      };

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockSubClient),
      });
      mockUserModel.deleteOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 1 }),
      });

      const result = await service.deleteClient(subClientId);

      expect(result).toHaveProperty('code', 200);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('message', 'Client deleted successfully.');
      expect(mockUserModel.findById).toHaveBeenCalledWith(subClientId);
      expect(mockUserModel.deleteOne).toHaveBeenCalledWith({ _id: subClientId });
    });

    it('should delete main client with no sub-clients successfully', async () => {
      const mainClientId = new Types.ObjectId().toString();
      const mockMainClient = {
        ...mockUser,
        _id: mainClientId,
        type: ClientType.MAIN_CLIENT,
      };

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMainClient),
      });
      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue([]), // No sub-clients
      });
      mockUserModel.deleteOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue({ deletedCount: 1 }),
      });

      const result = await service.deleteClient(mainClientId);

      expect(result).toHaveProperty('code', 200);
      expect(result.data).toHaveProperty('message', 'Client deleted successfully.');
      expect(mockUserModel.find).toHaveBeenCalledWith({ main_client: mainClientId });
      expect(mockUserModel.deleteOne).toHaveBeenCalledWith({ _id: mainClientId });
    });
  });

  describe('deleteClient - Negative Scenarios', () => {
    it('should throw error when client not found', async () => {
      const nonExistentId = new Types.ObjectId().toString();

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.deleteClient(nonExistentId)).rejects.toThrow();
      expect(mockUserModel.findById).toHaveBeenCalledWith(nonExistentId);
    });

    it('should throw error when trying to delete main client with sub-clients', async () => {
      const mainClientId = new Types.ObjectId().toString();
      const mockMainClient = {
        ...mockUser,
        _id: mainClientId,
        type: ClientType.MAIN_CLIENT,
      };
      const mockSubClients = [
        { _id: new Types.ObjectId(), main_client: mainClientId },
        { _id: new Types.ObjectId(), main_client: mainClientId },
      ];

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMainClient),
      });
      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockSubClients),
      });

      await expect(service.deleteClient(mainClientId)).rejects.toThrow();
      expect(mockUserModel.find).toHaveBeenCalledWith({ main_client: mainClientId });
      expect(mockUserModel.deleteOne).not.toHaveBeenCalled();
    });

    it('should handle database errors during client lookup', async () => {
      const clientId = new Types.ObjectId().toString();

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Database error')),
      });

      await expect(service.deleteClient(clientId)).rejects.toThrow('Database error');
    });

    it('should handle database errors during deletion', async () => {
      const clientId = new Types.ObjectId().toString();
      const mockClient = {
        ...mockUser,
        _id: clientId,
        type: ClientType.SUB_CLIENT,
      };

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockClient),
      });
      mockUserModel.deleteOne.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Delete failed')),
      });

      await expect(service.deleteClient(clientId)).rejects.toThrow('Delete failed');
    });

    it('should handle database errors during sub-client check', async () => {
      const mainClientId = new Types.ObjectId().toString();
      const mockMainClient = {
        ...mockUser,
        _id: mainClientId,
        type: ClientType.MAIN_CLIENT,
      };

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockMainClient),
      });
      mockUserModel.find.mockReturnValue({
        exec: jest.fn().mockRejectedValue(new Error('Sub-client check failed')),
      });

      await expect(service.deleteClient(mainClientId)).rejects.toThrow('Sub-client check failed');
    });
  });
});
