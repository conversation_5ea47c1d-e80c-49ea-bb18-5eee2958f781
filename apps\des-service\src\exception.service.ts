import { Injectable, HttpException, HttpStatus as NestHttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Exception } from './entities/exception.entity';
import { CreateExceptionInput, UpdateExceptionInput, FindAllExceptionsInput, PaginatedExceptionsResponse } from './dto/exception.dto';
import {
  ErrorType,
  ResponseCode,
  HttpStatus,
  Success,
  Error,
} from '@app/error';
import { instanceToPlain } from 'class-transformer';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class ExceptionService {
  constructor(
    @InjectModel('Exception') private readonly exceptionModel: Model<Exception>,
    private readonly mongoConnectionService: MongoConnectionService,
  ) {}

  /**
   * Get process collection using MongoConnectionService
   */
  private async getProcessCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('processes', orgId);
  }

  /**
   * Get template collection using MongoConnectionService
   */
  private async getTemplateCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('templates');
  }

  async create(input: CreateExceptionInput): Promise<Success> {
    try {
      const { templateId, processId, createdBy, updatedBy } = input;

      if (!Types.ObjectId.isValid(templateId)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          `Invalid Template ID format: ${templateId}`
        );
      }

      // const template = await this.exceptionModel.findById(templateId).lean().exec();
      // if (!template) {
      //   throw new Error(
      //     HttpStatus.NOT_FOUND,
      //     ResponseCode.NOT_FOUND,
      //     ErrorType.FORM,
      //     `Template not found with ID: ${templateId}`
      //   );
      // }

      if (!Types.ObjectId.isValid(processId)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          `Invalid Process ID format: ${processId}`
        );
      }

      // const process = await this.exceptionModel.findById(processId).lean().exec();
      // if (!process) {
      //   throw new Error(
      //     HttpStatus.NOT_FOUND,
      //     ResponseCode.NOT_FOUND,
      //     ErrorType.FORM,
      //     `Process not found with ID: ${processId}`
      //   );
      // }

      // Check for existing exception with same template and process
      const existingException = await this.exceptionModel.findOne({
        templateId: new Types.ObjectId(templateId),
        processId: new Types.ObjectId(processId),
        isActive: true,
      }).lean().exec();

      if (existingException) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.ALREADY_EXISTS,
          ErrorType.FORM,
          'An exception with the same Template and Process already exists.'
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const exceptionData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
        isActive: true,
      };

      delete exceptionData.flattenedValues;

      const newException = new this.exceptionModel(exceptionData);
      const savedDoc = await newException.save();
      const populatedDoc = await this.exceptionModel.findById(savedDoc._id)
        .populate('templateId')
        .populate('processId')
        .lean()
        .exec();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { exception: this.transformExceptionDocument(populatedDoc as any) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ExceptionService.create:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to create exception: ${(err as globalThis.Error).message}`
      );
    }
  }

  async findAll(input: FindAllExceptionsInput): Promise<PaginatedExceptionsResponse> {
   try {
      const {
        page = 1,
        limit = 10,
        search,
        filters,
        sortBy,
        sortOrder,
        selectedFields
      } = input;

      const query: any = {};

      // === Get dynamic fields ===
      const sampleDoc = await this.exceptionModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // === Projection & Searchable Fields ===
      let projection: Record<string, number> = {};
      const excludedFromSearch = ['__v', '_id', 'createdAt', 'updatedAt'];

      const searchableFields: string[] =
        selectedFields && Object.keys(selectedFields).length > 0
          ? Object.keys(selectedFields).filter(f => selectedFields[f] === 1 && !excludedFromSearch.includes(f))
          : allFields.filter(f => !excludedFromSearch.includes(f));

      projection =
        selectedFields && Object.keys(selectedFields).length > 0
          ? selectedFields
          : allFields.reduce((acc, f) => {
            if (!excludedFromSearch.includes(f)) acc[f] = 1;
            return acc;
          }, {} as Record<string, number>);

      // === Search Handling ===
      if (search?.trim()) {
        const term = search.trim();
        const regex = { $regex: term.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };

        query.$or = searchableFields.map(field => ({ [field]: regex }));

        if (/^[0-9a-fA-F]{24}$/.test(term)) {
          query.$or.push({ _id: term });
        }

        if (term.match(/^\d{4}-\d{2}-\d{2}/)) {
          const date = new Date(term);
          if (!isNaN(date.getTime())) {
            const start = new Date(date.setHours(0, 0, 0, 0));
            const end = new Date(date.setHours(23, 59, 59, 999));
            query.$or.push({ createdAt: { $gte: start, $lte: end } });
            query.$or.push({ updatedAt: { $gte: start, $lte: end } });
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        let parsedFilters: Record<string, any>;
        try {
          parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : {};
        } catch (e) {
          throw new HttpException('Invalid filters JSON', 400);
        }

        Object.entries(parsedFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            query[key] = typeof value === 'string'
              ? { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' }
              : value;
          }
        });
      }

      // === Sorting ===
      const sortQuery: Record<string, 1 | -1> = {};
      if (sortBy) {
        sortQuery[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sortQuery.createdAt = -1;
      }

      // === Pagination ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.exceptionModel.countDocuments(query);
      const exceptions = await this.exceptionModel
        .find(query)
        .select(projection)
        .sort(sortQuery)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items:exceptions,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems: totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1,
        },
      };

    } catch (error) {
      console.error('Error in findAll exceptions:', error);
      throw new HttpException(
        `Failed to retrieve exceptions: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async findById(id: string) {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          `Invalid ID format: ${id}`
        );
      }

      // Get exception without populate to avoid schema conflicts
      const exception = await this.exceptionModel.findById(id)
        .lean()
        .exec();

      if (!exception) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          `Exception not found with ID: ${id}`
        );
      }

      // Manually populate processId using collection approach
      if (exception.processId) {
        try {
          const processCollection = await this.getProcessCollection();
          const process = await processCollection.findOne({ _id: new Types.ObjectId(exception.processId) });
          if (process) {
            exception.processId = process._id;
          }
        } catch (processError) {
          console.warn('Failed to populate processId:', processError.message);
          // Keep original processId if population fails
        }
      }

      // Manually populate templateId using collection approach
      if (exception.templateId) {
        try {
          const templateCollection = await this.getTemplateCollection();
          const template = await templateCollection.findOne({ _id: new Types.ObjectId(exception.templateId) });
          if (template) {
            exception.templateId = template._id;
          }
        } catch (templateError) {
          console.warn('Failed to populate templateId:', templateError.message);
          // Keep original templateId if population fails
        }
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { exception: exception }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ExceptionService.findById:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to find exception: ${(err as globalThis.Error).message}`
      );
    }
  }

  async update(input: UpdateExceptionInput): Promise<Success> {
    const id = input.id;
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          `Invalid ID format: ${id}`
        );
      }

      const exception = await this.exceptionModel.findById(id);
      if (!exception) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          `Exception not found with ID: ${id}`
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const updateData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        updatedAt: new Date(),
      };

      delete updateData.flattenedValues;
      delete updateData.id;

      const updatedException = await this.exceptionModel
        .findByIdAndUpdate(id, updateData, { new: true })
        // .populate('templateId')
        .populate('processId')
        .lean()
        .exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { exception: this.transformExceptionDocument(updatedException) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ExceptionService.update:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to update exception: ${(err as globalThis.Error).message}`
      );
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          `Invalid ID format: ${id}`
        );
      }

      const exception = await this.exceptionModel.findById(id);
      if (!exception) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          `Exception not found with ID: ${id}`
        );
      }

      await this.exceptionModel.findByIdAndDelete(id);

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { message: 'Exception deleted successfully' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ExceptionService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to delete exception: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(filters?: Record<string, any>): Promise<Success> {
    try {
      let query: any = { isActive: true };

      if (filters) {
        Object.keys(filters).forEach(key => {
          if (filters[key] !== null && filters[key] !== undefined) {
            if (key === 'templateId' || key === 'processId') {
              if (!Types.ObjectId.isValid(filters[key])) {
                throw new Error(
                  HttpStatus.BAD_REQUEST,
                  ResponseCode.INVALID_PARAMETERS,
                  ErrorType.FORM,
                  `Invalid ${key} format: ${filters[key]}`
                );
              }
              query[key] = new Types.ObjectId(filters[key]);
            } else if (key === 'exceptions') {
              query[key] = { $in: filters[key] };
            } else if (key.startsWith('flattenedValues.')) {
              const fieldName = key.replace('flattenedValues.', '');
              query[`flattenedValues.${fieldName}`] = filters[key];
            } else {
              query[key] = filters[key];
            }
          }
        });
      }

      const count = await this.exceptionModel.countDocuments(query).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { count }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ExceptionService.count:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to get exception count: ${(err as globalThis.Error).message}`
      );
    }
  }

  private transformExceptionDocument(doc: any): any {
    if (!doc) return null;
    return {
      id: doc._id.toString(),
      createdBy: doc.createdBy,
      updatedBy: doc.updatedBy,
      isActive: doc.isActive,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      templateId: doc.templateId?._id?.toString() || doc.templateId?.toString(),
      values: doc.values,
      flattenedValues: doc.flattenedValues,
      processId: doc.processId?._id?.toString() || doc.processId?.toString(),
      exceptions: doc.exceptions,
    };
  }
} 