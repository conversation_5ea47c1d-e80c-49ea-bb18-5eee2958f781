version: '3.8'

networks:
  mynetwork:
    driver: bridge

services:
  identity-service:
    build:
      context: .
      dockerfile: apps/identity/Dockerfile
    container_name: identity-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
    ports:
      - "4001:4001"

  des-service-service:
    build:
      context: .
      dockerfile: apps/des-service/Dockerfile
    container_name: des-service-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
    ports:
      - "4002:4002"

  provider-service:
    build:
      context: .
      dockerfile: apps/provider/Dockerfile
    container_name: provider-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
    ports:
      - "4003:4003"   
      
  notification-service-service:
    build:
      context: .
      dockerfile: apps/notification-service/Dockerfile
    container_name: notification-service-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
    ports:
      - "4004:4004" 

  gateway-service:
    build:
      context: .
      dockerfile: apps/gateway/Dockerfile
    container_name: gateway-service
    networks:
      - mynetwork
    depends_on:
      - identity-service
      - des-service-service
      - provider-service
      - notification-service-service
    environment:
      - NODE_ENV=development
    ports:
      - "4000:4000"
    restart: always
