import { ArgsType, Field, InputType, ObjectType, Int, ID } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsBoolean, IsMongoId, IsNotEmpty } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { DocumentEntity } from '../entities/document.entity';

@InputType()
export class CreateDocumentInput {
  @Field()
  @IsString()
  @IsNotEmpty()
  documentType: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  documentName: string;

  @Field()
  @IsString()
  @IsNotEmpty()
  documentUrl: string;

  @Field(() => GraphQLJSON)
  values: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;
  
  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

@InputType()
export class UpdateDocumentInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  documentType?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  documentName?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  documentUrl?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  values?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

@ObjectType()
export class DocumentPaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;
}

@ObjectType()
export class DocumentsResponse {
  @Field(() => [DocumentEntity])
  documents: DocumentEntity[];

  @Field(() => DocumentPaginationMeta)
  pagination: DocumentPaginationMeta;
}

@InputType()
export class DocumentFilterInput {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    documentType?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    documentName?: string;
}

@InputType()
export class DocumentSortInput {
  @Field()
  @IsString()
  field: string;

  @Field({ defaultValue: false })
  @IsBoolean()
  ascending: boolean;
}

@ArgsType()
export class FindAllDocumentsArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => DocumentFilterInput, { nullable: true })
  @IsOptional()
  filters?: DocumentFilterInput;

  @Field(() => DocumentSortInput, { nullable: true })
  @IsOptional()
  sort?: DocumentSortInput;
} 