import { InputType, Field, Int } from '@nestjs/graphql';

@InputType()
export class CreateProcessInput {
  @Field()
  name: string;
}

@InputType()
export class CreateActionCodeInput {
  @Field()
  code: string;

  @Field()
  processId: string;
}

@InputType()
export class CreateStatusCodeInput {
  @Field()
  code: string;

  @Field()
  processId: string;
}

@InputType()
export class SortInput {
  @Field(() => String)
  field: string;

  @Field(() => String)
  order: 'asc' | 'desc';
}

@InputType()
export class FilterInput {
  @Field(() => String, { nullable: true })
  search?: string;

  @Field(() => String, { nullable: true })
  processId?: string;

  @Field(() => Int, { nullable: true, defaultValue: 0 })
  skip?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;

  @Field(() => [SortInput], { nullable: true })
  sort?: SortInput[];

  @Field(() => [FilterConditionInput], { nullable: true })
  filters?: FilterConditionInput[];
}

@InputType()
export class FilterConditionInput {
  @Field(() => String)
  field: string;

  @Field(() => String)
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'exists';

  @Field(() => String, { nullable: true })
  value?: string;

  @Field(() => [String], { nullable: true })
  values?: string[];
}
