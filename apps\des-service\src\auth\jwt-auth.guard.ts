import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import * as jwt from 'jsonwebtoken';
import { HttpStatus, ErrorType, Error, ResponseCode } from '@app/error';
import { GqlExecutionContext } from '@nestjs/graphql';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;
    const authHeader = request?.headers?.authorization;

    if (!authHeader) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.AUTHENTICATION,
        'Authorization header missing',
      );
    }

    const token = authHeader.replace('Bearer ', '');
    if (!token) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.AUTHENTICATION,
        'Token missing',
      );
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
      request.user = decoded;
      return true;
    } catch (error) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.AUTHENTICATION,
        'Invalid token',
      );
    }
  }
} 