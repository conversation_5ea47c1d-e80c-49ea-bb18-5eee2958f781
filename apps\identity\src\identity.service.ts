import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, set } from 'mongoose';
import * as nodemailer from 'nodemailer';
import * as jwt from 'jsonwebtoken';
import { CreateUserInput, TwoFAInput } from './dto/client.dto';
import {
  TOtpVerificationResult,
  OtpVerificationResult,
  OtpSend,
  DecodedToken
} from './interfaces/identity.interfaces';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { authenticator } from 'otplib';
import { ObjectId } from 'mongodb';
import * as fs from 'fs';
import * as path from 'path';
import { ValidateTokenResponse } from './dto/ValidateTokenResponse.do';
import { BaseResponse } from './dto/base.response.dto';
import { Organisation, UserType } from './entities/client.entities';
import { UpdateClientInput } from './dto/client.dto';
import { UpdateAgentInput } from './dto/agent.dto';
import { instanceToPlain } from 'class-transformer';
import { MongoConnectionService } from '@app/db';
import { ulid } from 'ulid';
import { User } from './entities/user.entity';
import { OrganisationProcessMapping } from './entities/organisation-process-mapping.entity';
import { Process } from './entities/process.entity';
import { OrganisationSettingsService } from './services/organisation-settings.service';
import { OrganisationSettings, PreferredCommunication } from './entities/organisation-settings.entity';
import { UserProcessAssignment } from './entities/user-process-assignment.entity';
import { OrganisationUser } from './entities/organisation-user.entity';

@Injectable()
export class IdentityService {
  constructor(
    @InjectModel(Organisation.name) private readonly userModel: Model<Organisation>,
    @InjectModel(OrganisationUser.name) private readonly organisationUserModel: Model<OrganisationUser>,
    @InjectModel(User.name) private readonly sysUserModel: Model<User>,
    @InjectModel(OrganisationSettings.name) private readonly OrganisationSettingsModel: Model<OrganisationSettings>,
    @InjectModel(UserProcessAssignment.name) private userProcessAssignmentModel: Model<UserProcessAssignment>,
    private readonly mongoConnectionService: MongoConnectionService,
    @InjectModel(Process.name) private readonly processModel: Model<any>,
    @InjectModel(OrganisationProcessMapping.name) private readonly organisationProcessMappingModel: Model<any>,
    private readonly organisationSettingsService: OrganisationSettingsService,
  ) { }

  /**
   * Get user collection based on orgId, fallback to master DB
   */
  private async getUserCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('organisations', orgId);
  }

  /**
   * Get tenant database based on orgId, fallback to master DB
   */
  private async getTenantDatabase(orgId?: string) {
    return this.mongoConnectionService.getDatabaseByOrgId(orgId);
  }

  async findAll(orgId?: string): Promise<any[]> {
    const userCollection = await this.getUserCollection(orgId);
    return userCollection.find({}).toArray();
  }

  /**
   * Fetches all users from the system with dynamic search, filter, and sort functionality.
   * @param search - Search term to search across multiple fields
   * @param filters - Object containing field-specific filters
   * @param sortBy - Field to sort by
   * @param sortOrder - Sort order ('asc' or 'desc')
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns A promise that resolves to users with pagination info.
   */
  /**
   * Enhanced findAllUsers with dynamic field support
   * Handles search, filter, and sort on both schema fields and dynamic template fields stored at root level
   */
  async findAllUsers(
    search?: string,
    selectedFields?: Record<string, any>, // optional field projection
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page = 1,
    limit = 10,
    userId?: string,
    orgId?: string,
    type?: string
  ): Promise<{
    users: any[];
    pagination: {
      page: number;
      limit: number;
      total?: number;
      totalItems: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const userCollection = await this.getUserCollection(orgId);
    let query: any = {};

    // If filters include main_client, do not override query._id or query.type
    const hasMainClientFilter =
      (filters && (
        (typeof filters === 'string' && JSON.parse(filters)?.filters?.main_client) ||
        (typeof filters === 'object' && (filters.filters?.main_client || filters.main_client))
      ));

    if (!hasMainClientFilter) {
      if (userId && !type) {
        const assignments = await this.userProcessAssignmentModel
          .find({ userId })
          .distinct('organisationId');
        const organisationIds = assignments.map(id => new ObjectId(id));

        if (organisationIds.length > 0) {
          query._id = { $in: organisationIds };
        }
      }
      else if (type === "Provider") {
        query.type = "MAIN_CLIENT";
      }
    }
    // === Load sample document for field detection ===
    const sampleDoc = await userCollection.findOne();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

    const excludedFields = [
      '__v', '_id', 'values', 'flattenedValues', 'token', 'secretKey',
      'qrcode', 'attempt', 'dailyOtpAttempts', 'lastOtpAttemptDate',
      'createdAt', 'updatedAt'
    ];

    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
      searchableFields = Object.keys(selectedFields).filter(
        key => selectedFields[key] === 1 && !excludedFields.includes(key)
      );
    } else {
      projection = allFields.reduce((acc, key) => {
        if (!['__v', 'values', 'flattenedValues'].includes(key)) acc[key] = 1;
        return acc;
      }, {} as Record<string, number>);

      searchableFields = allFields.filter(field => !excludedFields.includes(field));
    }

    // === Search Handling ===
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };

      // Search only in the determined searchable fields
      query.$or = searchableFields.map(field => ({ [field]: regex }));

      // Allow search by _id if valid ObjectId
      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: searchTerm });
      }

      // Handle date search separately if the search term looks like a date
      if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
        try {
          const searchDate = new Date(searchTerm);
          if (!isNaN(searchDate.getTime())) {
            // Search for dates within the same day
            const startOfDay = new Date(searchDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(searchDate);
            endOfDay.setHours(23, 59, 59, 999);

            query.$or.push(
              { createdAt: { $gte: startOfDay, $lte: endOfDay } },
              { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
            );
          }
        } catch (error) {
          // Ignore invalid date formats
        }
      }
    }
    let parsedFilters: Record<string, any> = {};
    // === Filters Handling ===
    if (filters) {
      try {
        parsedFilters =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters?.filters || filters;

        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i',
                }
                : value;
          }
        }
      } catch (err) {
        console.warn('Invalid filters provided. Skipping filters.', err);
      }
    }


    if (userId && parsedFilters.main_client) {
      delete query.created_by;
    }

    // === Sorting Handling ===
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }
    // === Pagination ===
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;
    const totalItems = await userCollection.countDocuments(query);
    console.log("query", query)
    const users = await userCollection
      .find(query)
      .project(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .toArray();

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      users,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems ?? 0,        // ✅ add this
        totalItems: totalItems ?? 0,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }



  /**
   * Sends a One-Time Password (OTP) to a user's email.
   * If 2FA is enabled for the user, informs the caller without sending an OTP.
   * Generates a new secret and QR code if not already set.
   *
   * @param email The email address to send the OTP to.
   * @throws Throws an error if email is invalid or user not found.
   * @returns Promise resolving to an OtpSend response indicating OTP sent or 2FA status.
   */
  async sendOtp(email: string): Promise<OtpSend> {
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.INVALID_EMAIL,
        ErrorType.IDENTITY,
        'Invalid Email',
      );
    }

    const user = await this.organisationUserModel.findOne({ email: email.trim().toLowerCase() }).exec();

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }
    let secret = user.secretKey;

    if (!secret) {
      secret = authenticator.generateSecret();
      const otpauthUrl = authenticator.keyuri(email, 'ASP-RCM', secret) + '&period=30';
      await this.organisationUserModel.updateOne(
        { email },
        { $set: { secretKey: secret, qrcode: otpauthUrl } },
      );
    }

    authenticator.options = { step: 30 };
    const otp = authenticator.generate(secret);

    await this.sendEmail(email, otp);

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      {
        is2FAenabled: user.is2FAenabled,
        bypass2FA: user.bypass2FA,
      },
    ).toJSON() as unknown as OtpSend;
  }

  /**
   * Verifies a provided OTP for the given email.
   * Uses the stored secret key to check OTP validity.
   *
   * @param email The email address of the user.
   * @param otp The OTP string to verify.
   * @throws Throws an error if user not found, no secret set, or OTP invalid.
   * @returns Promise resolving to OtpVerificationResult with user secret and QR code.
   */
  async verifyOtp(email: string, otp: string): Promise<OtpVerificationResult> {
    const normalizedEmail = email.trim().toLowerCase();
    const user = await this.organisationUserModel.findOne({ email: normalizedEmail }).exec();

    if (!user || !user.secretKey) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found or OTP not set.',
      );
    }

    authenticator.options = {
      step: 30,
      window: 1,
    };

    const isValid = authenticator.check(otp, user.secretKey);

    if (!isValid) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.OTP_INVALID,
        ErrorType.IDENTITY,
        'Invalid OTP.',
      );
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      {
        qrcode: user.qrcode ?? '',
        secret_code: user.secretKey,
      },
    ).toJSON() as unknown as OtpVerificationResult;
  }

  /**
   * Verifies a Time-based One-Time Password (TOTP) for 2FA login.
   * Can skip 2FA check if requested.
   *
   * @param email User's email.
   * @param otp The TOTP code (optional if skip2FACheck is true).
   * @param skip2FACheck Flag to bypass 2FA verification.
   * @throws Throws error if user not found, no secret set, or invalid TOTP.
   * @returns Promise resolving to TOtpVerificationResult including a JWT token.
   */
  async verifyTotp(
    email: string,
    otp?: string,
    skip2FACheck?: boolean,
  ): Promise<TOtpVerificationResult> {
    const user = await this.organisationUserModel.findOne({ email }).exec();

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    // Fetch organisation settings for this user
    const organisationId = user.organisationId || user.orgId;
    const organisationSettings = organisationId ? await this.organisationSettingsService.findByOrganisationId(organisationId) : null;
    console.log("organisationSettings", organisationSettings);

    // organisationSettings is now available for use in further logic

    if (!user.secretKey) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOTP_INVALID,
        ErrorType.IDENTITY,
        'TOTP not set.',
      );
    }
    if (skip2FACheck) {
      return this.generateAndSaveToken(user, false, true, organisationSettings);
    }

    authenticator.options = { step: 30, window: 3, digits: 6 };
    const isValid = authenticator.check(otp ?? '', user.secretKey);

    if (!isValid) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOTP_INVALID,
        ErrorType.IDENTITY,
        'Invalid TOTP.',
      );
    }

    return this.generateAndSaveToken(user, true, false, organisationSettings);
  }

  /**
   * Generates a JWT token for the user and updates the user document with token and 2FA flags.
   *
   * @param user User document.
   * @param is2FAenabled Boolean indicating if 2FA was verified.
   * @param bypass2FA Boolean indicating if 2FA was bypassed.
   * @returns Promise resolving to TOtpVerificationResult containing the JWT token.
   */
  private async generateAndSaveToken(
    user: any,
    is2FAenabled: boolean,
    bypass2FA: boolean,
    organisationSettings?: OrganisationSettings | null,
  ): Promise<TOtpVerificationResult> {
    const jwtSecret = process.env.JWT_SECRET ?? 'fallback-secret';
    const token = jwt.sign(
      { userId: user._id, role: user.roleId, orgId: user.orgId || "" , suborgId: user.subOrganisationId || "", organisationId: user.organisationId || ""},
      jwtSecret,
      { expiresIn: '1d' },
    );

    await this.organisationUserModel.updateOne(
      { _id: user._id },
      { token, is2FAenabled, bypass2FA },
    );

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      {
        token,
        user: user,
        settings: organisationSettings,
        orgId: user.orgId,
      },
    ).toJSON() as unknown as TOtpVerificationResult;
  }

  /**
   * Sends an email using Nodemailer SMTP transporter.
   *
   * @param to Recipient email address.
   * @param text Email body content.
   */
  private getOtpEmailTemplate(otp: string): string {
    const filePath = path.join(__dirname, 'templates', 'otp-email-template.html');
    let template = fs.readFileSync(filePath, 'utf8');
    return template.replace('{{OTP}}', otp);
  }

  public async sendEmail(to: string, otp: string): Promise<void> {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.SMTP_USER, // your Gmail address
        pass: process.env.SMTP_PASS, // your app password
      },
    });


    const htmlContent = this.getOtpEmailTemplate(otp);

    try {
      await transporter.sendMail({
        from: `"ASP RCM" <${process.env.SMTP_USER}>`,
        to,
        subject: 'OTP Verification - RCM Genie',
        html: htmlContent,
      });

      console.log(`Email sent successfully to ${to} via Mailtrap SMTP`);
    } catch (error) {
      console.error(`Failed to send email to ${to}:`, error.message);
      throw error;
    }
  }

  /**
   * Creates a new user in the database with validation for client type based on creator.
   * - If creator exists in user model, can only create SUB_CLIENT
   * - If creator exists in agent model, can only create MAIN_CLIENT
   * - If creator not found in either, throws error
   *
   * @param input User creation input DTO.
   * @throws Error if email already registered or invalid creator/type combination.
   * @returns Success response with the created user.
   */
  async createUser(input: CreateUserInput, orgId?: string): Promise<Success> {
    const userCollection = await this.getUserCollection(orgId);

    // Check for existing user by email
    const existingUser = await userCollection.findOne({ email: input.email });
    if (existingUser) {
      throw new Error(
        HttpStatus.CONFLICT,
        ResponseCode.ALREADY_EXISTS,
        ErrorType.IDENTITY,
        'Email already registered',
      );
    }

    if (!input.created_by) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.IDENTITY,
        'created_by field is required',
      );
    }

    // Validate creator (in sys users)
    const creator = await this.sysUserModel.findById(input.created_by);
    if (!creator) {
      const user = await userCollection.findOne({ _id: new ObjectId(input.created_by) });
      if (!user) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Creator not found',
        );
      }
    }

    const inputPlain = instanceToPlain(input) as Record<string, any>;
    const processedFlattenedValues: Record<string, any> = {};

    if (typeof input.flattenedValues === 'object') {
      Object.assign(processedFlattenedValues, input.flattenedValues);
    }

    let userData: Record<string, any> = {
      ...inputPlain,
      ...processedFlattenedValues,
      values: input.values || {},
    };

    delete userData.flattenedValues;

    // === MAIN_CLIENT Logic ===
    if (input.type === UserType.MAIN_CLIENT) {
      const generatedOrgId = ulid();

      const tenantDb = await this.getTenantDatabase(generatedOrgId);
      await tenantDb.collection('organisations').insertOne(userData);

      const mainClientData = {
        ...input,
        orgId: generatedOrgId,
        values: input.values || {},
        ...processedFlattenedValues,
        is2FAenabled: false,
        bypass2FA: false
      };
      delete mainClientData.flattenedValues;
      const newMainClient = new this.userModel(mainClientData);
      await newMainClient.save();

      // Create default organisation settings
      await this.organisationSettingsService.create({
        organisationId: newMainClient._id.toString(),
        preferredCommunication: PreferredCommunication.EMAIL,
        accessPortal: true,
        accessOrganisation: true,
        expiryTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        {
          user: newMainClient,
        },
      );
    }

    // === SUB_CLIENT Logic ===
    if (input.type === UserType.SUB_CLIENT) {
      if (!input.main_client) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.IDENTITY,
          'main_client field is required for sub-client',
        );
      }

      const mainClient = await this.userModel.findById(input.main_client);
      if (!mainClient || !mainClient.orgId) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Main client not found or missing orgId',
        );
      }

      const tenantDb = await this.getTenantDatabase(mainClient.orgId);
      const subClientData = {
        ...input,
        created_by: input.created_by,
        main_client: input.main_client,
        orgId: mainClient.orgId,
        createdAt: new Date(),
        ...processedFlattenedValues,
        is2FAenabled: false,
        bypass2FA: false
      };
      delete subClientData.flattenedValues;
      await tenantDb.collection('organisations').insertOne(subClientData);

      const newSubClient = new this.userModel(subClientData);
      await newSubClient.save();

      // Create default organisation settings for sub-organisation
      await this.organisationSettingsService.create({
        organisationId: newSubClient._id.toString(),
        preferredCommunication: PreferredCommunication.EMAIL,
        accessPortal: true,
        accessOrganisation: true,
        expiryTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });

      // Create OrganisationProcessMapping for all processes with isActive: false
      const allProcesses = await this.processModel.find().lean();
      for (const process of allProcesses) {
        await this.organisationProcessMappingModel.create({
          organisationId: mainClient._id.toString(),
          subOrganisationId: newSubClient._id.toString(),
          processId: (process._id as any).toString(),
          processName: process.name,
          isActive: false
        });
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        {
          user: newSubClient,
        },
      );
    }

    throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_PARAMETERS,
      ErrorType.IDENTITY,
      'Invalid user creation scenario',
    );
  }

  /**
   * Updates an existing client with new data including dynamic fields
   * @param clientId - The ID of the client to update
   * @param input - Update input containing new values
   * @returns Success response with updated client
   */
  async updateClient(clientId: string, input: UpdateClientInput): Promise<Success> {
    const existingClient = await this.userModel.findById(clientId);

    if (!existingClient) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'Client not found',
      );
    }

    if (input.email) {
      const emailExists = await this.userModel.findOne({
        email: input.email,
        _id: { $ne: clientId }, // Exclude the current client from check
      });

      if (emailExists) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.ALREADY_EXISTS,
          ErrorType.IDENTITY,
          'Email already exists',
        );
      }
    }
    // Convert DTO to plain object
    const inputPlain = instanceToPlain(input) as Record<string, any>;

    // Process flattenedValues - these will be stored as key-value pairs at root level
    const processedFlattenedValues: Record<string, any> = {};

    if (typeof input.flattenedValues === 'object') {
      // Handle object format: { key1: value1, key2: value2 }
      Object.assign(processedFlattenedValues, input.flattenedValues);
    }

    const updateData: Record<string, any> = {
      ...inputPlain,
      ...processedFlattenedValues, // Spread dynamic fields to root level
    };

    // Remove flattenedValues to avoid duplication
    delete updateData.flattenedValues;

    // Update values if provided
    if (input.values) {
      updateData.values = input.values;
    }
    const updatedClient = await this.userModel.findByIdAndUpdate(
      clientId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      { client: updatedClient },
    );
  }

  /**
   * Resends the OTP to the user's email if under daily limit.
   * Limits OTP resends to 5 times per day.
   *
   * @param email User's email.
   * @returns Message indicating success or limit reached.
   * @throws Error if user not found.
   */
  async resendOtp(email: string): Promise<{ message: string }> {
    const user = await this.organisationUserModel.findOne({ email });

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }
    const today = new Date().toISOString().slice(0, 10);
    const lastAttempt = user.lastOtpAttemptDate?.toISOString().slice(0, 10);

    let dailyAttempts = user.dailyOtpAttempts ?? 0;
    const totalAttempts = user.attempt ?? 0;

    if (today !== lastAttempt) {
      dailyAttempts = 0;
    }

    if (dailyAttempts >= 5) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.OTP_LIMIT_REACHED,
        ErrorType.IDENTITY,
        'You have reached the daily limit of 5 OTP resend attempts. Please try again tomorrow.',
      );
    }

    let secret = user.secretKey;
    let otpauthUrl = user.qrcode;

    if (!secret) {
      secret = authenticator.generateSecret();
      otpauthUrl = authenticator.keyuri(email, 'ASP-RCM', secret) + '&period=30';

      await this.organisationUserModel.updateOne(
        { email },
        {
          $set: {
            secretKey: secret,
            qrcode: otpauthUrl,
          },
        },
      );
    }

    authenticator.options = { step: 30 };
    const otp = authenticator.generate(secret);

    await this.sendEmail(
      email,
      otp,
    );

    await this.organisationUserModel.updateOne(
      { email },
      {
        $set: {
          attemp: totalAttempts + 1,
          dailyOtpAttempts: dailyAttempts + 1,
          lastOtpAttemptDate: new Date(),
        },
      },
    );

    return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.IDENTITY);
  }

  /**
   * Checks if a user exists by email (case-insensitive).
   *
   * @param email The email to check.
   * @returns True if user exists, false otherwise.
   */
  async agentExistsByEmail(email: string): Promise<boolean> {
    const user = await this.sysUserModel.findOne({
      email: { $regex: `^${email}$`, $options: 'i' },
    });

    return !!user;
  }

  /**
   * Handles user request to reset 2FA.
   * Sends an email to admin notifying about the reset request.
   *
   * @param email User's email requesting 2FA reset.
   */
  async request2FAReset(email: string): Promise<{ message: string }> {
    const user = await this.organisationUserModel.findOne({ email });

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    if (!user.is2FAenabled) {
      return new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.ALREADY_DISABLED,
        ErrorType.IDENTITY,
        '2FA is Already Disabled',
      );
    }
    const adminEmail = process.env.ADMIN_EMAIL;

    if (!adminEmail) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'Admin email not found.',
      );
    }

    await this.sendEmail(
      adminEmail,
      `User 2FA Reset Request: User ${email} has requested a 2FA reset.\n\nPlease log in to the admin panel and review this request.`,
    );

    return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.IDENTITY);
  }

  async adminReset2FA(
    email: string,
    token: string,
  ): Promise<{ message: string }> {
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret', { algorithms: ['HS256'] });
    } catch {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOKEN_INVALID,
        ErrorType.IDENTITY,
        'Invalid token.',
      );
    }

    if (decoded.role !== 'admin') {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.IDENTITY,
        'Unauthorized access.',
      );
    }

    const user = await this.organisationUserModel.findOne({ email });

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    const secret = authenticator.generateSecret();
    const otpauthUrl = authenticator.keyuri(email, 'ASP-RCM', secret) + '&period=30';

    await this.organisationUserModel.updateOne(
      { email },
      {
        $set: {
          is2FAenabled: false,
          secretKey: secret,
          qrcode: otpauthUrl,
        },
      },
    );

    await this.sendEmail(
      email,
      `Your 2FA has been reset. Use the following secret to reconfigure your 2FA:\n\nSecret: ${secret}\nQR Setup: ${otpauthUrl}`,
    );
    return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.IDENTITY);
  }

  async validateMicrosoftToken(token: string): Promise<ValidateTokenResponse> {
    if (!token) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.TOKEN_INVALID,
        ErrorType.IDENTITY,
        'Token is required.'
      );
    }

    try {
      const decoded = jwt.decode(token) as DecodedToken | null;
      if (!decoded) {
        throw new Error(
          HttpStatus.UNAUTHORIZED,
          ResponseCode.TOKEN_INVALID,
          ErrorType.IDENTITY,
          'Token decoding failed.'
        );
      }

      console.log("Validating Microsoft token:", decoded);

      const email =
        (decoded?.preferred_username ?? decoded?.email ?? decoded?.upn ?? 'unknown').toLowerCase();
      const name =
        decoded?.name ?? decoded?.given_name ?? decoded?.unique_name ?? 'unknown';
      const oid = decoded?.oid;

      const roleIdMap: Record<string, string> = {
        'b79fbf4d-3ef9-4689-8143-76b194e85509': 'Application Administrator',
        '62e90394-69f5-4237-9190-012177145e10': 'Global Administrator',
        'fe930be7-5e62-47db-91af-98c3a49a38b1': 'User Administrator',
      };

      let roles = decoded?.roles ?? decoded?.role ?? decoded?.groups ?? [];
      if ((!roles || roles.length === 0) && Array.isArray(decoded?.wids)) {
        roles = decoded.wids.map(wid => roleIdMap[wid] ?? wid);
      }

      const rolesArray = Array.isArray(roles) ? roles : [roles];
      const roleString = rolesArray.map(String).join(',');
      console.log("email", email);

      // Final name resolution
      const resolvedName =
        typeof name === 'string'
          ? name
          : (name && typeof (name as any).toString === 'function' ? (name as any).toString() : 'unknown');

      // Check existence in sysUserModel
      const userExists = await this.sysUserModel.exists({ email });
      console.log("User exists in sysUserModel:", userExists);

      if (!userExists || userExists === null) {
        throw new Error(
          HttpStatus.UNAUTHORIZED,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'You are not registered in this application. Please contact your system administrator for access.'
        );
      } else {
        return {
          email,
          exists: !!userExists,
          name: resolvedName,
          role: roleString,
        };
      }
    } catch (error) {
      // Only throw TOKEN_INVALID for actual token errors
      if (error?.code === ResponseCode.TOKEN_INVALID || error?.message === 'Token decoding failed.' || error?.message === 'Token is required.') {
        throw new Error(
          HttpStatus.UNAUTHORIZED,
          ResponseCode.TOKEN_INVALID,
          ErrorType.IDENTITY,
          'Token validation failed.'
        );
      }
      // For USER_NOT_FOUND and all other errors, rethrow as is
      throw error;
    }
  }


  /**
   * Logs out the user by clearing their stored JWT token.
   * @param email - User's email address.
   */
  async logout(token: string): Promise<{ message: string }> {
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret', { algorithms: ['HS256'] });
    } catch {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOKEN_INVALID,
        ErrorType.IDENTITY,
        'Invalid token.',
      );
    }

    const user = await this.organisationUserModel.findOne({ _id: decoded.userId });

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    await this.organisationUserModel.updateOne(
      { _id: user._id },
      {
        $unset: {
          token: '',
        },
      },
    );

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      { message: 'Successfully logged out.' },
    );
  }

  async disable2FA(input: TwoFAInput) {
    const user = await this.organisationUserModel.findOne({ email: input.email });

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    // You probably want to **disable** 2FA here
    await this.organisationUserModel.updateOne(
      { _id: user._id },
      {
        is2FAenabled: input.is2FAenabled,
        bypass2FA: input.bypass2FA,
      },
    );
    const updatedUser = await this.organisationUserModel.findOne({ _id: user._id });

    const secret = authenticator.generateSecret();
    const otpauthUrl = authenticator.keyuri(input.email, 'ASP-RCM', secret) + '&period=30';
    await this.organisationUserModel.updateOne(
      { email: input.email },
      { $set: { secretKey: secret, qrcode: otpauthUrl } },
    );
    authenticator.options = { step: 30 };
    const otp = authenticator.generate(secret);

    await this.sendEmail(input.email, otp);

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      { user: updatedUser },
    );
  }

  async getUser(token: string) {
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret', { algorithms: ['HS256'] });
    } catch {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.IDENTITY,
        'Invalid or expired token.',
      );
    }
    console.log("Decoded token:", decoded);

    // Assuming the decoded token has the email (or _id)
    const user = await this.organisationUserModel.findOne({ _id: new ObjectId("6866293b3f027a5517df9786") });
    console.log("adsadqadqd", user);

    if (!user) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.IDENTITY,
        'User not found.',
      );
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.IDENTITY,
      { user },
    );
  }

  /**
   * Fetches a client by their ID.
   * @param clientId - The client ID to fetch.
   * @returns A BaseResponse containing the client data or error.
   */
  async getByClientId(clientId: string): Promise<BaseResponse> {
    try {
      const client = await this.userModel.findById(clientId).exec();

      if (!client) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Client not found.',
        );
      }

      // Fetch organisation settings
      const orgId = client._id.toString();
      let organisationSettings: OrganisationSettings | null = null;
      if (orgId) {
        try {
          organisationSettings = await this.organisationSettingsService.findByOrganisationId(orgId);
        } catch {
          organisationSettings = null;
        }
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        {
          client,
          organisationSettings // optional field
        },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to fetch client.',
      );
    }
  }

  /**
   * Fetches all clients (sub-clients) that belong to a specific main client with dynamic search, filter, and sort functionality.
   * @param mainClientId - The main client ID to fetch sub-clients for.
   * @param search - Search term to search across multiple fields
   * @param filters - Object containing field-specific filters
   * @param sortBy - Field to sort by
   * @param sortOrder - Sort order ('asc' or 'desc')
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns A BaseResponse containing the list of clients with pagination or error.
   */
  async getByClients(
    mainClientId: string,
    search?: string,
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page = 1,
    limit = 10
  ): Promise<BaseResponse> {
    try {
      // First verify that the main client exists
      const mainClient = await this.userModel.findById(mainClientId).exec();

      if (!mainClient) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Main client not found.',
        );
      }

      // Find all clients that have this main client as their parent
      let clients = await this.userModel.find({ created_by: mainClientId }).lean().exec();

      // Filter by search
      if (search) {
        const lowerSearch = search.toLowerCase();
        clients = clients.filter(client => {
          const matchBasic =
            client.name?.toLowerCase().includes(lowerSearch) ||
            client.email?.toLowerCase().includes(lowerSearch) ||
            client.templateId?.toLowerCase().includes(lowerSearch) ||
            client._id.toString() === search ||
            client.status?.toLowerCase().includes(lowerSearch) ||
            client.type?.toLowerCase().includes(lowerSearch);

          if (matchBasic) return true;

          // Search in dynamic fields stored at root level
          // Fields like: EnrollmentType, GroupNPI, TIN, provider, ticketNumber, actionCode, statusCode, gender
          try {
            // const dynamicFields = this.getDynamicFields(client);

            // return dynamicFields.some(field => {
            //   const fieldValue = client[field];
            //   return fieldValue && String(fieldValue).toLowerCase().includes(lowerSearch);
            // });
          } catch {
            return false;
          }
        });
      }

      // Apply field filters
      if (filters) {
        let parsedFilters: any = typeof filters === 'string' ? JSON.parse(filters) : filters;
        parsedFilters = parsedFilters?.filters ?? parsedFilters;

        // Apply basic field filters (both schema and dynamic fields, exclude values field)
        Object.keys(parsedFilters).forEach(key => {
          if (key !== 'fieldFilters' && key !== 'values' && parsedFilters[key] !== undefined && parsedFilters[key] !== null) {
            clients = clients.filter(client => {
              // Get value directly from client (works for both schema and dynamic fields)
              const clientValue = client[key];
              const filterValue = parsedFilters[key];

              if (clientValue === undefined || clientValue === null) {
                return false;
              }

              if (typeof filterValue === 'string') {
                return String(clientValue).toLowerCase().includes(filterValue.toLowerCase());
              }
              return clientValue === filterValue;
            });
          }
        });

        // Apply field filters for dynamic fields (stored at root level)
        if (Array.isArray(parsedFilters?.fieldFilters)) {
          clients = clients.filter(client => {
            try {
              return parsedFilters.fieldFilters.every((filter: { label: string; value: any; fieldId?: string }) => {
                // Use fieldId if available, otherwise use label
                const fieldKey = filter.fieldId || filter.label;
                const fieldValue = client[fieldKey];

                if (fieldValue === undefined || fieldValue === null) {
                  return false;
                }

                // Handle different comparison types
                if (typeof filter.value === 'string' && typeof fieldValue === 'string') {
                  return fieldValue.toLowerCase().includes(filter.value.toLowerCase());
                }
                return fieldValue === filter.value;
              });
            } catch {
              return false;
            }
          });
        }
      }

      // Sorting (both schema fields and dynamic fields stored at root level)
      if (sortBy) {
        clients.sort((a, b) => {
          // Dynamic fields are stored directly at root level, so access them directly
          const aVal = a[sortBy];
          const bVal = b[sortBy];

          if (aVal == null && bVal == null) return 0;
          if (aVal == null) return sortOrder === 'asc' ? 1 : -1;
          if (bVal == null) return sortOrder === 'asc' ? -1 : 1;

          if (typeof aVal === 'number' && typeof bVal === 'number') {
            return sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
          }

          const aStr = String(aVal).toLowerCase();
          const bStr = String(bVal).toLowerCase();
          return sortOrder === 'asc' ? aStr.localeCompare(bStr) : bStr.localeCompare(aStr);
        });
      } else {
        // Default sort by creation date (newest first)
        clients.sort((a, b) => b._id.toString().localeCompare(a._id.toString()));
      }

      // Pagination
      const totalItems = clients.length;
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const totalPages = Math.ceil(totalItems / safeLimit);
      const safePage = Math.max(1, Math.min(page, totalPages));
      const skip = (safePage - 1) * safeLimit;

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        {
          clients: clients.slice(skip, skip + safeLimit),
          pagination: {
            page: safePage,
            limit: safeLimit,
            total: totalItems,
            totalItems,
            totalPages,
            hasNext: safePage < totalPages,
            hasPrev: safePage > 1,
          }
        },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to fetch clients.',
      );
    }
  }

  /**
   * Deletes a client from the system.
   * @param id - The client ID to delete.
   * @returns A BaseResponse indicating success or failure of client deletion.
   */
  async deleteClient(id: string): Promise<BaseResponse> {
    try {
      const client = await this.userModel.findById(id).exec();

      if (!client) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Client not found.',
        );
      }

      // Check if this is a main client with sub-clients
      if (client.type === UserType.MAIN_CLIENT) {
        const subClients = await this.userModel.find({ main_client: id }).exec();
        if (subClients.length > 0) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.IDENTITY,
            'Cannot delete main client with existing sub-clients. Please delete sub-clients first.',
          );
        }
      }

      await this.userModel.deleteOne({ _id: id }).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        { message: 'Client deleted successfully.' },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to delete client.',
      );
    }
  }

  // ==================== AGENT METHODS ====================

  /**
   * Fetches all agents from the system with dynamic search, filter, and sort functionality.
   * @param search - Search term to search across multiple fields
   * @param filters - Object containing field-specific filters
   * @param sortBy - Field to sort by
   * @param sortOrder - Sort order ('asc' or 'desc')
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns A promise that resolves to an array of Agent entities.
   */
  async findAllAgents(
    search?: string,
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page: number = 1,
    limit: number = 10
  ): Promise<{ agents: Organisation[]; total: number; page: number; limit: number }> {
    try {
      const query: any = {};

      // Dynamic search across multiple fields
      if (search) {
        const searchFields = ['name', 'email'];
        const searchConditions = searchFields.map(field => ({
          [field]: { $regex: search, $options: 'i' }
        }));
        query.$or = searchConditions;
      }

      // Dynamic filters
      if (filters) {
        Object.keys(filters).forEach(key => {
          const value = filters[key];
          if (value !== null && value !== undefined && value !== '') {
            // Handle different filter types
            if (typeof value === 'string') {
              // String fields - case insensitive partial match
              query[key] = { $regex: value, $options: 'i' };
            } else if (typeof value === 'boolean') {
              // Boolean fields - exact match
              query[key] = value;
            } else if (Array.isArray(value)) {
              // Array fields - match any value in array
              query[key] = { $in: value };
            } else if (typeof value === 'object' && value.hasOwnProperty('$gte') || value.hasOwnProperty('$lte')) {
              // Date/Number range filters
              query[key] = value;
            } else {
              // Default exact match
              query[key] = value;
            }
          }
        });
      }

      // Dynamic sorting
      const sortOptions: any = {};
      if (sortBy) {
        // Validate sortBy field exists in schema
        const validSortFields = ['name', 'email', 'createdAt', 'updatedAt', '_id'];
        if (validSortFields.includes(sortBy)) {
          sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
        } else {
          // Default sort by creation date if invalid field provided
          sortOptions['_id'] = -1;
        }
      } else {
        // Default sort by creation date (newest first)
        sortOptions['_id'] = -1;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute query with pagination
      const [agents, total] = await Promise.all([
        this.userModel
          .find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.userModel.countDocuments(query).exec()
      ]);

      return {
        agents,
        total,
        page,
        limit
      };
    } catch (error) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to fetch agents.',
      );
    }
  }

  /**
   * Fetches a specific agent by ID.
   * @param id - The agent ID to fetch.
   * @returns A promise that resolves to an Agent entity or null.
   */
  async findAgentById(id: string): Promise<Organisation | null> {
    try {
      const agent = await this.userModel.findById(id).exec();
      if (!agent) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Agent not found.',
        );
      }
      return agent;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to fetch agent.',
      );
    }
  }

  /**
   * Updates an existing agent in the system.
   * @param input - Data required to update the agent.
   * @returns A BaseResponse indicating success or failure of agent update.
   */
  async updateAgent(input: UpdateAgentInput): Promise<BaseResponse> {
    try {
      const agent = await this.userModel.findById(input.id).exec();

      if (!agent) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Agent not found.',
        );
      }

      // Check if email is being updated and if it already exists
      if (input.email && input.email !== agent.email) {
        const existingAgent = await this.userModel.findOne({ email: input.email }).exec();
        if (existingAgent) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.ALREADY_EXISTS,
            ErrorType.IDENTITY,
            'Email already exists.',
          );
        }
      }

      const updateData: any = {};
      if (input.name) updateData.name = input.name;
      if (input.email) updateData.email = input.email;

      await this.userModel.updateOne({ _id: input.id }, updateData).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        { message: 'Agent updated successfully.' },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to update agent.',
      );
    }
  }

  /**
   * Deletes an agent from the system.
   * @param id - The agent ID to delete.
   * @returns A BaseResponse indicating success or failure of agent deletion.
   */
  async deleteAgent(id: string): Promise<BaseResponse> {
    try {
      const agent = await this.userModel.findById(id).exec();

      if (!agent) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.USER_NOT_FOUND,
          ErrorType.IDENTITY,
          'Agent not found.',
        );
      }

      await this.userModel.deleteOne({ _id: id }).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        { message: 'Agent deleted successfully.' },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.SERVER_ERROR,
        ErrorType.IDENTITY,
        'Failed to delete agent.',
      );
    }
  }

  /**
   * Authenticates an agent using SSO token.
   * @param token - The SSO token.
   * @returns A BaseResponse containing the authentication result.
   */
  async agentSSOLogin(token: string): Promise<BaseResponse> {
    try {
      if (!token) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.TOKEN_INVALID,
          ErrorType.IDENTITY,
          'Token is required.',
        );
      }

      // Validate the Microsoft token and extract user information
      const tokenValidation = await this.validateMicrosoftToken(token);
      // Check if agent exists, if not create one
      let agent = await this.sysUserModel.findOne({ email: tokenValidation.email }).exec();

      if (!agent) {
        // Create new agent
        agent = new this.sysUserModel({
          email: tokenValidation.email,
          name: tokenValidation.name,
          role: tokenValidation.role,
        });
        await agent.save();
      }

      // Generate JWT token for the agent
      const jwtSecret = process.env.JWT_SECRET ?? 'fallback-secret';
      const generatedToken = jwt.sign(
        {
          userId: agent._id,
          email: agent.email,
          name: agent.name,
          role: agent.roleName,
        },
        jwtSecret,
        {
          expiresIn: '1 day',
          algorithm: 'HS256'  // Explicitly specify the algorithm
        }
      );
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.IDENTITY,
        {
          message: 'Agent authenticated successfully.',
          agent: {
            id: agent._id,
            email: agent.email,
            name: agent.name,
          },
          token: generatedToken,
          expiresIn: '1 day'
        },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      // Only throw TOKEN_INVALID for actual token errors
      if (error?.message === 'Token validation failed.' || error?.code === ResponseCode.TOKEN_INVALID) {
        throw new Error(
          HttpStatus.UNAUTHORIZED,
          ResponseCode.TOKEN_INVALID,
          ErrorType.IDENTITY,
          'Token validation failed.'
        );
      }
      // For USER_NOT_FOUND and all other errors, rethrow as is
      throw error;
    }
  }

  // Seed function to create a sub-organisation and default OrganisationProcessMapping records
  static async seedSubOrganisationWithMappings(
    userModel: any,
    processModel: any,
    organisationProcessMappingModel: any,
    subOrgData: any,
    mainClientId: string
  ) {
    // Create sub-organisation
    const newSubClient = new userModel(subOrgData);
    console.log("Creating new sub-organisation:", mainClientId);

    await newSubClient.save();
    // Fetch all processes
    const allProcesses = await processModel.find().lean();
    // Create OrganisationProcessMapping for each process
    for (const process of allProcesses) {
      await organisationProcessMappingModel.create({
        organisationId: newSubClient.main_client,
        subOrganisationId: newSubClient._id,
        processId: process._id,
        processName: process.name,
        isActive: false
      });
    }
    return newSubClient;
  }

  /**
   * Finds a user by email (case-insensitive, trimmed, lowercased)
   */
  public async getUserByEmail(email: string): Promise<OrganisationUser | null> {
    if (!email) return null;
    return this.organisationUserModel.findOne({ email: email.trim().toLowerCase() }).exec();
  }

  /**
   * Finds an organisation by its ID
   */
  public async getOrganisationById(id: string): Promise<OrganisationSettings | null> {
    if (!id) return null;
    return this.OrganisationSettingsModel.findOne({ organisationId: id }).exec();
  }
}

