#!/usr/bin/env node

import { NestFactory } from '@nestjs/core';
import { DesServiceModule } from '../des-service.module';
import { TemplateSeedingService } from '../template-seeding.service';

async function bootstrap() {
  console.log('🌱 Starting template seeding CLI...');
  
  try {
    // Create the NestJS application context
    const app = await NestFactory.createApplicationContext(DesServiceModule, {
      logger: ['error', 'warn', 'log'],
    });

    // Get the seeding service
    const seedingService = app.get(TemplateSeedingService);

    // Run the seeding
    const result = await seedingService.manualSeed();

    if (result.success) {
      console.log(`✅ ${result.message}`);
      console.log(`📊 Templates seeded: ${result.count}`);
    } else {
      console.error(`❌ ${result.message}`);
      process.exit(1);
    }

    // Close the application
    await app.close();
    console.log('🏁 Seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the bootstrap function
bootstrap();
