import { NestFactory } from '@nestjs/core';
import { DesServiceModule } from './des-service.module';
import { Response } from 'express';
import { getModelToken } from '@nestjs/mongoose';
// import { defaultTemplateSeeds, seedDefaultTemplates } from './entities/template.entity';
import { Template } from './entities/template.entity';

async function bootstrap() {
  const app = await NestFactory.create(DesServiceModule);
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
  });

  // Add simple health check endpoint
  app.use('/health', (_, res: Response) => {
    res.status(200).json({ status: 'ok', uptime: process.uptime() });
  });

  // Template seeding logic (run only if SEED_TEMPLATES env is true)
  // if (process.env.SEED_TEMPLATES === 'true') {
  //   try {
      // const templateModel = app.get(getModelToken('Template'));
      // await seedDefaultTemplates(templateModel, defaultTemplateSeeds);
      console.log('Default templates seeded successfully.');
  //   } catch (err) {
  //     console.error('Template seeding failed:', err);
  //   }
  // }

  await app.listen(process.env.PORT ?? 4002);
}

bootstrap();