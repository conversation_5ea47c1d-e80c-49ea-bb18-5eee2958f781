const rolesPermissionForSeeding = {
    "role_permission": [
        {
            "roleName": "Manager",
            "roleId": "1",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "displayName": "provider-credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" },
                                { "isEnabled": true, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Import" }
                            ]
                        }
                    ]
                },

            ]
        },
        {
            "roleName": "Supervisor",
            "roleId": "2",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" },
                                { "isEnabled": true, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "Agents",
            "roleId": "3",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" },
                                { "isEnabled": true, "displayName": "allocate" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Export" },
                                { "isEnabled": false, "displayName": "Delete" },
                                { "isEnabled": false, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "QC Manager",
            "roleId": "4",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Export" },
                                { "isEnabled": false, "displayName": "Delete" },
                                { "isEnabled": false, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "QC Supervisor",
            "roleId": "5",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Export" },
                                { "isEnabled": false, "displayName": "Delete" },
                                { "isEnabled": false, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "QC Agents",
            "roleId": "6",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Export" },
                                { "isEnabled": false, "displayName": "Delete" },
                                { "isEnabled": false, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": false, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "Admin",
            "roleId": "7",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" },
                                { "isEnabled": true, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        },
        {
            "roleName": "SubAdmin",
            "roleId": "8",
            "module": [
                {
                    "moduleId": 1,
                    "moduleName": "Provider Credentialing",
                    "subModules": [
                        {
                            "moduleId": 1,
                            "subModuleName": "All Ticket",
                            "displayName": "all_ticket",
                            "permissions": [
                                { "isEnabled": true, "displayName": "All" },
                                { "isEnabled": true, "displayName": "Self" },
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" }
                            ]
                        },
                        {
                            "moduleId": 2,
                            "subModuleName": "Source",
                            "displayName": "source",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Export" },
                                { "isEnabled": true, "displayName": "Delete" },
                                { "isEnabled": true, "displayName": "Move" }
                            ]
                        },
                        {
                            "moduleId": 3,
                            "subModuleName": "Exception",
                            "displayName": "exception",
                            "permissions": []
                        },
                        {
                            "moduleId": 4,
                            "subModuleName": "FollowUp",
                            "displayName": "followup",
                            "permissions": []
                        },
                        {
                            "moduleId": 5,
                            "subModuleName": "Import",
                            "displayName": "import",
                            "permissions": [
                                { "isEnabled": true, "displayName": "Import" }
                            ]
                        }
                    ]
                }
            ]
        }
    ]

}

export const permissionTemplate = [
  {
    "moduleName": "Tickets",
    "displayName": "Provider Credentialing",
    "isEnabled": true,
    "subModules": [
      {
        "moduleName": "All Ticket",
        "displayName": "All Ticket",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "All",
            "isEnabled": true
          },
          {
            "displayName": "Self",
            "isEnabled": true
          },
          {
            "displayName": "Export",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          },
          {
            "displayName": "Allocate",
            "isEnabled": true
          },
          {
            "displayName": "Reallocate",
            "isEnabled": true
          },
          {
            "displayName": "Print",
            "isEnabled": true
          },
          {
            "displayName": "Export as Excel",
            "isEnabled": true
          },
          {
            "displayName": "Export as CSV",
            "isEnabled": true
          },
          {
            "displayName": "Process",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Exception",
        "displayName": "Exception",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Process",
            "isEnabled": true
          },
          {
            "displayName": "Reallocate",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Completed",
        "displayName": "Completed",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Allocate",
            "isEnabled": true
          },
          {
            "displayName": "Rework",
            "isEnabled": true
          },
          {
            "displayName": "QC Rework",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Source",
        "displayName": "source",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Allocate",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Import",
        "displayName": "Import",
        "isEnabled": true,
        "permissions": []
      }
    ]
  },
  {
    "moduleName": "Dashboard",
    "displayName": "Dashboard",
    "isEnabled": true,
    "subModules": []
  },
  {
    "moduleName": "Reports",
    "displayName": "Reports",
    "isEnabled": true,
    "subModules": []
  },
  {
    "moduleName": "Settings",
    "displayName": "Settings",
    "isEnabled": true,
    "subModules": []
  },
  {
    "moduleName": "User Management",
    "displayName": "User Management",
    "isEnabled": true,
    "subModules": [
      {
        "moduleName": "System Users",
        "displayName": "System Users",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Roles and Managements",
        "displayName": "Roles and Managements",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Org Roles and Managements",
        "displayName": "Org Roles and Managements",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      }
    ]
  },
  {
    "moduleName": "Masters",
    "displayName": "Masters",
    "isEnabled": true,
    "subModules": [
      {
        "moduleName": "Payer",
        "displayName": "Payor",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "CPT Dictionary",
        "displayName": "CPT Dictonary",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Action and Status",
        "displayName": "Action and Status",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Exception",
        "displayName": "Exception",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Specialty",
        "displayName": "Specialty",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Scenario",
        "displayName": "Scenario",
        "isEnabled": true,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": true
          },
          {
            "displayName": "View",
            "isEnabled": true
          },
          {
            "displayName": "Edit",
            "isEnabled": true
          },
          {
            "displayName": "Delete",
            "isEnabled": true
          }
        ]
      },
      {
        "moduleName": "Des Form",
        "displayName": "Des Form",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Create Template",
            "isEnabled": false
          },
          {
            "displayName": "View Template",
            "isEnabled": false
          },
          {
            "displayName": "Edit Template",
            "isEnabled": false
          },
          {
            "displayName": "Delete Template",
            "isEnabled": false
          },
          {
            "displayName": "Customize Field",
            "isEnabled": false
          },
          {
            "displayName": "Add to Global",
            "isEnabled": false
          },
          {
            "displayName": "Delete Field",
            "isEnabled": false
          }
        ]
      }
    ]
  },
  {
    "moduleName": "Organizations",
    "displayName": "Organization",
    "isEnabled": true,
    "subModules": [
      {
        "moduleName": "Main Organization",
        "displayName": "Main Organization",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": false
          },
          {
            "displayName": "View",
            "isEnabled": false
          },
          {
            "displayName": "Update",
            "isEnabled": false
          },
          {
            "displayName": "Delete",
            "isEnabled": false
          }
        ]
      },
      {
        "moduleName": "Sub Organization",
        "displayName": "Sub orgaization",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": false
          },
          {
            "displayName": "View",
            "isEnabled": false
          },
          {
            "displayName": "Update",
            "isEnabled": false
          },
          {
            "displayName": "Delete",
            "isEnabled": false
          }
        ]
      },
      {
        "moduleName": "Process Settings",
        "displayName": "Process settings",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Assign user",
            "isEnabled": false
          },
          {
            "displayName": "Remove user",
            "isEnabled": false
          },
          {
            "displayName": "Enable/Disable process",
            "isEnabled": false
          }
        ]
      },
      {
        "moduleName": "Main Organization Settings",
        "displayName": "Main Organization Settings",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Add Users",
            "isEnabled": false
          },
          {
            "displayName": "Remove Users",
            "isEnabled": false
          },
          {
            "displayName": "View Users",
            "isEnabled": false
          },
          {
            "displayName": "Update Users",
            "isEnabled": false
          },
          {
            "displayName": "Customize Settings",
            "isEnabled": false
          }
        ]
      },
      {
        "moduleName": "Sub Organization Settings",
        "displayName": "Sub Organization Settings",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Add Users",
            "isEnabled": false
          },
          {
            "displayName": "Remove Users",
            "isEnabled": false
          },
          {
            "displayName": "View Users",
            "isEnabled": false
          },
          {
            "displayName": "Update Users",
            "isEnabled": false
          },
          {
            "displayName": "Customize Settings",
            "isEnabled": false
          }
        ]
      },
      {
        "moduleName": "Provider",
        "displayName": "Provider",
        "isEnabled": false,
        "permissions": [
          {
            "displayName": "Add",
            "isEnabled": false
          },
          {
            "displayName": "View",
            "isEnabled": false
          },
          {
            "displayName": "Update",
            "isEnabled": false
          },
          {
            "displayName": "Delete",
            "isEnabled": false
          }
        ]
      }
    ]
  }
]
;

export const organizationRoleTemplate=[
	{
		"moduleName": "organization",
		"displayName": "Organization",
		"isEnabled": true,
		"subModules": [
			{
				"displayName": "Organization Info",
				"isEnabled": true,
				"permissions": [
					{
						"displayName": "View",
						"isEnabled": true
					},
					{
						"displayName": "Edit",
						"isEnabled": true
					}
				]
			},
			{
				"displayName": "Sub Organization list",
				"isEnabled": true,
				"permissions": [
					{
						"displayName": "View",
						"isEnabled": true
					}
				]
			},
			{
				"displayName": "Settings",
				"isEnabled": true,
				"permissions": [
					{
						"displayName": "Organization Login Portal",
						"isEnabled": true
					},
					{
						"displayName": "Access Rights",
						"isEnabled": true
					}
				]
			}
		]
	},
	{
		"moduleName": "ticketing-system",
		"displayName": "Ticketing System",
		"isEnabled": true,
		"subModules": [
			{
				"displayName": "All Tickets",
				"isEnabled": true,
				"permissions": [
					{
						"displayName": "Add",
						"isEnabled": true
					},
					{
						"displayName": "Edit",
						"isEnabled": true
					},
					{
						"displayName": "View",
						"isEnabled": true
					},
					{
						"displayName": "Delete",
						"isEnabled": true
					}
				]
			}
		]
	}
]