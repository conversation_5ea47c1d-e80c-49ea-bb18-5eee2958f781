import { ServiceEndpointDefinition } from '@apollo/gateway';

export const subgraphs: Array<ServiceEndpointDefinition> = [
  {
    name: 'identity',
    url: `http://localhost:4001/graphql`,
  },
  {
    name: 'des-service',
    url: `http://localhost:4002/graphql`,
  },
  {
    name: 'provider',
    url: `http://localhost:4003/graphql`,
  },
  {
    name: 'notification-service',
    url: `http://localhost:4004/graphql`,
  },
];
