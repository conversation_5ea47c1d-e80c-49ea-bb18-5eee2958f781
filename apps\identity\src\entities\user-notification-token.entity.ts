import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'user_notification_tokens',
})
export class UserNotificationToken extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => ID)
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  userId: Types.ObjectId;

  @Field(() => [String], { nullable: true })
  @Prop({ type: [String], default: [] })
  token?: string[];
}

export const UserNotificationTokenSchema = SchemaFactory.createForClass(UserNotificationToken);
