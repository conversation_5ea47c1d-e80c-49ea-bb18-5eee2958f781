import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { GenerateUploadUrlInput, MakeFilePublicInput, MakeFilePublicResponse } from './dto/file-upload.dto';
import { FileUploadService } from './file-upload.service';
import { BaseResponse } from './dto/base.response.dto';

@Resolver()
export class FileUploadResolver {
  constructor(private readonly fileUploadService: FileUploadService) {}

  @Mutation(() => BaseResponse, { name: 'generateUploadUrl' })
  async generateUploadUrl(
    @Args('input') input: GenerateUploadUrlInput,
  ): Promise<BaseResponse> {
    const { filename, contentType } = input;
    return this.fileUploadService.generateV4UploadSignedUrl(filename, contentType);
  }

  @Mutation(() => MakeFilePublicResponse, { name: 'makeFilePublic' })
  async makeFilePublic(
    @Args('input') input: MakeFilePublicInput,
  ): Promise<MakeFilePublicResponse> {
    const publicUrl = await this.fileUploadService.makeFilePublic(input.filename);
    return { publicUrl };
  }

  @Mutation(() => BaseResponse, { name: 'generateViewUrl' })
  async generateViewUrl(
    @Args('filename') filename: string,
  ): Promise<BaseResponse> {
    // If filename is a full GCS URL, extract the object path
    let objectPath = filename;
    const gcsPrefix = 'https://storage.googleapis.com/';
    if (filename.startsWith(gcsPrefix)) {
      // Remove prefix and bucket name
      const parts = filename.replace(gcsPrefix, '').split('/');
      // Remove the bucket name
      parts.shift();
      objectPath = parts.join('/');
    }
    console.log("objectPath", objectPath);
    
    return this.fileUploadService.generateV4ViewSignedUrl(objectPath);
  }
} 