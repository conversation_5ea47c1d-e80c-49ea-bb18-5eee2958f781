import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { Types } from 'mongoose';
import { NotificationType, NotificationPriority, NotificationChannel, NotificationStatus } from '@app/notification';
import { GraphQLJSON } from 'graphql-type-json';

// Register enums for GraphQL
registerEnumType(NotificationType, { name: 'NotificationType' });
registerEnumType(NotificationPriority, { name: 'NotificationPriority' });
registerEnumType(NotificationChannel, { name: 'NotificationChannel' });
registerEnumType(NotificationStatus, { name: 'NotificationStatus' });

@ObjectType()
export class NotificationHistoryGraphQL {
  @Field(() => ID)
  _id: Types.ObjectId;

  @Field()
  notificationId: string;

  @Field({ nullable: true })
  senderId?: string;

  @Field(() => ID)
  userId: Types.ObjectId;

  @Field({ nullable: true })
  userEmail?: string;

  @Field(() => NotificationType)
  type: NotificationType;

  @Field()
  title: string;

  @Field()
  message: string;

  @Field(() => [NotificationChannel])
  channels: NotificationChannel[];

  @Field(() => NotificationPriority)
  priority: NotificationPriority;

  @Field(() => NotificationStatus)
  status: NotificationStatus;

  @Field(() => GraphQLJSON, { nullable: true })
  data?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: Record<string, any>;

  @Field({ nullable: true })
  sentAt?: Date;

  @Field({ nullable: true })
  deliveredAt?: Date;

  @Field({ nullable: true })
  readAt?: Date;

  @Field({ nullable: true })
  orgId?: string;

  @Field({ nullable: true })
  subOrgId?: string;

  @Field({ nullable: true })
  processId?: string;

  @Field()
  isRead: boolean;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field({ nullable: true })
  senderName?: string;

  @Field({ nullable: true })
  senderEmail?: string;

  @Field()
  isRouted: boolean;
}
