import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';

@ObjectType()
@Schema({
    timestamps: true,
    collection: 'providers',
    strict: false,

})
export class Provider extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field(() => ID, { nullable: true })
    @Prop({ index: true })
    templateId?: string;

    @Field(() => ID, { nullable: true })
    @Prop({ index: true })
    subOrganisationId?: string;

    @Field(() => GraphQLJSON)
    @Prop({
        type: Object,
        required: true,
        get: function (data: any) {
            try {
                return typeof data === 'string' ? JSON.parse(data) : data;
            } catch (e) {
                return data;
            }
        },
        set: function (data: any) {
            return typeof data === 'string' ? data : JSON.stringify(data);
        }
    })
    values: Record<string, any>;

    @Field({ nullable: true })
    @Prop()
    createdBy?: string;

    @Field(() => String, { nullable: true })
    @Prop()
    updatedBy?: string;

    @Field({ nullable: true })
    @Prop({ default: true })
    isActive?: boolean;
}

export const ProviderSchema = SchemaFactory.createForClass(Provider); 