import { InputType, Field, ObjectType, ID, Int } from '@nestjs/graphql';
import { IsOptional, IsBoolean, IsString, IsEnum, IsInt, Min, Max } from 'class-validator';
import { NotificationType, NotificationPriority, NotificationStatus } from '@app/notification';
import { NotificationHistoryGraphQL } from '../entities/notification-history-graphql.entity';

@InputType()
export class GetNotificationHistoryInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  userId?: string;

  @Field(() => [NotificationType], { nullable: true })
  @IsOptional()
  @IsEnum(NotificationType, { each: true })
  types?: NotificationType[];

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isRead?: boolean;

  @Field(() => NotificationStatus, { nullable: true })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 20 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  orgId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  subOrgId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  processId?: string;
}

@InputType()
export class MarkNotificationReadInput {
  @Field(() => [String])
  notificationIds: string[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  userId?: string;
}


@ObjectType()
export class NotificationHistoryResponse {
  @Field(() => [NotificationHistoryGraphQL])
  notifications: NotificationHistoryGraphQL[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;

  @Field(() => Int)
  unreadCount: number;
}

@ObjectType()
export class NotificationStatsResponse {
  @Field(() => Int)
  totalNotifications: number;

  @Field(() => Int)
  unreadCount: number;

  @Field(() => Int)
  readCount: number;

  @Field(() => [NotificationTypeStats])
  byType: NotificationTypeStats[];
}

@ObjectType()
export class NotificationTypeStats {
  @Field(() => NotificationType)
  type: NotificationType;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  unread: number;

  @Field(() => Int)
  read: number;
}

@ObjectType()
export class MarkNotificationResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field(() => Int)
  affectedCount: number;
}
