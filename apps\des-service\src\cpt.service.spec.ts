import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { MasterCptService } from './cpt.service';
import { Cpt } from './entities/cpt.entity';
import { Specialty } from './entities/specialty.entity';
import { Icd } from './entities/icd.entity';
import { CptCode } from './entities/cpt-code.entity';
import { Diagnosis } from './entities/diagnosis.entity';
import { Error, Success, HttpStatus, ResponseCode, ErrorType } from '@app/error';
import { CreateCptInput, UpdateCptInput, PaginateCptArgs } from './dto/cpt.dto';
import { ClientType } from './entities/template.entity';

describe('MasterCptService', () => {
  let service: MasterCptService;
  let masterCptModel: Model<Cpt>;
  let specialtyModel: Model<Specialty>;
  let icdModel: Model<Icd>;
  let cptCodeModel: Model<CptCode>;
  let diagnosisModel: Model<Diagnosis>;

  const mockSpecialty = {
    _id: new Types.ObjectId(),
    name: 'Test Specialty',
    isActive: true
  };

  const mockIcd = {
    _id: new Types.ObjectId(),
    code: 'A00',
    description: 'Test ICD',
    isActive: true
  };

  const mockCptCode = {
    _id: new Types.ObjectId(),
    code: '12345',
    values: { description: 'Test CPT Code' },
    specialtyId: mockSpecialty._id,
    isActive: true
  };

  const mockDiagnosis = {
    _id: new Types.ObjectId(),
    name: 'Test Diagnosis',
    icd: mockIcd._id,
    isActive: true
  };

  const mockCpt = {
    _id: new Types.ObjectId(),
    type: ClientType.MAIN_CLIENT,
    values: { description: 'Test CPT' },
    specialtyId: mockSpecialty._id,
    diagnosisId: mockDiagnosis._id,
    cptCodeId: mockCptCode._id,
    icdId: mockIcd._id,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    specialty: mockSpecialty,
    diagnosis: mockDiagnosis,
    cptCode: mockCptCode,
    icd: mockIcd
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MasterCptService,
        {
          provide: getModelToken(Cpt.name),
          useValue: {
            new: jest.fn(),
            constructor: jest.fn(),
            find: jest.fn(),
            findById: jest.fn(),
            findOne: jest.fn(),
            findByIdAndUpdate: jest.fn(),
            findByIdAndDelete: jest.fn(),
            countDocuments: jest.fn(),
            exec: jest.fn(),
            lean: jest.fn().mockReturnThis(),
            save: jest.fn(),
            skip: jest.fn().mockReturnThis(),
            limit: jest.fn().mockReturnThis(),
            sort: jest.fn().mockReturnThis(),
            populate: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getModelToken(Specialty.name),
          useValue: {
            findById: jest.fn(),
            lean: jest.fn().mockReturnThis(),
            exec: jest.fn(),
          },
        },
        {
          provide: getModelToken(Icd.name),
          useValue: {
            findById: jest.fn(),
            lean: jest.fn().mockReturnThis(),
            exec: jest.fn(),
          },
        },
        {
          provide: getModelToken(CptCode.name),
          useValue: {
            findById: jest.fn(),
            lean: jest.fn().mockReturnThis(),
            exec: jest.fn(),
          },
        },
        {
          provide: getModelToken(Diagnosis.name),
          useValue: {
            findById: jest.fn(),
            lean: jest.fn().mockReturnThis(),
            exec: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MasterCptService>(MasterCptService);
    masterCptModel = module.get<Model<Cpt>>(getModelToken(Cpt.name));
    specialtyModel = module.get<Model<Specialty>>(getModelToken(Specialty.name));
    icdModel = module.get<Model<Icd>>(getModelToken(Icd.name));
    cptCodeModel = module.get<Model<CptCode>>(getModelToken(CptCode.name));
    diagnosisModel = module.get<Model<Diagnosis>>(getModelToken(Diagnosis.name));
  });

  describe('findAll', () => {
    it('should return paginated CPTs', async () => {
  const args: PaginateCptArgs = {
    page: 1,
    limit: 10,
    filters: JSON.stringify({ type: 'test' }) // ✅ valid JSON
  };

  const mockDocs = [mockCpt];
  const mockTotal = 1;

  const mockFind = {
    populate: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    lean: jest.fn().mockReturnThis(),
    exec: jest.fn().mockResolvedValue(mockDocs),
  };

  jest.spyOn(masterCptModel, 'find').mockReturnValue(mockFind as any);
  jest.spyOn(masterCptModel, 'countDocuments').mockResolvedValue(mockTotal); // ✅ correct

  const result = await service.findAll(args);

  expect(result.items).toHaveLength(1);
  expect(result.pagination.total).toBe(mockTotal);
  expect(result.pagination.page).toBe(1);
  expect(result.pagination.limit).toBe(10);
});


    it('should handle search term', async () => {
      const args: PaginateCptArgs = { page: 1, limit: 10, search: 'test' };
      const mockDocs = [mockCpt];
      const mockTotal = 1;

      jest.spyOn(masterCptModel, 'find').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs),
      } as any);

      jest.spyOn(masterCptModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal),
      } as any);

      const result = await service.findAll(args);

      expect(result.items).toHaveLength(1);
      expect(masterCptModel.find).toHaveBeenCalledWith(expect.objectContaining({
        $or: expect.any(Array)
      }));
    });

    it('should handle filters', async () => {
      const args: PaginateCptArgs = { 
        page: 1, 
        limit: 10,
        filters: JSON.stringify({
          type: 'test',
          isActive: true,
          specialtyId: mockSpecialty._id.toString(),
          fieldFilters: [{ path: 'description', value: 'test' }]
        })
      };
      const mockDocs = [mockCpt];
      const mockTotal = 1;

      jest.spyOn(masterCptModel, 'find').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs),
      } as any);

      jest.spyOn(masterCptModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal),
      } as any);

      const result = await service.findAll(args);
      expect(result.items).toHaveLength(1);
      expect(masterCptModel.find).toHaveBeenCalledWith(expect.objectContaining({
        type: expect.any(Object),
        isActive: true,
        specialtyId: mockSpecialty._id
      }));
    });

    it('should handle invalid filters JSON', async () => {
      const args: PaginateCptArgs = { 
        page: 1, 
        limit: 10,
        filters: 'invalid-json'
      };
      const mockDocs = [mockCpt];
      const mockTotal = 1;

      jest.spyOn(masterCptModel, 'find').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs),
      } as any);

      jest.spyOn(masterCptModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal),
      } as any);

      const result = await service.findAll(args);
      expect(result.items).toHaveLength(1);
    });

    it('should handle sorting', async () => {
      const args: PaginateCptArgs = { 
        page: 1, 
        limit: 10,
        sortBy: 'type',
        sortOrder: 'desc'
      };
      const mockDocs = [mockCpt];
      const mockTotal = 1;

      const mockFind = {
        populate: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDocs),
      };

      jest.spyOn(masterCptModel, 'find').mockReturnValue(mockFind as any);
      jest.spyOn(masterCptModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal)
      } as any);

      await service.findAll(args);
      expect(mockFind.sort).toHaveBeenCalledWith({ type: -1 });
    });

    it('should handle service error', async () => {
      const args: PaginateCptArgs = { page: 1, limit: 10 };
      jest.spyOn(masterCptModel, 'find').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.findAll(args)).rejects.toThrow();
      await expect(service.findAll(args)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          type: ResponseCode.CPT
        }
      });
    });
  });

  describe('findById', () => {
  it('should return a CPT when found', async () => {
    jest.spyOn(masterCptModel, 'findById').mockReturnValue({
      populate: jest.fn().mockReturnThis(),
      lean: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue(mockCpt),
    } as any);

    const result = await service.findById(mockCpt._id.toString());
    expect(result).toBeDefined();
    expect(result._id.toString()).toBe(mockCpt._id.toString());
  });

  it('should throw error for invalid ID format', async () => {
      await expect(service.findById('invalid-id')).rejects.toThrow(Error);
    });

it('should throw error when CPT not found', async () => {
      jest.spyOn(masterCptModel, 'findById').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.findById(mockCpt._id.toString())).rejects.toThrow(Error);
    });

});


  describe('create', () => {
    const createInput: CreateCptInput = {
      type: ClientType.MAIN_CLIENT,
      values: { description: 'Test CPT' },
      specialtyId: mockSpecialty._id.toString(),
      diagnosisId: mockDiagnosis._id.toString(),
      cptCodeId: mockCptCode._id.toString(),
      icdId: mockIcd._id.toString(),
      templateId: new Types.ObjectId().toString(),
      isActive: true,
      createdBy: 'test-user'
    };

    beforeEach(() => {
      // Mock successful finds
      jest.spyOn(specialtyModel, 'findById').mockImplementation((id) => ({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(id === mockSpecialty._id.toString() ? mockSpecialty : null),
      } as any));

      jest.spyOn(icdModel, 'findById').mockImplementation((id) => ({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(id === mockIcd._id.toString() ? mockIcd : null),
      } as any));

      jest.spyOn(cptCodeModel, 'findById').mockImplementation((id) => ({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(id === mockCptCode._id.toString() ? mockCptCode : null),
      } as any));

      jest.spyOn(diagnosisModel, 'findById').mockImplementation((id) => ({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(id === mockDiagnosis._id.toString() ? mockDiagnosis : null),
      } as any));

      // Mock existing CPT check (null by default)
      jest.spyOn(masterCptModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      // Mock save and model creation
      const mockSave = jest.fn().mockResolvedValue({
        ...mockCpt,
        specialty: mockSpecialty,
        diagnosis: mockDiagnosis,
        cptCode: mockCptCode,
        icd: mockIcd
      });
      const mockModel = {
        save: mockSave
      };
      (masterCptModel as any).new = jest.fn().mockReturnValue(mockModel);

      // Mock populate after save
      jest.spyOn(masterCptModel, 'findById').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({
          ...mockCpt,
          specialty: mockSpecialty,
          diagnosis: mockDiagnosis,
          cptCode: mockCptCode,
          icd: mockIcd
        }),
      } as any);
    });

    // it('should create a new CPT', async () => {
    //   const result = await service.create(createInput);
    //   expect(result).toBeInstanceOf(Success);
    //   const cpt = (result as unknown as { data: { cpt: Cpt } }).data?.cpt;
    //   expect(cpt).toBeDefined();
    //   expect(cpt?.specialty).toBeDefined();
    //   expect(cpt?.diagnosis).toBeDefined();
    //   expect(cpt?.cptCode).toBeDefined();
    //   expect(cpt?.icd).toBeDefined();
    // });

    it('should throw error when specialty not found', async () => {
      const input = {
        ...createInput,
        specialtyId: new Types.ObjectId().toString()
      };

      await expect(service.create(input)).rejects.toThrow();
      await expect(service.create(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should throw error when ICD not found', async () => {
      const input = {
        ...createInput,
        icdId: new Types.ObjectId().toString()
      };

      await expect(service.create(input)).rejects.toThrow();
      await expect(service.create(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should throw error when CPT Code not found', async () => {
      const input = {
        ...createInput,
        cptCodeId: new Types.ObjectId().toString()
      };

      await expect(service.create(input)).rejects.toThrow();
      await expect(service.create(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should throw error when Diagnosis not found', async () => {
      const input = {
        ...createInput,
        diagnosisId: new Types.ObjectId().toString()
      };

      await expect(service.create(input)).rejects.toThrow();
      await expect(service.create(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should throw error when CPT code specialty does not match', async () => {
      // Create a CPT code with different specialty
      const differentSpecialtyCptCode = {
        ...mockCptCode,
        specialtyId: new Types.ObjectId()
      };
      
      jest.spyOn(cptCodeModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(differentSpecialtyCptCode),
      } as any);

      await expect(service.create(createInput)).rejects.toThrow();
      await expect(service.create(createInput)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should throw error when Diagnosis ICD does not match', async () => {
      // Create a diagnosis with different ICD
      const differentIcdDiagnosis = {
        ...mockDiagnosis,
        icd: new Types.ObjectId()
      };
      
      jest.spyOn(diagnosisModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(differentIcdDiagnosis),
      } as any);

      await expect(service.create(createInput)).rejects.toThrow();
      await expect(service.create(createInput)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should throw error when CPT already exists', async () => {
      jest.spyOn(masterCptModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCpt),
      } as any);

      await expect(service.create(createInput)).rejects.toThrow();
      await expect(service.create(createInput)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });
});

  describe('update', () => {
    const updateInput: UpdateCptInput = {
      id: mockCpt._id.toString(),
      type: ClientType.SUB_CLIENT,
      values: { description: 'Updated CPT' },
      isActive: false
    };

    beforeEach(() => {
      jest.spyOn(masterCptModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCpt),
      } as any);

      jest.spyOn(masterCptModel, 'findByIdAndUpdate').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({ ...mockCpt, ...updateInput }),
      } as any);
    });

    it('should update a CPT with basic fields', async () => {
      const result = await service.update(updateInput);
      expect(result).toBeInstanceOf(Success);
      const cpt = (result as unknown as { data: { cpt: Cpt } }).data?.cpt;
      expect(cpt).toBeDefined();
      expect(cpt?.type).toBe(ClientType.SUB_CLIENT);
      expect(cpt?.values.description).toBe('Updated CPT');
      expect(cpt?.isActive).toBe(false);
    });

    it('should throw error for invalid CPT ID format', async () => {
      const input = {
        ...updateInput,
        id: 'invalid-id'
      };

      await expect(service.update(input)).rejects.toThrow();
      await expect(service.update(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should throw error when CPT not found', async () => {
      jest.spyOn(masterCptModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.update(updateInput)).rejects.toThrow();
      await expect(service.update(updateInput)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should update with new specialty and validate CPT code specialty match', async () => {
      const newSpecialtyId = new Types.ObjectId();
      const input = {
        ...updateInput,
        specialtyId: newSpecialtyId.toString(),
        cptCodeId: mockCpt.cptCodeId.toString()
      };

      jest.spyOn(specialtyModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({ _id: newSpecialtyId }),
      } as any);

      jest.spyOn(cptCodeModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({
          ...mockCptCode,
          specialtyId: newSpecialtyId
        }),
      } as any);

      jest.spyOn(masterCptModel, 'findByIdAndUpdate').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({
          ...mockCpt,
          ...updateInput,
          specialtyId: newSpecialtyId
        }),
      } as any);

      const result = await service.update(input);
      expect(result).toBeInstanceOf(Success);
      const cpt = (result as unknown as { data: { cpt: Cpt } }).data?.cpt;
      expect(cpt).toBeDefined();
      expect(cpt?.specialtyId.toString()).toBe(newSpecialtyId.toString());
    });

    it('should throw error when CPT code specialty does not match', async () => {
      const newSpecialtyId = new Types.ObjectId();
      const input = {
        ...updateInput,
        specialtyId: newSpecialtyId.toString(),
        cptCodeId: mockCpt.cptCodeId.toString()
      };

      jest.spyOn(specialtyModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({ _id: newSpecialtyId }),
      } as any);

      jest.spyOn(cptCodeModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCptCode), // Original specialtyId
      } as any);

      await expect(service.update(input)).rejects.toThrow();
      await expect(service.update(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should update with new diagnosis and validate ICD match', async () => {
      const newIcdId = new Types.ObjectId();
      const newDiagnosisId = new Types.ObjectId();
      const input = {
        ...updateInput,
        icdId: newIcdId.toString(),
        diagnosisId: newDiagnosisId.toString()
      };

      jest.spyOn(icdModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({ _id: newIcdId }),
      } as any);

      jest.spyOn(diagnosisModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({
          ...mockDiagnosis,
          _id: newDiagnosisId,
          icd: newIcdId
        }),
      } as any);

      jest.spyOn(masterCptModel, 'findByIdAndUpdate').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({
          ...mockCpt,
          ...updateInput,
          diagnosisId: newDiagnosisId,
          icdId: newIcdId
        }),
      } as any);

      const result = await service.update(input);
      expect(result).toBeInstanceOf(Success);
      const cpt = (result as unknown as { data: { cpt: Cpt } }).data?.cpt;
      expect(cpt).toBeDefined();
      expect(cpt?.diagnosisId.toString()).toBe(newDiagnosisId.toString());
      expect(cpt?.icdId.toString()).toBe(newIcdId.toString());
    });

    it('should throw error when Diagnosis ICD does not match', async () => {
      const newIcdId = new Types.ObjectId();
      const input = {
        ...updateInput,
        icdId: newIcdId.toString(),
        diagnosisId: mockDiagnosis._id.toString()
      };

      jest.spyOn(icdModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue({ _id: newIcdId }),
      } as any);

      jest.spyOn(diagnosisModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockDiagnosis), // Original ICD
      } as any);

      await expect(service.update(input)).rejects.toThrow();
      await expect(service.update(input)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should handle database error during update', async () => {
      jest.spyOn(masterCptModel, 'findByIdAndUpdate').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.update(updateInput)).rejects.toThrow();
      await expect(service.update(updateInput)).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          type: 'CPT'
        }
      });
    });
  });

  describe('delete', () => {
    it('should delete a CPT', async () => {
      jest.spyOn(masterCptModel, 'findByIdAndDelete').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCpt),
      } as any);

      const result = await service.delete(mockCpt._id.toString());
      expect(result).toBeInstanceOf(Success);
      expect((result as unknown as Success).data?.message).toBe('CPT deleted successfully');
    });

    it('should throw error when CPT not found', async () => {
      jest.spyOn(masterCptModel, 'findByIdAndDelete').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.delete(mockCpt._id.toString())).rejects.toThrow();
      await expect(service.delete(mockCpt._id.toString())).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.NOT_FOUND,
          type: 'CPT'
        }
      });
    });

    it('should throw error for invalid ID format', async () => {
      await expect(service.delete('invalid-id')).rejects.toThrow();
      await expect(service.delete('invalid-id')).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.BAD_REQUEST,
          type: 'CPT'
        }
      });
    });

    it('should handle database error', async () => {
      jest.spyOn(masterCptModel, 'findByIdAndDelete').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.delete(mockCpt._id.toString())).rejects.toThrow();
      await expect(service.delete(mockCpt._id.toString())).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          type: 'CPT'
        }
      });
    });
  });

  describe('count', () => {
    it('should return total count of CPTs', async () => {
      const mockCount = 10;
      jest.spyOn(masterCptModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCount),
      } as any);

      const result = await service.count();
      expect(result).toBe(mockCount);
    });

    it('should handle service error', async () => {
      jest.spyOn(masterCptModel, 'countDocuments').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.count()).rejects.toThrow();
      await expect(service.count()).rejects.toMatchObject({
        extensions: {
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          type: 'CPT'
        }
      });
    });
  });

  describe('getAvailableSortFields', () => {
    it('should return array of sort fields', () => {
      const fields = service.getAvailableSortFields();
      expect(Array.isArray(fields)).toBe(true);
      expect(fields).toContain('type');
      expect(fields).toContain('isActive');
      expect(fields).toContain('createdAt');
    });
  });

  describe('getSearchableFields', () => {
    it('should return array of searchable fields', () => {
      const fields = service.getSearchableFields();
      expect(Array.isArray(fields)).toBe(true);
      expect(fields).toContain('type');
      expect(fields).toContain('values.description');
    });
  });

  describe('transformCptDocument', () => {
    it('should transform a valid document', () => {
      const doc = {
        _id: new Types.ObjectId(),
        type: 'TEST',
        values: { test: 'value' },
        specialtyId: new Types.ObjectId(),
        diagnosisId: new Types.ObjectId(),
        cptCodeId: new Types.ObjectId(),
        icdId: new Types.ObjectId(),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        specialty: { name: 'Test Specialty' },
        diagnosis: { name: 'Test Diagnosis' },
        cptCode: { code: '12345' },
        icd: { code: 'A00' }
      };

      const result = service['transformCptDocument'](doc);
      expect(result).toBeDefined();
      expect(result._id).toEqual(doc._id);
      expect(result.specialty).toEqual(doc.specialty);
    });

    it('should throw error for null document', () => {
      expect(() => service['transformCptDocument'](null)).toThrow();
    });

    it('should handle missing fields with defaults', () => {
      const doc = {
        _id: new Types.ObjectId()
      };

      const result = service['transformCptDocument'](doc);
      expect(result.type).toBe('');
      expect(result.values).toEqual({});
      expect(result.isActive).toBe(true);
    });
  });
});