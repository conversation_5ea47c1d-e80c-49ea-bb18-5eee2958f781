import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { IcdService } from './icd.service';
import { Icd } from './entities/icd.entity';
import {
  CreateIcdInput,
  UpdateIcdInput,
  PaginateIcdArgs,
  PaginatedIcdResponse,
  GetIcdInput,
  SuccessResponse
} from './dto/icd.dto';
import { HttpStatus, ErrorType, ResponseCode } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { RequirePermission } from '@app/permissions';

@Resolver(() => Icd)
export class IcdResolver {
  constructor(private readonly icdService: IcdService) {}

  @Query(() => PaginatedIcdResponse, { name: 'icds' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getIcdsWithPagination(
    @Args('input', { nullable: true }) input?: GetIcdInput,
    @Context() context?: any
  ) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters;

    // Get the authorization token from the context if available
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.icdService.findAll({
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      selectedFields,
    });
  }

  @Query(() => Icd, { name: 'icd' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getIcdById(@Args('id') id: string) {
    return this.icdService.findById(id);
  }

  @Query(() => Number, { name: 'countIcds' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async countIcds() {
    return this.icdService.count();
  }

  @Query(() => [String], { name: 'icdSortFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getIcdSortFields() {
    return this.icdService.getAvailableSortFields();
  }

  @Query(() => [String], { name: 'icdSearchFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getIcdSearchFields() {
    return this.icdService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Add' })
  async createIcd(
    @Args('input') input: CreateIcdInput,
    @Context() context: any
  ) {
    const authHeader = context.req?.headers?.authorization ?? '';
    let userId: string | undefined;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        userId = typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
      } catch (jwtError) {
        console.error('JWT verification failed for ICD creation:', jwtError);
      }
    }
    if (userId) {
      input.createdBy = userId;
    }

    const result = await this.icdService.create(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Edit' })
  async updateIcd(@Args('input') input: UpdateIcdInput) {
    const result = await this.icdService.update(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Delete' })
  async deleteIcd(@Args('id') id: string) {
    const result = await this.icdService.delete(id);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }
} 