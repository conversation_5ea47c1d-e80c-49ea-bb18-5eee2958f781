import { InputType, Field, ID, Int, ArgsType, ObjectType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsBoolean, IsInt, ValidateNested, IsMongoId, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { CptCode } from '../entities/cpt-code.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { SuccessResponse } from './base.response.dto';

@InputType()
export class GetCptCodeInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

@InputType()
export class CreateCptCodeInput {
  @Field(() => String)
  @IsString()
  code: string;

  @Field(() => GraphQLJSON)
  @IsObject()
  values: Record<string, any>;

  @Field(() => ID)
  @IsMongoId()
  templateId: string;

  @Field(() => ID)
  @IsMongoId()
  specialtyId: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdateCptCodeInput {
  @Field(() => ID)
  id: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  code?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  specialtyId?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@ObjectType()
export class CptCodeError {
  @Field(() => String)
  code: string;

  @Field(() => String)
  message: string;

  @Field(() => String)
  type: string;
}

@ObjectType()
export class CptCodeData {
  @Field(() => CptCode)
  cptCode: CptCode;
}

@ObjectType()
export class CptCodeResponse {
  @Field(() => Boolean)
  success: boolean;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => CptCodeData, { nullable: true })
  data?: CptCodeData;

  @Field(() => CptCodeError, { nullable: true })
  error?: CptCodeError;
}

@ObjectType()
export class CptCodePaginationInfo {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class PaginatedCptCodeResponse {
  @Field(() => [GraphQLJSON])
  items: any[];

  @Field(() => CptCodePaginationInfo)
  pagination: CptCodePaginationInfo;
}

@ArgsType()
export class PaginateCptCodeArgs {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

export { SuccessResponse }; 