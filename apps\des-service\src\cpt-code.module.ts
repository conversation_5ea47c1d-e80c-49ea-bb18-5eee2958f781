import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CptCode, CptCodeSchema } from './entities/cpt-code.entity';
import { Specialty, SpecialtySchema } from './entities/specialty.entity';
import { CptCodeService } from './cpt-code.service';
import { CptCodeResolver } from './cpt-code.resolver';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CptCode.name, schema: CptCodeSchema },
      { name: Specialty.name, schema: SpecialtySchema }
    ])
  ],
  providers: [CptCodeService, CptCodeResolver],
  exports: [CptCodeService]
})
export class CptCodeModule {} 