#!/bin/bash

APP_NAME=$1
FIELDS=$2
PORT=${3:-4003}  # Default port is 4003 if not provided

if [ -z "$APP_NAME" ] || [ -z "$FIELDS" ]; then
  echo "Usage: ./generate-crud-microservice.sh <app-name> \"field:type,field:type,...\" [port]"
  exit 1
fi

CLASS_NAME="${APP_NAME^}"  # Capitalize first letter
CLASS_NAME_UPPER="${CLASS_NAME^^}"  # Uppercase for ErrorType
ENTITY_NAME="${CLASS_NAME}"  # Use same name for entity
ENTITY_FILE="${APP_NAME}.entity"

# Check if NestJS CLI is installed
if ! command -v nest &> /dev/null; then
  echo "NestJS CLI not found. Installing globally..."
  npm install -g @nestjs/cli
fi

# Parse fields and build code snippets for entity, schema, dto, interface
IFS=',' read -ra FIELD_ARRAY <<< "$FIELDS"

ENTITY_FIELDS=""
SCHEMA_FIELDS=""
CREATE_DTO_FIELDS=""
UPDATE_DTO_FIELDS=""
INTERFACE_FIELDS=""
SEARCH_FIELDS=""
SEARCH_FIELD_NAMES=""
FILTER_FIELDS=""
SORT_FIELDS=""
SORT_FIELDS_DESCRIPTION=""
SORT_FIELDS_ENUM=""

for field in "${FIELD_ARRAY[@]}"; do
  FIELD_NAME="${field%%:*}"
  FIELD_TYPE="${field##*:}"

  # Map TS and Mongoose types
  case $FIELD_TYPE in
    string) TS_TYPE="string"; MONGO_TYPE="String"; GRAPHQL_TYPE="String";;
    number) TS_TYPE="number"; MONGO_TYPE="Number"; GRAPHQL_TYPE="Float";;
    boolean) TS_TYPE="boolean"; MONGO_TYPE="Boolean"; GRAPHQL_TYPE="Boolean";;
    date) TS_TYPE="Date"; MONGO_TYPE="Date"; GRAPHQL_TYPE="DateTime";;
    *) TS_TYPE="any"; MONGO_TYPE="Schema.Types.Mixed"; GRAPHQL_TYPE="String";;
  esac

  # Fixed formatting by removing \n escape characters
  if [ "$FIELD_TYPE" = "date" ]; then
    ENTITY_FIELDS+="  @Field(() => Date)
  @Prop({ required: true })
  $FIELD_NAME: $TS_TYPE;

"
    CREATE_DTO_FIELDS+="  @Field(() => Date)
  $FIELD_NAME: $TS_TYPE;

"
    UPDATE_DTO_FIELDS+="  @Field(() => Date, { nullable: true })
  $FIELD_NAME?: $TS_TYPE;

"
  else
    ENTITY_FIELDS+="  @Field()
  @Prop({ required: true })
  $FIELD_NAME: $TS_TYPE;

"
    CREATE_DTO_FIELDS+="  @Field()
  $FIELD_NAME: $TS_TYPE;

"
    UPDATE_DTO_FIELDS+="  @Field({ nullable: true })
  $FIELD_NAME?: $TS_TYPE;

"
  fi

  SCHEMA_FIELDS+="  $FIELD_NAME: { type: $MONGO_TYPE, required: true },
"
  INTERFACE_FIELDS+="  $FIELD_NAME: $TS_TYPE;
"

  # Note: Search fields are now dynamically detected from schema at runtime

  # Add sort fields for all types
  SORT_FIELDS+="'$FIELD_NAME', "
  SORT_FIELDS_DESCRIPTION+="$FIELD_NAME, "

  # Create enum entry (convert to uppercase and replace special chars)
  ENUM_NAME=$(echo "$FIELD_NAME" | tr '[:lower:]' '[:upper:]' | sed 's/[^A-Z0-9]/_/g')
  SORT_FIELDS_ENUM+="  ${ENUM_NAME} = '${FIELD_NAME}',
"
done

# Search fields are now dynamically detected - no static generation needed

# Remove trailing comma from sort fields
if [ -n "$SORT_FIELDS" ]; then
  SORT_FIELDS=${SORT_FIELDS%, }
fi

# Remove trailing comma from sort fields description
if [ -n "$SORT_FIELDS_DESCRIPTION" ]; then
  SORT_FIELDS_DESCRIPTION=${SORT_FIELDS_DESCRIPTION%, }
fi

# Remove trailing comma from sort fields enum
if [ -n "$SORT_FIELDS_ENUM" ]; then
  SORT_FIELDS_ENUM=${SORT_FIELDS_ENUM%,*}
fi

# Step 1: Generate Nest app
echo "Generating NestJS application: $APP_NAME"
npx nest g app $APP_NAME

# Create directory structure if it doesn't exist
mkdir -p apps/$APP_NAME/src/dto apps/$APP_NAME/src/entities apps/$APP_NAME/src/interfaces

# Step 2: Change directory to the new app
cd apps/$APP_NAME/src || exit

# Step 4: Create DTOs
cat > dto/${APP_NAME}.dto.ts <<EOL
import { InputType, Field, ID, Int, ArgsType } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsMongoId } from 'class-validator';

@InputType()
export class Create${CLASS_NAME}Input {
${CREATE_DTO_FIELDS}}

@InputType()
export class Update${CLASS_NAME}Input {
  @Field(() => ID)
  @IsMongoId()
  id: string;

${UPDATE_DTO_FIELDS}}

@InputType()
export class Get${CLASS_NAME}Input {
  @Field(() => ID)
  @IsMongoId()
  id: string;
}

@InputType()
export class Filter${CLASS_NAME}Input {
${UPDATE_DTO_FIELDS}}

@InputType()
export class Sort${CLASS_NAME}Input {
  @Field(() => String, {
    description: 'Field to sort by. Use getSortFields query to get available fields. Common fields: _id, createdAt, updatedAt, SORT_FIELDS_DESCRIPTION',
    defaultValue: 'createdAt'
  })
  @IsString()
  field: string;

  @Field(() => Boolean, {
    defaultValue: false,
    description: 'Sort direction: true for ascending, false for descending'
  })
  ascending: boolean;
}

@InputType()
export class MultiSort${CLASS_NAME}Input {
  @Field(() => [Sort${CLASS_NAME}Input], {
    description: 'Multiple sort criteria. Applied in order of array.',
    nullable: true
  })
  @IsOptional()
  sorts?: Sort${CLASS_NAME}Input[];
}

@InputType()
export class AdvancedSearch${CLASS_NAME}Input {
  @Field(() => String, {
    description: 'Search term. Use quotes for exact phrase search: "exact phrase"',
    nullable: true
  })
  @IsOptional()
  @IsString()
  term?: string;

  @Field(() => [String], {
    description: 'Specific fields to search in. If not provided, searches all string fields.',
    nullable: true
  })
  @IsOptional()
  fields?: string[];

  @Field(() => Boolean, {
    description: 'Whether to use case-sensitive search',
    defaultValue: false
  })
  @IsOptional()
  caseSensitive?: boolean;
}

@ArgsType()
export class Paginate${CLASS_NAME}Args {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number;

  @Field(() => String, {
    nullable: true,
    description: 'Search term to filter results. Searches across all string fields. Use getSearchFields query to see searchable fields.'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => Filter${CLASS_NAME}Input, { nullable: true })
  @IsOptional()
  filters?: Filter${CLASS_NAME}Input;

  @Field(() => Sort${CLASS_NAME}Input, { nullable: true })
  @IsOptional()
  sort?: Sort${CLASS_NAME}Input;
}
EOL

# Step 5: Create Base Response DTO (using identity-style format)
cat > dto/base.response.dto.ts <<EOL
import { Field, ObjectType, Directive, Int } from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';

@ObjectType()
@Directive('@key(fields: "message")')
export class BaseResponse {
  @Field()
  @Directive('@shareable')
  message: string;

  @Field()
  @Directive('@shareable')
  code: number;

  @Field()
  @Directive('@shareable')
  type: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Directive('@shareable')
  data?: Record<string, unknown>;
}

@ObjectType()
export class PaginatedResponse<T> {
  @Field(() => [GraphQLJSONObject])
  items: T[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  pages: number;
}
EOL

# Step 6: Create Entity
cat > entities/${ENTITY_FILE}.ts <<EOL
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class ${ENTITY_NAME} extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

${ENTITY_FIELDS}
  @Field()
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field()
  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const ${ENTITY_NAME}Schema = SchemaFactory.createForClass(${ENTITY_NAME});
EOL

# Step 7: Create Interface
cat > interfaces/${APP_NAME}.interface.ts <<EOL
import { Types } from 'mongoose';

export interface I${CLASS_NAME} {
  _id: Types.ObjectId;
${INTERFACE_FIELDS}
  createdAt: Date;
  updatedAt: Date;
}

export interface ${CLASS_NAME}Response {
  message: string;
  code: number;
  type: string;
  data?: Record<string, any>;
}

export interface Paginated${CLASS_NAME}Response {
  items: I${CLASS_NAME}[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface QueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    ascending: boolean;
  };
}
EOL

# Step 8: Create Service with CRUD operations (updated for identity-style responses)
cat > ${APP_NAME}.service.ts <<'EOL'
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ENTITY_NAME } from './entities/ENTITY_FILE';
import {
  CreateCLASS_NAMEInput,
  UpdateCLASS_NAMEInput,
  PaginateCLASS_NAMEArgs
} from './dto/APP_NAME.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';

@Injectable()
export class CLASS_NAMEService {
  constructor(
    @InjectModel(ENTITY_NAME.name)
    private APP_NAMEModel: Model<ENTITY_NAME>,
  ) {}

  /**
   * Retrieves all APP_NAMEs from the database with pagination, filtering, and sorting
   * @param options Query options for pagination, filtering, and sorting
   * @returns Promise resolving to paginated results
   */
  async findAll(options: PaginateCLASS_NAMEArgs = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        filters = {},
        sort = { field: 'createdAt', ascending: false },
      } = options;

      const filterQuery: any = {};

      // Dynamic Search - Build advanced search query
      if (search) {
        const searchQuery = this.buildSearchQuery(search);
        Object.assign(filterQuery, searchQuery);
      }

      // Apply additional filters
      for (const key in filters) {
        if (filters[key] !== undefined && filters[key] !== null) {
          filterQuery[key] = filters[key];
        }
      }

      // Pagination
      const skip = (page - 1) * limit;

      // Dynamic Sorting - Get available fields from schema
      const allowedSortFields = this.getAvailableSortFields();
      let sortOptions: Record<string, 1 | -1> = {};

      if (sort) {
        const sortField = sort.field;
        const sortDirection = sort.ascending ? 1 : -1;

        // Dynamic validation - check if field exists in schema
        if (!allowedSortFields.includes(sortField)) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.CLASS_NAME_UPPER,
            `Invalid sort field: ${sortField}. Available fields: ${allowedSortFields.join(', ')}`,
          );
        }

        sortOptions[sortField] = sortDirection;
      } else {
        // Default sort
        sortOptions = { createdAt: -1 };
      }

      // Fetch data
      const items = await this.APP_NAMEModel
        .find(filterQuery)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .exec();

      const total = await this.APP_NAMEModel.countDocuments(filterQuery);
      const pages = Math.ceil(total / limit);

      return {
        items,
        total,
        page,
        limit,
        pages,
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to retrieve APP_NAMEs: ${error.message}`,
      );
    }
  }

  /**
   * Retrieves a APP_NAME by its ID
   * @param id The APP_NAME's ID
   * @returns Promise resolving to the ENTITY_NAME entity
   */
  async findById(id: string): Promise<ENTITY_NAME> {
    try {
      // Validate ObjectId format
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CLASS_NAME_UPPER,
          'Invalid ID format',
        );
      }

      const APP_NAME = await this.APP_NAMEModel.findById(id).exec();

      if (!APP_NAME) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.CLASS_NAME_UPPER,
          'CLASS_NAME not found',
        );
      }

      return APP_NAME;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to find APP_NAME: ${error.message}`,
      );
    }
  }

  /**
   * Creates a new APP_NAME
   * @param input Creation data
   * @returns Success response
   */
  async create(input: CreateCLASS_NAMEInput) {
    try {
      const newCLASS_NAME = new this.APP_NAMEModel(input);
      await newCLASS_NAME.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.CLASS_NAME_UPPER,
        { APP_NAME: newCLASS_NAME },
      );
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to create APP_NAME: ${error.message}`,
      );
    }
  }

  /**
   * Updates an existing APP_NAME
   * @param input Update data including ID
   * @returns Success response
   */
  async update(input: UpdateCLASS_NAMEInput) {
    try {
      const { id, ...updateData } = input;

      // Validate ObjectId format
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CLASS_NAME_UPPER,
          'Invalid ID format',
        );
      }

      const updatedCLASS_NAME = await this.APP_NAMEModel.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true },
      );

      if (!updatedCLASS_NAME) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.CLASS_NAME_UPPER,
          'CLASS_NAME not found',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CLASS_NAME_UPPER,
        { APP_NAME: updatedCLASS_NAME },
      );
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to update APP_NAME: ${error.message}`,
      );
    }
  }

  /**
   * Deletes a APP_NAME by ID
   * @param id The APP_NAME's ID
   * @returns Success response
   */
  async delete(id: string) {
    try {
      // Validate ObjectId format
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CLASS_NAME_UPPER,
          'Invalid ID format',
        );
      }

      const result = await this.APP_NAMEModel.findByIdAndDelete(id);

      if (!result) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.CLASS_NAME_UPPER,
          'CLASS_NAME not found',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CLASS_NAME_UPPER,
        { message: 'CLASS_NAME deleted successfully' },
      );
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to delete APP_NAME: ${error.message}`,
      );
    }
  }


  /**
   * Gets the total count of APP_NAMEs
   * @returns Promise resolving to the total count
   */
  async count(): Promise<number> {
    try {
      return await this.APP_NAMEModel.countDocuments().exec();
    } catch (error) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CLASS_NAME_UPPER,
        `Failed to count APP_NAMEs: ${error.message}`,
      );
    }
  }

  /**
   * Gets available sort fields dynamically from schema
   * @returns Array of available sort field names
   */
  getAvailableSortFields(): string[] {
    const schemaFields = Object.keys(this.APP_NAMEModel.schema.paths);
    return [
      '_id',
      'createdAt',
      'updatedAt',
      ...schemaFields.filter(field =>
        !field.startsWith('_') &&
        !['__v', 'createdAt', 'updatedAt'].includes(field)
      )
    ];
  }

  /**
   * Gets searchable fields dynamically from schema (string and text fields only)
   * @returns Array of searchable field names
   */
  getSearchableFields(): string[] {
    const schema = this.APP_NAMEModel.schema;
    const searchableFields: string[] = [];

    // Iterate through schema paths to find string/text fields
    for (const [fieldName, schemaType] of Object.entries(schema.paths)) {
      // Skip internal MongoDB fields
      if (fieldName.startsWith('_') || ['__v'].includes(fieldName)) {
        continue;
      }

      // Check if field is string type (searchable)
      const fieldType = (schemaType as any).instance || (schemaType as any).constructor.name;
      if (fieldType === 'String' || fieldType === 'string') {
        searchableFields.push(fieldName);
      }
    }

    return searchableFields;
  }

  /**
   * Builds advanced search query with field-specific search options
   * @param searchTerm The search term
   * @param searchFields Optional specific fields to search in
   * @param caseSensitive Whether to use case-sensitive search
   * @returns MongoDB query object
   */
  buildSearchQuery(searchTerm: string, searchFields?: string[], caseSensitive = false): any {
    if (!searchTerm) return {};

    const availableSearchFields = this.getSearchableFields();
    let fieldsToSearch = searchFields || availableSearchFields;

    // Validate provided search fields
    if (searchFields) {
      const invalidFields = searchFields.filter(field => !availableSearchFields.includes(field));
      if (invalidFields.length > 0) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CLASS_NAME_UPPER,
          `Invalid search fields: ${invalidFields.join(', ')}. Available fields: ${availableSearchFields.join(', ')}`,
        );
      }
      fieldsToSearch = searchFields;
    }

    if (fieldsToSearch.length === 0) {
      return {};
    }

    const regexOptions = caseSensitive ? '' : 'i';

    // Support for exact phrase search if quoted
    if (searchTerm.startsWith('"') && searchTerm.endsWith('"')) {
      const exactTerm = searchTerm.slice(1, -1);
      const exactQueries = fieldsToSearch.map(field => ({
        [field]: { $regex: exactTerm, $options: regexOptions }
      }));
      return { $or: exactQueries };
    }

    // Support for multiple words (AND search within fields)
    if (searchTerm.includes(' ')) {
      const words = searchTerm.split(' ').filter(word => word.trim());
      const wordQueries = fieldsToSearch.map(field => ({
        [field]: {
          $regex: words.map(word => `(?=.*${word})`).join(''),
          $options: regexOptions
        }
      }));
      return { $or: wordQueries };
    }

    // Simple regex search
    const searchRegex = new RegExp(searchTerm, regexOptions);
    const searchQueries = fieldsToSearch.map(field => ({
      [field]: searchRegex
    }));

    return { $or: searchQueries };
  }

  /**
   * Validates and builds sort options from multiple sort criteria
   * @param sorts Array of sort criteria
   * @returns MongoDB sort options object
   */
  private buildSortOptions(sorts: any[]): Record<string, 1 | -1> {
    const allowedSortFields = this.getAvailableSortFields();
    const sortOptions: Record<string, 1 | -1> = {};

    for (const sort of sorts) {
      if (!allowedSortFields.includes(sort.field)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CLASS_NAME_UPPER,
          `Invalid sort field: ${sort.field}. Available fields: ${allowedSortFields.join(', ')}`,
        );
      }
      sortOptions[sort.field] = sort.ascending ? 1 : -1;
    }

    return Object.keys(sortOptions).length > 0 ? sortOptions : { createdAt: -1 };
  }
}
EOL

# Replace placeholders in service file
sed -i "s/ENTITY_NAME/${ENTITY_NAME}/g" ${APP_NAME}.service.ts
sed -i "s/ENTITY_FILE/${ENTITY_FILE}/g" ${APP_NAME}.service.ts
sed -i "s/CLASS_NAME_UPPER/${CLASS_NAME_UPPER}/g" ${APP_NAME}.service.ts
sed -i "s/CLASS_NAME/${CLASS_NAME}/g" ${APP_NAME}.service.ts
sed -i "s/APP_NAME/${APP_NAME}/g" ${APP_NAME}.service.ts
# Search fields are now dynamically detected - no replacement needed

# Replace placeholders in DTO file
sed -i "s/SORT_FIELDS_DESCRIPTION/${SORT_FIELDS_DESCRIPTION}/g" dto/${APP_NAME}.dto.ts

# Step 9: Create Resolver with CRUD operations
cat > ${APP_NAME}.resolver.ts <<EOL
import { Resolver, Query, Mutation, Args, ID, ObjectType, Field } from '@nestjs/graphql';
import { ${CLASS_NAME}Service } from './${APP_NAME}.service';
import { ${ENTITY_NAME} } from './entities/${ENTITY_FILE}';
import {
  Create${CLASS_NAME}Input,
  Update${CLASS_NAME}Input,
  Paginate${CLASS_NAME}Args
} from './dto/${APP_NAME}.dto';
import { BaseResponse, PaginatedResponse } from './dto/base.response.dto';

@ObjectType()
class Paginated${CLASS_NAME}Response extends PaginatedResponse<${ENTITY_NAME}> {
  @Field(() => [${ENTITY_NAME}])
 declare items: ${ENTITY_NAME}[];
}

@Resolver(() => ${ENTITY_NAME})
export class ${CLASS_NAME}Resolver {
  constructor(private readonly ${APP_NAME}Service: ${CLASS_NAME}Service) {}

  @Query(() => Paginated${CLASS_NAME}Response, { name: '${APP_NAME}s' })
  async getAll${CLASS_NAME}s(@Args() args: Paginate${CLASS_NAME}Args) {
    return this.${APP_NAME}Service.findAll(args);
  }

  @Query(() => ${ENTITY_NAME}, { name: '${APP_NAME}', nullable: true })
  async get${CLASS_NAME}(@Args('id', { type: () => ID }) id: string) {
    return this.${APP_NAME}Service.findById(id);
  }

  @Query(() => Number, { name: 'count${CLASS_NAME}s' })
  async count${CLASS_NAME}s() {
    return this.${APP_NAME}Service.count();
  }

  @Query(() => [String], { name: '${APP_NAME}SortFields' })
  async get${CLASS_NAME}SortFields() {
    return this.${APP_NAME}Service.getAvailableSortFields();
  }

  @Query(() => [String], { name: '${APP_NAME}SearchFields' })
  async get${CLASS_NAME}SearchFields() {
    return this.${APP_NAME}Service.getSearchableFields();
  }

  @Mutation(() => BaseResponse)
  async create${CLASS_NAME}(@Args('input') input: Create${CLASS_NAME}Input) {
    return this.${APP_NAME}Service.create(input);
  }

  @Mutation(() => BaseResponse)
  async update${CLASS_NAME}(@Args('input') input: Update${CLASS_NAME}Input) {
    return this.${APP_NAME}Service.update(input);
  }

  @Mutation(() => BaseResponse)
  async delete${CLASS_NAME}(@Args('id', { type: () => ID }) id: string) {
    return this.${APP_NAME}Service.delete(id);
  }
}
EOL

# Step 10: Create Controller (basic stub)
cat > ${APP_NAME}.controller.ts <<EOL
import { Controller } from '@nestjs/common';
import { ${CLASS_NAME}Service } from './${APP_NAME}.service';

@Controller('${APP_NAME}')
export class ${CLASS_NAME}Controller {
  constructor(private readonly ${APP_NAME}Service: ${CLASS_NAME}Service) {}
}
EOL

# Step 11: Create Module with Federation/Mongoose
cat > ${APP_NAME}.module.ts <<EOL
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import {
  ApolloFederationDriver,
  ApolloFederationDriverConfig,
} from '@nestjs/apollo';
import { ${CLASS_NAME}Controller } from './${APP_NAME}.controller';
import { ${CLASS_NAME}Service } from './${APP_NAME}.service';
import { ${CLASS_NAME}Resolver } from './${APP_NAME}.resolver';
import { ${ENTITY_NAME}, ${ENTITY_NAME}Schema } from './entities/${ENTITY_FILE}';
import { DatabaseModule } from '@app/db';
import { BaseResponseResolver } from './base-response.resolver';

@Module({
  imports: [
    DatabaseModule,
    MongooseModule.forFeature([{ name: ${ENTITY_NAME}.name, schema: ${ENTITY_NAME}Schema }]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req }) => ({
        query: req?.body?.query,
      }),
    }),
  ],
  controllers: [${CLASS_NAME}Controller],
  providers: [${CLASS_NAME}Service, ${CLASS_NAME}Resolver, BaseResponseResolver],
})
export class ${CLASS_NAME}Module {}
EOL

# Step 12: Create BaseResponseResolver for federation
cat > base-response.resolver.ts <<EOL
import { Resolver, ResolveReference } from '@nestjs/graphql';
import { BaseResponse } from './dto/base.response.dto';

@Resolver(() => BaseResponse)
export class BaseResponseResolver {
  @ResolveReference()
  resolveReference(reference: { __typename: string; message: string }): BaseResponse {
    return {
      message: reference.message,
      code: 0,
      type: '',
      data: {}
    };
  }
}
EOL

# Step 13: Update main.ts to include CORS
cat > main.ts <<EOL
import { NestFactory } from '@nestjs/core';
import { ${CLASS_NAME}Module } from './${APP_NAME}.module';

async function bootstrap() {
  const app = await NestFactory.create(${CLASS_NAME}Module);
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
  });
  await app.listen(process.env.PORT ?? ${PORT});
}
bootstrap();
EOL

# Step 14: Create Dockerfile
cd ../ || exit
cat > Dockerfile <<EOL
FROM node:22-alpine AS builder

WORKDIR /app

COPY package.json ./

RUN npm install --legacy-peer-deps

COPY . .

RUN npx nest build ${APP_NAME}

# Stage 2:
FROM node:22-alpine AS runtime

WORKDIR /app

COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package*.json /app/
COPY --from=builder /app/apps/${APP_NAME} /app/apps/${APP_NAME}

RUN npm install --legacy-peer-deps

EXPOSE ${PORT}

CMD ["node", "dist/apps/${APP_NAME}/main.js"]
EOL

# Step 15: Update gateway configuration
echo "🔧 Updating gateway configuration files..."

# Function to add service to config file
add_service_to_config() {
  local config_file="$1"
  local service_name="$2"
  local service_url="$3"
  local config_type="$4"

  # Create backup
  cp "$config_file" "${config_file}.backup"

  # Try different patterns to find where to insert
  if grep -q "export const subgraphs" "$config_file"; then
    # Method 1: Look for closing bracket of subgraphs array
    if grep -q "];$" "$config_file"; then
      # Insert before the closing bracket
      sed -i "/];$/i\\
  {\\
    name: '${service_name}',\\
    url: \`${service_url}\`,\\
  }," "$config_file"
      echo "✅ Added ${service_name} to ${config_type} gateway configuration"
      return 0
    elif grep -q "];" "$config_file"; then
      # Insert before ]; (with possible whitespace)
      sed -i "/];/i\\
  {\\
    name: '${service_name}',\\
    url: \`${service_url}\`,\\
  }," "$config_file"
      echo "✅ Added ${service_name} to ${config_type} gateway configuration"
      return 0
    else
      # Method 2: Look for last service entry and add after it
      local last_service_line=$(grep -n "name:" "$config_file" | tail -1 | cut -d: -f1)
      if [ -n "$last_service_line" ]; then
        # Find the closing brace of the last service
        local insert_line=$(awk "NR>${last_service_line} && /},/ {print NR; exit}" "$config_file")
        if [ -n "$insert_line" ]; then
          sed -i "${insert_line}a\\
  {\\
    name: '${service_name}',\\
    url: \`${service_url}\`,\\
  }," "$config_file"
          echo "✅ Added ${service_name} to ${config_type} gateway configuration"
          return 0
        fi
      fi
    fi
  fi

  # If all methods fail, restore backup and return error
  mv "${config_file}.backup" "$config_file"
  return 1
}

# Function to add service to datasources configuration
add_service_to_datasources() {
  local datasources_file="$1"
  local service_name="$2"

  # Create backup
  cp "$datasources_file" "${datasources_file}.backup"

  # Check if service already exists
  if grep -q "'${service_name}':" "$datasources_file" || grep -q "\"${service_name}\":" "$datasources_file"; then
    echo "⚠️ ${service_name} already exists in datasources configuration"
    rm -f "${datasources_file}.backup"
    return 0
  fi

  # Find the closing brace of the dataSources object
  local closing_brace_line=$(grep -n "^};" "$datasources_file" | head -1 | cut -d: -f1)

  if [ -n "$closing_brace_line" ]; then
    # Insert before the closing brace
    sed -i "${closing_brace_line}i\\
  '${service_name}': {\\
    dataSourceClass: OpenDataSource,\\
  }," "$datasources_file"
    echo "✅ Added ${service_name} to datasources configuration"
    return 0
  else
    # Try alternative pattern - look for closing brace with whitespace
    closing_brace_line=$(awk '/^[[:space:]]*};[[:space:]]*$/ {print NR; exit}' "$datasources_file")
    if [ -n "$closing_brace_line" ]; then
      sed -i "${closing_brace_line}i\\
  '${service_name}': {\\
    dataSourceClass: OpenDataSource,\\
  }," "$datasources_file"
      echo "✅ Added ${service_name} to datasources configuration"
      return 0
    fi
  fi

  # If all methods fail, restore backup and return error
  mv "${datasources_file}.backup" "$datasources_file"
  return 1
}

# Update local gateway configuration
LOCAL_CONFIG_PATH="../../apps/gateway/src/application/config/local/index.ts"
if [ -f "$LOCAL_CONFIG_PATH" ]; then
  # Check if service already exists in local config
  if grep -q "name: '${APP_NAME}'" "$LOCAL_CONFIG_PATH"; then
    echo "⚠️ ${APP_NAME} already exists in local gateway configuration"
  else
    if add_service_to_config "$LOCAL_CONFIG_PATH" "$APP_NAME" "http://localhost:${PORT}/graphql" "local"; then
      # Remove backup if successful
      rm -f "${LOCAL_CONFIG_PATH}.backup"
    else
      echo "⚠️ Could not automatically update local gateway config"
      echo "📝 Please manually add the following to your local gateway config:"
      echo "
{
  name: '${APP_NAME}',
  url: \`http://localhost:${PORT}/graphql\`,
},"
    fi
  fi
else
  echo "⚠️ Could not find local gateway config at $LOCAL_CONFIG_PATH"
  echo "📝 Please manually add the following to your local gateway config:"
  echo "
{
  name: '${APP_NAME}',
  url: \`http://localhost:${PORT}/graphql\`,
},"
fi

# Update server gateway configuration
SERVER_CONFIG_PATH="../../apps/gateway/src/application/config/server/index.ts"
if [ -f "$SERVER_CONFIG_PATH" ]; then
  # Check if service already exists in server config
  if grep -q "name: '${APP_NAME}'" "$SERVER_CONFIG_PATH"; then
    echo "⚠️ ${APP_NAME} already exists in server gateway configuration"
  else
    if add_service_to_config "$SERVER_CONFIG_PATH" "$APP_NAME" "http://${APP_NAME}-service:${PORT}/graphql" "server"; then
      # Remove backup if successful
      rm -f "${SERVER_CONFIG_PATH}.backup"
    else
      echo "⚠️ Could not automatically update server gateway config"
      echo "📝 Please manually add the following to your server gateway config:"
      echo "
{
  name: '${APP_NAME}',
  url: \`http://${APP_NAME}-service:${PORT}/graphql\`,
},"
    fi
  fi
else
  echo "⚠️ Could not find server gateway config at $SERVER_CONFIG_PATH"
  echo "📝 Please manually add the following to your server gateway config:"
  echo "
{
  name: '${APP_NAME}',
  url: \`http://${APP_NAME}-service:${PORT}/graphql\`,
},"
fi

# Update gateway datasources configuration
DATASOURCES_CONFIG_PATH="../../apps/gateway/src/domain/datasources/index.ts"
if [ -f "$DATASOURCES_CONFIG_PATH" ]; then
  # Check if service already exists in datasources config
  if grep -q "'${APP_NAME}':" "$DATASOURCES_CONFIG_PATH" || grep -q "\"${APP_NAME}\":" "$DATASOURCES_CONFIG_PATH"; then
    echo "⚠️ ${APP_NAME} already exists in datasources configuration"
  else
    if add_service_to_datasources "$DATASOURCES_CONFIG_PATH" "$APP_NAME"; then
      # Remove backup if successful
      rm -f "${DATASOURCES_CONFIG_PATH}.backup"
    else
      echo "⚠️ Could not automatically update datasources configuration"
      echo "📝 Please manually add the following to your datasources configuration:"
      echo "
  '${APP_NAME}': {
    dataSourceClass: OpenDataSource,
  },"
    fi
  fi
else
  echo "⚠️ Could not find datasources config at $DATASOURCES_CONFIG_PATH"
  echo "📝 Please manually add the following to your datasources configuration:"
  echo "
  '${APP_NAME}': {
    dataSourceClass: OpenDataSource,
  },"
fi

# Function to add service to docker-compose.yml
add_service_to_docker_compose() {
  local compose_file="$1"
  local service_name="$2"
  local port="$3"

  # Create backup
  cp "$compose_file" "${compose_file}.backup"

  # Check if we're inside a services block
  if grep -q "^services:" "$compose_file"; then
    # Add the service at the end of the file (before any volumes/networks sections)
    # Find the last service entry or add after services:
    local insert_line

    # Look for volumes: or networks: sections to insert before them
    if grep -q "^volumes:" "$compose_file"; then
      insert_line=$(grep -n "^volumes:" "$compose_file" | head -1 | cut -d: -f1)
      insert_line=$((insert_line - 1))
    elif grep -q "^networks:" "$compose_file"; then
      insert_line=$(grep -n "^networks:" "$compose_file" | head -1 | cut -d: -f1)
      insert_line=$((insert_line - 1))
    else
      # Add at the end of file
      insert_line=$(wc -l < "$compose_file")
    fi

    # Insert the service configuration
    sed -i "${insert_line}a\\
\\
  ${service_name}-service:\\
    build:\\
      context: .\\
      dockerfile: apps/${service_name}/Dockerfile\\
    container_name: ${service_name}-service\\
    networks:\\
      - mynetwork\\
    environment:\\
      - NODE_ENV=development\\
      - PORT=${port}\\
    ports:\\
      - \"${port}:${port}\"\\
    depends_on:\\
      - mongodb" "$compose_file"

    echo "✅ Added ${service_name}-service to docker-compose.yml"
    return 0
  else
    # Restore backup and return error
    mv "${compose_file}.backup" "$compose_file"
    return 1
  fi
}

# Update docker-compose.yml
DOCKER_COMPOSE_PATH="../../docker-compose.yml"
if [ -f "$DOCKER_COMPOSE_PATH" ]; then
  # Check if service already exists in docker-compose
  if grep -q "${APP_NAME}-service:" "$DOCKER_COMPOSE_PATH"; then
    echo "⚠️ ${APP_NAME}-service already exists in docker-compose.yml"
  else
    if add_service_to_docker_compose "$DOCKER_COMPOSE_PATH" "$APP_NAME" "$PORT"; then
      # Remove backup if successful
      rm -f "${DOCKER_COMPOSE_PATH}.backup"
    else
      echo "⚠️ Could not automatically update docker-compose.yml"
      echo "📝 Please manually add the following to your docker-compose.yml:"
      echo "
  ${APP_NAME}-service:
    build:
      context: .
      dockerfile: apps/${APP_NAME}/Dockerfile
    container_name: ${APP_NAME}-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
      - PORT=${PORT}
    ports:
      - \"${PORT}:${PORT}\"
    depends_on:
      - mongodb
"
    fi
  fi
else
  echo "⚠️ Could not find docker-compose.yml at $DOCKER_COMPOSE_PATH"
  echo "📝 Please manually add the following to your docker-compose.yml:"
  echo "
  ${APP_NAME}-service:
    build:
      context: .
      dockerfile: apps/${APP_NAME}/Dockerfile
    container_name: ${APP_NAME}-service
    networks:
      - mynetwork
    environment:
      - NODE_ENV=development
      - PORT=${PORT}
    ports:
      - \"${PORT}:${PORT}\"
    depends_on:
      - mongodb
"
fi

# Step 15.5: Update package.json scripts (optional)
PACKAGE_JSON_PATH="../../package.json"
if [ -f "$PACKAGE_JSON_PATH" ]; then
  echo "🔧 Checking package.json scripts..."

  # Check if start script for this service already exists
  if grep -q "\"start:${APP_NAME}\"" "$PACKAGE_JSON_PATH"; then
    echo "⚠️ start:${APP_NAME} script already exists in package.json"
  else
    # Add start script for the new service
    # This is a bit complex with sed, so we'll provide manual instructions
    echo "📝 Consider adding the following scripts to your package.json:"
    echo "
\"start:${APP_NAME}\": \"nest start ${APP_NAME}\",
\"start:dev:${APP_NAME}\": \"nest start --watch ${APP_NAME}\",
\"build:${APP_NAME}\": \"nest build ${APP_NAME}\","
  fi
else
  echo "⚠️ Could not find package.json at $PACKAGE_JSON_PATH"
fi

# Step 16: Update ErrorType enum in errorCodes.ts
ERROR_CODES_PATH="../../libs/error/src/errorCodes.ts"
if [ -f "$ERROR_CODES_PATH" ]; then
  # Check if the error type already exists
  if grep -q "${CLASS_NAME^^}" "$ERROR_CODES_PATH"; then
    echo "⚠️ ${CLASS_NAME^^} already exists in ErrorType enum"
  else
    # Add the new error type before the closing brace of the enum
    sed -i "/^enum ErrorType {/,/^}/ s/^}/  ${CLASS_NAME^^},\n}/" "$ERROR_CODES_PATH"
    echo "✅ Added ${CLASS_NAME^^} to ErrorType enum in $ERROR_CODES_PATH"
  fi
else
  echo "⚠️ Could not find errorCodes.ts to update ErrorType enum"
  echo "Please manually add ${CLASS_NAME^^}, to the ErrorType enum in libs/error/src/errorCodes.ts"
fi

echo "✅ CRUD Microservice '${APP_NAME}' generated with fields: ${FIELDS}"
echo "✅ Service running on port ${PORT}"
echo "✅ All files created with full CRUD operations using identity-style responses"
echo ""
echo "📁 Generated files:"
echo "   - Entity: entities/${ENTITY_FILE}.ts"
echo "   - DTOs: dto/${APP_NAME}.dto.ts, dto/base.response.dto.ts"
echo "   - Service: ${APP_NAME}.service.ts (with count method)"
echo "   - Resolver: ${APP_NAME}.resolver.ts"
echo "   - Controller: ${APP_NAME}.controller.ts"
echo "   - Module: ${APP_NAME}.module.ts"
echo "   - Interface: interfaces/${APP_NAME}.interface.ts"
echo "   - Main: main.ts"
echo "   - Dockerfile: Dockerfile"
echo "   - BaseResponseResolver: base-response.resolver.ts"
echo ""
echo "🔧 Features included:"
echo "   - Full CRUD operations (Create, Read, Update, Delete, Count)"
echo "   - Pagination, filtering, and sorting"
echo "   - MongoDB ObjectId validation"
echo "   - GraphQL federation support"
echo "   - Error handling with custom Error/Success classes"
echo "   - Type-safe DTOs with validation"
echo "   - Docker containerization"
echo ""
echo "⚠️  Next steps:"
echo "   1. Gateway configurations updated automatically (local, server, and datasources)"
echo "   2. Install dependencies: npm install"
echo "   3. Build the service: npm run build ${APP_NAME}"
echo "   4. Start the service: npm run start:dev ${APP_NAME}"



