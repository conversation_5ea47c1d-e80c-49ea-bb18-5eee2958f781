import { InputType, Field, ID, ObjectType, Int, ArgsType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsMongoId, IsEnum, IsObject } from 'class-validator';
import { ProviderStatus, ProviderEntryType } from '../entities/provider.entity';
import { GraphQLJSON } from 'graphql-type-json';

@InputType()
export class CreateProviderTicketInput {

  @Field({ nullable: true })
  @IsString()
  templateId: string;

  @Field(() => GraphQLJSON, { nullable: true })
  values?: Record<string, any>;

  @Field(() => ProviderEntryType, { nullable: true })
  @IsEnum(ProviderEntryType)
  type: ProviderEntryType;

  @Field(() => ProviderStatus, { nullable: true })
  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  allocated_type?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  follow_up_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  received_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  worked_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  audit_by?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  flattenedValues?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  assignedTo?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdby?: string;


  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  source_ticket_id?: string;

}

@InputType()
export class UpdateProviderTicketInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  ticketId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => ProviderStatus, { nullable: true })
  @IsOptional()
  @IsEnum(ProviderStatus)
  status?: ProviderStatus;

  @Field(() => ProviderEntryType, { nullable: true })
  @IsOptional()
  @IsEnum(ProviderEntryType)
  type?: ProviderEntryType;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  allocated_type?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  follow_up_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  received_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  worked_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  audit_by?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdby?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  flattenedValues?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  assignedTo?: string;
  
  @Field({ nullable: true })
  @IsOptional()
  assignedName?: string;
}

@InputType()
export class GetProviderTicketInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;
}


@ObjectType()
@Directive('@shareable')
export class PaginationMetas {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}



@ObjectType()
export class ProviderResponse {
  @Field(() => [GraphQLJSON])
  provider: any[];

  @Field(() => PaginationMetas)
  pagination: PaginationMetas;
}

@InputType()
export class PaginateProviderTicketArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;
}

