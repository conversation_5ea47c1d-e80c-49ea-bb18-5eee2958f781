import { Module, Global } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { NotificationService } from './notification.service';
import { FirebasePushService } from './firebase-push.service';
import { DatabaseModule } from '@app/db';

@Global()
@Module({
  imports: [
    EventEmitterModule.forRoot(),
    DatabaseModule,
  ],
  providers: [
    NotificationService,
    FirebasePushService
  ],
  exports: [
    NotificationService,
    FirebasePushService
  ],
})
export class NotificationModule {}
