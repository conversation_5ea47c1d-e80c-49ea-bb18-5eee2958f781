import { ObjectId } from 'mongodb';
import { NotificationType, NotificationChannel, NotificationPriority, NotificationStatus } from '../enums/notification.enum';

export interface NotificationEntity {
  senderId: string;
  _id?: ObjectId;
  notificationId: string;
  userId: string;
  userEmail?: string;
  type: NotificationType;
  title: string;
  message: string;
  isRouted?: boolean;
  channels: NotificationChannel[];
  priority: NotificationPriority;
  status: NotificationStatus;
  data?: Record<string, any>;
  metadata?: Record<string, any>;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  orgId?: string;
  subOrgId?: string;
  processId?: string;
  isRead: boolean;
  createdAt: Date; 
  updatedAt: Date;
} 