import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { GridTemplateService } from './grid_template.service';
import { GridTemplate } from './entities/grid_template.entity';
import {
  FindAllGridTemplatesArgs,
  GridTemplatesResponse,
  CreateGridTemplateInput,
  UpdateGridTemplateInput
} from './dto/grid_template.dto';
import { BaseResponse } from './dto/base.response.dto';
import * as jwt from 'jsonwebtoken';

@Resolver(() => GridTemplate)
export class GridTemplateResolver {
  constructor(private readonly gridTemplateService: GridTemplateService) { }

  /**
   * Fetches all grid templates with dynamic search, filter, and sort
   * @param args Query arguments including pagination, search, filters, and sort
   * @returns A BaseResponse containing grid templates and pagination info
   */
  @Query(() => BaseResponse, { name: 'gridTemplates' })
  async getGridTemplates(
    @Args() args: FindAllGridTemplatesArgs,
    @Context() context?: any,

  ) {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');

    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.gridTemplateService.findAll(args, userId);
  }

  /**
   * Fetches a grid template by its ID
   * @param id The grid template's ID
   * @returns A BaseResponse containing the grid template
   */
  @Query(() => BaseResponse, { name: 'gridTemplate' })
  async getGridTemplate(@Args('id', { type: () => ID }) id: string) {
    return this.gridTemplateService.findById(id);
  }

  /**
   * Creates a new grid template
   * @param input Grid template creation data
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  async createGridTemplate(@Args('input') input: CreateGridTemplateInput) {
    return this.gridTemplateService.create(input);
  }

  /**
   * Updates an existing grid template
   * @param input Grid template update data
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  async updateGridTemplate(@Args('input') input: UpdateGridTemplateInput) {
    return this.gridTemplateService.update(input);
  }

  /**
   * Soft deletes a grid template
   * @param id The grid template's ID
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  async deleteGridTemplate(@Args('id', { type: () => ID }) id: string) {
    return this.gridTemplateService.delete(id);
  }
}
