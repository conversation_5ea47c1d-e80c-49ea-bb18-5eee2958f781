import { Field, InputType, ObjectType, Int, ArgsType, registerEnumType } from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { OrganisationType } from '../entities/organisation-role.entity';

@InputType()
export class CreateOrganisationUserInput {
  @Field()
  name: string;
  @Field()
  email: string;
  @Field()
  employeeId: string;
  @Field()
  organisationId: string;
  @Field({ nullable: true })
  orgId?: string;
  @Field({ nullable: true })
  subOrganisationId?: string;
  @Field()
  roleId: string;
  @Field(() => OrganisationType)
  type: OrganisationType;
}

@InputType()
export class UpdateOrganisationUserInput {
  @Field()
  id: string;
  @Field({ nullable: true })
  name?: string;
  @Field({ nullable: true })
  email?: string;
  @Field({ nullable: true })
  employeeId?: string;
  @Field({ nullable: true })
  organisationId?: string;
  @Field({ nullable: true })
  subOrganisationId?: string;
  @Field({ nullable: true })
  orgId?: string;
  @Field({ nullable: true })
  roleId?: string;
  @Field({ nullable: true })
  isActive?: boolean;
  @Field(() => OrganisationType, { nullable: true })
  type?: OrganisationType;
}

@ArgsType()
export class PaginateOrganisationUserArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number = 1;
  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number = 10;
  @Field({ nullable: true })
  search?: string;
  @Field(() => GraphQLJSONObject, { nullable: true })
  filters?: any;
  @Field({ nullable: true })
  sortBy?: string;
  @Field({ nullable: true, defaultValue: 'asc' })
  sortOrder?: 'asc' | 'desc';
  @Field(() => GraphQLJSONObject, { nullable: true })
  selectedFields?: any;
  @Field({ nullable: true })
  organisationId?: string;
  @Field(() => OrganisationType, { nullable: true })
  type?: OrganisationType;
}

@ObjectType()
export class OrganisationUserResponse {
  @Field()
  _id: string;
  @Field()
  name: string;
  @Field()
  email: string;
  @Field()
  employeeId: string;
  @Field()
  organisationId: string;
  @Field({ nullable: true })
  orgId?: string;
  @Field()
  roleId: string;
  @Field({ nullable: true })
  roleName?: string;
  @Field(() => OrganisationType)
  type: OrganisationType;
  @Field()
  isActive: boolean;
  @Field({ nullable: true })
  createdAt?: string;
  @Field({ nullable: true })
  updatedAt?: string;
}

@ObjectType()
export class PaginationOrganisationUserResponse {
  @Field(() => [OrganisationUserResponse])
  items: OrganisationUserResponse[];
  @Field(() => GraphQLJSONObject)
  pagination: any;
} 