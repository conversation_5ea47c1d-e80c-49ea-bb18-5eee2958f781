{"name": "gateway", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/gateway/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/gateway/test/jest-e2e.json", "seed:templates": "ts-node -r tsconfig-paths/register apps/des-service/template.seed.ts", "seed:templates:cli": "ts-node -r tsconfig-paths/register apps/des-service/src/cli/seed-templates.command.ts"}, "dependencies": {"@apollo/gateway": "^2.10.2", "@apollo/server": "^4.12.1", "@apollo/subgraph": "^2.10.2", "@bull-board/api": "^6.11.0", "@bull-board/express": "^6.11.0", "@google-cloud/pubsub": "^5.1.0", "@google-cloud/storage": "^7.16.0", "@nestjs/apollo": "^13.1.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/microservices": "^11.1.4", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.3", "apollo-server-core": "^3.13.0", "axios": "^1.10.0", "bullmq": "^5.56.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "crypto": "^1.0.1", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "firebase-admin": "^13.4.0", "gateway": "file:", "graphql": "^16.11.0", "graphql-type-json": "^0.3.2", "ioredis": "^5.6.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash.isequal": "^4.5.0", "moment": "^2.30.1", "mongoose": "^8.14.3", "nodemailer": "^7.0.3", "otplib": "^12.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "totp": "^0.0.1", "ulid": "^3.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.10.7", "@types/nodemailer": "^6.4.17", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/error(.*)$": "<rootDir>/libs/error/src$1", "^@app/db(.*)$": "<rootDir>/libs/db/src$1", "^@app/config(.*)$": "<rootDir>/libs/config/src$1", "^@app/functions(.*)$": "<rootDir>/libs/functions/src$1"}}}