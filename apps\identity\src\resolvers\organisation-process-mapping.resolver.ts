import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { OrganisationProcessMappingService } from '../services/organisation-process-mapping.service';
import { OrganisationProcessMapping } from '../entities/organisation-process-mapping.entity';
import { RequirePermission } from '@app/permissions';

@Resolver(() => OrganisationProcessMapping)
export class OrganisationProcessMappingResolver {
  constructor(private readonly service: OrganisationProcessMappingService) {}

  @Query(() => [OrganisationProcessMapping])
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  async organisationProcessMappings(
    @Args('organisationId', { nullable: true }) organisationId?: string,
    @Args('subOrganisationId', { nullable: true }) subOrganisationId?: string
  ) {
    return this.service.list(organisationId, subOrganisationId);
  }

  @Mutation(() => OrganisationProcessMapping)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  )
  async updateOrganisationProcessMappingIsActive(
    @Args('id') id: string,
    @Args('isActive') isActive: boolean,
    @Context() context: any
  ) {
    // Extract user context from headers
    const subOrgId = context.req.headers.suborgid ?? '';
    const processId = context.req.headers.processid ?? '';

    const userContext = {
      subOrgId,
      processId
    };

    return this.service.updateIsActive(id, isActive, userContext);
  }
} 