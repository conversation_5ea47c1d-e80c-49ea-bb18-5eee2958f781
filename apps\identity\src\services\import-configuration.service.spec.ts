import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { ImportConfigurationService } from './import-configuration.service';
import { ImportConfiguration } from '../entities/import-configuration.entity';
import { CreateImportConfigurationInput, UpdateImportConfigurationInput } from '../dto/import.dto';

describe('ImportConfigurationService', () => {
  let service: ImportConfigurationService;
  let model: Model<ImportConfiguration>;

  const mockUserId = new Types.ObjectId().toString();
  const mockConfigId = new Types.ObjectId().toString();

  const mockConfiguration = {
    _id: new Types.ObjectId(mockConfigId),
    userId: new Types.ObjectId(mockUserId),
    templateId: 'test-template-123',
    collectionName: 'users',
    mappingJson: { name: 'Name', email: 'Email' },
    requiredFields: ['name', 'email'],
    uniqueFields: ['email'],
    isActive: true,
    orgId: 'test-org',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockModel = {
    create: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    countDocuments: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ImportConfigurationService,
        {
          provide: getModelToken(ImportConfiguration.name),
          useValue: mockModel,
        },
      ],
    }).compile();

    service = module.get<ImportConfigurationService>(ImportConfigurationService);
    model = module.get<Model<ImportConfiguration>>(getModelToken(ImportConfiguration.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createConfiguration', () => {
    it('should create a new configuration successfully', async () => {
      const input: CreateImportConfigurationInput = {
        templateId: 'test-template-123',
        collectionName: 'users',
        mappingJson: { name: 'Name', email: 'Email' },
        requiredFields: ['name', 'email'],
        uniqueFields: ['email'],
        orgId: 'test-org',
      };

      mockModel.create.mockResolvedValue(mockConfiguration);

      const result = await service.createConfiguration(mockUserId, input);

      expect(mockModel.create).toHaveBeenCalledWith({
        userId: new Types.ObjectId(mockUserId),
        ...input,
      });
      expect(result).toEqual(mockConfiguration);
    });

    it('should throw ConflictException for duplicate template ID', async () => {
      const input: CreateImportConfigurationInput = {
        templateId: 'test-template-123',
        collectionName: 'users',
        mappingJson: { name: 'Name', email: 'Email' },
        requiredFields: ['name', 'email'],
      };

      mockModel.create.mockRejectedValue({ code: 11000 });

      await expect(service.createConfiguration(mockUserId, input)).rejects.toThrow(ConflictException);
    });
  });

  describe('getConfigurationById', () => {
    it('should return configuration when found', async () => {
      mockModel.findOne.mockResolvedValue(mockConfiguration);

      const result = await service.getConfigurationById(mockConfigId, mockUserId);

      expect(mockModel.findOne).toHaveBeenCalledWith({
        _id: new Types.ObjectId(mockConfigId),
        userId: new Types.ObjectId(mockUserId),
      });
      expect(result).toEqual(mockConfiguration);
    });

    it('should throw NotFoundException when configuration not found', async () => {
      mockModel.findOne.mockResolvedValue(null);

      await expect(service.getConfigurationById(mockConfigId, mockUserId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getUserConfigurations', () => {
    it('should return paginated configurations', async () => {
      const mockConfigurations = [mockConfiguration];
      mockModel.countDocuments.mockResolvedValue(1);
      mockModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              lean: jest.fn().mockResolvedValue(mockConfigurations),
            }),
          }),
        }),
      });

      const result = await service.getUserConfigurations(mockUserId, {
        page: 1,
        limit: 10,
      });

      expect(result.items).toEqual(mockConfigurations);
      expect(result.pagination.total).toBe(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });
  });

  describe('updateConfiguration', () => {
    it('should update configuration successfully', async () => {
      const input: UpdateImportConfigurationInput = {
        configurationId: mockConfigId,
        templateId: 'updated-template-456',
      };

      const updatedConfig = { ...mockConfiguration, templateId: 'updated-template-456' };
      mockModel.findOneAndUpdate.mockResolvedValue(updatedConfig);

      const result = await service.updateConfiguration(mockUserId, input);

      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        {
          _id: new Types.ObjectId(mockConfigId),
          userId: new Types.ObjectId(mockUserId),
        },
        { $set: { templateId: 'updated-template-456' } },
        { new: true }
      );
      expect(result).toEqual(updatedConfig);
    });

    it('should throw NotFoundException when configuration not found for update', async () => {
      const input: UpdateImportConfigurationInput = {
        configurationId: mockConfigId,
        templateId: 'updated-template-456',
      };

      mockModel.findOneAndUpdate.mockResolvedValue(null);

      await expect(service.updateConfiguration(mockUserId, input)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteConfiguration', () => {
    it('should delete configuration successfully', async () => {
      mockModel.deleteOne.mockResolvedValue({ deletedCount: 1 });

      const result = await service.deleteConfiguration(mockConfigId, mockUserId);

      expect(mockModel.deleteOne).toHaveBeenCalledWith({
        _id: new Types.ObjectId(mockConfigId),
        userId: new Types.ObjectId(mockUserId),
      });
      expect(result).toBe(true);
    });

    it('should return false when configuration not found for deletion', async () => {
      mockModel.deleteOne.mockResolvedValue({ deletedCount: 0 });

      const result = await service.deleteConfiguration(mockConfigId, mockUserId);

      expect(result).toBe(false);
    });
  });

  describe('validateConfiguration', () => {
    it('should validate configuration successfully', async () => {
      mockModel.findOne.mockResolvedValue(mockConfiguration);

      const result = await service.validateConfiguration(mockConfigId, mockUserId);

      expect(result).toEqual(mockConfiguration);
    });

    it('should throw ConflictException for missing required fields in mapping', async () => {
      const invalidConfig = {
        ...mockConfiguration,
        mappingJson: { name: 'Name' }, // missing email
        requiredFields: ['name', 'email'],
      };
      mockModel.findOne.mockResolvedValue(invalidConfig);

      await expect(service.validateConfiguration(mockConfigId, mockUserId)).rejects.toThrow(ConflictException);
    });
  });

  describe('getConfigurationsByCollection', () => {
    it('should return configurations for specific collection', async () => {
      const mockConfigurations = [mockConfiguration];
      mockModel.find.mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockConfigurations),
      });

      const result = await service.getConfigurationsByCollection(mockUserId, 'users');

      expect(mockModel.find).toHaveBeenCalledWith({
        userId: new Types.ObjectId(mockUserId),
        collectionName: 'users',
        isActive: true,
      });
      expect(result).toEqual(mockConfigurations);
    });
  });
});
