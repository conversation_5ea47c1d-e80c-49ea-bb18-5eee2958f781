import * as dotenv from 'dotenv';
dotenv.config();
 
import { ServiceEndpointDefinition } from '@apollo/gateway';
import { subgraphs as localSubgraphs } from './local';
import { subgraphs as serverSubgraph } from './server';
 
const env = process.env.PORTAL ?? 'server';
console.log('process.env.PORTAL:', process.env.PORTAL);
console.log('the env is given as', env);
 
const subgraphs =
  {
    local: localSubgraphs,
    server: serverSubgraph,
  }[env] || [];
 
if (!subgraphs.length) {
  throw new Error(`No subgraphs found for env "${env}".`);
}
 
export const getSubgraphs = (): Array<ServiceEndpointDefinition> => {
  console.log('the subgraphs is given as:', subgraphs);
  return subgraphs;
};