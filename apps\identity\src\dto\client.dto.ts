import { InputType, Field, ID, ObjectType, Int, ArgsType, Directive } from '@nestjs/graphql';
import { UserType, ClientStatus } from '../entities/client.entities';
import { GraphQLJSON } from 'graphql-type-json';
import { Organisation } from '../entities/client.entities';
import { IsString, IsOptional, IsBoolean, IsObject, IsMongoId, IsEnum, IsInt, Min, Max, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

@InputType()
export class CreateUserInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  templateId: string;

  @Field(() => GraphQLJSON, { nullable: true })
  values?: Record<string, any>;

  @Field(() => UserType)
  type: UserType;

  @Field(() => ID, { nullable: true })
  main_client?: string;

  @Field(() => ID, { nullable: true })
  created_by?: string;

  @Field({ nullable: true })
  is2FAenabled?: boolean;

  @Field({ nullable: true })
  bypass2FA?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  flattenedValues?: Record<string, any>;


}


@InputType()
export class UpdateUserInput {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  templateId?: string;

  @Field({ nullable: true })
  email?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  values?: Record<string, any>;

  @Field(() => UserType, { nullable: true })
  type?: UserType;

  @Field(() => ID, { nullable: true })
  created_by?: string;

  @Field(() => ID, { nullable: true })
  main_client?: string;

  @Field({ nullable: true })
  is2FAenabled?: boolean;

  @Field({ nullable: true })
  bypass2FA?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  flattenedValues?: Record<string, any>;

}


@InputType()
export class GetClientInput {
  @Field(() => ID)
  id: string;
}

@InputType()
export class GetSubClientsInput {
  @Field(() => ID)
  mainClientId: string;
}

@InputType()
export class GetClientsByMainClientInput {
  @Field(() => ID)
  mainClientId: string;

  @Field({ nullable: true })
  search?: string;

  @Field(() => String, { nullable: true })
  filters?: string; // JSON string of filters

  @Field({ nullable: true })
  sortBy?: string;

  @Field({ nullable: true })
  sortOrder?: 'asc' | 'desc';

  @Field({ nullable: true })
  page?: number;

  @Field({ nullable: true })
  limit?: number;
}

@InputType()
export class DeleteClientInput {
  @Field(() => ID)
  id: string;
  
  @Field(() => UserType, { nullable: true })
  type?: UserType;
}

@InputType()
export class GetClientsFilterInput {
  @Field(() => UserType, { nullable: true })
  type?: UserType;
}

@InputType()
export class OtpRequestInput {
  @Field()
  email: string;
}

@InputType()
export class OtpVerifyInput {
  @Field()
  email: string;

  @Field()
  otp: string;
}

@InputType()
export class TotpVerifyInput {
  @Field()
  email: string;

  @Field({ nullable: true })
  otp?: string;

  @Field({ nullable: true })
  skip2FACheck?: boolean;
}

@InputType()
export class TwoFAInput {
  @Field()
  email: string;

  @Field({ nullable: true })
  is2FAenabled: boolean;

  @Field({ nullable: true })
  bypass2FA: boolean;
}

// ===== DYNAMIC SEARCH, FILTER, AND SORT DTOs =====

@InputType()
export class GetUsersInput {
  @Field({ nullable: true })
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  filters?: Record<string, any>;

  @Field({ nullable: true })
  sortBy?: string;

  @Field({ nullable: true })
  sortOrder?: 'asc' | 'desc';

  @Field({ nullable: true })
  page?: number;

    @Field({ nullable: true })
    limit?: number;


  @Field(() => GraphQLJSON, { nullable: true })
  selectedFields?: Record<string, any>;

  @Field({ nullable: true })
  type?: string;

  @Field({ nullable: true })
  orgType?: string;

}

@ObjectType()
@Directive('@shareable')
export class PaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class UsersResponse {
  @Field(() => [GraphQLJSON])
  users: any[];

  @Field(() => PaginationMeta)
  pagination: PaginationMeta;
}

@InputType()
export class UpdateClientInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  email?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  main_client?: string;
}

// ===== ENHANCED SEARCH, FILTER, AND SORT DTOs =====

@InputType()
export class ClientSortInput {
  @Field()
  @IsString()
  field: string;

  @Field({ defaultValue: true })
  @IsBoolean()
  ascending: boolean;
}

@InputType()
export class FieldFilter {
  @Field()
  @IsString()
  path: string;

  @Field(() => GraphQLJSON)
  value: any;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  operator?: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'regex' | 'exists';

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  label?: string;
}

@InputType()
export class DateRangeFilter {
  @Field({ nullable: true })
  @IsOptional()
  from?: Date;

  @Field({ nullable: true })
  @IsOptional()
  to?: Date;
}

@InputType()
export class ClientFilterInput {
  @Field(() => UserType, { nullable: true })
  @IsOptional()
  @IsEnum(UserType)
  type?: UserType;

  @Field(() => ClientStatus, { nullable: true })
  @IsOptional()
  @IsEnum(ClientStatus)
  status?: ClientStatus;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  main_client?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  created_by?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  is2FAenabled?: boolean;

  @Field(() => [FieldFilter], { nullable: true })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FieldFilter)
  fieldFilters?: FieldFilter[];

  @Field(() => DateRangeFilter, { nullable: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeFilter)
  createdAt?: DateRangeFilter;

  @Field(() => DateRangeFilter, { nullable: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeFilter)
  updatedAt?: DateRangeFilter;
}

@ArgsType()
export class FindAllClientsArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  filters?: string; // JSON string of filters

  @Field(() => ClientSortInput, { nullable: true })
  @IsOptional()
  @ValidateNested()
  @Type(() => ClientSortInput)
  sort?: ClientSortInput;
}

@ObjectType()
export class ClientPaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;
}

@ObjectType()
export class ClientsResponse {
  @Field(() => [Organisation])
  clients: Organisation[];

  @Field(() => ClientPaginationMeta)
  pagination: ClientPaginationMeta;
}