import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TaskQueue } from '../entities/task.entity';
import { GcsService } from './gcs.service';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class TaskExpirationService {
  constructor(
    @InjectModel(TaskQueue.name) private taskModel: Model<TaskQueue>,
    private readonly gcsService: GcsService,
  ) {}

  /**
   * Check for expired tasks and clean them up
   * Runs daily at 6:00 AM
   */
  @Cron('0 6 * * *') // Daily at 6:00 AM
  async checkExpiredTasks() {
    console.log('🌅 Starting daily expired task cleanup...');
    await this.runExpiredTaskCleanup();
  }

  /**
   * Manual trigger for expired task cleanup
   * Can be called directly or via API endpoint
   */
  async runExpiredTaskCleanup() {
    try {
      const now = new Date();

      // Find tasks with COMPLETED, FAILED, or PARTIAL_COMPLETED status that have expired URLs
      const expiredTasks = await this.taskModel.find({
        status: { $in: ['COMPLETED', 'FAILED', 'PARTIAL_COMPLETED'] },
        $or: [
          { 'expires.importUrl': { $lte: now } },
          { 'expires.backupUrl': { $lte: now } },
          { 'expires.exportUrl': { $lte: now } },
          { 'expires.auditUrl': { $lte: now } }
        ]
      }).lean();

      console.log(`📋 Found ${expiredTasks.length} tasks with expired URLs`);

      for (const task of expiredTasks) {
        await this.processExpiredTask(task);
      }

      console.log('✅ Expired task cleanup completed');
      return {
        success: true,
        processedTasks: expiredTasks.length,
        message: `Successfully processed ${expiredTasks.length} expired tasks`
      };
    } catch (error) {
      console.error('❌ Failed to check expired tasks:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to process expired tasks'
      };
    }
  }

  /**
   * Process a single expired task
   */
  private async processExpiredTask(task: any) {
    console.log(`🔄 Processing expired task: ${task.taskId}`);
    
    const now = new Date();
    const urlsToDelete: string[] = [];
    const expiredFields: string[] = [];

    try {
      // Check which URLs have expired and collect them for deletion
      if (task.expires?.importUrl && new Date(task.expires.importUrl) <= now) {
        if (task.downloadUrl) {
          urlsToDelete.push(task.downloadUrl);
          expiredFields.push('downloadUrl');
        }
      }

      if (task.expires?.backupUrl && new Date(task.expires.backupUrl) <= now) {
        if (task.backupUrl) {
          urlsToDelete.push(task.backupUrl);
          expiredFields.push('backupUrl');
        }
        // Handle backup URLs in affected object
        if (task.affected && typeof task.affected === 'object') {
          Object.keys(task.affected).forEach(key => {
            if (key.endsWith('BackupUrl') && task.affected[key]) {
              urlsToDelete.push(task.affected[key]);
              expiredFields.push(`affected.${key}`);
            }
          });
        }
        // Handle backupUrls array
        if (task.backupUrls && Array.isArray(task.backupUrls)) {
          urlsToDelete.push(...task.backupUrls);
          expiredFields.push('backupUrls');
        }
      }

      if (task.expires?.exportUrl && new Date(task.expires.exportUrl) <= now) {
        if (task.downloadUrl && task.type === 'export') {
          urlsToDelete.push(task.downloadUrl);
          expiredFields.push('downloadUrl');
        }
      }

      if (task.expires?.auditUrl && new Date(task.expires.auditUrl) <= now) {
        if (task.auditLogUrl) {
          urlsToDelete.push(task.auditLogUrl);
          expiredFields.push('auditLogUrl');
        }
      }

      // Delete URLs from GCS bucket
      if (urlsToDelete.length > 0) {
        console.log(`🗑️ Deleting ${urlsToDelete.length} expired URLs for task ${task.taskId}`);
        await this.deleteUrlsFromBucket(urlsToDelete);
      }

      // Update task status and clear expired URLs
      const updateFields: any = {
        status: 'EXPIRED',
        $push: {
          statusLog: {
            status: 'EXPIRED',
            message: `Task expired and URLs deleted from bucket. Cleaned up: ${expiredFields.join(', ')}`,
            timestamp: new Date(),
          },
        },
      };

      // Clear expired URL fields
      if (expiredFields.includes('downloadUrl')) {
        updateFields.downloadUrl = null;
      }
      if (expiredFields.includes('backupUrl')) {
        updateFields.backupUrl = null;
      }
      if (expiredFields.includes('auditLogUrl')) {
        updateFields.auditLogUrl = null;
      }
      if (expiredFields.includes('backupUrls')) {
        updateFields.backupUrls = [];
      }
      if (expiredFields.some(field => field.startsWith('affected.'))) {
        // Clear affected backup URLs
        const clearedAffected = { ...task.affected };
        Object.keys(clearedAffected).forEach(key => {
          if (key.endsWith('BackupUrl')) {
            clearedAffected[key] = null;
          }
        });
        updateFields.affected = clearedAffected;
      }

      await this.taskModel.updateOne(
        { taskId: task.taskId },
        updateFields
      );

      console.log(`✅ Task ${task.taskId} marked as EXPIRED and URLs cleaned up`);
    } catch (error) {
      console.error(`❌ Failed to process expired task ${task.taskId}:`, error);
    }
  }

  /**
   * Delete URLs from GCS bucket
   */
  private async deleteUrlsFromBucket(urls: string[]) {
    const deletePromises = urls.map(async (url) => {
      try {
        if (url && typeof url === 'string') {
          await this.gcsService.deleteFile(url);
          console.log(`🗑️ Deleted file: ${url}`);
        }
      } catch (error) {
        console.error(`❌ Failed to delete file ${url}:`, error);
        // Don't throw here - continue with other deletions
      }
    });

    await Promise.allSettled(deletePromises);
  }
}
