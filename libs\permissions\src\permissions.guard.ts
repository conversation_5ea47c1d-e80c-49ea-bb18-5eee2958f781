import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Optional,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import * as jwt from 'jsonwebtoken';
import {
  RequiredPermission,
  REQUIRE_PERMISSION_KEY,
} from './permissions.decorator';
import { RedisCacheService } from './cache/redis-cache.service';
import { CachedUserPermission } from './cache/cache-types';
import { MongoConnectionService } from '@app/db';
 
export type GetAssignmentFn = (
  userId: string,
  orgId: string,
  subOrgId: string,
  processId: string
) => Promise<any>;
 
export type GetRoleFn = (roleId: string) => Promise<any>;
 
function normalizePermission(p: RequiredPermission): RequiredPermission {
  return {
    module: p.module,
    subModule: p.subModule,
    permission: p.permission,
  };
}
 
@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly performanceThreshold = 100;
 
  constructor(
    private reflector: Reflector,
    private getAssignment: GetAssignmentFn,
    private getRole: {
      getSystemRole: GetRoleFn;
      getOrganisationRole: GetRoleFn;
    },
    private jwtSecret: string,
    @Optional() private cacheService?: RedisCacheService,
    @Optional() private mongoConnectionService?: MongoConnectionService
  ) { }
 
  private async getProcessCollection() {
    if (!this.mongoConnectionService) {
      throw new Error('MongoConnectionService is not initialized');
    }
    return this.mongoConnectionService.getCollectionByOrgId('organisation_process_mappings');
  }
 
  /**
   * Validate that the process is active for the given sub-organisation
   * Throws ForbiddenException if process is not found or isActive is false
   */
  private async validateProcessActivation(processId: string, subOrganisationId: string): Promise<void> {
    try {
      console.log(`🔍 [validateProcessActivation] Checking process activation:`, {
        processId,
        subOrganisationId,
        processIdType: typeof processId,
        subOrganisationIdType: typeof subOrganisationId,
        processIdEmpty: !processId || processId.trim() === '',
        subOrganisationIdEmpty: !subOrganisationId || subOrganisationId.trim() === ''
      });
 
      // Skip validation if either processId or subOrganisationId is empty/invalid
      if (!processId || processId.trim() === '' || !subOrganisationId || subOrganisationId.trim() === '') {
        console.log(`⚠️ [validateProcessActivation] Skipping validation - missing processId or subOrganisationId`);
        return;
      }
 
      const processCollection = await this.getProcessCollection();
 
      // Find the process mapping for this processId and subOrganisationId
      const processMapping = await processCollection.findOne({
        processId: processId,
        subOrganisationId: subOrganisationId
      });
 
      console.log(`🔍 [validateProcessActivation] Process mapping found:`, processMapping);
 
      if (!processMapping) {
        console.log(`❌ [validateProcessActivation] No process mapping found for processId: ${processId}, subOrganisationId: ${subOrganisationId}`);
        throw new ForbiddenException(`Process is not configured for this sub-organisation`);
      }
 
      if (processMapping.isActive === false) {
        console.log(`❌ [validateProcessActivation] Process is inactive:`, {
          processId,
          subOrganisationId,
          processName: processMapping.processName,
          isActive: processMapping.isActive
        });
        throw new ForbiddenException(`Process "${processMapping.processName || processId}" is currently disabled for this sub-organisation`);
      }
 
      console.log(`✅ [validateProcessActivation] Process validation passed:`, {
        processName: processMapping.processName,
        isActive: processMapping.isActive
      });
 
    } catch (error) {
      console.error(`❌ [validateProcessActivation] Process validation error:`, error.message);
      if (error instanceof ForbiddenException) {
        throw error;
      }
      // For other errors (database connectivity, etc.), log but don't block access
      console.warn(`⚠️ [validateProcessActivation] Skipping process validation due to error: ${error.message}`);
    }
  }
 
  async canActivate(context: ExecutionContext): Promise<boolean> {
    console.log('🚀 [canActivate] Starting permission check');

    const requiredPermissions = this.reflector.getAllAndOverride<RequiredPermission[]>(
      REQUIRE_PERMISSION_KEY,
      [context.getHandler(), context.getClass()]
    );
    console.log('🔍 [canActivate] Required permissions:', requiredPermissions);

    const ctx = GqlExecutionContext.create(context);
    const gqlContext = ctx.getContext();
    const request = gqlContext.req || gqlContext.request;
    console.log('🔍 [canActivate] Request context created');

    const token = (request?.headers?.authorization || '').replace('Bearer ', '');
    console.log('🔍 [canActivate] Token extracted:', token ? 'Present' : 'Missing');

    if (!token && (!requiredPermissions || requiredPermissions.length === 0)) {
      console.log('✅ [canActivate] No token and no required permissions - allowing access');
      return true;
    }

    console.log('🔍 [canActivate] Verifying token...');
    const decoded = this.verifyToken(token);
    console.log('✅ [canActivate] Token verified successfully:', { userId: decoded.userId, role: decoded.role });

    console.log('🔍 [canActivate] Building permission context...');
    const contextData = this.buildPermissionContext(decoded, request);
    console.log('✅ [canActivate] Permission context built');

    if (!requiredPermissions || requiredPermissions.length === 0) {
      console.log('✅ [canActivate] No required permissions - setting role and allowing access');
      this.setRoleInResponse(decoded.role || '', gqlContext, request, contextData.subOrgId);
      return true;
    }

    console.log('🔍 [canActivate] Checking permissions with cache...');
    const hasPermission = await this.checkPermissionsWithCache(
      requiredPermissions,
      contextData,
      gqlContext,
      request
    );
    console.log(`🎯 [canActivate] Final permission result: ${hasPermission}`);
    return hasPermission;
  }
 
  private verifyToken(token: string): { userId: string; role?: string } {
    console.log('🔍 [verifyToken] Attempting to verify token');
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as { userId: string; role?: string };
      console.log('✅ [verifyToken] Token verification successful:', {
        userId: decoded.userId,
        role: decoded.role,
        hasUserId: !!decoded.userId,
        hasRole: !!decoded.role
      });
      return decoded;
    } catch (error) {
      console.log('❌ [verifyToken] Token verification failed:', error.message);
      throw new ForbiddenException('Invalid or expired token');
    }
  }
 
  private buildPermissionContext(decoded: any, request: any) {
    // Extract subOrgId from multiple sources with priority order
    const headerSubOrgId = request?.headers?.suborgid || request?.headers?.subOrgId;
    const tokenSubOrgId = decoded.subOrgId || decoded.suborgId;
    const finalSubOrgId = headerSubOrgId || tokenSubOrgId || '';
    const context = {
      userId: decoded.userId,
      orgId: request?.headers?.orgid || request?.headers?.orgId || decoded.organisationId,
      subOrgId: finalSubOrgId,
      processId: request?.headers?.processid || request?.headers?.processId,
      roleIdFromToken: decoded.role,
      roleIdFromHeader: request?.headers?.roleid || request?.headers?.roleId,
    };
 
    console.log(`🔍 [buildPermissionContext] Token decoded:`, {
      userId: decoded.userId,
      role: decoded.role,
      orgId: decoded.orgId,
      organisationId: decoded.organisationId,
      subOrgId: decoded.subOrgId,
      suborgId: decoded.suborgId,
      tokenSubOrgId: tokenSubOrgId
    });
 
    console.log(`🔍 [buildPermissionContext] Headers:`, {
      orgid: request?.headers?.orgid,
      orgId: request?.headers?.orgId,
      suborgid: request?.headers?.suborgid,
      subOrgId: request?.headers?.subOrgId,
      processid: request?.headers?.processid,
      processId: request?.headers?.processId,
      headerSubOrgId: headerSubOrgId
    });
 
    console.log(`🔍 [buildPermissionContext] Final context:`, context);
 
    return context;
  }
 
  private async checkPermissionsWithCache(
    requiredPermissions: RequiredPermission[],
    context: any,
    gqlContext: any,
    request: any
  ): Promise<boolean> {
    const { userId, orgId, subOrgId, processId } = context;
 
    console.log(`🔍 [checkPermissionsWithCache] Context:`, {
      userId,
      orgId,
      subOrgId,
      processId,
      hasProcessId: !!processId,
      hasSubOrgId: !!subOrgId,
      processIdEmpty: !processId || processId.trim() === '',
      subOrgIdEmpty: !subOrgId || subOrgId.trim() === ''
    });
 
    // IMPORTANT: Check process activation BEFORE using cache
    // Even if we have cached permissions, the process might have been disabled since cache was created
    // Only for SYSTEM ROLES (when orgId, subOrgId, and processId are ALL present and non-empty)
    if (orgId && orgId.trim() !== '' && processId && processId.trim() !== '' && subOrgId && subOrgId.trim() !== '') {
      console.log(`🔍 [checkPermissionsWithCache] Running process validation for SYSTEM ROLE (orgId + subOrgId + processId)`);
      
    } else {
      console.log(`🔍 [checkPermissionsWithCache] Skipping process validation - ORGANISATION ROLE or missing context`, {
        hasOrgId: !!orgId,
        hasSubOrgId: !!subOrgId,
        hasProcessId: !!processId
      });
    }
 
    try {
      if (this.cacheService && orgId && subOrgId && processId) {
        console.log('🔍 [checkPermissionsWithCache] Checking SYSTEM ROLE cache (with processId)');
        console.log('🔍 [checkPermissionsWithCache] Running process validation before cache check...');
        await this.validateProcessActivation(context.processId, context.subOrgId);
        console.log('✅ [checkPermissionsWithCache] Process validation passed, checking cache...');

        const cached = await this.cacheService.getUserPermission(userId, orgId, subOrgId, processId);
        console.log('🔍 [checkPermissionsWithCache] System role cache result:', cached ? 'Found' : 'Not found');

        if (cached && this.checkCachedPermissions(requiredPermissions, cached.permissions)) {
          console.log('✅ [checkPermissionsWithCache] System role cache hit - permissions granted');
          this.setRoleInResponse(cached.roleName, gqlContext, request, cached.subOrgId);
          return true;
        } else if (cached) {
          console.log('❌ [checkPermissionsWithCache] System role cache hit but permissions denied');
        }
      } else if (this.cacheService && orgId) {
        console.log("🔍 [checkPermissionsWithCache] Checking ORGANISATION ROLE cache (orgId only)", {orgId, userId});

        const cached = await this.cacheService.getOrgUserPermission(userId, orgId);
        console.log("🔍 [checkPermissionsWithCache] Organisation role cache result:", cached ? 'Found' : 'Not found');

        if (cached) {
          console.log('✅ [checkPermissionsWithCache] Organisation role cache found:', {
            roleKey: cached.roleKey,
            roleName: cached.roleName,
            roleCategory: cached.roleCategory,
            isActive: cached.isActive,
            permissionsCount: cached.permissions?.length || 0
          });

          if (this.checkCachedPermissions(requiredPermissions, cached.permissions)) {
            console.log('✅ [checkPermissionsWithCache] Organisation role cache hit - permissions granted');
            this.setRoleInResponse(cached.roleName, gqlContext, request, subOrgId);
            return true;
          } else {
            console.log('❌ [checkPermissionsWithCache] Organisation role cache hit but permissions denied');
          }
        }
      } else {
        console.log('⚠️ [checkPermissionsWithCache] No cache service or insufficient context for caching');
      }
    } catch (err) {
      console.log("❌ [checkPermissionsWithCache] Cache error:", err.message);
      console.log("❌ [checkPermissionsWithCache] Error stack:", err.stack);

      if (err instanceof ForbiddenException) throw err;
    }
 
    console.log(`❌ [checkPermissionsWithCache] Cache miss - falling back to database lookup`);
    return await this.getUserPermissionFromDatabase(context, gqlContext, request, requiredPermissions);
  }
 
  private async getUserPermissionFromDatabase(
    context: any,
    gqlContext: any,
    request: any,
    requiredPermissions: RequiredPermission[]
  ): Promise<boolean> {
    console.log('🔍 [getUserPermissionFromDatabase] Starting database permission lookup');
    console.log('🔍 [getUserPermissionFromDatabase] Context received:', {
      userId: context.userId,
      orgId: context.orgId,
      subOrgId: context.subOrgId,
      processId: context.processId,
      roleIdFromToken: context.roleIdFromToken,
      isSystemRole: context.isSystemRole
    });
    console.log('🔍 [getUserPermissionFromDatabase] Required permissions:', requiredPermissions);

    let role: any;
    let roleName = '';
 
    console.log(`🔍 [getUserPermissionFromDatabase] Context:`, {
      userId: context.userId,
      orgId: context.orgId,
      subOrgId: context.subOrgId,
      processId: context.processId,
      roleIdFromToken: context.roleIdFromToken,
      hasProcessId: !!context.processId,
      hasSubOrgId: !!context.subOrgId,
      processIdEmpty: !context.processId || context.processId.trim() === '',
      subOrgIdEmpty: !context.subOrgId || context.subOrgId.trim() === ''
    });
 
    // Check process enablement for system roles (orgId, subOrgId, and processId ALL required and non-empty)
    if (context.orgId && context.orgId.trim() !== '' && context.processId && context.processId.trim() !== '' && context.subOrgId && context.subOrgId.trim() !== '') {
      console.log(`🔍 [getUserPermissionFromDatabase] Running process validation for SYSTEM ROLE (orgId + subOrgId + processId)`);
      
    } else {
      console.log(`🔍 [getUserPermissionFromDatabase] Skipping process validation - ORGANISATION ROLE or missing context`, {
        hasOrgId: !!context.orgId,
        hasSubOrgId: !!context.subOrgId,
        hasProcessId: !!context.processId
      });
    }
 
    console.log(`🔍 [getUserPermissionFromDatabase] Role context evaluation:`, {
      hasOrgId: !!context.orgId,
      hasSubOrgId: !!context.subOrgId,
      hasProcessId: !!context.processId,
      orgIdValue: context.orgId,
      subOrgIdValue: context.subOrgId,
      processIdValue: context.processId,
      systemRoleCondition: !!(context.orgId && context.subOrgId && context.processId),
      orgRoleCondition: !!(context.orgId && (!context.processId || context.processId.trim() === ''))
    });
 
    if (context.orgId && context.subOrgId && context.processId) {
      console.log(`🔍 [getUserPermissionFromDatabase] Taking SYSTEM ROLE path (with processId)`);
      console.log('🔍 [getUserPermissionFromDatabase] Running process validation...');
      await this.validateProcessActivation(context.processId, context.subOrgId);
      console.log('✅ [getUserPermissionFromDatabase] Process validation passed');

      console.log('🔍 [getUserPermissionFromDatabase] Getting role assignment...');
      const assignment = await this.getAssignment(
        context.userId,
        context.orgId,
        context.subOrgId,
        context.processId
      );
      console.log('🔍 [getUserPermissionFromDatabase] Assignment result:', assignment ? 'Found' : 'Not found');
      if (!assignment) throw new ForbiddenException('No role assignment found');

      console.log('🔍 [getUserPermissionFromDatabase] Getting system role...');
      role = await this.getRole.getSystemRole(assignment.roleId);
      console.log('🔍 [getUserPermissionFromDatabase] System role result:', role ? 'Found' : 'Not found');
      roleName = role?.key;
 
      if (role && this.cacheService) {
        const userPermission: CachedUserPermission = {
          userId: context.userId,
          orgId: context.orgId,
          subOrgId: context.subOrgId,
          processId: context.processId,
          roleId: assignment.roleId,
          roleName: roleName,
          roleKey: role.key,
          roleCategory: role.category,
          permissions: role.permissions || [],
          assignmentId: assignment._id.toString(),
          reportToUserId: assignment.reportToUserId?.toString(),
          isActive: role.isActive,
          cachedAt: Date.now(),
          expiresAt: Date.now() + 3600 * 1000,
        };
        await this.cacheService.cacheUserPermission(
          context.userId,
          context.orgId,
          context.subOrgId,
          context.processId,
          userPermission,
          3600
        );
      }
    } else if (context.orgId && (!context.processId || context.processId.trim() === '')) {
      console.log(`🔍 [getUserPermissionFromDatabase] Taking ORGANISATION ROLE path (orgId with optional subOrgId, no processId)`);
      console.log('🔍 [getUserPermissionFromDatabase] Getting organisation role with roleIdFromToken:', context.roleIdFromToken);

      role = await this.getRole.getOrganisationRole(context.roleIdFromToken);
      console.log('🔍 [getUserPermissionFromDatabase] Organisation role result:', role ? 'Found' : 'Not found');
      if (!role) {
        console.log('❌ [getUserPermissionFromDatabase] Organisation role not found for roleIdFromToken:', context.roleIdFromToken);
        throw new ForbiddenException('Org role not found');
      }

      roleName = role?.name;
      console.log('✅ [getUserPermissionFromDatabase] Organisation role found:', { roleName, roleId: role._id || role.id });
 
      if (this.cacheService) {
        await this.cacheService.setOrgUserPermission(context.userId, context.orgId, {
          roleKey: role._id.toString(),
          roleName: role.name,
          roleCategory: role.type,
          isActive: role.isActive,
          permissions: role.permissions,
        });
      }
    } else {
      console.log(`❌ [getUserPermissionFromDatabase] Missing role context - none of the conditions matched`);
      console.log(`❌ [getUserPermissionFromDatabase] Context details:`, {
        orgId: context.orgId,
        subOrgId: context.subOrgId,
        processId: context.processId,
        roleIdFromToken: context.roleIdFromToken,
        orgIdPresent: !!context.orgId,
        subOrgIdPresent: !!context.subOrgId,
        processIdPresent: !!context.processId,
        subOrgIdEmpty: !context.subOrgId || context.subOrgId.trim() === '',
        processIdEmpty: !context.processId || context.processId.trim() === '',
        systemRoleCondition: !!(context.orgId && context.subOrgId && context.processId),
        orgRoleCondition: !!(context.orgId && (!context.processId || context.processId.trim() === ''))
      });
      throw new ForbiddenException('Missing role context');
    }
 
    if (!role) {
      console.log('❌ [getUserPermissionFromDatabase] No role found after all paths');
      throw new ForbiddenException('Role not found');
    }

    console.log(`✅ [getUserPermissionFromDatabase] Role found: ${roleName} (ID: ${role._id || role.id})`);
    console.log(`🔍 [getUserPermissionFromDatabase] Role details:`, {
      roleName,
      roleId: role._id || role.id,
      roleType: role.type || role.category,
      isActive: role.isActive,
      permissionsCount: role.permissions?.length || 0
    });
    console.log(`🔍 [getUserPermissionFromDatabase] Role permissions:`, JSON.stringify(role.permissions, null, 2));

    console.log('🔍 [getUserPermissionFromDatabase] Checking required permissions against role permissions...');
    const hasPermission = requiredPermissions.some((required) => {
      const normalized = normalizePermission(required);
      const result = this.checkPermission(role.permissions || [], normalized);
      console.log(`🔍 [getUserPermissionFromDatabase] Permission check:`, {
        required: normalized,
        result: result ? 'GRANTED' : 'DENIED'
      });
      return result;
    });

    console.log(`🎯 [getUserPermissionFromDatabase] Overall permission result: ${hasPermission ? 'GRANTED' : 'DENIED'}`);

    if (!hasPermission) {
      console.log(`❌ [getUserPermissionFromDatabase] Permission denied for user ${context.userId} with role ${roleName}`);
      console.log(`❌ [getUserPermissionFromDatabase] Required permissions:`, requiredPermissions);
      console.log(`❌ [getUserPermissionFromDatabase] Available permissions:`, role.permissions);
      throw new ForbiddenException('Permission denied');
    }

    console.log('✅ [getUserPermissionFromDatabase] Setting role in response and granting access');
    this.setRoleInResponse(roleName, gqlContext, request, context.subOrgId);
    return true;
  }
 
  private checkCachedPermissions(requiredPermissions: RequiredPermission[], cachedPermissions: any[]): boolean {
    console.log('🔍 [checkCachedPermissions] Checking cached permissions');
    console.log('🔍 [checkCachedPermissions] Required permissions:', requiredPermissions);
    console.log('🔍 [checkCachedPermissions] Cached permissions count:', cachedPermissions?.length || 0);

    const result = requiredPermissions.some((required) => {
      const normalized = normalizePermission(required);
      const hasPermission = this.checkPermission(cachedPermissions, normalized);
      console.log(`🔍 [checkCachedPermissions] Permission check:`, {
        required: normalized,
        result: hasPermission ? 'GRANTED' : 'DENIED'
      });
      return hasPermission;
    });

    console.log(`🎯 [checkCachedPermissions] Overall cached permission result: ${result ? 'GRANTED' : 'DENIED'}`);
    return result;
  }
 
  private setRoleInResponse(
    roleName: string,
    gqlContext: any,
    request: any,
    subOrgId?: string
  ): void {
    console.log('🔍 [setRoleInResponse] Setting role in response');
    console.log('🔍 [setRoleInResponse] Parameters:', { roleName, subOrgId, hasGqlContext: !!gqlContext, hasRequest: !!request });

    gqlContext.roleName = roleName;
    request.headers.rolename = roleName;
    request.headers.suborgid = subOrgId || '';
    console.log('🔍 [setRoleInResponse] Set context and headers');

    if (gqlContext.res) {
      console.log('🔍 [setRoleInResponse] Setting response headers');
      gqlContext.res.setHeader('roleName', roleName);
      gqlContext.res.setHeader('suborgid', subOrgId || '');
    }
    console.log(`✅ [setRoleInResponse] Role "${roleName}" and SubOrgId "${subOrgId}" set in response`);
  }
 
  private checkPermission(permissionsJson: any[], required: RequiredPermission): boolean {
    console.log(`🔍 [checkPermission] Checking permission: ${required.module} > ${required.subModule} > ${required.permission}`);
 
    const result = permissionsJson?.some((module) => {
      const moduleMatch =
        module?.moduleName?.toLowerCase() === required.module.toLowerCase() ||
        module?.displayName?.toLowerCase() === required.module.toLowerCase();
 
      if (!moduleMatch) {
        console.log(`❌ [checkPermission] Module not found: ${required.module}`);
        return false;
      }
 
      if (!module.isEnabled) {
        console.log(`❌ [checkPermission] Module disabled: ${module.displayName || module.moduleName}`);
        return false;
      }
 
      console.log(`✅ [checkPermission] Module found and enabled: ${module.displayName || module.moduleName}`);
 
      return (module.subModules || []).some((sub: any) => {
        const subMatch =
          sub?.moduleName?.toLowerCase() === required.subModule.toLowerCase() ||
          sub?.displayName?.toLowerCase() === required.subModule.toLowerCase();
 
        if (!subMatch) return false;
 
        if (!sub.isEnabled) {
          console.log(`❌ [checkPermission] SubModule disabled: ${sub.displayName || sub.moduleName}`);
          return false;
        }
 
        console.log(`✅ [checkPermission] SubModule found and enabled: ${sub.displayName || sub.moduleName}`);
 
        if (required.permission.toLowerCase() === 'view') {
          console.log('🔍 View permission requested - allowed since submodule is enabled');
          return true;
        }
 
        const permissionResult = (sub.permissions || []).some((perm: any) => {
          const match = perm?.displayName?.toLowerCase() === required.permission.toLowerCase();
          if (match) {
            console.log(`🔍 Permission match: ${perm.displayName} - Enabled: ${perm.isEnabled}`);
            return perm?.isEnabled === true;
          }
          return false;
        });
 
        if (!permissionResult) {
          console.log(`❌ Permission "${required.permission}" not found/enabled in submodule`);
        }
 
        return permissionResult;
      });
    });
 
    console.log(`🎯 Final permission result: ${result}`);
    return result;
  }
}