import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { PayerService } from './payer.service';
import { Payer } from './entities/payer.entity';
import { FindAllPayersArgs, PayersResponse, CreatePayerInput, UpdatePayerInput } from './dto/payer.dto';
import { BaseResponse } from './dto/base.response.dto';
import * as jwt from 'jsonwebtoken';
import { RequirePermission } from '@app/permissions';

@Resolver(() => Payer)
export class PayerResolver {
  constructor(private readonly payerService: PayerService) { }

  /**
   * Fetches all payers with dynamic search, filter, and sort
   * @param args Query arguments including pagination, search, filters, and sort
   * @returns PayersResponse containing payers and pagination info
   */
  @Query(() => PayersResponse, { name: 'payers' })
  @RequirePermission(
    { module: 'Masters', subModule: 'Payer', permission: 'View' }
  )
  async getPayers(@Args() args: FindAllPayersArgs, @Context() context?: any): Promise<PayersResponse> {
        const authHeader = context.req.headers.authorization ?? '';

    const token = authHeader.replace('Bearer ', '');

    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');

    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
    
    return this.payerService.findAll(args, userId);
  }

  /**
   * Fetches a payer by ID
   * @param id The payer's ID
   * @returns BaseResponse containing the payer
   */
  @Query(() => BaseResponse, { name: 'payer' })
  @RequirePermission(
    { module: 'Masters', subModule: 'Payer', permission: 'View' }
  )
  async getPayer(@Args('id', { type: () => ID }) id: string): Promise<BaseResponse> {
    return this.payerService.findById(id);
  }

  /**
   * Creates a new payer
   * @param input Payer creation data
   * @returns BaseResponse with the created payer
   */
  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Masters', subModule: 'Payer', permission: 'Add' }
  )
  async createPayer(@Args('input') input: CreatePayerInput, @Context() context?: any): Promise<BaseResponse> {
    const authHeader = context.req.headers.authorization ?? '';

    const token = authHeader.replace('Bearer ', '');

    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');

    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.payerService.create(input, userId);
  }

  /**
   * Updates a payer
   * @param input Payer update data
   * @returns BaseResponse with the updated payer
   */
  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Masters', subModule: 'Payer', permission: 'Edit' }
  )
  async updatePayer(@Args('input') input: UpdatePayerInput): Promise<BaseResponse> {
    return this.payerService.update(input);
  }

  /**
   * Deletes a payer
   * @param id The payer's ID
   * @returns BaseResponse with success message
   */
  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Masters', subModule: 'Payer', permission: 'Delete' }
  )
  async deletePayer(@Args('id', { type: () => ID }) id: string): Promise<BaseResponse> {
    return this.payerService.delete(id);
  }
}
