import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { ProviderService } from './provider.service';
import { Provider } from './entities/provider.entity';
import {
  GetProviderInput,
  CreateProviderInput,
  UpdateProviderInput,
  PaginateProviderArgs,
  PaginatedProviderResponse
} from './dto/provider.dto';
import { BaseResponse, SuccessResponse } from './dto/base.response.dto';
import { HttpStatus, ErrorType, ResponseCode } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { Success, Error } from '@app/error';

@Resolver(() => Provider)
export class ProviderResolver {
  constructor(private readonly providerService: ProviderService) { }

  @Query(() => PaginatedProviderResponse, { name: 'providers' })
  async getProvidersWithPagination(
    @Args('input', { nullable: true }) input?: GetProviderInput,
    @Context() context?: any
  ) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters;

    // Get the authorization token from the context if available
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.providerService.findAll({
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      selectedFields,
    });
  }

  @Query(() => BaseResponse, { name: 'provider' })
  async getProviderById(@Args('id') id: string) {
    try {
      const result = await this.providerService.findById(id);
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { provider: result }
      );
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new Error(
          error.response.statusCode,
          error.response.responseCode,
          error.response.errorType,
          error.response.message
        );
      }
      return new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.UNKNOWN,
        error.message || 'An unexpected error occurred'
      );
    }
  }

  @Query(() => Number, { name: 'countProviders' })
  async countProviders() {
    return this.providerService.count();
  }

  @Query(() => [String], { name: 'providerSortFields' })
  async getProviderSortFields() {
    return this.providerService.getAvailableSortFields();
  }

  @Query(() => [String], { name: 'providerSearchFields' })
  async getProviderSearchFields() {
    return this.providerService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  async createProvider(
    @Args('input') input: CreateProviderInput,
    @Context() context: any
  ) {
    const authHeader = context.req?.headers?.authorization ?? '';
    let userId: string | undefined;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        userId = typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
      } catch (jwtError) {
        console.error('JWT verification failed for Provider creation:', jwtError);
      }
    }
    if (userId) {
      input.createdBy = userId;
    }

    const result = await this.providerService.create(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  async updateProvider(@Args('input') input: UpdateProviderInput, ) {
    const result = await this.providerService.update(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }


  @Mutation(() => Provider)
  async validateProviderToken(
    @Args('token') token: string,
    @Args('id') id: string,
  ): Promise<Provider> {
    return this.providerService.validateToken(token, id);
  }

  @Mutation(() => SuccessResponse)
  async deleteProvider(@Args('id') id: string) {
    const result = await this.providerService.delete(id);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }
} 