import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, Document } from 'mongoose';
import { Provider } from './entities/provider.entity';
import {
  CreateProviderInput,
  UpdateProviderInput,
  PaginateProviderArgs,
  PaginatedProviderResponse,
  AuthPayload
} from './dto/provider.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { instanceToPlain } from 'class-transformer';
import * as crypto from 'crypto';
import * as nodemailer from 'nodemailer';
import dayjs from 'dayjs';


type ProviderDocument = Provider & Document;

@Injectable()
export class ProviderService {
  constructor(
    @InjectModel(Provider.name)
    private providerModel: Model<ProviderDocument>,
  ) { }

  private transformDocument(doc: any): ProviderDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        'Cannot transform null document',
      );
    }

    const plainDoc = {
      _id: doc._id || new Types.ObjectId(),
      type: doc.type,
      templateId: doc.templateId || '',
      subOrganisationId: doc.subOrganisationId || '',
      values: doc.values || {},
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || '',
      updatedBy: doc.updatedBy || '',
    };

    return new this.providerModel(plainDoc) as ProviderDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { 'values.description': searchRegex },
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ templateId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ?
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } :
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }




  async findAll(input: PaginateProviderArgs): Promise<PaginatedProviderResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Get a sample document to extract dynamic fields
      const sampleDoc = await this.providerModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        projection = selectedFields;
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v', 'values', 'flattenedValues'];
        projection = allFields.reduce((acc, field) => {
          if (!excluded.includes(field)) acc[field] = 1;
          return acc;
        }, {} as Record<string, number>);

        // Search in all available fields (except excluded ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = allFields.filter(field => !excludedFromSearch.includes(field));
      }

      // Search Handling
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.providerModel.countDocuments(query);
      // === Fetch Data ===
      const providers = await this.providerModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);
      console.log("providers", providers);

      return {
        providers,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ProviderService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.PROVIDER, `Failed to fetch providers: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string): Promise<ProviderDocument> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.PROVIDER, 'Invalid ID format');
      }
      const doc = await this.providerModel.findById(id).lean().exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.PROVIDER, 'Provider not found');
      }
      return doc;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ProviderService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.PROVIDER, `Failed to find Provider: ${(err as globalThis.Error).message}`);
    }
  }
  async create(input: CreateProviderInput): Promise<Success> {
    try {
      // Validate email format BEFORE creating the provider
      const email = input?.email;
      if (email && !/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(email)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid email format'
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const providerData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
        subOrganisationId: input.subOrganisationId || '',
      };

      delete providerData.flattenedValues;

      const newProvider = new this.providerModel(providerData);
      const savedDoc = await newProvider.save();

      const token = crypto.randomBytes(32).toString('hex');
      const expiresAt = dayjs().add(2, 'day').toDate();
      if (email) {

        await this.providerModel.findByIdAndUpdate(savedDoc._id, {
          $push: {
            loginTokens: {
              token,
              expiresAt,
            },
          },
        });

        const loginUrl = `https://devrcmgenie.asprcmsolutions.com/provider-edit?token=${token}&id=${savedDoc._id}`;

        const htmlContent = `
  <p>Hello ${(savedDoc.values?.name as string) || 'Provider'},</p>
  <p>Click the link below to log in and complete your profile:</p>
  <p><a href="${loginUrl}" target="_blank" rel="noopener noreferrer">Complete Profile</a></p>
  <p><strong>Note:</strong> This link will expire in <strong>2 days</strong>.</p>
`;

        await this.sendEmail(email, 'Magic Login - Complete Profile', htmlContent);
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { provider: this.transformDocument(savedDoc) },
      );
    } catch (err) {
      console.error('Create provider failed:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to create provider: ${(err as globalThis.Error).message}`,
      );
    }
  }


  async validateToken(token: string, id: string): Promise<Provider> {
    const provider = await this.providerModel.findOne({
      _id: id,
      loginTokens: {
        $elemMatch: {
          token,
          expiresAt: { $gt: new Date() },
        },
      },
    });

    if (!provider) {
      console.log("provider seervice erorr")
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.PROVIDER,
        'Invalid or expired token.'
      );
    }

    return provider;
  }

  async update(input: UpdateProviderInput): Promise<Success> {
    try {
      const { id } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid Provider ID format for update.'
        );
      }

      const existingProvider = await this.providerModel.findById(id);
      if (!existingProvider) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider not found for update.'
        );
      }


      // Convert DTO to plain object
      const inputPlain = instanceToPlain(input) as Record<string, any>;

      // Process flattenedValues - these will be stored as key-value pairs at root level
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        // Handle object format: { key1: value1, key2: value2 }
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const updateDatas: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues, // Spread dynamic fields to root level
      };

      // Remove flattenedValues to avoid duplication
      delete updateDatas.flattenedValues;

      // Update values if provided
      if (input.values) {
        updateDatas.values = input.values;
      }
      const updatedDoc = await this.providerModel.findByIdAndUpdate(
        id,
        { $set: updateDatas },
        { new: true }
      );

      if (!updatedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Failed to update provider (doc missing after update).'
        );
      }

      if (input.token) {
        await this.providerModel.findByIdAndUpdate(id, {
          $pull: { loginTokens: { token: input.token } },
          $unset: { token: '' }, // Efficiently removes 'token' field
        });
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { provider: this.transformDocument(updatedDoc) }
      );

    } catch (err) {
      console.error('Provider update failed:', err);
      if (err instanceof Error) throw err;
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to update Provider: ${(err as any).message || 'Unknown error'}`
      );
    }
  }


  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid ID format'
        );
      }

      const deletedDoc = await this.providerModel
        .findByIdAndDelete(id)
        .lean()
        .exec();

      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { message: 'Provider deleted successfully.' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in ProviderService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to delete Provider: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.providerModel.countDocuments().exec();
    } catch (err) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to count Providers: ${(err as globalThis.Error).message}`,
      );
    }
  }

  // Dummy sendEmail implementation (replace with actual email logic as needed)
  async sendEmail(to: string, subject: string, html: string): Promise<void> {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.SMTP_USER, // your Gmail address
        pass: process.env.SMTP_PASS, // your app password
      },
    });
    try {
      await transporter.sendMail({
        from: `"ASP RCM" <${process.env.SMTP_USER}>`,
        to,
        subject: subject,
        html: html,
      });

      console.log(`Email sent successfully to ${to} via Mailtrap SMTP`);
    } catch (error) {
      console.error(`Failed to send email to ${to}:`, error.message);
      throw error;
    }
    // Simulate async email sending
    return;
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'type',
      'templateId',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy'
    ];
  }

  getSearchableFields(): string[] {
    return ['values.description'];
  }

}
