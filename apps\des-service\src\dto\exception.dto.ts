import { Field, InputType, ObjectType, ID } from '@nestjs/graphql';
import { IsString, IsObject, IsArray, IsOptional, IsBoolean } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { PaginationInfo } from './pagination.dto';

@InputType()
export class CreateExceptionInput {
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  updatedBy?: string;

  @Field(() => ID)
  @IsString()
  templateId: string;

  @Field(() => GraphQLJSON)
  @IsObject()
  @IsOptional()
  values?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsObject()
  @IsOptional()
  flattenedValues?: Record<string, any>;

  @Field(() => ID)
  @IsString()
  processId: string;

  @Field(() => String)
  @IsArray()
  exceptions: string[];
}

@InputType()
export class UpdateExceptionInput {
  @Field(() => ID)
  @IsString()
  id: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  updatedBy?: string;

  @Field(() => Boolean, { nullable: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsObject()
  @IsOptional()
  values?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsObject()
  @IsOptional()
  flattenedValues?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsArray()
  @IsOptional()
  exceptions?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  templateId?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  processId?: string;
}

@InputType()
export class FindAllExceptionsInput {
  @Field(() => GraphQLJSON, { nullable: true })
  selectedFields?: Record<string, any>;

  @Field(() => Number, { nullable: true })
  limit?: number;

  @Field(() => Number, { nullable: true })
  page?: number;

  @Field(() => String, { nullable: true })
  sortOrder?: string;

  @Field(() => String, { nullable: true })
  sortBy?: string;

  @Field(() => String, { nullable: true })
  filters?: string;

  @Field(() => String, { nullable: true })
  search?: string;
}

@InputType()
export class CountExceptionsInput {
  @Field(() => GraphQLJSON, { nullable: true })
  @IsObject()
  @IsOptional()
  filters?: Record<string, any>;
}

@ObjectType()
export class ExceptionResponse {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  createdBy: string;

  @Field(() => String)
  updatedBy: string;

  @Field(() => Boolean)
  isActive: boolean;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;

  @Field(() => ID)
  templateId: string;

  @Field(() => GraphQLJSON)
  values: Record<string, any>;

  @Field(() => GraphQLJSON)
  flattenedValues: Record<string, any>;

  @Field(() => ID)
  processId: string;

  @Field(() => String)
  exceptions: string;
}

@ObjectType()
export class PaginatedExceptionsResponse {
  @Field(() => [GraphQLJSON])
  items: any[];

  @Field(() => PaginationInfo)
  pagination: PaginationInfo;
} 