import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { ImportService } from './import.service';
import { ExportService } from './export.service';

@Processor('task-queue{identity}')
export class TaskProcessor extends WorkerHost {
  constructor(
    private readonly importService: ImportService,
    private readonly exportService: ExportService,
  ) {
    super();
  }
  async process(job: Job<any, any, string>): Promise<any> {
    console.log(`🔄 Processing job: ${job.name} with taskId: ${job.data.taskId}`);

    try {
      if (job.name === 'import') {
        await this.importService.processImportTask(job.data.taskId);
      } else if (job.name === 'export') {
        await this.exportService.processExportTask(job.data.taskId);
      } else {
        throw new Error(`Unknown job type: ${job.name}`);
      }

      console.log(`✅ Job completed: ${job.name} with taskId: ${job.data.taskId}`);
    } catch (error) {
      console.error(`❌ Job failed: ${job.name} with taskId: ${job.data.taskId}`, error);
      throw error; // Re-throw to mark job as failed
    }
  }
} 