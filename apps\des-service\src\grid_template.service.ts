import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { GridTemplate } from './entities/grid_template.entity';
import {
  FindAllGridTemplatesArgs,
  GridTemplatesResponse,
  CreateGridTemplateInput,
  UpdateGridTemplateInput
} from './dto/grid_template.dto';
import { BaseResponse } from './dto/base.response.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { hashGridFields } from '@app/functions/hash-converter';

@Injectable()
export class GridTemplateService {
  constructor(
    @InjectModel('GridTemplate') private readonly gridTemplateModel: Model<GridTemplate>,
  ) { }

  /**
   * Creates a new grid template
   * @param input Grid template creation data
   * @returns Success response with the created grid template
   */
  async create(input: CreateGridTemplateInput): Promise<BaseResponse> {
    try {
      const userId = input.user_id ? new Types.ObjectId(input.user_id) : undefined;

      // 1. Check for duplicate template name
      if (userId) {
        const existingTemplate = await this.gridTemplateModel.findOne({
          name: input.name,
          user_id: userId,
          isActive: true,
        });

        if (existingTemplate) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.FORM,
            'Grid template with this name already exists for this user.',
          );
        }

        // 2. Enforce max 5 templates per user
        const count = await this.gridTemplateModel.countDocuments({
          user_id: userId,
          isActive: true,
        });

        if (count >= 5) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.LIMIT_EXCEEDED,
            ErrorType.FORM,
            'Maximum of 5 grid templates allowed per user.'
          );
        }
      }

      // 3. Validate duplicate objects inside input.grid_fields array itself
      // const seen = new Set<string>();
      // for (const field of input.grid_fields) {
      //   const canonical = JSON.stringify(
      //     Object.keys(field).sort().reduce((acc, key) => {
      //       acc[key] = field[key];
      //       return acc;
      //     }, {} as Record<string, any>)
      //   );

      //   if (seen.has(canonical)) {
      //     throw new Error(
      //       HttpStatus.BAD_REQUEST,
      //       ResponseCode.INVALID_PARAMETERS,
      //       ErrorType.FORM,
      //       'Duplicate fields found within grid_fields array.'
      //     );
      //   }
      //   seen.add(canonical);
      // }

      // 4. Generate canonical hash for the full grid_fields array
      const gridHash = hashGridFields(input.grid_fields);

      // 5. Check for identical grid_fields using hash
      if (userId) {
        const duplicate = await this.gridTemplateModel.findOne({
          user_id: userId,
          grid_hash: gridHash,
          isActive: true,
        });

        if (duplicate) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.FORM,
            'A grid template with identical fields already exists for this user.'
          );
        }
      }

      // 6. Save document
      const gridTemplateData = {
        ...input,
        user_id: userId,
        grid_hash: gridHash,
        isActive: input.isActive !== undefined ? input.isActive : true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const newGridTemplate = new this.gridTemplateModel(gridTemplateData);
      const savedGridTemplate = await newGridTemplate.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { gridTemplate: savedGridTemplate },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to create grid template: ${error.message}`,
      );
    }
  }


  /**
   * Finds all grid templates with pagination, search, filter, and sort
   * @param args Query arguments
   * @returns Grid templates with pagination info
   */
  async findAll(args: FindAllGridTemplatesArgs, userId: string,): Promise<BaseResponse> {
    try {
      const { page = 1, limit = 10, search, filters, sort, type } = args;
      const skip = (page - 1) * limit;

      // Build query  
      const query: any = { isActive: true, user_id: new Types.ObjectId(userId) };

      if (type) {
        query.type = type;
      }

      // Apply filters
      if (filters) {
        if (filters.name) {
          query.name = { $regex: filters.name, $options: 'i' };
        }
        if (filters.description) {
          query.description = { $regex: filters.description, $options: 'i' };
        }
      }

      // Apply search
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Build sort
      const sortOptions: any = {};
      if (sort) {
        sortOptions[sort.field] = sort.ascending ? 1 : -1;
      } else {
        sortOptions.createdAt = -1; // Default sort by creation date descending
      }
      // Execute queries
      const [gridTemplates, total] = await Promise.all([
        this.gridTemplateModel
          .find(query)
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.gridTemplateModel.countDocuments(query).exec()
      ]);

      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const response: GridTemplatesResponse = {
        gridTemplates,
        pagination: {
          page,
          limit,
          total: gridTemplates.length,
          totalItems: total,
          totalPages,
          hasNext,
          hasPrev,
        },
      };

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { data: response },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to fetch grid templates: ${error.message}`,
      );
    }
  }

  /**
   * Finds a grid template by ID
   * @param id Grid template ID
   * @returns Grid template or error
   */
  async findById(id: string): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          'Invalid grid template ID format.',
        );
      }

      const gridTemplate = await this.gridTemplateModel.findOne({
        _id: new Types.ObjectId(id),
        isActive: true
      }).exec();

      if (!gridTemplate) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          'Grid template not found.',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { gridTemplate },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to fetch grid template: ${error.message}`,
      );
    }
  }

  /**
   * Updates a grid template
   * @param input Update data
   * @returns Updated grid template or error
   */
  async update(input: UpdateGridTemplateInput): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(input.id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          'Invalid grid template ID format.',
        );
      }

      const existingTemplate = await this.gridTemplateModel.findOne({
        _id: new Types.ObjectId(input.id),
        isActive: true
      }).exec();

      if (!existingTemplate) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          'Grid template not found.',
        );
      }

      // Check for duplicate name if name is being updated
      if (input.name && input.name !== existingTemplate.name) {
        const duplicateTemplate = await this.gridTemplateModel.findOne({
          name: input.name,
          user_id: existingTemplate.user_id,
          _id: { $ne: new Types.ObjectId(input.id) },
          isActive: true
        }).exec();

        if (duplicateTemplate) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.FORM,
            'Grid template with this name already exists for this user.',
          );
        }
      }

      const updateData: any = {
        ...input,
        updatedAt: new Date()
      };

      if (input.user_id) {
        updateData.user_id = new Types.ObjectId(input.user_id);
      }

      // If grid_fields is being updated, check for duplicate hash and update grid_hash
      if (input.grid_fields) {
        const gridHash = hashGridFields(input.grid_fields);
        // Check for duplicate grid_fields in other templates for this user
        const duplicate = await this.gridTemplateModel.findOne({
          user_id: existingTemplate.user_id,
          grid_hash: gridHash,
          _id: { $ne: new Types.ObjectId(input.id) },
          isActive: true,
        });
        if (duplicate) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.FORM,
            'A grid template with identical fields already exists for this user.'
          );
        }
        updateData.grid_hash = gridHash;
      }

      delete updateData.id; // Remove id from update data

      const updatedTemplate = await this.gridTemplateModel.findByIdAndUpdate(
        new Types.ObjectId(input.id),
        updateData,
        { new: true, runValidators: true }
      ).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { gridTemplate: updatedTemplate },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to update grid template: ${error.message}`,
      );
    }
  }

  /**
   * Soft deletes a grid template
   * @param id Grid template ID
   * @returns Success response
   */
  async delete(id: string): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.FORM,
          'Invalid grid template ID format.',
        );
      }

      const gridTemplate = await this.gridTemplateModel.findOne({
        _id: new Types.ObjectId(id),
        isActive: true
      }).exec();

      if (!gridTemplate) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.FORM,
          'Grid template not found.',
        );
      }

      await this.gridTemplateModel.findByIdAndUpdate(
        new Types.ObjectId(id),
        { isActive: false, updatedAt: new Date() }
      ).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { message: 'Grid template deleted successfully.' },
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.FORM,
        `Failed to delete grid template: ${error.message}`,
      );
    }
  }
}
