import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { IdentityService } from './identity.service';
import {
  CreateUserInput,
  OtpRequestInput,
  OtpVerifyInput,
  TotpVerifyInput,
  TwoFAInput,
  GetUsersInput,
  UsersResponse,
  DeleteClientInput,
  GetClientsByMainClientInput,
  UpdateClientInput,
  FindAllClientsArgs,
  ClientsResponse,
} from './dto/client.dto';
import { OtpVerificationResult } from './interfaces/identity.interfaces';
import { ValidateTokenResponse } from './dto/ValidateTokenResponse.do';
import { Request } from 'express';
import { BaseResponse } from './dto/base.response.dto';
import { Organisation } from './entities/client.entities';
import * as jwt from 'jsonwebtoken';
import { RequirePermission, RequireConditionalPermission, createConditionalPermission, ConditionalPermissionsGuard } from '@app/permissions';
import { UseGuards } from '@nestjs/common';


@Resolver(() => Organisation)
export class IdentityResolver {
  constructor(private readonly identityService: IdentityService) { }

  /**
   * Fetches all users from the system.
   * @returns A promise that resolves to an array of User entities.
   */
  @Query(() => [Organisation], { name: 'users' })
  async getUsers() {
    return this.identityService.findAll();
  }

  /**
   * Fetches all users from the system with dynamic search, filter, and sort functionality.
   * @param input - Contains search, filter, sort, and pagination parameters.
   * @returns A promise that resolves to a UsersResponse with users and pagination info.
   */

  
  @Query(() => UsersResponse, { name: 'getUsersWithPagination' })
    @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.orgType === 'MAIN_CLIENT',
      'Organizations',
      'Main Organization',
      'View'
    ),
    createConditionalPermission(
      (input) => input.orgType === 'SUB_CLIENT',
      'Organizations',
      'Sub Organization',
      'View'
    )
  )
  //  @RequirePermission(
  //   { module: 'organization', subModule: 'Organization Info', permission: 'View' },
  //   { module: 'organization', subModule: 'Sub Organization list', permission: 'View' }
  // )
  async getUsersWithPagination(@Args('input', { nullable: true }) input?: GetUsersInput, @Context() context?: any, @Args('orgId', { nullable: true }) orgId?: string,) {

    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const type = input?.type;

    // Get the authorization token from the context if available
   const authHeader = context.req.headers.authorization ?? '';

   const token = authHeader.replace('Bearer ', '');

   const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');

    const userId =  typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    // Parse filters - input.filters is already an object based on DTO definition
    const filters: Record<string, any> = input?.filters || {};

    return this.identityService.findAllUsers(
      search,
      selectedFields,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      userId,
      orgId,
      type
    );
  }

 @Query(() => UsersResponse, { name: 'getOrganisationsWithPagination' })
  //   @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   createConditionalPermission(
  //     (input) => input.orgType === 'MAIN_CLIENT',
  //     'Organizations',
  //     'Main Organization',
  //     'View'
  //   ),
  //   createConditionalPermission(
  //     (input) => input.orgType === 'SUB_CLIENT',
  //     'Organizations',
  //     'Sub Organization',
  //     'View'
  //   )
  // )
  
  async getOrganisationsWithPagination(@Args('input', { nullable: true }) input?: GetUsersInput, @Context() context?: any, @Args('orgId', { nullable: true }) orgId?: string,) {

    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const type = input?.type;

    // Get the authorization token from the context if available
   const authHeader = context.req.headers.authorization ?? '';

   const token = authHeader.replace('Bearer ', '');

   const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');

    const userId =  typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    // Parse filters - input.filters is already an object based on DTO definition
    const filters: Record<string, any> = input?.filters || {};

    return this.identityService.findAllUsers(
      search,
      selectedFields,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      userId,
      orgId,
      type
    );
  }

  /**
   * Creates a new user in the system.
   * @param input - Data required to create a new user.
   * @returns A BaseResponse indicating success or failure of user creation.
   */
  @Mutation(() => BaseResponse)
  /**
   * Creates a new user with conditional permission checking based on user type
   * - MAIN_CLIENT requires 'Organizations > Main Organization > Add' permission
   * - SUB_CLIENT requires 'Organizations > Sub Organization > Add' permission
   */
  @Mutation(() => BaseResponse)
  @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_CLIENT',
      'Organizations',
      'Main Organization',
      'Add'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_CLIENT',
      'Organizations',
      'Sub Organization',
      'Add'
    )
  )
 
  async createUser(@Args('input') input: CreateUserInput) {
    const result = await this.identityService.createUser(input);
    return result;
  }

  /**
   * Deletes a client from the system.
   * @param input - Contains the client ID to delete.
   * @returns A BaseResponse indicating success or failure of client deletion.
   */
  @Mutation(() => BaseResponse)
 @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_CLIENT',
      'Organizations',
      'Main Organization',
      'Delete'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_CLIENT',
      'Organizations',
      'Sub Organization',
      'Delete'
    )
  )
  async deleteClient(@Args('input') input: DeleteClientInput) {
    const result = await this.identityService.deleteClient(input.id);
    return result;
  }

  /**
   * Initiates a login request by sending an OTP to the provided email.
   * @param input - Contains the email to send the OTP to.
   * @returns A BaseResponse indicating success or failure of sending the OTP.
   */
  @Mutation(() => BaseResponse)
  async loginRequest(@Args('input') input: OtpRequestInput) {
   const user = await this.identityService.getUserByEmail(input.email);
    if (!user) {
      throw new Error('You are not registered in this application. Please contact your system administrator for access.');
    }

    const organisation = await this.identityService.getOrganisationById(user.organisationId);
    console.log("organisation", organisation);
    
    if (!organisation?.accessPortal) {
      throw new Error('Portal access is not enabled for your organization');
    }

    const result = await this.identityService.sendOtp(input.email);
    return result;
  }

  /**
   * Verifies the OTP submitted by the user.
   * @param input - Contains the email and OTP to verify.
   * @returns An OtpVerificationResult indicating whether verification succeeded.
   */
  @Mutation(() => BaseResponse)
  async otpVerify(@Args('input') input: OtpVerifyInput) {
    const result: OtpVerificationResult = await this.identityService.verifyOtp(
      input.email,
      input.otp,
    );
    return result;
  }

  /**
   * Verifies the Time-based One-Time Password (TOTP) for 2FA.
   * @param input - Contains email, OTP, and a flag to optionally skip 2FA check.
   * @returns A BaseResponse indicating verification success or failure.
   */
  @Mutation(() => BaseResponse)
  async totpVerify(@Args('input') input: TotpVerifyInput) {
    const data = await this.identityService.verifyTotp(input.email, input.otp, input.skip2FACheck);
    return data;
  }

  /**
   * Resends the OTP to the user's email.
   * @param email - The email address to resend the OTP to.
   * @returns A BaseResponse indicating success or failure of the resend operation.
   */
  @Mutation(() => BaseResponse)
  async resendOtp(@Args('email') email: string) {
    const result = await this.identityService.resendOtp(email);
    return result;
  }

  /**
   * Validates a Microsoft-issued JWT token from the Authorization header.
   * Extracts the user's email from the token payload and checks if the user exists.
   * @param context - GraphQL context providing access to the HTTP request.
   * @returns A ValidateTokenResponse with email and existence flag.
   */

  @Mutation(() => ValidateTokenResponse)
  async validateMicrosoftToken(
    @Context() context: { req: Request },
  ): Promise<ValidateTokenResponse> {
    const authHeader = context.req?.headers?.authorization;
    const token = authHeader?.replace('Bearer ', '') ?? '';

    const result = await this.identityService.validateMicrosoftToken(token);
    return result;
  }


  /**
   * Requests a reset of the user's 2FA (Two-Factor Authentication).
   * @param email - The user's email requesting 2FA reset.
   * @returns A BaseResponse indicating success or failure of the request.
   */
  @Mutation(() => BaseResponse)
  async request2FAReset(@Args('email') email: string) {
    const result = await this.identityService.request2FAReset(email);
    return result;
  }

  /**
   * Allows an administrator to reset a user's 2FA after validating the admin's JWT token.
   * @param email - The email of the user whose 2FA is to be reset.
   * @param context - GraphQL context to access the admin's request headers.
   * @returns A string message indicating success or failure of the operation.
   */
  @Mutation(() => BaseResponse)
  async adminReset2FA(
    @Args('email', { type: () => String }) email: string,
    @Context() context: { req: Request },
  ) {
    const authHeader = context.req.headers.authorization ?? '';

    if (!authHeader) {
      return 'Authorization header missing';
    }

    const token = authHeader.replace('Bearer ', '');

    if (!token) {
      return 'Token missing';
    }

    const result = await this.identityService.adminReset2FA(email, token);
    return result.message;
  }
  @Mutation(() => BaseResponse)
  async logout(
    @Context() context: any,) {
    const authHeader = context.req.headers.authorization ?? '';

    if (!authHeader) {
      return 'Authorization header missing';
    }

    const token = authHeader.replace('Bearer ', '');
    const result = await this.identityService.logout(token);
    return result;
  }


  @Mutation(() => BaseResponse)
  async disable2FA(
    @Args('input') input: TwoFAInput,
  ) {
    const result = await this.identityService.disable2FA(input);

    return result;
  }


  @Mutation(() => BaseResponse)
  async getUser(
    @Context() context: any,) {
    const authHeader = context.req.headers.authorization ?? '';
    if (!authHeader) {
      return 'Authorization header missing';
    }

    const token = authHeader.replace('Bearer ', '');
    const result = await this.identityService.getUser(token);
    return result;
  }

  @Mutation(() => BaseResponse)
  async getByClient(
    @Args('clientId') clientId: string,
  ) {
    const result = await this.identityService.getByClientId(clientId);
    return result;
  }

  @Mutation(() => BaseResponse)
  async getByClients(
    @Args('input') input: GetClientsByMainClientInput,
  ) {
    const { mainClientId, search, filters, sortBy, sortOrder = 'asc', page = 1, limit = 10 } = input;

    // Parse filters from JSON string if provided
    let parsedFilters: Record<string, any> = {};
    if (filters) {
      try {
        parsedFilters = JSON.parse(filters);
      } catch (error) {
        // If JSON parsing fails, ignore filters
        parsedFilters = {};
      }
    }

    const result = await this.identityService.getByClients(
      mainClientId,
      search,
      parsedFilters,
      sortBy,
      sortOrder,
      page,
      limit
    );
    return result;
  }

  /**
   * Updates an existing client in the system.
   * @param input - Data required to update the client.
   * @returns A BaseResponse indicating success or failure of client update.
   */
  @Mutation(() => BaseResponse)
   @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_CLIENT',
      'Organizations',
      'Main Organization',
      'Update'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_CLIENT',
      'Organizations',
      'Sub Organization',
      'Update'
    )
  )
  //  @RequirePermission(
  //   { module: 'organization', subModule: 'Organization Info', permission: 'Edit' }
  // )
  async updateClient(@Args('input') input: UpdateClientInput) {
    const result = await this.identityService.updateClient(input.id, input);
    return result;
  }


  @Mutation(() => BaseResponse)
 @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_CLIENT',
      'organization',
      'Organization Info',
      'Edit'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_CLIENT',
      'Organizations',
      'Organization Info',
      'Edit'
    )
  )
  //  @RequirePermission(
  //   { module: 'organization', subModule: 'Organization Info', permission: 'Edit' }
  // )
  async updateClientPortal(@Args('input') input: UpdateClientInput) {
    const result = await this.identityService.updateClient(input.id, input);
    return result;
  }
}
