import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ProviderTicket, Counter } from '../entities/provider.entity';

import {
  CreateProviderTicketInput,
  UpdateProviderTicketInput,
} from '../dto/provider_ticket.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { MongoConnectionService } from '@app/db';
import { instanceToPlain } from 'class-transformer';
import { ObjectId, UpdateFilter } from 'mongodb';
import { EmailService, KafkaService } from '@app/email';
import moment from 'moment';
import { NotificationService, NotificationType, NotificationPriority, NotificationChannel, NotificationPayload, } from '@app/notification';

@Injectable()
export class ProviderService {
  constructor(
    private readonly mongoConnectionService: MongoConnectionService,
    @InjectModel('Counter') private counterModel: Model<Counter>,
    private readonly notificationService: NotificationService,
    private readonly emailService: EmailService,
  ) { }



  /**
 * Get user collection based on orgId, fallback to master DB
 */
  private async getProviderCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('tickets', orgId);
  }

  /**
* Get user collection based on orgId, fallback to master DB
*/
  private async getProviderEmailTicketCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId(
      'email_tickets',
      orgId
    );
  }

  private async getUserEmail(userId: string): Promise<string | undefined> {
    const userCollection = await this.mongoConnectionService.getCollectionByOrgId('users');
    const user = await userCollection.findOne({ _id: new ObjectId(userId) });

    if (user?.email) return user.email;

    const orgUserCollection = await this.mongoConnectionService.getCollectionByOrgId('organisation_users');
    const orgUser = await orgUserCollection.findOne({ _id: new ObjectId(userId) });

    return orgUser?.email;
  }


  private async getDeviceTokenForUser(userId: string): Promise<string | null> {
    try {
      const collection = await this.mongoConnectionService.getCollectionByOrgId(
        'user_notification_tokens',
      );

      const tokenRecord = await collection.findOne({ userId });

      if (!tokenRecord || !tokenRecord.token) {
        return null;
      }

      return tokenRecord.token;
    } catch (err) {
      console.error(`Error retrieving device token for user ${userId}:`, err);
      return null;
    }
  }

  private async getorganisationsCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('organisations');
  }

  private async getProvidersCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('providers');
  }

  private async getPayorsCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('payers');
  }


  private async getorganisationuserCollection() {
    return this.mongoConnectionService.getCollectionByOrgId(
      'organisation_users',
    );
  }

  private async getuserCollection() {
    return this.mongoConnectionService.getCollectionByOrgId(
      'users',
    );
  }
  /**
   * Retrieves all provider tickets from the database with pagination, filtering, and sorting
   * @param options Query options for pagination, filtering, and sorting
   * @returns Promise resolving to paginated results
   */
  async findAll(
    search?: string,
    selectedFields?: Record<string, any>,
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page = 1,
    limit = 10,
    userId?: string,
    orgId?: string,
    roleName?: string,
  ): Promise<{
    provider: any[];
    pagination: {
      page: number;
      limit: number;
      total?: number;
      totalItems: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const userCollection = await this.getProviderCollection(orgId);
    const query: any = {};

    if (roleName && ['agent', 'qc-agent'].includes(roleName)) {
      query.assignedTo = userId;
    }

    const sampleDoc = await userCollection.findOne();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];
    const excludedFields = ['__v', '_id', 'values', 'flattenedValues', 'source_ticket_id', 'assign'];

    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = { ...selectedFields, assign: 1, createdby: 1, assignedTo: 1, allocated_type: 1 };
      searchableFields = Object.keys(selectedFields).filter(
        key => selectedFields[key] === 1 && !excludedFields.includes(key),
      );
    } else {
      projection = allFields.reduce((acc, key) => {
        if (!excludedFields.includes(key)) acc[key] = 1;
        return acc;
      }, {} as Record<string, number>);
      projection['assign'] = 1;
      projection['createdby'] = 1;
      projection['assignedTo'] = 1;
      projection['allocated_type'] = 1;
      searchableFields = allFields.filter(field => !excludedFields.includes(field));
    }

    // === Search Handling ===
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i',
      };

      query.$or = searchableFields.map(field => ({ [field]: regex }));

      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: new ObjectId(searchTerm) });
      }

      if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
        try {
          const searchDate = new Date(searchTerm);
          if (!isNaN(searchDate.getTime())) {
            const startOfDay = new Date(searchDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(searchDate);
            endOfDay.setHours(23, 59, 59, 999);
            query.$or.push(
              { createdAt: { $gte: startOfDay, $lte: endOfDay } },
              { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
            );
          }
        } catch (err) {
          // Ignore invalid date formats
        }
      }
    }

    // === Filters Handling ===
    console.log('🔍 [DEBUG] Raw filters input:', filters);
    console.log('🔍 [DEBUG] Filters type:', typeof filters);

    let parsedFilters: Record<string, any> = {};
    const typeMapping: Record<string, number> = {
      ticket: 2,
      email: 1,
      import: 3,
    };

    if (filters) {
      try {
        const rawParsed = typeof filters === 'string' ? JSON.parse(filters) : filters;
        parsedFilters = rawParsed?.filters || rawParsed;
        console.log('🔍 [DEBUG] Parsed filters:', JSON.stringify(parsedFilters, null, 2));

        // Normalize duplicate keys like 'receiveddate' -> 'received_date'
        if (parsedFilters.receiveddate && !parsedFilters.received_date) {
          parsedFilters.received_date = parsedFilters.receiveddate;
          delete parsedFilters.receiveddate;
        }

        const dateKeys = ['updatedAt', 'createdAt', 'follow_up_date', 'received_date', 'worked_date'];
        console.log('🔍 [DEBUG] Date keys array:', dateKeys);

        for (const [key, value] of Object.entries(parsedFilters)) {
          console.log(`🔍 [DEBUG] Processing filter: ${key} = "${value}" (type: ${typeof value})`);

          if (value === undefined || value === '') {
            console.log(`🔍 [DEBUG] Skipping ${key}: value is undefined or empty`);
            continue;
          }

          if (key === 'type') {
            if (typeof value === 'string' && value) {
              const mappedValue = typeMapping[value.toLowerCase()];
              if (mappedValue !== undefined) {
                query.type = mappedValue;
              }
            }
            continue;
          }

          if (dateKeys.includes(key)) {
            console.log(`🔍 [DEBUG] ${key} is a date field - processing...`);

            // Check if value is already a MongoDB query object (processed by resolver)
            if (typeof value === 'object' && value !== null && (value.$gte || value.$lte)) {
              console.log(`🔍 [DEBUG] ${key} is already a MongoDB query object - using as-is`);
              query[key] = value;
              console.log(`✅ [DEBUG] Pre-processed date filter applied for ${key}:`, value);
              continue;
            }

            // Handle date range format: "23-07-2025_27-07-2025"
            if (typeof value === 'string' && value.includes('_')) {
              console.log(`🔍 [DEBUG] ${key} contains underscore - processing as date range`);

              const [fromDateStr, toDateStr] = value.split('_');
              console.log(`🔍 [DEBUG] Split dates: from="${fromDateStr}", to="${toDateStr}"`);

              if (fromDateStr && toDateStr) {
                const fromMoment = moment(fromDateStr.trim(), 'DD-MM-YYYY');
                const toMoment = moment(toDateStr.trim(), 'DD-MM-YYYY');

                console.log(`🔍 [DEBUG] From moment: ${fromMoment.format()} (valid: ${fromMoment.isValid()})`);
                console.log(`🔍 [DEBUG] To moment: ${toMoment.format()} (valid: ${toMoment.isValid()})`);

                if (fromMoment.isValid() && toMoment.isValid()) {
                  const fromDate = fromMoment.startOf('day').toDate();
                  const toDate = toMoment.endOf('day').toDate();

                  query[key] = {
                    $gte: fromDate,
                    $lte: toDate,
                  };

                  console.log(`✅ [DEBUG] Date range filter applied for ${key}:`);
                  console.log(`   $gte: ${fromDate.toISOString()}`);
                  console.log(`   $lte: ${toDate.toISOString()}`);
                  console.log(`   From: ${fromDate.toDateString()} 00:00:00`);
                  console.log(`   To:   ${toDate.toDateString()} 23:59:59`);
                } else {
                  console.log(`❌ [DEBUG] Invalid moment dates for ${key}`);
                }
              } else {
                console.log(`❌ [DEBUG] Missing fromDateStr or toDateStr for ${key}`);
              }
            } else if (typeof value === 'string') {
              console.log(`🔍 [DEBUG] ${key} is a string without underscore - processing as single date`);

              // Handle single date format: "23-07-2025"
              const momentDate = moment(value, 'DD-MM-YYYY');
              console.log(`🔍 [DEBUG] Single date moment: ${momentDate.format()} (valid: ${momentDate.isValid()})`);

              if (momentDate.isValid()) {
                query[key] = {
                  $gte: momentDate.startOf('day').toDate(),
                  $lte: momentDate.endOf('day').toDate(),
                };
                console.log(`✅ [DEBUG] Single date filter applied for ${key}`);
              } else {
                console.log(`❌ [DEBUG] Invalid single date for ${key}`);
              }
            } else {
              console.log(`❌ [DEBUG] ${key} has unexpected value type: ${typeof value}`);
            }
            continue;
          } else {
            console.log(`🔍 [DEBUG] ${key} is NOT a date field`);
          }

          query[key] = typeof value === 'string'
            ? { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' }
            : value;
        }
      } catch (err) {
        console.warn('[findAll] Invalid filters provided. Skipping filters.', err);
      }
    } else {
      console.log('🔍 [DEBUG] No filters provided');
    }

    console.log('🔍 [DEBUG] Final parsedFilters:', JSON.stringify(parsedFilters, null, 2));

    // === Sort Handling ===
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1;
    }

    // === Pagination Handling ===
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;
    console.log('🔍 [DEBUG] Final MongoDB query before execution:', JSON.stringify(query, null, 2));
    console.log('🔍 [DEBUG] Query has received_date filter:', !!query.received_date);

    const totalItems = await userCollection.countDocuments(query);
    console.log('🔍 [DEBUG] Total items found:', totalItems);


    let provider = await userCollection
      .find(query)
      .project(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .toArray();;

    const createdByIds = provider.map(p => p.createdby || p.createdBy).filter(id =>
      typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)
    );
    const assignedToIds = provider.map(p => p.assigned_to || p.assignedTo).filter(id =>
      typeof id === 'string' && /^[0-9a-fA-F]{24}$/.test(id)
    );

    const userIds = [...new Set([...createdByIds, ...assignedToIds])].map(id => new ObjectId(id));

    console.log('[findAll] Raw user IDs:', createdByIds, assignedToIds);

    const [userColl, orgUserColl] = await Promise.all([
      this.getuserCollection(),
      this.getorganisationuserCollection()
    ]);

    const [users, orgUsers] = await Promise.all([
      userColl.find({ _id: { $in: userIds } }).toArray(),
      orgUserColl.find({ _id: { $in: userIds } }).toArray()
    ]);

    const userMap = new Map<string, any>();
    [...users, ...orgUsers].forEach(u => {
      userMap.set(u._id.toString(), u);
    });

    provider = provider.map(p => ({
      ...p,
      createdByName: userMap.get((p.createdby || p.createdBy)?.toString())?.name || null,
      userAgentName: userMap.get((p.assigned_to || p.assignedTo)?.toString())?.name || null,
    }));

    const totalPages = Math.ceil(totalItems / safeLimit);
    return {
      provider,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems, // always a number, never null
        totalItems: totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }


  /**
   * Retrieves a provider ticket by its ID
   * @param id The provider ticket's ID
   * @returns Promise resolving to the ProviderTicket entity
   */
  async findById(id: string, orgId?: string, userId?: string): Promise<ProviderTicket> {
    try {
      const userCollection = await this.getProviderCollection(orgId);
      // Validate ObjectId format
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid ID format',
        );
      }

      const providerTicketDoc = await userCollection.findOne({ _id: new Types.ObjectId(id) });

      if (!providerTicketDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider ticket not found',
        );
      }

      // Map the document to ProviderTicket type
      const providerTicket = providerTicketDoc as unknown as ProviderTicket;

      return providerTicket;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to find provider ticket: ${error.message}`,
      );
    }
  }

  /**
    * Create a new provider ticket in the tenant's collection
    */
  async create(input: CreateProviderTicketInput, orgId?: string, userId?: string) {
    try {
      const userCollection = await this.getProviderCollection(orgId);

      // 👇 Auto-increment ticket ID
      const result = await this.counterModel.findOneAndUpdate(
        { name: 'provider_ticket' },
        { $inc: { seq: 1 } },
        { new: true, upsert: true }
      );

      if (!result?.seq) {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.PROVIDER,
          'Failed to generate ticket sequence'
        );
      }

      const ticketId = result.seq.toString().padStart(5, '0');
      const inputPlain = instanceToPlain(input) as Record<string, any>;

      const processedFlattenedValues: Record<string, any> = {};
      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const ticketData: Record<string, any> = {
        ticketId,
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
        createdAt: new Date(),
        updatedAt: new Date(),
        createdby: input.createdby ?? userId,
        received_date: new Date(),
        allocated_type: 'Manual',
      };

      delete ticketData.flattenedValues;

      // 👇 Insert the new ticket
      const insertResult = await userCollection.insertOne(ticketData);
      const newProviderTicket = await userCollection.findOne({ _id: insertResult.insertedId });

      // 👇 Update the source email ticket
      const emailTicketCollection = await this.getProviderEmailTicketCollection(orgId);
      const updateSourceTicket = await emailTicketCollection.findOne({
        _id: new Types.ObjectId(input.source_ticket_id),
      });

      if (updateSourceTicket) {
        updateSourceTicket.ticketId = ticketId;
        updateSourceTicket.updatedAt = new Date();
        updateSourceTicket.assignedToLead = userId;

        await emailTicketCollection.updateOne(
          { _id: new Types.ObjectId(input.source_ticket_id) },
          { $set: updateSourceTicket }
        );
        // 👇 If type is 2, notify the manager
        if (updateSourceTicket.type === 2) {
          const managerEmail = await this.getUserEmail(updateSourceTicket.created_by);

          if (managerEmail) {
            const html = `
            <!DOCTYPE html>
            <html>
              <head>
                <meta charset="UTF-8" />
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    background-color: #f4f6f9;
                    padding: 20px;
                    color: #333;
                  }
                  .container {
                    background: #fff;
                    padding: 20px;
                    border-radius: 8px;
                    max-width: 600px;
                    margin: auto;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                  }
                  h2 {
                    color: #2c3e50;
                  }
                  .btn {
                    margin-top: 20px;
                    display: inline-block;
                    padding: 10px 20px;
                    background-color: #28a745;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                  }
                  .footer {
                    font-size: 12px;
                    color: #888;
                    text-align: center;
                    margin-top: 30px;
                  }
                </style>
              </head>
              <body>
                <div class="container">
                  <h2>🩺 New Health Ticket Processed</h2>
                  <p>Dear <strong>Client</strong>,</p>
                  <p>A new healthcare support ticket has been created.</p>
                  <a href="https://devrcmgenie.asprcmsolutions.com" class="btn">Open Ticket</a>
                  <div class="footer">This is an automated message from the Healthcare Support System.</div>
                </div>
              </body>
            </html>
          `;

            await this.emailService.sendEmail(managerEmail, html, '🩺 New Ticket Created');
          }
        }
      }
      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { providerTicket: newProviderTicket }
      );
    } catch (error: any) {
      console.error('ProviderTicket creation failed:', error);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to create provider ticket: ${error.message || 'Unknown error'}`
      );
    }
  }

  /**
   * Update a provider ticket in the tenant's collection
   */
  async update(input: UpdateProviderTicketInput, orgId?: string, userId?: string) {
    try {
      const userCollection = await this.getProviderCollection(orgId);
      const { id, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid ID format',
        );
      }

      const updateResult = await userCollection.findOneAndUpdate(
        { _id: new Types.ObjectId(id) },
        { $set: { ...updateData, updatedAt: new Date() } },
        { returnDocument: 'after' }
      );

      if (!updateResult) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider ticket not found',
        );
      }


      if (input.assignedTo && userId) {
        const updateEmailTickets = await this.getProviderEmailTicketCollection(orgId);
        const updateSourceTicket = await updateEmailTickets.findOne({ _id: new Types.ObjectId(updateResult.source_ticket_id) });
        if (updateSourceTicket) {
          updateSourceTicket.assignedToAgent = input.assignedTo;
          await updateEmailTickets.updateOne({ _id: new Types.ObjectId(updateResult.source_ticket_id) }, { $set: updateSourceTicket });
        }
        await userCollection.findOneAndUpdate(
          { _id: new Types.ObjectId(id) },
          { $set: { assign: true } },
          { returnDocument: 'after' }
        );

        // Notify user via Firebase Push
        const deviceToken = await this.getDeviceTokenForUser(input.assignedTo); // Implement this

        const title = 'Ticket Assigned';
        const body = `A ticket has been assigned to you. Ticket ID: ${updateResult.ticketId}`;

        // Firebase Push - only send if device token exists
        if (deviceToken) {
          await this.notificationService.notifyUserWithFirebasePush(deviceToken, title, body, {
            ticketId: id,
            type: 'assignment',
          });
        }

        // Store in DB
        const notificationId = new Types.ObjectId().toHexString(); // Or use a UUID
        await this.notificationService.saveNotificationToDb(
          {
            senderId: userId,
            userId: input.assignedTo,
            type: NotificationType.TICKET_ASSIGNMENT,
            title,
            message: body,
            data: {
              ticketId: id,
            },
            metadata: {
              orgId,
            },
          },
          notificationId,
          orgId,

        );
      }
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { providerTicket: updateResult }
      );
    } catch (error: any) {
      console.error('ProviderTicket update failed:', error);
      throw error instanceof Error
        ? error
        : new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.PROVIDER,
          'Failed to update provider ticket: ' + (error.message || 'Unknown error'),
        );
    }
  }

  /**
   * Delete a provider ticket in the tenant's collection
   */
  async delete(id: string, orgId?: string, userId?: string) {
    try {
      const userCollection = await this.getProviderCollection(orgId);

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid ID format'
        );
      }

      const deleteResult = await userCollection.findOneAndDelete({ _id: new Types.ObjectId(id) });

      if (!deleteResult) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider ticket not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { message: 'Provider ticket deleted successfully' }
      );
    } catch (error: any) {
      console.error('ProviderTicket deletion failed:', error);
      throw error instanceof Error
        ? error
        : new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.PROVIDER,
          `Failed to delete provider ticket: ${error.message || 'Unknown error'}`
        );
    }
  }


  async npiInformations(
    orgId?: string,
    userId?: string,
    suborgid?: string,
    types?: string[]
  ) {
    const userCollection = await this.getorganisationsCollection();
    const providers = await userCollection.find({ _id: new Types.ObjectId(suborgid) }).toArray();

    if (!providers || providers.length === 0) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider not found'
      );
    }

    const results: Record<string, any>[] = [];

    for (const provider of providers) {
      const parsedValues = this.safeJSONParse(provider.values);
      const result: Record<string, any> = {};

      if (Array.isArray(types) && types.length > 0) {
        for (const path of types) {
          const pathKeys = path.split('.');
          const lastKey = pathKeys[pathKeys.length - 1];

          const values = this.getValueByDotPath(parsedValues, path);

          if (Array.isArray(values)) {
            result[path] = values.map(item => ({ [lastKey]: item }));
          } else if (values !== undefined) {
            result[path] = values;
          } else {
            result[path] = null;
          }
        }
      }

      results.push(result);
    }

    return {
      message: 'Match found',
      code: 200,
      type: 'SUCCESS',
      data: results,
    };
  }

  async providerInformations(
    orgId?: string,
    userId?: string,
    suborgid?: string,
    types?: string[]
  ) {
    const userCollection = await this.getProvidersCollection();
    const providers = await userCollection.find({ subOrganisationId: suborgid }).toArray();

    if (!providers || providers.length === 0) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider not found'
      );
    }

    const results: Record<string, any>[] = [];

    for (const provider of providers) {
      const parsedValues = this.safeJSONParse(provider.values);
      const result: Record<string, any> = {};

      if (Array.isArray(types) && types.length > 0) {
        for (const path of types) {
          const lastKey = path.split('.').pop() || path;

          let value = this.getValueByDotPath(parsedValues, path);
          if (value === undefined) value = provider[path];

          if (Array.isArray(value)) {
            result[path] = value.map(item => ({ [lastKey]: item }));
          } else if (value !== undefined) {
            result[path] = value;
          } else {
            result[path] = null;
          }
        }
      }

      results.push(result);
    }

    return {
      message: 'Match found',
      code: 200,
      type: 'SUCCESS',
      data: results,
    };
  }

  async payorInformations(
    orgId?: string,
    userId?: string,
    suborgid?: string,
    types?: string[]
  ) {
    const userCollection = await this.getPayorsCollection();
    const providers = await userCollection.find({ subOrganisationId: suborgid }).toArray();

    if (!providers || providers.length === 0) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider not found'
      );
    }

    const results: Record<string, any>[] = [];

    for (const provider of providers) {
      const parsedValues = this.safeJSONParse(provider.values);
      const result: Record<string, any> = {};

      if (Array.isArray(types) && types.length > 0) {
        for (const path of types) {
          const lastKey = path.split('.').pop() || path;

          let value = this.getValueByDotPath(parsedValues, path);
          if (value === undefined) value = provider[path];

          if (Array.isArray(value)) {
            result[path] = value.map(item => ({ [lastKey]: item }));
          } else if (value !== undefined) {
            result[path] = value;
          } else {
            result[path] = null;
          }
        }
      }

      results.push(result);
    }

    return {
      message: 'Match found',
      code: 200,
      type: 'SUCCESS',
      data: results,
    };
  }

  private getValueByDotPath(obj: any, path: string): any {
    try {
      const keys = path.split('.').map(k => k.trim());
      let current = obj;

      for (const key of keys) {
        if (Array.isArray(current)) {
          current = current.map(item => item?.[key]).filter(v => v !== undefined);
        } else {
          current = current?.[key];
        }
      }

      return current;
    } catch {
      return undefined;
    }
  }

  private safeJSONParse(value: any): any {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return {};
    }
  }

}


