import { Module, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { Role, RoleSchema } from './entites/role.entites';
import { UserProcessAssignment, UserProcessAssignmentSchema } from './entites/user_process_assignment';
import { OrganisationRole, OrganisationRoleSchema } from './entites/organisation-role.entity';
import { RedisCacheService } from './cache/redis-cache.service';
import { CacheCleanupService } from './cache/cache-cleanup.service';
import { CacheManagementController } from './cache/cache-management.controller';

@Global()
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    ScheduleModule.forRoot(),
    MongooseModule.forFeature([
      { name: Role.name, schema: RoleSchema },
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },
    ]),
  ],
  controllers: [
    CacheManagementController,
  ],
  providers: [
    RedisCacheService,
    CacheCleanupService,
  ],
  exports: [
    MongooseModule,
    RedisCacheService,
    CacheCleanupService,
  ],
})
export class PermissionsModule {}
