import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, Document } from 'mongoose';
import { Diagnosis } from './entities/diagnosis.entity';
import { Icd } from './entities/icd.entity';
import {
  CreateDiagnosisInput,
  UpdateDiagnosisInput,
  PaginateDiagnosisArgs,
  PaginatedDiagnosisResponse,
  DiagnosisFilterInput
} from './dto/diagnosis.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { instanceToPlain } from 'class-transformer';

type IcdDocument = Icd & Document;
type DiagnosisDocument = Diagnosis & Document;

@Injectable()
export class DiagnosisService {
  constructor(
    @InjectModel(Diagnosis.name)
    private diagnosisModel: Model<DiagnosisDocument>,
    @InjectModel(Icd.name)
    private icdModel: Model<IcdDocument>,
  ) {}

  private transformDocument(doc: any): DiagnosisDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DIAGNOSIS,
        'Cannot transform null document',
      );
    }
    
    const plainDoc = {
      _id: doc._id || new Types.ObjectId(),
      type: doc.type,
      name: doc.name || '',
      icd: doc.icd || '',
      values: doc.values || {},
      templateId: doc.templateId || '',
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || '',
      updatedBy: doc.updatedBy || '',
    };
    
    return new this.diagnosisModel(plainDoc) as DiagnosisDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { name: searchRegex },
      { 'values.description': searchRegex },
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ templateId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ? 
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } : 
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }

  async findAll(input: PaginateDiagnosisArgs): Promise<PaginatedDiagnosisResponse> {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Get a sample document to extract dynamic fields
      const sampleDoc = await this.diagnosisModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        projection = selectedFields;
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v', 'values', 'flattenedValues'];
        projection = allFields.reduce((acc, field) => {
          if (!excluded.includes(field)) acc[field] = 1;
          return acc;
        }, {} as Record<string, number>);

        // Search in all available fields (except excluded ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = allFields.filter(field => !excludedFromSearch.includes(field));
      }

      // Search Handling
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }
      
      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.diagnosisModel.countDocuments(query);
      // === Fetch Data ===
      const diagnosis = await this.diagnosisModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: diagnosis,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.DIAGNOSIS, `Failed to fetch diagnoses: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string): Promise<DiagnosisDocument> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.DIAGNOSIS, 'Invalid ID format');
      }
      const doc = await this.diagnosisModel.findById(id).lean().exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.DIAGNOSIS, 'Diagnosis not found');
      }
      return this.transformDocument(doc);
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.DIAGNOSIS, `Failed to find diagnosis: ${(err as globalThis.Error).message}`);
    }
  }

  async findByName(name: string,icd?:string): Promise<DiagnosisDocument | null> {
    try {
      const doc = await this.diagnosisModel.findOne({ name,icd }).lean().exec();
      return doc ? this.transformDocument(doc) : null;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.findByName:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.DIAGNOSIS, `Failed to find diagnosis by name: ${(err as globalThis.Error).message}`);
    }
  }

  async create(input: CreateDiagnosisInput): Promise<Success> {
    try {
      const existing = await this.findByName(input.name,input.icd);
      if (existing) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.DUPLICATE_ENTRY,
          ErrorType.DIAGNOSIS,
          'Diagnosis with this name already exists'
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const diagnosisData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
      };

      delete diagnosisData.flattenedValues;

      const newDiagnosis = new this.diagnosisModel(diagnosisData);
      const savedDoc = await newDiagnosis.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.DIAGNOSIS,
        { diagnosis: this.transformDocument(savedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.create:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.DIAGNOSIS, `Failed to create diagnosis: ${(err as globalThis.Error).message}`);
    }
  }

  async update(input: UpdateDiagnosisInput): Promise<Success> {
    try {
      const { id,flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.DIAGNOSIS, 'Invalid diagnosis ID format for update.');
      }

      const existingDiagnosis = await this.diagnosisModel.findById(id).lean().exec();
      if (!existingDiagnosis) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.DIAGNOSIS, 'Diagnosis not found for update.');
      }

      if (input.name ) {
        const existingByName = await this.findByName(input.name,input.icd);
        if (existingByName && existingByName._id.toString() !== id) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.DIAGNOSIS,
            'Diagnosis with this name already exists'
          );
        }
      }

      const updatePayload: any = { ...updateData, updatedAt: new Date() };
      if (input.isActive !== undefined) updatePayload.isActive = input.isActive;

const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.diagnosisModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );


      // const updatedDoc = await this.diagnosisModel
      //   .findByIdAndUpdate(id, { $set: updatePayload }, { new: true })
      //   .lean()
      //   .exec();

      if (!updatedDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.DIAGNOSIS, 'Diagnosis not found during update process, or optimistic lock failed.');
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DIAGNOSIS,
        { diagnosis: this.transformDocument(updatedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.update:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.DIAGNOSIS, `Failed to update diagnosis: ${(err as globalThis.Error).message}`);
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.DIAGNOSIS,
          'Invalid ID format'
        );
      }

      const deletedDoc = await this.diagnosisModel
        .findByIdAndDelete(id)
        .lean()
        .exec();

      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.DIAGNOSIS,
          'Diagnosis not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DIAGNOSIS,
        { message: 'Diagnosis deleted successfully.' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in DiagnosisService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DIAGNOSIS,
        `Failed to delete diagnosis: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.diagnosisModel.countDocuments().exec();
    } catch (err) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DIAGNOSIS,
        `Failed to count diagnoses: ${(err as globalThis.Error).message}`,
      );
    }
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'name',
      'type',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'templateId'
    ];
  }

  getSearchableFields(): string[] {
    return ['name', 'values.description'];
  }
} 