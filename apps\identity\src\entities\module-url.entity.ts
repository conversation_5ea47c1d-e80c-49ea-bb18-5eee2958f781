import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ObjectType, Field, ID, Int, Directive } from '@nestjs/graphql';

@ObjectType()
@Directive('@shareable')
export class PaginationMetas {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'module_urls'
})

export class ModuleUrl extends Document {
  @Field(() => ID)
  declare _id: string;

  @Field()
  @Prop({ required: true })
  moduleName: string;

  @Field(() => Int, { nullable: true })
  @Prop({ default: 30 })
  importUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @Prop({ default: 30 })
  backupUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @Prop({ default: 30 })
  exportUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @Prop({ default: 30 })
  auditUrlTime?: number;

  @Field(() => Boolean)
  @Prop({ default: false })
  isImport: boolean;

  @Field(() => Boolean)
  @Prop({ default: false })
  isExport: boolean;

  @Field()
  @Prop({ required: true })
  updatedBy: string;

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Field()
  @Prop({ default: true })
  collectionName: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

export const ModuleUrlSchema = SchemaFactory.createForClass(ModuleUrl);


