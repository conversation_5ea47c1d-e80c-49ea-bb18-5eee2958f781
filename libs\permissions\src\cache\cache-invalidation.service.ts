import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { RedisCacheService } from './redis-cache.service';
import { CacheInvalidationEvent, CACHE_EVENTS } from './cache-types';

export interface InvalidationRule {
  event: string;
  pattern: string;
  condition?: (data: any) => boolean;
  delay?: number; // milliseconds
  batchSize?: number;
}

export interface InvalidationJob {
  id: string;
  type: 'immediate' | 'delayed' | 'batch';
  status: 'pending' | 'running' | 'completed' | 'failed';
  patterns: string[];
  affectedKeys: string[];
  startTime: number;
  endTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class CacheInvalidationService implements OnModuleInit {
  private readonly logger = new Logger(CacheInvalidationService.name);
  private readonly invalidationRules: Map<string, InvalidationRule[]> = new Map();
  private readonly pendingJobs: Map<string, InvalidationJob> = new Map();
  private readonly batchQueue: Map<string, Set<string>> = new Map();
  private batchTimer?: NodeJS.Timeout;

  constructor(
    private readonly cacheService: RedisCacheService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async onModuleInit() {
    this.setupDefaultInvalidationRules();
    this.startBatchProcessor();
  }

  private setupDefaultInvalidationRules() {
    // User-related invalidations
    this.addInvalidationRule('user.role.assigned', {
      event: 'user.role.assigned',
      pattern: 'perm:user:{userId}:*',
    });

    this.addInvalidationRule('user.role.unassigned', {
      event: 'user.role.unassigned',
      pattern: 'perm:user:{userId}:*',
    });

    this.addInvalidationRule('user.deleted', {
      event: 'user.deleted',
      pattern: 'perm:user:{userId}:*',
    });

    this.addInvalidationRule('user.updated', {
      event: 'user.updated',
      pattern: 'perm:user:{userId}:*',
      condition: (data) => data.rolesChanged || data.statusChanged,
    });

    // Role-related invalidations
    this.addInvalidationRule('role.permissions.updated', {
      event: 'role.permissions.updated',
      pattern: 'perm:role:*:{roleId}',
    });

    this.addInvalidationRule('role.updated', {
      event: 'role.updated',
      pattern: 'perm:role:*:{roleId}',
    });

    this.addInvalidationRule('role.deleted', {
      event: 'role.deleted',
      pattern: 'perm:role:*:{roleId}',
    });

    // Organization-related invalidations
    this.addInvalidationRule('organization.updated', {
      event: 'organization.updated',
      pattern: 'perm:*:{orgId}:*',
      batchSize: 100,
      delay: 5000, // 5 second delay for batch processing
    });

    this.addInvalidationRule('process.updated', {
      event: 'process.updated',
      pattern: 'perm:*:*:*:{processId}',
      batchSize: 50,
      delay: 3000,
    });

    // System-wide invalidations
    this.addInvalidationRule('permissions.template.updated', {
      event: 'permissions.template.updated',
      pattern: 'perm:*',
      delay: 10000, // 10 second delay for system-wide changes
    });
  }

  addInvalidationRule(key: string, rule: InvalidationRule) {
    if (!this.invalidationRules.has(rule.event)) {
      this.invalidationRules.set(rule.event, []);
    }
    this.invalidationRules.get(rule.event)!.push(rule);
    this.logger.log(`Added invalidation rule: ${key} for event: ${rule.event}`);
  }

  removeInvalidationRule(event: string, pattern: string) {
    const rules = this.invalidationRules.get(event);
    if (rules) {
      const index = rules.findIndex(rule => rule.pattern === pattern);
      if (index !== -1) {
        rules.splice(index, 1);
        this.logger.log(`Removed invalidation rule for event: ${event}, pattern: ${pattern}`);
      }
    }
  }

  // Event handlers for common invalidation scenarios
  @OnEvent('user.role.assigned')
  async handleUserRoleAssigned(data: { userId: string; roleId: string; orgId: string; subOrgId: string; processId: string }) {
    await this.invalidateUserPermissions(data.userId, 'user.role.assigned', data);
  }

  @OnEvent('user.role.unassigned')
  async handleUserRoleUnassigned(data: { userId: string; roleId: string; orgId: string; subOrgId: string; processId: string }) {
    await this.invalidateUserPermissions(data.userId, 'user.role.unassigned', data);
  }

  @OnEvent('role.permissions.updated')
  async handleRolePermissionsUpdated(data: { roleId: string; roleType: 'system' | 'organization' }) {
    await this.invalidateRole(data.roleId, 'role.permissions.updated', data);
    // Also invalidate all users with this role
    await this.invalidateUsersByRole(data.roleId, 'role.permissions.updated', data);
  }

  @OnEvent('organization.structure.changed')
  async handleOrganizationStructureChanged(data: { orgId: string; subOrgIds?: string[]; processIds?: string[] }) {
    await this.invalidateOrganization(data.orgId, 'organization.structure.changed', data);
  }

  @OnEvent('system.permissions.updated')
  async handleSystemPermissionsUpdated(data: { reason: string }) {
    await this.invalidateAllPermissions('system.permissions.updated', data);
  }

  // Core invalidation methods
  async invalidateUserPermissions(userId: string, reason: string, metadata?: any): Promise<void> {
    try {
      const jobId = this.generateJobId('user', userId);
      const job: InvalidationJob = {
        id: jobId,
        type: 'immediate',
        status: 'running',
        patterns: [`perm:user:${userId}:*`],
        affectedKeys: [],
        startTime: Date.now(),
        metadata: { userId, reason, ...metadata },
      };

      this.pendingJobs.set(jobId, job);

      await this.cacheService.invalidateUserPermissions(userId);
      
      job.status = 'completed';
      job.endTime = Date.now();

      await this.logInvalidation({
        type: 'user',
        target: userId,
        reason,
        affectedKeys: job.affectedKeys,
        timestamp: Date.now(),
        source: 'CacheInvalidationService',
        metadata,
      });

      this.logger.log(`Invalidated user permissions for userId: ${userId}, reason: ${reason}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate user permissions for userId: ${userId}`, error);
      throw error;
    }
  }

  async invalidateRole(roleId: string, reason: string, metadata?: any): Promise<void> {
    try {
      const jobId = this.generateJobId('role', roleId);
      const job: InvalidationJob = {
        id: jobId,
        type: 'immediate',
        status: 'running',
        patterns: [`perm:role:*:${roleId}`],
        affectedKeys: [],
        startTime: Date.now(),
        metadata: { roleId, reason, ...metadata },
      };

      this.pendingJobs.set(jobId, job);

      await this.cacheService.invalidateRole(roleId);
      
      job.status = 'completed';
      job.endTime = Date.now();

      await this.logInvalidation({
        type: 'role',
        target: roleId,
        reason,
        affectedKeys: job.affectedKeys,
        timestamp: Date.now(),
        source: 'CacheInvalidationService',
        metadata,
      });

      this.logger.log(`Invalidated role cache for roleId: ${roleId}, reason: ${reason}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate role cache for roleId: ${roleId}`, error);
      throw error;
    }
  }

  async invalidateOrganization(orgId: string, reason: string, metadata?: any): Promise<void> {
    try {
      const jobId = this.generateJobId('organization', orgId);
      const job: InvalidationJob = {
        id: jobId,
        type: 'batch',
        status: 'running',
        patterns: [`perm:*:${orgId}:*`],
        affectedKeys: [],
        startTime: Date.now(),
        metadata: { orgId, reason, ...metadata },
      };

      this.pendingJobs.set(jobId, job);

      // Add to batch queue for processing
      this.addToBatchQueue(`org:${orgId}`, `perm:*:${orgId}:*`);

      await this.logInvalidation({
        type: 'organization',
        target: orgId,
        reason,
        affectedKeys: [],
        timestamp: Date.now(),
        source: 'CacheInvalidationService',
        metadata,
      });

      this.logger.log(`Queued organization invalidation for orgId: ${orgId}, reason: ${reason}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate organization cache for orgId: ${orgId}`, error);
      throw error;
    }
  }

  async invalidateUsersByRole(roleId: string, reason: string, metadata?: any): Promise<void> {
    try {
      // This would require a reverse lookup - in practice, you might want to
      // maintain a separate index of users by role for efficient invalidation
      const pattern = `perm:user:*`;
      await this.cacheService.invalidateByPattern(pattern);

      await this.logInvalidation({
        type: 'role',
        target: `users_with_role_${roleId}`,
        reason,
        affectedKeys: [],
        timestamp: Date.now(),
        source: 'CacheInvalidationService',
        metadata: { roleId, ...metadata },
      });

      this.logger.log(`Invalidated all user permissions due to role change: ${roleId}, reason: ${reason}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate users by role: ${roleId}`, error);
      throw error;
    }
  }

  async invalidateAllPermissions(reason: string, metadata?: any): Promise<void> {
    try {
      const jobId = this.generateJobId('system', 'all');
      const job: InvalidationJob = {
        id: jobId,
        type: 'batch',
        status: 'running',
        patterns: ['perm:*'],
        affectedKeys: [],
        startTime: Date.now(),
        metadata: { reason, ...metadata },
      };

      this.pendingJobs.set(jobId, job);

      // Add to batch queue with delay
      this.addToBatchQueue('system:all', 'perm:*');

      await this.logInvalidation({
        type: 'system',
        target: 'all',
        reason,
        affectedKeys: [],
        timestamp: Date.now(),
        source: 'CacheInvalidationService',
        metadata,
      });

      this.logger.warn(`Queued system-wide cache invalidation, reason: ${reason}`);
    } catch (error) {
      this.logger.error('Failed to invalidate all permissions', error);
      throw error;
    }
  }

  // Batch processing
  private addToBatchQueue(key: string, pattern: string) {
    if (!this.batchQueue.has(key)) {
      this.batchQueue.set(key, new Set());
    }
    this.batchQueue.get(key)!.add(pattern);
  }

  private startBatchProcessor() {
    this.batchTimer = setInterval(async () => {
      if (this.batchQueue.size === 0) return;

      const batchesToProcess = new Map(this.batchQueue);
      this.batchQueue.clear();

      for (const [key, patterns] of batchesToProcess) {
        try {
          let totalInvalidated = 0;
          for (const pattern of patterns) {
            const count = await this.cacheService.invalidateByPattern(pattern);
            totalInvalidated += count;
          }
          
          this.logger.log(`Batch processed ${patterns.size} patterns for ${key}, invalidated ${totalInvalidated} keys`);
        } catch (error) {
          this.logger.error(`Batch processing failed for ${key}:`, error);
        }
      }
    }, 5000); // Process batches every 5 seconds
  }

  // Utility methods
  private generateJobId(type: string, target: string): string {
    return `${type}_${target}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async logInvalidation(event: CacheInvalidationEvent): Promise<void> {
    try {
      // Emit event for monitoring/logging systems
      this.eventEmitter.emit(CACHE_EVENTS.USER_PERMISSION_INVALIDATED, event);
      
      // You could also store in database or external logging system
      this.logger.debug(`Cache invalidation logged: ${JSON.stringify(event)}`);
    } catch (error) {
      this.logger.warn('Failed to log cache invalidation:', error);
    }
  }

  // Management methods
  async getInvalidationJobs(limit: number = 50): Promise<InvalidationJob[]> {
    const jobs = Array.from(this.pendingJobs.values())
      .sort((a, b) => b.startTime - a.startTime)
      .slice(0, limit);
    
    return jobs;
  }

  async clearCompletedJobs(): Promise<number> {
    const completedJobs = Array.from(this.pendingJobs.entries())
      .filter(([_, job]) => job.status === 'completed' || job.status === 'failed');
    
    for (const [id] of completedJobs) {
      this.pendingJobs.delete(id);
    }
    
    this.logger.log(`Cleared ${completedJobs.length} completed invalidation jobs`);
    return completedJobs.length;
  }

  async getInvalidationStats(): Promise<any> {
    const jobs = Array.from(this.pendingJobs.values());
    const stats = {
      totalJobs: jobs.length,
      pendingJobs: jobs.filter(j => j.status === 'pending').length,
      runningJobs: jobs.filter(j => j.status === 'running').length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length,
      batchQueueSize: this.batchQueue.size,
      averageProcessingTime: this.calculateAverageProcessingTime(jobs),
    };
    
    return stats;
  }

  private calculateAverageProcessingTime(jobs: InvalidationJob[]): number {
    const completedJobs = jobs.filter(j => j.status === 'completed' && j.endTime);
    if (completedJobs.length === 0) return 0;
    
    const totalTime = completedJobs.reduce((sum, job) => sum + (job.endTime! - job.startTime), 0);
    return totalTime / completedJobs.length;
  }
}
