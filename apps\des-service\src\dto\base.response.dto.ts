import { Field, ObjectType, Directive, Int } from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Graph<PERSON>JSON } from 'graphql-type-json';

@ObjectType()
@Directive('@key(fields: "message")')
export class BaseResponse {
  @Field()
  @Directive('@shareable')
  message: string;

  @Field()
  @Directive('@shareable')
  code: number;

  @Field()
  @Directive('@shareable')
  type: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Directive('@shareable')
  data?: Record<string, unknown> | null;
}

@ObjectType()
@Directive('@shareable')
export class SuccessResponse {
  @Field(() => Int)
  code: number;

  @Field()
  message: string;

  @Field()
  type: string;

  @Field(() => GraphQLJSON, { nullable: true })
  data?: Record<string, any>;
}

@ObjectType()
@Directive('@shareable')
export class PaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

