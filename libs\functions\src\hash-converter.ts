import { createHash } from 'crypto';

export function hashGridFields(fields: Record<string, any>[]): string {
  const canonicalized = fields.map(obj =>
    JSON.stringify(
      Object.keys(obj).sort().reduce((acc, key) => {
        acc[key] = obj[key];
        return acc;
      }, {} as Record<string, any>)
    )
  );
  const sortedArray = canonicalized.sort().join('|');
  return createHash('sha256').update(sortedArray).digest('hex');
}
