import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class AuditLog extends Document {
  @Prop({ required: true })
  auditId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  orgId: string;

  @Prop()
  userEmail?: string;

  @Prop({ required: true })
  action: string;

  @Prop({ required: true })
  entityType: string;

  @Prop()
  entityId?: string;

  @Prop({ type: [String], default: [] })
  message: string[];

  @Prop({ type: Object })
  beforeData?: any;

  @Prop({ type: Object })
  afterData?: any;

  @Prop({ required: true })
  severity: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop()
  ipAddress?: string;

  @Prop()
  userAgent?: string;

  @Prop()
  sessionId?: string;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);
