import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class GcsService {
  private storage: Storage;
  private bucket: string;

  constructor() {
    this.bucket = process.env.GCS_BUCKET_NAME || 'asp-rcm-dev-documents-bucket';
    if (!this.bucket) {
      throw new Error('GCP_BUCKET or GCS_BUCKET_NAME environment variable is not set!');
    }
    this.storage = new Storage();
  }

  async uploadBuffer(buffer: Buffer, destination: string, contentType = 'application/octet-stream'): Promise<string> {
    const file = this.storage.bucket(this.bucket).file(destination);
    await file.save(buffer, {
      contentType,
      resumable: false,
      public: false,
    });
    const url = `https://storage.googleapis.com/${this.bucket}/${destination}`;
    return url;
  }

  async downloadBuffer(filePath: string): Promise<Buffer> {
    const file = this.storage.bucket(this.bucket).file(filePath);
    const [contents] = await file.download();
    return contents;
  }

  async deleteFile(fileUrl: string): Promise<void> {
    try {
      // Extract file path from URL
      // URL format: https://storage.googleapis.com/bucket-name/file-path
      const urlParts = fileUrl.split('/');
      const bucketIndex = urlParts.findIndex(part => part === this.bucket);

      if (bucketIndex === -1 || bucketIndex === urlParts.length - 1) {
        throw new Error(`Invalid GCS URL format: ${fileUrl}`);
      }

      // Get file path (everything after bucket name)
      const filePath = urlParts.slice(bucketIndex + 1).join('/');

      const file = this.storage.bucket(this.bucket).file(filePath);
      await file.delete();

      console.log(`🗑️ Successfully deleted file: ${filePath}`);
    } catch (error) {
      console.error(`❌ Failed to delete file ${fileUrl}:`, error);
      throw error;
    }
  }

  // async generateV4UploadSignedUrl(filename: string, contentType: string): Promise<string> {
  //   const [url] = await this.storage
  //     .bucket(this.bucket)
  //     .file(filename)
  //     .getSignedUrl({
  //       version: 'v4',
  //       action: 'write',
  //       expires: Date.now() + 15 * 60 * 1000,
  //       contentType,
  //     });
  //   return url;
  // }

  // async generateV4ViewSignedUrl(filename: string): Promise<string> {
  //   const [url] = await this.storage
  //     .bucket(this.bucket)
  //     .file(filename)
  //     .getSignedUrl({
  //       version: 'v4',
  //       action: 'read',
  //       expires: Date.now() + 15 * 60 * 1000,
  //     });
  //   return url;
  // }
} 