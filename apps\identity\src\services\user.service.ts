import { Injectable, ConflictException, HttpException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User } from './../entities/user.entity';
import { Role } from '../entities/role.entity';
import { UserProcessAssignment, UserProcessAssignmentSchema } from '../entities/user-process-assignment.entity';
import { CreateSystemUserInput, UpdateSystemUserInput, PaginateUserArgs, AssignUserRoleInput, UpdateRolePermissionsInput, AssignRoleHierarchyInput, RemoveRoleMappingInput, AssignOperationsRoleInput, AssignAuditRoleInput, AssignManagementsRoleInput, RemoveOperationsRoleInput, RemoveAuditRoleInput, RemoveManagementsRoleInput } from '../dto/user.dto';
//import { GraphQLJSON } from 'graphql-type-json';
// import { Client } from './entities/client.entity';
// import { Process } from './entities/process.entity';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { isValidObjectId } from 'mongoose';
import { UserNotificationToken, UserNotificationTokenSchema } from '../entities/user-notification-token.entity';
@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Role.name) private roleModel: Model<Role>,
    @InjectModel(UserProcessAssignment.name) private userProcessAssignmentModel: Model<UserProcessAssignment>,
    @InjectModel('ProcessRoleHierarchy') private processRoleHierarchyModel: Model<any>,
    @InjectModel(UserNotificationToken.name) private userNotificationTokenModel: Model<UserNotificationToken>,
  ) { }

  async create(input: CreateSystemUserInput, userId: string) {

    if (!input.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.email)) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.INVALID_EMAIL,
        ErrorType.IDENTITY,
        'Invalid Email',
      );
    }
    // Check for existing user with same email or employeeId
    const existing = await this.userModel.findOne({
      $or: [
        { email: input.email },
        { employeeId: input.employeeId }
      ]
    });
    if (existing) {
      throw new ConflictException('A user with this email or employeeId already exists.');
    }
    const now = new Date();
    const createdUser = new this.userModel({
      ...input,
      isActive: true,
      createdBy: userId,
      updatedBy: userId,
      createdAt: now,
      updatedAt: now,
    });
    return createdUser.save();
  }

  async findAll(input: PaginateUserArgs, userId?: string): Promise<any> {
    const {
      page = 1,
      limit = 10,
      search,
      filters,
      sortBy,
      sortOrder = 'asc',
      selectedFields
    } = input;
    const query: any = {};

    if (userId && input.type === 'list') {
      query._id = { $ne: new Types.ObjectId(userId) };
    }

    // Get a sample document to extract dynamic fields
    const sampleDoc = await this.userModel.findOne().lean();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

    // Field Selection Logic
    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
      // Restrict search to only name, email, and employeeId
      searchableFields = ['name', 'email', 'employeeId'];
    } else {
      const excluded = ['__v'];
      projection = allFields.reduce((acc, field) => {
        if (!excluded.includes(field)) acc[field] = 1;
        return acc;
      }, {} as Record<string, number>);
      // Restrict search to only name, email, and employeeId
      searchableFields = ['name', 'email', 'employeeId'];
    }

    // Search Handling
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };
      query.$or = searchableFields.map(field => ({ [field]: regex }));
      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: searchTerm });
      }
    }

    // Filters Handling
    if (filters) {
      const parsed =
        typeof filters === 'string'
          ? JSON.parse(filters)?.filters || JSON.parse(filters)
          : filters.filters || filters;
      for (const [key, value] of Object.entries(parsed)) {
        if (value !== undefined && value !== '') {
          // Check if the field is a date in the sample document
          if (sampleDoc && sampleDoc[key] instanceof Date) {
            // If the value is a string, convert to Date
            const dateValue = typeof value === 'string' ? new Date(value) : value;
            if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
              // Optionally, you can do a range for the whole day
              const nextDay = new Date(dateValue.getTime());
              nextDay.setDate(nextDay.getDate() + 1);
              query[key] = { $gte: dateValue, $lt: nextDay };
            } else {
              // If not a valid date, skip this filter
              continue;
            }
          } else if (typeof value === 'string') {
            query[key] = {
              $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
              $options: 'i'
            };
          } else {
            query[key] = value;
          }
        }
      }
    }

    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;

    const totalItems = await this.userModel.countDocuments(query);
    const users = await this.userModel
      .find(query)
      .select(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .lean();

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      items: users,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems,
        totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async findOne(id: string): Promise<User | null> {
    return this.userModel.findById(id).exec();
  }

  async update(input: UpdateSystemUserInput, userId: string): Promise<User | null> {
    // === Validate Input ===
    if (!input?.id || !isValidObjectId(input.id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'Invalid user ID.',
      );
    }

    // === Check Uniqueness of email & employeeId (excluding current user) ===
    const duplicateUser = await this.userModel.findOne({
      $or: [
        { email: input.email },
        { employeeId: input.employeeId }
      ],
      _id: { $ne: input.id }
    });

    if (duplicateUser) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.DUPLICATE_ENTRY,
        ErrorType.SYSTEMUSER,
        'Email or Employee ID is already used by another user.',
      );
    }

    // === Proceed with update ===
    const updatedUser = await this.userModel.findByIdAndUpdate(
      input.id,
      {
        ...input,
        updatedBy: userId,
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    // === Handle not found ===
    if (!updatedUser) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.SYSTEMUSER,
        'User not found or already deleted.',
      );
    }

    return updatedUser;
  }

  async remove(id: string, userId?: string): Promise<User | null> {

    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'Invalid user ID.',
      );

    }
    if (userId && id === userId) {
      throw new Error(
        HttpStatus.FORBIDDEN,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'You cannot delete your own account',
      );
    }

    // Check if user exists first
    const userToDelete = await this.userModel.findById(id).exec();
    if (!userToDelete) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.USER_NOT_FOUND,
        ErrorType.SYSTEMUSER,
        'User not found',
      );
    }

    // Check if user is assigned to any processes
    const processAssignments = await this.userProcessAssignmentModel.find({
      userId: id,
    }).populate('organisationId', 'name').lean();

    if (processAssignments.length > 0) {
      // Get unique organization names
      const organizationNames = [...new Set(
        processAssignments
          .map(assignment => {
            if (assignment.organisationId && typeof assignment.organisationId === 'object' && 'name' in assignment.organisationId) {
              return (assignment.organisationId as any).name;
            }
            return 'Unknown Organization';
          })
          .filter(name => name)
      )];

      const orgList = organizationNames.length > 1
        ? organizationNames.slice(0, -1).join(', ') + ' and ' + organizationNames.slice(-1)
        : organizationNames[0];

      console.log(`User deletion prevented: User ${id} is assigned to ${processAssignments.length} process(es) in organizations: ${orgList}`);

      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        `Cannot delete user. User is currently assigned to processes in ${orgList}. Please unassign the user from all processes before deletion.`,
      );
    }

    // If no assignments, proceed with deletion
    console.log(`User deletion allowed: User ${id} has no process assignments`);
    const deletedUser = await this.userModel.findByIdAndDelete(id).exec();

    return deletedUser;
  }

  async assignUserRole(input: AssignUserRoleInput) {
    // Remove existing assignment for this user/org/suborg/process
    await this.userProcessAssignmentModel.deleteMany({
      userId: input.userId,
      organisationId: input.organisationId,
      subOrganisationId: input.subOrganisationId,
      processId: input.processId,
    });
    // Create new assignment
    return this.userProcessAssignmentModel.create({
      userId: input.userId,
      roleId: input.roleId,
      organisationId: input.organisationId,
      subOrganisationId: input.subOrganisationId,
      processId: input.processId,
      reportToUserId: input.reportToUserId,
    });
  }

  async updateRolePermissions(input: UpdateRolePermissionsInput) {
    return this.roleModel.findByIdAndUpdate(
      input.roleId,
      { permissions: input.permissions },
      { new: true },
    );
  }

  async getUsersUnderManager(
    userId: string,
    // orgId: string,
    subOrgId: string,
    processId: string
  ): Promise<{
    role: string | null;
    users: any[];
  }> {
    try {
      // Get the process role hierarchy document
      const hierarchyDoc = await this.processRoleHierarchyModel.findOne({
        // orgId,
        subOrgId,
        "process.processId": processId
      }).lean();

      if (!hierarchyDoc || !(hierarchyDoc as any).process || !Array.isArray((hierarchyDoc as any).process)) {
        return {
          role: null,
          users: []
        };
      }

      const processObj = ((hierarchyDoc as any).process as any[]).find((p: any) =>
        p.processId.toString() === processId.toString()
      );

      if (!processObj || !processObj.roles) {
        return {
          role: null,
          users: []
        };
      }

      // Check Operations hierarchy
      const operations = processObj.roles.operations || {};
      const managerToSupervisors = operations.manager_to_supervisors || {};
      const supervisorToAgents = operations.supervisor_to_agents || {};

      // Check if user is operations manager
      if (managerToSupervisors[userId]) {
        const role = 'manager';
        const supervisorIds = managerToSupervisors[userId] || [];
        let users: any[] = [];
        // Get supervisor details
        if (supervisorIds.length > 0) {
          const supervisors = await this.userModel.find({
            _id: { $in: supervisorIds }
          }).select('name email employeeId').lean();
          users = supervisors.map(s => ({ ...s, roleName: 'supervisor' }));
        }
        // Get all agents under these supervisors
        const allAgentIds: string[] = [];
        for (const supervisorId of supervisorIds) {
          const agentIds = supervisorToAgents[supervisorId] || [];
          allAgentIds.push(...agentIds);
        }
        if (allAgentIds.length > 0) {
          const agents = await this.userModel.find({
            _id: { $in: allAgentIds }
          }).select('name email employeeId').lean();
          users = users.concat(agents.map(a => ({ ...a, roleName: 'agent' })));
        }
        // Add the manager himself/herself
        const manager = await this.userModel.findById(userId).select('name email employeeId').lean();
        if (manager) {
          users.unshift({ ...manager, roleName: 'manager' });
        }
        return { role, users };
      }
      // Check if user is operations supervisor
      else if (supervisorToAgents[userId]) {
        const role = 'operations-supervisor';
        const agentIds = supervisorToAgents[userId] || [];
        let users: any[] = [];
        if (agentIds.length > 0) {
          const agents = await this.userModel.find({
            _id: { $in: agentIds }
          }).select('name email employeeId').lean();
          users = agents.map(a => ({ ...a, roleName: 'agent' }));
        }
        // Add the supervisor himself/herself
        const supervisor = await this.userModel.findById(userId).select('name email employeeId').lean();
        if (supervisor) {
          users.unshift({ ...supervisor, roleName: 'operations-supervisor' });
        }
        return { role, users };
      }

      // Check Audit hierarchy
      const audit = processObj.roles.audit || {};
      const auditManagerToSupervisors = audit.manager_to_supervisors || {};
      const auditSupervisorToAgents = audit.supervisor_to_agents || {};

      // Check if user is audit manager (qc-manager)
      if (auditManagerToSupervisors[userId]) {
        const role = 'qc-manager';
        const supervisorIds = auditManagerToSupervisors[userId] || [];
        let users: any[] = [];
        // Get qc-supervisor details
        if (supervisorIds.length > 0) {
          const supervisors = await this.userModel.find({
            _id: { $in: supervisorIds }
          }).select('name email employeeId').lean();
          users = supervisors.map(s => ({ ...s, roleName: 'qc-supervisor' }));
        }
        // Get all qc-agents under these supervisors
        const allAgentIds: string[] = [];
        for (const supervisorId of supervisorIds) {
          const agentIds = auditSupervisorToAgents[supervisorId] || [];
          allAgentIds.push(...agentIds);
        }
        if (allAgentIds.length > 0) {
          const agents = await this.userModel.find({
            _id: { $in: allAgentIds }
          }).select('name email employeeId').lean();
          users = users.concat(agents.map(a => ({ ...a, roleName: 'qc-agent' })));
        }
        // Add the qc-manager himself/herself
        const qcManager = await this.userModel.findById(userId).select('name email employeeId').lean();
        if (qcManager) {
          users.unshift({ ...qcManager, roleName: 'qc-manager' });
        }
        return { role, users };
      }
      // Check if user is audit supervisor (qc-supervisor)
      else if (auditSupervisorToAgents[userId]) {
        const role = 'qc-supervisor';
        const agentIds = auditSupervisorToAgents[userId] || [];
        let users: any[] = [];
        if (agentIds.length > 0) {
          const agents = await this.userModel.find({
            _id: { $in: agentIds }
          }).select('name email employeeId').lean();
          users = agents.map(a => ({ ...a, roleName: 'qc-agent' }));
        }
        // Add the qc-supervisor himself/herself
        const qcSupervisor = await this.userModel.findById(userId).select('name email employeeId').lean();
        if (qcSupervisor) {
          users.unshift({ ...qcSupervisor, roleName: 'qc-supervisor' });
        }
        return { role, users };
      }

      return { role: null, users: [] };
    } catch (error) {
      console.error('Error getting users under manager:', error);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.SYSTEMUSER,
        'Failed to get users under manager'
      );
    }
  }

  async listOrganizations(userId: string) {
    // Check if user has global admin role first
    const globalRole = await this.isGlobalAdmin(userId);

    if (globalRole.isGlobal) {
      console.log(`🌐 User ${userId} has global ${globalRole.roleKey} role - returning all organizations`);

      try {
        // For global admins, get ALL unique organisationIds from user assignments
        const allAssignments = await this.userProcessAssignmentModel.find({
          organisationId: { $ne: null }
        }).populate('organisationId', 'name orgId').lean();

        const uniqueOrgs = new Map();
        for (const assignment of allAssignments) {
          const org = assignment.organisationId;
          if (org && typeof org === 'object' && '_id' in org && 'name' in org) {
            uniqueOrgs.set(org._id.toString(), {
              id: org._id.toString(),
              name: (org as any).name,
              orgId: (org as any).orgId?.toString() ?? org._id.toString()
            });
          }
        }

        console.log(`📊 Found ${uniqueOrgs.size} unique organizations from user assignments`);
        return Array.from(uniqueOrgs.values());

      } catch (error) {
        console.error(`❌ Error fetching all organizations for global admin:`, error);
        return [];
      }
    }

    // For regular users, get unique organisationIds for the user
    const assignments = await this.userProcessAssignmentModel.find({ userId }).populate('organisationId', 'name orgId').lean();
    const uniqueMap = new Map();
    for (const a of assignments) {
      const subOrg = a.organisationId;
      if (
        subOrg &&
        typeof subOrg === 'object' &&
        '_id' in subOrg &&
        'name' in subOrg &&
        !uniqueMap.has(subOrg._id.toString())
      ) {
        uniqueMap.set(
          subOrg._id.toString(),
          {
            id: subOrg._id.toString(),
            name: (subOrg as any).name,
            orgId: (subOrg as any).orgId?.toString() ?? ''
          }
        );
      }
    }
    // Return array of unique sub-organizations
    return Array.from(uniqueMap.values());
  }

  async listSubOrganizations(userId: string, organisationId: string) {
    // Check if user has global admin role first
    const globalRole = await this.isGlobalAdmin(userId);

    let assignments;
    if (globalRole.isGlobal) {
      // For global admins, get ALL sub-organizations for the given organisationId
      assignments = await this.userProcessAssignmentModel.find({
        organisationId,
        subOrganisationId: { $ne: null }
      }).populate('subOrganisationId', 'name orgId').lean();
    } else {
      // For regular users, get sub-organizations they have access to
      assignments = await this.userProcessAssignmentModel.find({
        userId,
        organisationId,
        subOrganisationId: { $ne: null }
      }).populate('subOrganisationId', 'name orgId').lean();
    }

    // Get unique sub-organizations
    const uniqueSubOrgs = new Map();
    for (const assignment of assignments) {
      const subOrg = assignment.subOrganisationId;
      if (subOrg && typeof subOrg === 'object' && '_id' in subOrg && 'name' in subOrg) {
        uniqueSubOrgs.set(subOrg._id.toString(), {
          id: subOrg._id.toString(),
          name: (subOrg as any).name,
          orgId: (subOrg as any).orgId?.toString() ?? subOrg._id.toString()
        });
      }
    }

    console.log(`📊 Found ${uniqueSubOrgs.size} unique sub-organizations for organisationId: ${organisationId}`);
    return Array.from(uniqueSubOrgs.values());
  }

  async listProcesses(userId: string, organisationId: string, subOrganisationId: string) {
    // Check if user has global admin role first
    const globalRole = await this.isGlobalAdmin(userId);

    let assignments;
    if (globalRole.isGlobal) {
      // For global admins, get ALL processes for the given organisationId and subOrganisationId
      assignments = await this.userProcessAssignmentModel.find({
        organisationId,
        subOrganisationId,
        processId: { $ne: null }
      }).populate('processId', 'name orgId').lean();
    } else {
      // For regular users, get processes they have access to
      assignments = await this.userProcessAssignmentModel.find({
        userId,
        organisationId,
        subOrganisationId,
        processId: { $ne: null }
      }).populate('processId', 'name orgId').lean();
    }

    // Get unique processes
    const uniqueProcesses = new Map();
    for (const assignment of assignments) {
      const process = assignment.processId;
      if (process && typeof process === 'object' && '_id' in process && 'name' in process) {
        uniqueProcesses.set(process._id.toString(), {
          id: process._id.toString(),
          name: (process as any).name,
          orgId: (process as any).orgId?.toString() ?? process._id.toString()
        });
      }
    }

    console.log(`📊 Found ${uniqueProcesses.size} unique processes for organisationId: ${organisationId}, subOrganisationId: ${subOrganisationId}`);
    return Array.from(uniqueProcesses.values());
  }

  async listRoles({ page = 1, limit = 10, search = '', filters = {}, sortBy, sortOrder = 'asc' }: { page?: number; limit?: number; search?: string; filters?: Record<string, any>; sortBy?: string; sortOrder?: 'asc' | 'desc' }) {
    const query: any = {};
    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }
    if (filters) {
      const parsed =
        typeof filters === 'string'
          ? JSON.parse(filters)?.filters || JSON.parse(filters)
          : filters.filters || filters;
      for (const [key, value] of Object.entries(parsed)) {
        if (key === 'id' || key === '_id') {
          query._id = value;
        } else if (value !== undefined && value !== '') {
          // Handle date fields robustly
          if ((key === 'createdAt' || key === 'updatedAt')) {
            const dateValue = typeof value === 'string' ? new Date(value) : value;
            if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
              const nextDay = new Date(dateValue.getTime());
              nextDay.setDate(nextDay.getDate() + 1);
              query[key] = { $gte: dateValue, $lt: nextDay };
            } else {
              continue;
            }
          } else if (typeof value === 'string') {
            query[key] = { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
          } else {
            query[key] = value;
          }
        }
      }
    }
    const skip = (page - 1) * limit;

    // Sorting logic
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    const [items, total] = await Promise.all([
      this.roleModel.find(query).sort(sort).skip(skip).limit(limit).lean(),
      this.roleModel.countDocuments(query),
    ]);
    // Ensure isActive and updatedAt are present in each role
    const mappedItems = items.map((item: any) => ({
      ...item,
      isActive: typeof item.isActive !== 'undefined' ? item.isActive : null,
      createdAt: item.createdAt ? new Date(item.createdAt).toISOString() : null,
      updatedAt: item.updatedAt ? new Date(item.updatedAt).toISOString() : null,
      category: typeof item.category !== 'undefined' ? item.category : null,
    }));
    return {
      items: mappedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  async createRole(name: string, permissions: any[]) {
    // Add moduleName to each submodule
    // const permissionsWithModuleName = (permissions || []).map(module => ({
    //   ...module,
    //   subModules: (module.subModules || []).map(subModule => ({
    //     ...subModule,
    //     moduleName: module.moduleName
    //   }))
    // }));
    return this.roleModel.create({ name, permissions: permissions });
  }

  async listOrgHierarchy(orgId?: string, subOrgId?: string) {
    const match: any = {};
    if (orgId) match.organisationId = orgId;
    if (subOrgId) match.subOrganisationId = subOrgId;

    const assignments = await this.userProcessAssignmentModel.find(match)
      .populate('organisationId')
      .populate('subOrganisationId')
      .populate('processId')
      .lean();

    function isPopulatedDoc(doc: unknown): doc is { _id: any } {
      return typeof doc === 'object' && doc !== null && '_id' in (doc as any);
    }

    const organizations = [
      ...new Map(assignments
        .filter(a => isPopulatedDoc(a.organisationId))
        .map(a => [String((a.organisationId as any)._id), a.organisationId])
      ).values()
    ];
    const subOrganizations = [
      ...new Map(assignments
        .filter(a => isPopulatedDoc(a.subOrganisationId))
        .map(a => [String((a.subOrganisationId as any)._id), a.subOrganisationId])
      ).values()
    ];
    const processes = [
      ...new Map(assignments
        .filter(a => isPopulatedDoc(a.processId))
        .map(a => [String((a.processId as any)._id), a.processId])
      ).values()
    ];

    return {
      organizations,
      subOrganizations,
      processes,
    };
  }

  async getRoleById(id: string) {
    return this.roleModel.findById(id).lean();
  }

  /**
   * Assign global admin or sub-admin role to a user
   * Global roles have organisationId, subOrganisationId, and processId set to null
   */
  async assignGlobalRole(userId: string, roleKey: 'admin' | 'sub-admin'): Promise<boolean> {
    // 1. Find the role by key
    const role = await this.roleModel.findOne({ key: roleKey, category: 'Management' });
    if (!role) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.SYSTEMUSER,
        `Role with key "${roleKey}" not found`
      );
    }

    // 2. Check if user already has this global role
    const existingAssignment = await this.userProcessAssignmentModel.findOne({
      userId,
      roleId: role._id,
      organisationId: null,
      subOrganisationId: null,
      processId: null
    });

    if (existingAssignment) {
      console.log(`User ${userId} already has global ${roleKey} role`);
      return true;
    }

    // 3. Create global role assignment
    const globalAssignment = new this.userProcessAssignmentModel({
      userId,
      roleId: role._id,
      organisationId: null,
      subOrganisationId: null,
      processId: null,
      orgId: null // Also set orgId to null for consistency
    });

    await globalAssignment.save();

    console.log(`✅ Assigned global ${roleKey} role to user ${userId}`);
    return true;
  }

  /**
   * Remove global admin or sub-admin role from a user
   */
  async removeGlobalRole(userId: string, roleKey: 'admin' | 'sub-admin'): Promise<boolean> {
    // 1. Find the role by key
    const role = await this.roleModel.findOne({ key: roleKey, category: 'Management' });
    if (!role) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.SYSTEMUSER,
        `Role with key "${roleKey}" not found`
      );
    }

    // 2. Remove global role assignment
    const result = await this.userProcessAssignmentModel.deleteOne({
      userId,
      roleId: role._id,
      organisationId: null,
      subOrganisationId: null,
      processId: null
    });

    console.log(`✅ Removed global ${roleKey} role from user ${userId}`);
    return result.deletedCount > 0;
  }

  /**
   * Check if user has global admin or sub-admin role
   */
  async isGlobalAdmin(userId: string): Promise<{ isGlobal: boolean; roleKey?: string }> {
    const assignment = await this.userProcessAssignmentModel.findOne({
      userId,
      organisationId: null,
      subOrganisationId: null,
      processId: null
    }).populate('roleId', 'key category');

    if (!assignment || !assignment.roleId) {
      return { isGlobal: false };
    }

    const role = assignment.roleId as any;
    if (role.category === 'Management' && (role.key === 'admin' || role.key === 'sub-admin')) {
      return { isGlobal: true, roleKey: role.key };
    }

    return { isGlobal: false };
  }

  /**
   * List all global admins and sub-admins
   */
  async listGlobalAdmins(): Promise<{
    admins: any[];
    subAdmins: any[];
    totalCount: number;
  }> {
    // Find admin and sub-admin roles
    const adminRole = await this.roleModel.findOne({ key: 'admin', category: 'Management' });
    const subAdminRole = await this.roleModel.findOne({ key: 'sub-admin', category: 'Management' });

    if (!adminRole || !subAdminRole) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.SYSTEMUSER,
        'Admin or Sub-Admin roles not found'
      );
    }

    // Find all global assignments for admin and sub-admin roles
    const globalAssignments = await this.userProcessAssignmentModel.find({
      roleId: { $in: [adminRole._id, subAdminRole._id] },
      organisationId: null,
      subOrganisationId: null,
      processId: null
    })
    .populate('userId', 'name email employeeId')
    .populate('roleId', 'key name')
    .lean();

    const admins: any[] = [];
    const subAdmins: any[] = [];

    for (const assignment of globalAssignments) {
      const user = assignment.userId as any;
      const role = assignment.roleId as any;

      if (!user || !role) continue;

      const globalUser = {
        _id: user._id.toString(),
        name: user.name,
        email: user.email,
        employeeId: user.employeeId || null,
        roleKey: role.key,
        roleName: role.name,
        assignedAt: (assignment as any).createdAt || new Date()
      };

      if (role.key === 'admin') {
        admins.push(globalUser);
      } else if (role.key === 'sub-admin') {
        subAdmins.push(globalUser);
      }
    }

    return {
      admins,
      subAdmins,
      totalCount: admins.length + subAdmins.length
    };
  }

  async getOrganisationsByIds(ids: string[]): Promise<{ id: string, name: string }[]> {
    // Import Organisation model at the top if not already
    // @InjectModel(Organisation.name) private organisationModel: Model<Organisation>,
    // But for now, let's use the existing injected model if available
    const organisations = await this.userProcessAssignmentModel.db.model('organisations').find({ _id: { $in: ids } }).lean();
    return organisations.map((org: any) => ({ id: org._id.toString(), name: org.name }));
  }

  async listProcessAssignmentsByCategory(processId: string) {
    return this.userProcessAssignmentModel.aggregate([
      { $match: { processId: new Types.ObjectId(processId) } },
      {
        $lookup: {
          from: 'roles',
          localField: 'roleId',
          foreignField: '_id',
          as: 'role'
        }
      },
      { $unwind: '$role' },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      // Normalize category and roleName for grouping
      {
        $addFields: {
          normalizedCategory: { $trim: { input: { $ifNull: ['$role.category', 'Uncategorized'] } } },
          normalizedRoleName: { $trim: { input: { $toLower: { $ifNull: ['$role.name', ''] } } } }
        }
      },
      {
        $group: {
          _id: { category: '$normalizedCategory', roleName: '$normalizedRoleName' },
          users: { $push: '$user.name' }
        }
      },
      {
        $group: {
          _id: '$_id.category',
          roles: {
            $push: {
              roleName: '$_id.roleName',
              users: '$users'
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          category: '$_id',
          roles: 1
        }
      }
    ]);
  }

  async listProcessAssignmentsByCategoryPopulate(processId: string, organisationId?: string, subOrganisationId?: string) {
    // 1. Find the hierarchy document
    const hierarchy = await this.processRoleHierarchyModel.findOne({
      orgId: organisationId,
      subOrgId: subOrganisationId,
      'process.processId': processId,
    }).lean();

    // Return default object with empty users array if not found or invalid
    if (!hierarchy || Array.isArray(hierarchy) || !hierarchy.process || !Array.isArray(hierarchy.process) || !hierarchy.process.length) {
      return {
        processId,
        isActive: false,
        users: [],
      };
    }
    const processArr = hierarchy.process;

    // 2. Find the process entry
    const processEntry = processArr.find(
      (p: any) => p.processId.toString() === processId.toString()
    );
    if (!processEntry || !processEntry.roles) {
      return {
        processId,
        isActive: false,
        users: [],
      };
    }

    const result: any[] = [];
    const roles = processEntry.roles;

    // Helper to collect userIds by role
    function collectRoleUsers(map: Record<string, string[]>, parentRole: string, childRole: string) {
      const parentUsers: Record<string, string[]> = {};
      for (const [parentId, childIds] of Object.entries(map || {})) {
        if (!parentUsers[parentRole]) parentUsers[parentRole] = [];
        parentUsers[parentRole].push(parentId);
        if (!parentUsers[childRole]) parentUsers[childRole] = [];
        parentUsers[childRole].push(...(childIds as string[]));
      }
      return parentUsers;
    }

    // 3. For each category, build the output
    for (const [categoryKey, categoryRolesRaw] of Object.entries(roles)) {
      const categoryRoles = categoryRolesRaw as any;
      if (categoryKey === 'operations') {
        const managerToSupervisors = categoryRoles.manager_to_supervisors || {};
        const supervisorToAgents = categoryRoles.supervisor_to_agents || {};

        const hierarchies: { managerId: string; supervisorId: string; agentId: string }[] = [];
        for (const [managerId, supervisorIds] of Object.entries(managerToSupervisors)) {
          for (const supervisorId of supervisorIds as string[]) {
            const agentIds = supervisorToAgents[supervisorId] || [];
            for (const agentId of agentIds as string[]) {
              hierarchies.push({ managerId, supervisorId, agentId });
            }
          }
        }

        // Populate user objects
        const allUserIds = new Set<string>();
        hierarchies.forEach(h => {
          allUserIds.add(h.managerId);
          allUserIds.add(h.supervisorId);
          allUserIds.add(h.agentId);
        });
        const users = await this.userModel.find(
          { _id: { $in: Array.from(allUserIds) } },
          'name employeeId email'
        ).lean();
        const userMap = Object.fromEntries(
          users.map((u: any) => [
            u._id.toString(),
            {
              id: u._id.toString(),
              name: u.name,
              employeeId: u.employeeId,
              email: u.email,
            }
          ])
        );

        result.push({
          category: categoryKey,
          hierarchies: hierarchies.map(h => ({
            manager: userMap[h.managerId] || { id: h.managerId },
            supervisor: userMap[h.supervisorId] || { id: h.supervisorId },
            agent: userMap[h.agentId] || { id: h.agentId },
          })),
        });
        continue;
      }
      if (categoryKey === 'audit') {
        const managerToSupervisors = categoryRoles.manager_to_supervisors || {};
        const supervisorToAgents = categoryRoles.supervisor_to_agents || {};
        const hierarchies: { qcManagerId: string; qcSupervisorId: string; qcAgentId: string }[] = [];
        for (const [qcManagerId, qcSupervisorIds] of Object.entries(managerToSupervisors)) {
          for (const qcSupervisorId of qcSupervisorIds as string[]) {
            const qcAgentIds = supervisorToAgents[qcSupervisorId] || [];
            for (const qcAgentId of qcAgentIds as string[]) {
              hierarchies.push({ qcManagerId, qcSupervisorId, qcAgentId });
            }
          }
        }
        // Populate user objects
        const allUserIds = new Set<string>();
        hierarchies.forEach(h => {
          allUserIds.add(h.qcManagerId);
          allUserIds.add(h.qcSupervisorId);
          allUserIds.add(h.qcAgentId);
        });
        const users = await this.userModel.find(
          { _id: { $in: Array.from(allUserIds) } },
          'name employeeId email'
        ).lean();
        const userMap = Object.fromEntries(
          users.map((u: any) => [
            u._id.toString(),
            {
              id: u._id.toString(),
              name: u.name,
              employeeId: u.employeeId,
              email: u.email,
            }
          ])
        );
        result.push({
          category: categoryKey,
          hierarchies: hierarchies.map(h => ({
            qcManager: userMap[h.qcManagerId] || { id: h.qcManagerId },
            qcSupervisor: userMap[h.qcSupervisorId] || { id: h.qcSupervisorId },
            qcAgent: userMap[h.qcAgentId] || { id: h.qcAgentId },
          })),
        });
        continue;
      }
      if (categoryKey === 'management') {
        const managerToSupervisors = categoryRoles.manager_to_supervisors || {};
        const hierarchies: { adminId: string; subAdminId: string }[] = [];
        for (const [adminId, subAdminIds] of Object.entries(managerToSupervisors)) {
          for (const subAdminId of subAdminIds as string[]) {
            hierarchies.push({ adminId, subAdminId });
          }
        }
        // Populate user objects
        const allUserIds = new Set<string>();
        hierarchies.forEach(h => {
          allUserIds.add(h.adminId);
          allUserIds.add(h.subAdminId);
        });
        const users = await this.userModel.find(
          { _id: { $in: Array.from(allUserIds) } },
          'name employeeId email'
        ).lean();
        const userMap = Object.fromEntries(
          users.map((u: any) => [
            u._id.toString(),
            {
              id: u._id.toString(),
              name: u.name,
              employeeId: u.employeeId,
              email: u.email,
            }
          ])
        );
        result.push({
          category: categoryKey,
          hierarchies: hierarchies.map(h => ({
            admin: userMap[h.adminId] || { id: h.adminId },
            subAdmin: userMap[h.subAdminId] || { id: h.subAdminId },
          })),
        });
        continue;
      }
      let roleUserMap: Record<string, string[]> = {};
      if (categoryRoles.manager_to_supervisors) {
        const map = collectRoleUsers(categoryRoles.manager_to_supervisors, 'Manager', 'Supervisor');
        for (const k in map) {
          if (!roleUserMap[k]) roleUserMap[k] = [];
          roleUserMap[k].push(...map[k]);
        }
      }
      if (categoryRoles.supervisor_to_agents) {
        const map = collectRoleUsers(categoryRoles.supervisor_to_agents, 'Supervisor', 'Agent');
        for (const k in map) {
          if (!roleUserMap[k]) roleUserMap[k] = [];
          roleUserMap[k].push(...map[k]);
        }
      }
      // Add other mappings as needed (QC, management, etc.)

      // Remove duplicates
      for (const k in roleUserMap) {
        roleUserMap[k] = Array.from(new Set(roleUserMap[k]));
      }

      // Populate user objects
      const rolesArr: { roleName: string; users: string[] }[] = [];
      for (const [roleName, userIds] of Object.entries(roleUserMap)) {
        const users = await this.userModel.find({ _id: { $in: userIds } }, 'name employeeId email').lean();
        rolesArr.push({
          roleName,
          users: users.map((u: any) => u.email),
        });
      }
      result.push({
        category: categoryKey,
        roles: rolesArr,
      });
    }

    return {
      processId: processEntry.processId,
      isActive: !!processEntry.isActive,
      users: result
    };
  }

  async getUnassignedUsers(orgId: string, subOrgId: string, processId: string) {
    // Find all userIds assigned to this process
    const assignedUserIds = await this.userProcessAssignmentModel
      .find({ organisationId: orgId, subOrganisationId: subOrgId, processId })
      .distinct('userId');

    // Find all users not in assignedUserIds
    const query: any = {};
    if (assignedUserIds.length > 0) {
      query._id = { $nin: assignedUserIds };
    }
    // Optionally, filter by org/suborg if User model has those fields
    // query.organisationId = orgId;
    // query.subOrganisationId = subOrgId;
    return this.userModel.find(query).lean();
  }

  async assignRoleHierarchy(input: AssignRoleHierarchyInput): Promise<boolean> {
    // 1. Ensure the document for orgId + subOrgId exists
    await this.processRoleHierarchyModel.updateOne(
      { orgId: input.organisationId, subOrgId: input.subOrganisationId },
      {
        $setOnInsert: {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          process: [],
          sla: [],
        }
      },
      { upsert: true }
    );

    // 2. Push process if not present
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": { $ne: input.processId }
      },
      {
        $push: {
          process: {
            processId: input.processId,
            isActive: true,
            roles: {
              operations: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              audit: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              management: {
                manager_to_supervisors: {},
              },
            }
          }
        }
      }
    );

    // 3. Fetch the current process object
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();
    if (!doc || Array.isArray(doc)) return true;
    if (!('process' in doc) || !Array.isArray((doc as any).process)) return true;
    const processArr = (doc as any).process;
    const processIndex = processArr.findIndex((p: any) => p.processId.toString() === input.processId.toString());
    if (processIndex === -1) return true;
    const processObj = processArr[processIndex];
    const roles = processObj.roles || {};
    const operations = roles.operations || {};

    // Merge supervisor for manager
    const manager_to_supervisors = { ...(operations.manager_to_supervisors || {}) };
    const currentSupervisors = new Set(manager_to_supervisors[input.managerId] || []);
    currentSupervisors.add(input.supervisorId);
    manager_to_supervisors[input.managerId] = Array.from(currentSupervisors);

    // Merge agent for supervisor
    const supervisor_to_agents = { ...(operations.supervisor_to_agents || {}) };
    const currentAgents = new Set(supervisor_to_agents[input.supervisorId] || []);
    currentAgents.add(input.agentId);
    supervisor_to_agents[input.supervisorId] = Array.from(currentAgents);

    // 4. Update the roles in the process array
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $set: {
          "process.$.roles.operations.manager_to_supervisors": manager_to_supervisors,
          "process.$.roles.operations.supervisor_to_agents": supervisor_to_agents
        }
      }
    );

    // 5. Ensure UserProcessAssignment for each user
    for (const userId of [input.managerId, input.supervisorId, input.agentId]) {
      const exists = await this.userProcessAssignmentModel.findOne({
        userId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
      if (!exists) {
        await this.userProcessAssignmentModel.create({
          userId,
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId
        });
      }
    }
    return true;
  }

  async removeRoleMapping(input: RemoveRoleMappingInput): Promise<boolean> {
    // 1. Remove from ProcessRoleHierarchy (pseudo-logic, adjust as needed)
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $pull: {
          ["process.$.roles.operations.manager_to_supervisors." + input.managerId]: input.supervisorId,
          ["process.$.roles.operations.supervisor_to_agents." + input.supervisorId]: input.agentId
        }
      }
    );

    // 2. For each user, check if they exist in any other mapping for the same org/suborg/process
    for (const [userId, roleKey, parentId] of [
      [input.agentId, "supervisor_to_agents", input.supervisorId],
      [input.supervisorId, "manager_to_supervisors", input.managerId]
    ]) {
      const stillMapped = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId,
        ["process.roles.operations." + roleKey + "." + parentId]: userId
      });
      if (!stillMapped) {
        await this.userProcessAssignmentModel.deleteOne({
          userId,
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId
        });
      }
    }
    // Manager: only remove if not referenced elsewhere (similar logic as above)
    return true;
  }

  async assignOperationsRole(input: AssignOperationsRoleInput, orgId?: string): Promise<boolean> {
    // Validation: Require at least one userId
    if (!isValidObjectId(input.managerId) || !isValidObjectId(input.supervisorId) || !isValidObjectId(input.agentId)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'Manager, Supervisor, and Agent are required fields. Please ensure all roles are selected before proceeding.'
      );
    }
    // Validation: All three IDs must be unique (if provided)
    const ids = [input.managerId, input.supervisorId, input.agentId].filter(Boolean);
    if (new Set(ids).size !== ids.length) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'Manager, Supervisor, and Agent must be assigned to different users.'
      );
    }

    // --- Cross-category and uniqueness checks ---
    const assignments = await this.userProcessAssignmentModel.find({
      organisationId: input.organisationId,
      subOrganisationId: input.subOrganisationId,
      processId: input.processId,
    }).lean();
    const allRoleIds = Array.from(new Set(assignments.map(a => a.roleId?.toString()).filter(Boolean)));
    const rolesOp = await this.roleModel.find({ _id: { $in: allRoleIds } }, 'key category').lean();
    const roleIdToKey = Object.fromEntries(rolesOp.map(r => [r._id.toString(), r.key]));
    const roleIdToCategory = Object.fromEntries(rolesOp.map(r => [r._id.toString(), r.category]));

    // Fetch the current process object early
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();

    let processObj;
    if (doc && !Array.isArray(doc) && Array.isArray(doc.process)) {
      const processArr = doc.process;
      const processIndex = processArr.findIndex((p: any) => p.processId.toString() === input.processId.toString());
      if (processIndex !== -1) {
        processObj = processArr[processIndex];
      }
    }

    // 1. User cannot exist in another category for this process
    for (const userId of [input.managerId, input.supervisorId, input.agentId]) {
      if (!userId) continue;
      const userAssignments = assignments.filter(a => a.userId.toString() === userId);
      const categories = new Set(userAssignments.map(a => roleIdToCategory[a.roleId?.toString()]));
      if (categories.size > 0 && !categories.has('Operation')) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'User is already assigned in another category for this process.'
        );
      }
      // New: user can't exist in another role in the same category
      let currentRole = '';
      let otherRoles: string[] = [];
      if (userId === input.managerId) {
        currentRole = 'manager';
        otherRoles = ['supervisor', 'agent'];
      } else if (userId === input.supervisorId) {
        currentRole = 'supervisor';
        otherRoles = ['manager', 'agent'];
      } else if (userId === input.agentId) {
        currentRole = 'agent';
        otherRoles = ['manager', 'supervisor'];
      }
      const keys = new Set(userAssignments.map(a => roleIdToKey[a.roleId?.toString()]));
      console.log(`Checking user ${userId} with currentRole ${currentRole} and otherRoles ${otherRoles.join(', ')}`);
      console.log("checking", keys, otherRoles);

      for (const key of keys) {
        if (otherRoles.includes(key)) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_INPUT,
            ErrorType.SYSTEMUSER,
            `User is already assigned as ${key} in Operation for this process.`
          );
        }
      }
    }
    // 3. Prevent supervisor from being assigned to multiple managers in operations
    if (input.supervisorId && processObj) {
      const opManagerToSupervisors = processObj?.roles?.operations?.manager_to_supervisors || {};
      for (const [mgrId, supervisors] of Object.entries(opManagerToSupervisors)) {
        if (
          mgrId !== input.managerId &&
          Array.isArray(supervisors) &&
          supervisors.includes(input.supervisorId)
        ) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_INPUT,
            ErrorType.SYSTEMUSER,
            'This supervisor is already assigned to another manager in Operations for this process.'
          );
        }
      }
    }
    // 2. Agent cannot be duplicated in operation/audit
    if (input.agentId) {
      const agentAssignments = assignments.filter(a => a.userId.toString() === input.agentId);
      const agentRoles = agentAssignments.map(a => roleIdToKey[a.roleId?.toString()]);
      if (agentRoles.includes('agent') || agentRoles.includes('qc-agent')) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'User is already assigned as Agent in this process.'
        );
      }
    }
    // 3. Prevent duplicate manager-supervisor pair in both operation and audit
    // if (input.managerId && input.supervisorId && processObj) {
    //   // Check if this manager-supervisor pair exists in audit hierarchy for this process
    //   const auditManagerToSupervisors = processObj?.roles?.audit?.manager_to_supervisors || {};
    //   const isPairInAudit = Array.isArray(auditManagerToSupervisors[input.managerId]) &&
    //     auditManagerToSupervisors[input.managerId].includes(input.supervisorId);
    //   if (isPairInAudit) {
    //     throw new Error(
    //       HttpStatus.BAD_REQUEST,
    //       ResponseCode.INVALID_INPUT,
    //       ErrorType.SYSTEMUSER,
    //       'This manager-supervisor pair already exists in Audit for this process.'
    //     );
    //   }
    // }

    // 3. Prevent supervisor from being assigned to multiple managers across operation and audit
    if (input.supervisorId && processObj) {
      // Check in audit hierarchy for assignOperationsRole
      const auditManagerToSupervisors = processObj?.roles?.audit?.manager_to_supervisors || {};
      let supervisorAssignedInAudit = false;
      for (const supervisors of Object.values(auditManagerToSupervisors)) {
        if (Array.isArray(supervisors) && supervisors.includes(input.supervisorId)) {
          supervisorAssignedInAudit = true;
          break;
        }
      }
      if (supervisorAssignedInAudit) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'This supervisor is already assigned to a manager in Audit for this process.'
        );
      }
    }

    // --- End checks ---

    // 1. Ensure the document for orgId + subOrgId exists
    await this.processRoleHierarchyModel.updateOne(
      { orgId: input.organisationId, subOrgId: input.subOrganisationId },
      {
        $setOnInsert: {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          process: [],
          sla: [],
        }
      },
      { upsert: true }
    );
    // 2. Push process if not present
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": { $ne: input.processId }
      },
      {
        $push: {
          process: {
            processId: input.processId,
            isActive: true,
            roles: {
              operations: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              audit: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              management: {
                manager_to_supervisors: {},
              },
            }
          }
        }
      }
    );

    const roles = processObj?.roles || {};
    const operations = roles.operations || {};
    // Merge supervisor for manager
    const manager_to_supervisors = { ...(operations.manager_to_supervisors || {}) };
    const currentSupervisors = new Set(manager_to_supervisors[input.managerId] || []);
    currentSupervisors.add(input.supervisorId);
    manager_to_supervisors[input.managerId] = Array.from(currentSupervisors);

    // Merge agent for supervisor
    const supervisor_to_agents = { ...(operations.supervisor_to_agents || {}) };
    const currentAgents = new Set(supervisor_to_agents[input.supervisorId] || []);
    currentAgents.add(input.agentId);
    supervisor_to_agents[input.supervisorId] = Array.from(currentAgents);

    // 4. Update the roles in the process array
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $set: {
          "process.$.roles.operations.manager_to_supervisors": manager_to_supervisors,
          "process.$.roles.operations.supervisor_to_agents": supervisor_to_agents
        }
      }
    );

    // 5. Ensure UserProcessAssignment for each user
    // Assign correct roleId for each user
    const operationsManagerRole = await this.roleModel.findOne({ key: 'manager' });
    const operationsSupervisorRole = await this.roleModel.findOne({ key: 'supervisor' });
    const operationsAgentRole = await this.roleModel.findOne({ key: 'agent' });
    if (!operationsManagerRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "manager" not found'
    );
    if (!operationsSupervisorRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "supervisor" not found'
    );
    if (!operationsAgentRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "agent" not found'
    );
    const roleMap = {
      [input.managerId]: operationsManagerRole._id,
      [input.supervisorId]: operationsSupervisorRole._id,
      [input.agentId]: operationsAgentRole._id,
    };
    const finalOrgId = input.orgId || orgId || '';
    for (const userId of [input.managerId, input.supervisorId, input.agentId]) {
      const exists = await this.userProcessAssignmentModel.findOne({
        userId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
      if (!exists) {
        await this.userProcessAssignmentModel.create({
          userId,
          roleId: roleMap[userId],
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId,
          orgId: finalOrgId,
        });
      }
    }
    return true;
  }
  async assignAuditRole(input: AssignAuditRoleInput, orgId?: string): Promise<boolean> {
    // Validation: Require at least one userId
    if (!isValidObjectId(input.qcManagerId) || !isValidObjectId(input.qcSupervisorId) || !isValidObjectId(input.qcAgentId)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'QC Manager, QC Supervisor, and QC Agent are required fields. Please ensure all roles are selected before proceeding.'
      );
    }
    // Validation: All three IDs must be unique (if provided)
    const ids = [input.qcManagerId, input.qcSupervisorId, input.qcAgentId].filter(isValidObjectId);
    if (new Set(ids).size !== ids.length) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'QC Manager, QC Supervisor, and QC Agent must be assigned to different users.'
      );
    }
    // Validation: Each provided ID must be a valid ObjectId
    const idFields = [
      { key: 'qcManagerId', value: input.qcManagerId },
      { key: 'qcSupervisorId', value: input.qcSupervisorId },
      { key: 'qcAgentId', value: input.qcAgentId }
    ];
    for (const { key, value } of idFields) {
      if (value && !isValidObjectId(value)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          `${key} is not a valid user.`
        );
      }
    }
    // --- Cross-category and uniqueness checks ---
    const assignments = await this.userProcessAssignmentModel.find({
      organisationId: input.organisationId,
      subOrganisationId: input.subOrganisationId,
      processId: input.processId,
    }).lean();
    const allRoleIds = Array.from(new Set(assignments.map(a => a.roleId?.toString()).filter(Boolean)));
    const rolesAudit = await this.roleModel.find({ _id: { $in: allRoleIds } }, 'key category').lean();
    const roleIdToKey = Object.fromEntries(rolesAudit.map(r => [r._id.toString(), r.key]));
    const roleIdToCategory = Object.fromEntries(rolesAudit.map(r => [r._id.toString(), r.category]));

    // 3. Fetch the current process object first
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();

    let processObj;
    if (doc && !Array.isArray(doc) && Array.isArray(doc.process)) {
      const processArr = doc.process;
      const processIndex = processArr.findIndex((p: any) => p.processId.toString() === input.processId.toString());
      if (processIndex !== -1) {
        processObj = processArr[processIndex];
      }
    }

    // 1. User cannot exist in another category for this process
    for (const userId of [input.qcManagerId, input.qcSupervisorId, input.qcAgentId]) {
      if (!userId) continue;
      const userAssignments = assignments.filter(a => a.userId.toString() === userId);
      const categories = new Set(userAssignments.map(a => roleIdToCategory[a.roleId?.toString()]));
      if (categories.size > 0 && !categories.has('Audit')) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'User is already assigned in another category for this process.'
        );
      }
      // New: user can't exist in another role in the same category
      let currentRole = '';
      let otherRoles: string[] = [];
      if (userId === input.qcManagerId) {
        currentRole = 'qc-manager';
        otherRoles = ['qc-supervisor', 'qc-agent'];
      } else if (userId === input.qcSupervisorId) {
        currentRole = 'qc-supervisor';
        otherRoles = ['qc-manager', 'qc-agent'];
      } else if (userId === input.qcAgentId) {
        currentRole = 'qc-agent';
        otherRoles = ['qc-manager', 'qc-supervisor'];
      }
      const keys = new Set(userAssignments.map(a => roleIdToKey[a.roleId?.toString()]));
      for (const key of keys) {
        if (otherRoles.includes(key)) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_INPUT,
            ErrorType.SYSTEMUSER,
            `User is already assigned as ${key} in Audit for this process.`
          );
        }
      }
    }
    // 2. QC-Agent cannot be duplicated in operation/audit
    if (input.qcAgentId) {
      const qcAgentAssignments = assignments.filter(a => a.userId.toString() === input.qcAgentId);
      const qcAgentRoles = qcAgentAssignments.map(a => roleIdToKey[a.roleId?.toString()]);
      if (qcAgentRoles.includes('agent') || qcAgentRoles.includes('qc-agent')) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'User is already assigned as Agent/QC-Agent in this process.'
        );
      }
    }
    // 3. Prevent duplicate qc-manager-qc-supervisor pair in both operation and audit
    if (input.qcManagerId && input.qcSupervisorId && processObj) {
      // Check if this qc-manager-qc-supervisor pair exists in operations hierarchy for this process
      const opManagerToSupervisors = processObj?.roles?.operations?.manager_to_supervisors || {};
      const isPairInOperations = Array.isArray(opManagerToSupervisors[input.qcManagerId]) &&
        opManagerToSupervisors[input.qcManagerId].includes(input.qcSupervisorId);
      if (isPairInOperations) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'This manager-supervisor pair already exists in Operations for this process.'
        );
      }
    }
    // 3. Prevent supervisor from being assigned to multiple managers across operation and audit
    if (input.qcSupervisorId && processObj) {
      // Check in operations hierarchy for assignAuditRole
      const opManagerToSupervisors = processObj?.roles?.operations?.manager_to_supervisors || {};
      let supervisorAssignedInOperations = false;
      for (const supervisors of Object.values(opManagerToSupervisors)) {
        if (Array.isArray(supervisors) && supervisors.includes(input.qcSupervisorId)) {
          supervisorAssignedInOperations = true;
          break;
        }
      }
      if (supervisorAssignedInOperations) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'This supervisor is already assigned to a manager in Operations for this process.'
        );
      }
    }
    // 3. Prevent qc-supervisor from being assigned to multiple qc-managers in audit
    if (input.qcSupervisorId && processObj) {
      const auditManagerToSupervisors = processObj?.roles?.audit?.manager_to_supervisors || {};
      for (const [mgrId, supervisors] of Object.entries(auditManagerToSupervisors)) {
        if (
          mgrId !== input.qcManagerId &&
          Array.isArray(supervisors) &&
          supervisors.includes(input.qcSupervisorId)
        ) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_INPUT,
            ErrorType.SYSTEMUSER,
            'This supervisor is already assigned to another manager in Audit for this process.'
          );
        }
      }
    }
    // --- End checks ---

    // 1. Ensure the document for orgId + subOrgId exists
    await this.processRoleHierarchyModel.updateOne(
      { orgId: input.organisationId, subOrgId: input.subOrganisationId },
      {
        $setOnInsert: {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          process: [],
          sla: [],
        }
      },
      { upsert: true }
    );
    // 2. Push process if not present
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": { $ne: input.processId }
      },
      {
        $push: {
          process: {
            processId: input.processId,
            isActive: true,
            roles: {
              operations: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              audit: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              management: {
                manager_to_supervisors: {},
              },
            }
          }
        }
      }
    );

    if (!processObj) return true;

    const roles = processObj.roles || {};
    const audit = roles.audit || {};
    // Merge supervisor for audit manager
    const manager_to_supervisors = { ...(audit.manager_to_supervisors || {}) };
    const currentSupervisors = new Set(manager_to_supervisors[input.qcManagerId] || []);
    currentSupervisors.add(input.qcSupervisorId);
    manager_to_supervisors[input.qcManagerId] = Array.from(currentSupervisors);

    // Merge agent for audit supervisor
    const supervisor_to_agents = { ...(audit.supervisor_to_agents || {}) };
    const currentAgents = new Set(supervisor_to_agents[input.qcSupervisorId] || []);
    currentAgents.add(input.qcAgentId);
    supervisor_to_agents[input.qcSupervisorId] = Array.from(currentAgents);

    // 4. Update the roles in the process array
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $set: {
          "process.$.roles.audit.manager_to_supervisors": manager_to_supervisors,
          "process.$.roles.audit.supervisor_to_agents": supervisor_to_agents
        }
      }
    );

    // Assign correct roleId for each user
    const auditManagerRole = await this.roleModel.findOne({ key: 'qc-manager' });
    const auditSupervisorRole = await this.roleModel.findOne({ key: 'qc-supervisor' });
    const auditAgentRole = await this.roleModel.findOne({ key: 'qc-agent' });
    if (!auditManagerRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "qc-manager" not found'
    );
    if (!auditSupervisorRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "qc-supervisor" not found'
    );
    if (!auditAgentRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "qc-agent" not found'
    );
    const roleMap = {
      [input.qcManagerId]: auditManagerRole._id,
      [input.qcSupervisorId]: auditSupervisorRole._id,
      [input.qcAgentId]: auditAgentRole._id,
    };
    const auditOrgId = input.orgId || orgId || '';
    for (const userId of [input.qcManagerId, input.qcSupervisorId, input.qcAgentId]) {
      const exists = await this.userProcessAssignmentModel.findOne({
        userId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
      if (!exists) {
        await this.userProcessAssignmentModel.create({
          userId,
          roleId: roleMap[userId],
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId,
          orgId: auditOrgId,
        });
      }
    }
    return true;
  }
  async assignManagementsRole(input: AssignManagementsRoleInput, orgId?: string): Promise<boolean> {
    // Validation: Require at least one userId
    if (!isValidObjectId(input.adminId) || !isValidObjectId(input.subAdminId)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'At least one of admin or subAdmin is required.'
      );
    }
    // Validation: Both IDs must be unique (if provided)
    const ids = [input.adminId, input.subAdminId].filter(isValidObjectId);
    if (new Set(ids).size !== ids.length) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_INPUT,
        ErrorType.SYSTEMUSER,
        'admin and subAdmin must be different.'
      );
    }
    // Validation: Each provided ID must be a valid ObjectId
    const idFields = [
      { key: 'adminId', value: input.adminId },
      { key: 'subAdminId', value: input.subAdminId }
    ];
    for (const { key, value } of idFields) {
      if (value && !isValidObjectId(value)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          `${key} is not a valid user.`
        );
      }
    }
    // --- Cross-category and uniqueness checks ---
    const assignments = await this.userProcessAssignmentModel.find({
      organisationId: input.organisationId,
      subOrganisationId: input.subOrganisationId,
      processId: input.processId,
    }).lean();
    const allRoleIds = Array.from(new Set(assignments.map(a => a.roleId?.toString()).filter(Boolean)));
    const rolesMgmt = await this.roleModel.find({ _id: { $in: allRoleIds } }, 'key category').lean();
    const roleIdToKey = Object.fromEntries(rolesMgmt.map(r => [r._id.toString(), r.key]));
    const roleIdToCategory = Object.fromEntries(rolesMgmt.map(r => [r._id.toString(), r.category]));
    // 1. User cannot exist in another category for this process
    for (const userId of [input.adminId, input.subAdminId]) {
      if (!userId) continue;
      const userAssignments = assignments.filter(a => a.userId.toString() === userId);
      const categories = new Set(userAssignments.map(a => roleIdToCategory[a.roleId?.toString()]));
      if (categories.size > 0 && !categories.has('Management')) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'User is already assigned in another category for this process.'
        );
      }
      // New: user can't exist in another role in the same category
      let currentRole = '';
      let otherRoles: string[] = [];
      if (userId === input.adminId) {
        currentRole = 'admin';
        otherRoles = ['sub-admin'];
      } else if (userId === input.subAdminId) {
        currentRole = 'sub-admin';
        otherRoles = ['admin'];
      }
      const keys = new Set(userAssignments.map(a => roleIdToKey[a.roleId?.toString()]));
      for (const key of keys) {
        if (otherRoles.includes(key)) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_INPUT,
            ErrorType.SYSTEMUSER,
            `User is already assigned as ${key} in Management for this process.`
          );
        }
      }
    }
    // 2. Prevent duplicate admin-subadmin pair in another category
    if (input.adminId && input.subAdminId) {
      const adminPairExists = assignments.some(a => a.userId.toString() === input.adminId && roleIdToKey[a.roleId?.toString()] === 'admin') &&
        assignments.some(b => b.userId.toString() === input.subAdminId && roleIdToKey[b.roleId?.toString()] === 'sub-admin');
      if (adminPairExists) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_INPUT,
          ErrorType.SYSTEMUSER,
          'This admin-subadmin pair already exists in another category for this process.'
        );
      }
    }
    // --- End checks ---

    // 1. Ensure the document for orgId + subOrgId exists
    await this.processRoleHierarchyModel.updateOne(
      { orgId: input.organisationId, subOrgId: input.subOrganisationId },
      {
        $setOnInsert: {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          process: [],
          sla: [],
        }
      },
      { upsert: true }
    );
    // 2. Push process if not present
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": { $ne: input.processId }
      },
      {
        $push: {
          process: {
            processId: input.processId,
            isActive: true,
            roles: {
              operations: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              audit: {
                manager_to_supervisors: {},
                supervisor_to_agents: {},
              },
              management: {
                manager_to_supervisors: {},
              },
            }
          }
        }
      }
    );
    // 3. Fetch the current process object
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();
    if (!doc || Array.isArray(doc)) return true;
    if (!Array.isArray(doc.process)) return true;
    const processArr = doc.process;
    const processIndex = processArr.findIndex((p: any) => p.processId.toString() === input.processId.toString());
    if (processIndex === -1) return true;
    const processObj = processArr[processIndex];
    const roles = processObj.roles || {};
    const management = roles.management || {};
    // Merge subAdmin for admin
    const manager_to_supervisors = { ...(management.manager_to_supervisors || {}) };
    const currentSubAdmins = new Set(manager_to_supervisors[input.adminId] || []);
    currentSubAdmins.add(input.subAdminId);
    manager_to_supervisors[input.adminId] = Array.from(currentSubAdmins);

    // 4. Update the roles in the process array
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $set: {
          "process.$.roles.management.manager_to_supervisors": manager_to_supervisors
        }
      }
    );

    // Assign correct roleId for each user
    const managementAdminRole = await this.roleModel.findOne({ key: 'admin' });
    const managementSubAdminRole = await this.roleModel.findOne({ key: 'sub-admin' });
    if (!managementAdminRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "admin" not found'
    );
    if (!managementSubAdminRole) throw new Error(
      HttpStatus.BAD_REQUEST,
      ResponseCode.INVALID_INPUT,
      ErrorType.SYSTEMUSER,
      'Role with key "sub-admin" not found'
    );
    const roleMap = {
      [input.adminId]: managementAdminRole._id,
      [input.subAdminId]: managementSubAdminRole._id,
    };
    const managementOrgId = input.orgId || orgId || '';
    for (const userId of [input.adminId, input.subAdminId]) {
      const exists = await this.userProcessAssignmentModel.findOne({
        userId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
      if (!exists) {
        await this.userProcessAssignmentModel.create({
          userId,
          roleId: roleMap[userId],
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId,
          orgId: managementOrgId,
        });
      }
    }
    return true;
  }

  async removeOperationsRole(input: RemoveOperationsRoleInput): Promise<boolean> {
    // Remove agent from supervisor_to_agents
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $pull: {
          ["process.$.roles.operations.supervisor_to_agents." + input.supervisorId]: input.agentId
        }
      }
    );
    // Clean up empty arrays in supervisor_to_agents and manager_to_supervisors
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();
    if (!doc || Array.isArray(doc)) return true;
    if (!Array.isArray(doc.process)) return true;
    const processObj = doc.process.find((p: any) => p.processId.toString() === input.processId.toString());
    const supervisorToAgents = processObj?.roles?.operations?.supervisor_to_agents || {};
    const managerToSupervisors = processObj?.roles?.operations?.manager_to_supervisors || {};
    // If supervisor has no more agents, remove supervisor from manager_to_supervisors
    if (supervisorToAgents[input.supervisorId]?.length === 0) {
      await this.processRoleHierarchyModel.updateOne(
        {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          "process.processId": input.processId
        },
        {
          $pull: {
            ["process.$.roles.operations.manager_to_supervisors." + input.managerId]: input.supervisorId
          }
        }
      );
      // After removing supervisor, check if manager has any supervisors left
      const updatedDoc = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      }).lean();
      if (
        updatedDoc &&
        !Array.isArray(updatedDoc) &&
        Object.prototype.hasOwnProperty.call(updatedDoc, 'process') &&
        typeof (updatedDoc as any).process === 'object' &&
        Array.isArray((updatedDoc as any).process)
      ) {
        const updatedProcessObj = (updatedDoc as any).process.find((p: any) => p.processId.toString() === input.processId.toString());
        const updatedManagerToSupervisors = updatedProcessObj?.roles?.operations?.manager_to_supervisors || {};
        if (
          Array.isArray(updatedManagerToSupervisors[input.managerId]) &&
          updatedManagerToSupervisors[input.managerId].length === 0
        ) {
          // Remove manager assignment if not referenced elsewhere
          await this.userProcessAssignmentModel.deleteOne({
            userId: input.managerId,
            organisationId: input.organisationId,
            subOrganisationId: input.subOrganisationId,
            processId: input.processId
          });
          // Optionally, also unset the manager key in the hierarchy
          await this.processRoleHierarchyModel.updateOne(
            {
              orgId: input.organisationId,
              subOrgId: input.subOrganisationId,
              "process.processId": input.processId
            },
            {
              $unset: {
                ["process.$.roles.operations.manager_to_supervisors." + input.managerId]: ""
              }
            }
          );
        }
      }
      // After removing supervisor, check if supervisor is still referenced anywhere
      const supervisorStillMapped = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId,
        ["process.roles.operations.manager_to_supervisors." + input.managerId]: input.supervisorId
      });
      if (!supervisorStillMapped) {
        await this.userProcessAssignmentModel.deleteOne({
          userId: input.supervisorId,
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId
        });
      }
    }
    // Remove agent from UserProcessAssignment if not referenced elsewhere
    const stillMapped = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId,
      ["process.roles.operations.supervisor_to_agents." + input.supervisorId]: input.agentId
    });
    if (!stillMapped) {
      await this.userProcessAssignmentModel.deleteOne({
        userId: input.agentId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
    }
    return true;
  }

  async removeAuditRole(input: RemoveAuditRoleInput): Promise<boolean> {
    // Remove agent from audit supervisor_to_agents
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $pull: {
          ["process.$.roles.audit.supervisor_to_agents." + input.qcSupervisorId]: input.qcAgentId
        }
      }
    );
    // Clean up empty arrays in supervisor_to_agents and manager_to_supervisors
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();
    if (!doc || Array.isArray(doc)) return true;
    if (!Array.isArray(doc.process)) return true;
    const processObj = doc.process.find((p: any) => p.processId.toString() === input.processId.toString());
    const supervisorToAgents = processObj?.roles?.audit?.supervisor_to_agents || {};
    // If supervisor has no more agents, remove supervisor from manager_to_supervisors
    if (Array.isArray(supervisorToAgents[input.qcSupervisorId]) && supervisorToAgents[input.qcSupervisorId].length === 0) {
      await this.processRoleHierarchyModel.updateOne(
        {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          "process.processId": input.processId
        },
        {
          $pull: {
            ["process.$.roles.audit.manager_to_supervisors." + input.qcManagerId]: input.qcSupervisorId
          }
        }
      );
      // After removing supervisor, check if manager has any supervisors left
      const updatedDoc = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      }).lean();
      if (
        updatedDoc &&
        !Array.isArray(updatedDoc) &&
        Object.prototype.hasOwnProperty.call(updatedDoc, 'process') &&
        typeof (updatedDoc as any).process === 'object' &&
        Array.isArray((updatedDoc as any).process)
      ) {
        const updatedProcessObj = (updatedDoc as any).process.find((p: any) => p.processId.toString() === input.processId.toString());
        const updatedManagerToSupervisors = updatedProcessObj?.roles?.audit?.manager_to_supervisors || {};
        if (
          Array.isArray(updatedManagerToSupervisors[input.qcManagerId]) &&
          updatedManagerToSupervisors[input.qcManagerId].length === 0
        ) {
          // Remove manager assignment if not referenced elsewhere
          await this.userProcessAssignmentModel.deleteOne({
            userId: input.qcManagerId,
            organisationId: input.organisationId,
            subOrganisationId: input.subOrganisationId,
            processId: input.processId
          });
          // Optionally, also unset the manager key in the hierarchy
          await this.processRoleHierarchyModel.updateOne(
            {
              orgId: input.organisationId,
              subOrgId: input.subOrganisationId,
              "process.processId": input.processId
            },
            {
              $unset: {
                ["process.$.roles.audit.manager_to_supervisors." + input.qcManagerId]: ""
              }
            }
          );
        }
      }
      // After removing supervisor, check if supervisor is still referenced anywhere
      const supervisorStillMapped = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId,
        ["process.roles.audit.manager_to_supervisors." + input.qcManagerId]: input.qcSupervisorId
      });
      if (!supervisorStillMapped) {
        await this.userProcessAssignmentModel.deleteOne({
          userId: input.qcSupervisorId,
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId
        });
      }
    }
    // Remove agent from UserProcessAssignment if not referenced elsewhere
    const stillMapped = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId,
      ["process.roles.audit.supervisor_to_agents." + input.qcSupervisorId]: input.qcAgentId
    });
    if (!stillMapped) {
      await this.userProcessAssignmentModel.deleteOne({
        userId: input.qcAgentId,
        organisationId: input.organisationId,
        subOrganisationId: input.subOrganisationId,
        processId: input.processId
      });
    }
    return true;
  }

  async removeManagementsRole(input: RemoveManagementsRoleInput): Promise<boolean> {
    // Remove subAdmin from management manager_to_supervisors
    await this.processRoleHierarchyModel.updateOne(
      {
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      },
      {
        $pull: {
          ["process.$.roles.management.manager_to_supervisors." + input.adminId]: input.subAdminId
        }
      }
    );
    // Clean up empty arrays in manager_to_supervisors
    const doc = await this.processRoleHierarchyModel.findOne({
      orgId: input.organisationId,
      subOrgId: input.subOrganisationId,
      "process.processId": input.processId
    }).lean();
    if (!doc || Array.isArray(doc)) return true;
    if (!Array.isArray(doc.process)) return true;
    const processObj = doc.process.find((p: any) => p.processId.toString() === input.processId.toString());
    const managerToSupervisors = processObj?.roles?.management?.manager_to_supervisors || {};
    // If admin has no more subAdmins, remove the admin key
    if (Array.isArray(managerToSupervisors[input.adminId]) && managerToSupervisors[input.adminId].length === 0) {
      await this.processRoleHierarchyModel.updateOne(
        {
          orgId: input.organisationId,
          subOrgId: input.subOrganisationId,
          "process.processId": input.processId
        },
        {
          $unset: {
            ["process.$.roles.management.manager_to_supervisors." + input.adminId]: ""
          }
        }
      );
      // After removing subAdmin, check if admin has any subAdmins left
      const updatedDoc = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId
      }).lean();
      if (
        updatedDoc &&
        !Array.isArray(updatedDoc) &&
        Object.prototype.hasOwnProperty.call(updatedDoc, 'process') &&
        typeof (updatedDoc as any).process === 'object' &&
        Array.isArray((updatedDoc as any).process)
      ) {
        const updatedProcessObj = (updatedDoc as any).process.find((p: any) => p.processId.toString() === input.processId.toString());
        const updatedManagerToSupervisors = updatedProcessObj?.roles?.management?.manager_to_supervisors || {};
        if (
          Array.isArray(updatedManagerToSupervisors[input.adminId]) &&
          updatedManagerToSupervisors[input.adminId].length === 0
        ) {
          // Remove admin assignment if not referenced elsewhere
          await this.userProcessAssignmentModel.deleteOne({
            userId: input.adminId,
            organisationId: input.organisationId,
            subOrganisationId: input.subOrganisationId,
            processId: input.processId
          });
        }
      }
      // After removing subAdmin, check if subAdmin is still referenced anywhere
      const subAdminStillMapped = await this.processRoleHierarchyModel.findOne({
        orgId: input.organisationId,
        subOrgId: input.subOrganisationId,
        "process.processId": input.processId,
        ["process.roles.management.manager_to_supervisors." + input.adminId]: input.subAdminId
      });
      if (!subAdminStillMapped) {
        await this.userProcessAssignmentModel.deleteOne({
          userId: input.subAdminId,
          organisationId: input.organisationId,
          subOrganisationId: input.subOrganisationId,
          processId: input.processId
        });
      }
    }
    return true;
  }

  /**
   * Returns users not assigned to excluded roles for a given org, suborg, process, category, and role.
   * @param orgId
   * @param subOrgId
   * @param processId
   * @param category (operations | audit | management)
   * @param role (role key, e.g. manager, supervisor, agent, qc-manager, etc.)
   */
  async getUnassignedUsersByRoleAndCategory(
    orgId: string,
    subOrgId: string,
    processId: string,
    category: 'operations' | 'audit' | 'management',
    role: string
  ) {
    // 1. Role exclusion map
    const roleExclusionMap: Record<string, Record<string, string[]>> = {
      operations: {
        manager: ['supervisor', 'agent'],
        supervisor: ['manager', 'agent'],
        agent: ['manager', 'supervisor', 'agent'],
      },
      audit: {
        'qc-manager': ['qc-supervisor', 'qc-agent'],
        'qc-supervisor': ['qc-manager', 'qc-agent'],
        'qc-agent': ['qc-manager', 'qc-supervisor', 'qc-agent'],
      },
      management: {
        admin: ['sub-admin'],
        'sub-admin': ['admin', 'sub-admin'],
      },
    };
    const excludedRoles = roleExclusionMap[category]?.[role] || [];

    // 2. Fetch all assignments for this org/suborg/process
    const assignments = await this.userProcessAssignmentModel.find({
      organisationId: orgId,
      subOrganisationId: subOrgId,
      processId,
    }).lean();
    if (assignments.length === 0) {
      // No assignments yet, return all users
      return this.userModel.find().lean();
    }
    // 3. Get all unique roleIds in these assignments
    const allRoleIds = Array.from(new Set(assignments.map(a => a.roleId?.toString()).filter(Boolean)));
    // 4. Fetch all roles in one query
    const roles = await this.roleModel.find({ _id: { $in: allRoleIds } }, 'key').lean();
    const roleIdToKey = Object.fromEntries(roles.map((r: any) => [r._id.toString(), r.key]));
    // 5. Find userIds assigned to excluded roles
    const excludedUserIds = new Set(
      assignments
        .filter(a => excludedRoles.includes(roleIdToKey[a.roleId?.toString()]))
        .map(a => a.userId.toString())
    );
    // 6. For roles that allow re-assigning the same user (e.g., manager can be assigned again as manager),
    //    you may want to allow users already assigned to this role. For now, we only exclude users in excluded roles.
    // 7. Return users not in excludedUserIds
    const users = await this.userModel.find({
      _id: { $nin: Array.from(excludedUserIds) }
    }).lean();
    return users;
  }


  async addNotificationToken(userId: string, token: string[]): Promise<boolean> {   

    const userNotificationToken = await this.userNotificationTokenModel.findOne({ userId });

    if (!userNotificationToken) {
      // Create new record with unique tokens
      const uniqueTokens = [...new Set(token)];
      await this.userNotificationTokenModel.create({ userId, token: uniqueTokens });
    } else {
      // Merge new tokens with existing, avoiding duplicates
      const existingTokens = userNotificationToken.token || [];
      const mergedTokens = Array.from(new Set([...existingTokens, ...token]));

      await this.userNotificationTokenModel.updateOne(
        { userId },
        { $set: { token: mergedTokens } }
      );
    }
    return true;
  }


  async deleteNotificationToken(userId: string, tokens: string[]): Promise<boolean> {
    const userNotificationToken = await this.userNotificationTokenModel.findOne({ userId });
    if (!userNotificationToken) {
      // Nothing to delete
      return true;
    }

    // Filter out the tokens to be removed
    userNotificationToken.token = (userNotificationToken.token || []).filter(
      (t) => !tokens.includes(t),
    );
    await userNotificationToken.save();
    return true;
  }

} 