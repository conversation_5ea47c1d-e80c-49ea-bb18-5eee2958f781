import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, Document } from 'mongoose';
import { Specialty } from './entities/specialty.entity';
import {
  CreateSpecialtyInput,
  UpdateSpecialtyInput,
  PaginateSpecialtyArgs,
  PaginatedSpecialtyResponse,
  SpecialtyFilterInput
} from './dto/specialty.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { instanceToPlain } from 'class-transformer';

type SpecialtyDocument = Specialty & Document;

@Injectable()
export class SpecialtyService {
  constructor(
    @InjectModel(Specialty.name)
    private specialtyModel: Model<SpecialtyDocument>,
  ) {}

  private transformDocument(doc: any): SpecialtyDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.SPECIALTY,
        'Cannot transform null document',
      );
    }
    
    const plainDoc = {
      _id: doc._id || new Types.ObjectId(),
      type: doc.type,
      name: doc.name || '',
      values: doc.values || {},
      templateId: doc.templateId || '',
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || '',
      updatedBy: doc.updatedBy || '',
    };
    
    return plainDoc as SpecialtyDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { name: searchRegex },
      { 'values.description': searchRegex },
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ templateId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ? 
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } : 
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }

  async findAll(input: PaginateSpecialtyArgs): Promise<PaginatedSpecialtyResponse> {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Get a sample document to extract dynamic fields
      const sampleDoc = await this.specialtyModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        projection = selectedFields;
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v', 'values', 'flattenedValues'];
        projection = allFields.reduce((acc, field) => {
          if (!excluded.includes(field)) acc[field] = 1;
          return acc;
        }, {} as Record<string, number>);

        // Search in all available fields (except excluded ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = allFields.filter(field => !excludedFromSearch.includes(field));
      }

       // === Search Handling ===
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.specialtyModel.countDocuments(query);
      // === Fetch Data ===
      const specialty = await this.specialtyModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: specialty,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.SPECIALTY, `Failed to fetch specialties: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string): Promise<SpecialtyDocument> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.SPECIALTY, 'Invalid ID format');
      }
      const doc = await this.specialtyModel.findById(id).lean().exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.SPECIALTY, 'Specialty not found');
      }
      return this.transformDocument(doc);
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.SPECIALTY, `Failed to find specialty: ${(err as globalThis.Error).message}`);
    }
  }

  async findByName(name: string): Promise<SpecialtyDocument | null> {
    try {
      const doc = await this.specialtyModel.findOne({ name }).lean().exec();
      return doc ? this.transformDocument(doc) : null;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.findByName:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.SPECIALTY, `Failed to find specialty by name: ${(err as globalThis.Error).message}`);
    }
  }

  async create(input: CreateSpecialtyInput): Promise<Success> {
    try {
      const existing = await this.findByName(input.name);
      if (existing) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.DUPLICATE_ENTRY,
          ErrorType.SPECIALTY,
          'Specialty with this name already exists'
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const specialtyData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
      };

      delete specialtyData.flattenedValues;

      const newSpecialty = new this.specialtyModel(specialtyData);
      const savedDoc = await newSpecialty.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.SPECIALTY,
        { specialty: this.transformDocument(savedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.create:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.SPECIALTY, `Failed to create specialty: ${(err as globalThis.Error).message}`);
    }
  }

  async update(input: UpdateSpecialtyInput): Promise<Success> {
    try {
      const { id,flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.SPECIALTY, 'Invalid specialty ID format for update.');
      }

      const existingSpecialty = await this.specialtyModel.findById(id).lean().exec();
      if (!existingSpecialty) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.SPECIALTY, 'Specialty not found for update.');
      }

      if (input.name) {
        const existingByName = await this.findByName(input.name);
        if (existingByName && existingByName._id.toString() !== id) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.SPECIALTY,
            'Specialty with this name already exists'
          );
        }
      }

      const updatePayload: any = { ...updateData, updatedAt: new Date() };
      if (input.isActive !== undefined) updatePayload.isActive = input.isActive;

      const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.specialtyModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      // const updatedDoc = await this.specialtyModel
      //   .findByIdAndUpdate(id, { $set: updatePayload }, { new: true })
      //   .lean()
      //   .exec();

      if (!updatedDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.SPECIALTY, 'Specialty not found during update process, or optimistic lock failed.');
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.SPECIALTY,
        { specialty: this.transformDocument(updatedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.update:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.SPECIALTY, `Failed to update specialty: ${(err as globalThis.Error).message}`);
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.SPECIALTY,
          'Invalid ID format'
        );
      }

      const deletedDoc = await this.specialtyModel
        .findByIdAndDelete(id)
        .lean()
        .exec();

      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.SPECIALTY,
          'Specialty not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.SPECIALTY,
        { message: 'Specialty deleted successfully.' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in SpecialtyService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.SPECIALTY,
        `Failed to delete specialty: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.specialtyModel.countDocuments().exec();
    } catch (err) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.SPECIALTY,
        `Failed to count specialties: ${(err as globalThis.Error).message}`,
      );
    }
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'name',
      'type',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'templateId'
    ];
  }

  getSearchableFields(): string[] {
    return ['name', 'values.description'];
  }
}