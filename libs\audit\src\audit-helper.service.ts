import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { KafkaService } from '@app/email';
import { MongoConnectionService } from '@app/db';
import { Kafka, Consumer, EachMessagePayload } from 'kafkajs';

export interface AuditEventData {
  eventType: 'CREATE' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT' | 'ROLE_ASSIGNMENT';
  recordType: string;
  beforeData?: any;
  afterData?: any;
  authoredInfo: {
    id: string;
    name: string;
    email: string;
  };
  sessionId?: string;
  notify?: boolean;
  notifyInfo?: {
    email?: {
      recipients?: string[];
      subject?: string;
      body?: string;
      templateId?: string;
    };
    socket?: {
      channels?: string[];
      message?: string;
    };
  };
}

@Injectable()
export class AuditHelperService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(AuditHelperService.name);
  private kafka: Kafka;
  private consumer: Consumer;

  constructor(
    private readonly kafkaService: KafkaService,
    private readonly mongoConnectionService: MongoConnectionService
  ) {
    // Initialize Kafka consumer for audit messages
    this.kafka = new Kafka({
      clientId: process.env.KAFKA_CLIENT_ID || 'audit-service',
      brokers: (process.env.KAFKA_BROKERS || '**************:9092').split(','),
      retry: {
        initialRetryTime: 300,
        retries: 5,
      },
    });

    this.consumer = this.kafka.consumer({
      groupId: process.env.KAFKA_AUDIT_CONSUMER_GROUP_ID || 'audit-consumer-group',
    });
  }

  /**
   * Single audit function for all operations - stores directly in database
   */
  async logAuditEvent(eventData: AuditEventData): Promise<void> {
    try {
      this.logger.log(`🔥 AUDIT HELPER CALLED: ${eventData.eventType} ${eventData.recordType}`);
      this.logger.log(`🔥 Event Data: ${JSON.stringify(eventData, null, 2)}`);
console.log('🔥 Event Data:', eventData.authoredInfo);

      const auditEvent = {
        event_type: eventData.eventType,
        record_type: eventData.recordType,
        data: {
          before: eventData.beforeData || null,
          after: eventData.afterData || null,
        },
        message: this.generateChangeMessages(eventData.eventType, eventData.beforeData, eventData.afterData,eventData.recordType),
        authored_info: eventData.authoredInfo,
        notify: eventData.notify || false,
        notify_info: eventData.notifyInfo || {
          email: {
            recipients: null,
            subject: "",
            body: "",
            template_id: ""
          },
          socket: {
            channels: null,
            message: ""
          }
        },
        session_id: eventData.sessionId || this.generateId(),
        event_timestamp: new Date().toISOString(),
        id: this.generateId(),
      };

      this.logger.log(`🔥 AUDIT EVENT CREATED: ${JSON.stringify(auditEvent, null, 2)}`);
console.log("🔥 AUDIT EVENT CREATED: ", auditEvent.authored_info);

      // Store directly in database
      await this.storeAuditEvent(auditEvent);

    } catch (error) {
      this.logger.error('🔥 FAILED TO LOG AUDIT EVENT:', error);
      // Don't throw - audit failures shouldn't break business operations
    }
  }

  /**
   * Store audit event by sending to Kafka
   */
  private async storeAuditEvent(auditEvent: any): Promise<void> {
    try {
      this.logger.log(`🔥 SENDING AUDIT EVENT TO KAFKA: ${auditEvent.id}`);

      const auditLogData = {
        auditId: auditEvent.id,
        userId: auditEvent.authored_info.id,
        orgId: auditEvent.authored_info.orgId || 'default-org',
        userEmail: auditEvent.authored_info.email,
        action: auditEvent.event_type,
        entityType: auditEvent.record_type,
        entityId: auditEvent.data.after?.id || auditEvent.data.before?.id,
        message: auditEvent.message, // Store as array
        beforeData: auditEvent.data.before,
        afterData: auditEvent.data.after,
        severity: this.mapSeverity(auditEvent.event_type),
        category: this.mapCategory(auditEvent.record_type),
        timestamp: new Date(auditEvent.event_timestamp),
        sessionId: auditEvent.session_id,
      };

      // Send to Kafka audit topic
      await this.sendToKafka(auditLogData);

    } catch (error) {
      this.logger.error('🔥 AUDIT STORAGE FAILED:', error);
      this.logger.error('🔥 STORAGE ERROR DETAILS:', error.stack);
    }
  }

  /**
   * Send audit data to Kafka
   */
  private async sendToKafka(auditLogData: any): Promise<void> {
    try {
      const auditTopic = process.env.KAFKA_AUDIT_TOPIC || 'audit-history';

      console.log(`🔥 AUDIT HELPER - SENDING TO KAFKA:`, JSON.stringify(auditLogData, null, 2));
      console.log(`🔥 KAFKA TOPIC: ${auditTopic}`);

      await this.kafkaService.publishToKafka(auditLogData, auditTopic);
      this.logger.log(`✅ AUDIT DATA SENT TO KAFKA TOPIC: ${auditTopic}`);
      console.log(`✅ AUDIT DATA SENT TO KAFKA TOPIC: ${auditTopic}`);

    } catch (kafkaError) {
      this.logger.error('❌ KAFKA AUDIT PUBLISH FAILED:', kafkaError);
      console.error('❌ KAFKA AUDIT PUBLISH FAILED:', kafkaError);
      // Fallback to console logging
      this.logger.log(`📊 FALLBACK AUDIT LOG: ${JSON.stringify(auditLogData, null, 2)}`);
    }
  }

  /**
   * Generate change messages for audit log
   */
  private generateChangeMessages(eventType: string, beforeData: any, afterData: any, recordType: any): string[] {
    const messages: string[] = [];
    const skipFields = ['id', '_id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy', '__v'];

    // Helper to compare and format changes recursively
    const compareFields = (before: any, after: any, prefix = '') => {
      const allKeys = new Set([
        ...Object.keys(before || {}),
        ...Object.keys(after || {})
      ]);
      for (const key of allKeys) {
        if (skipFields.includes(key)) continue;
        const oldValue = before ? before[key] : undefined;
        const newValue = after ? after[key] : undefined;
        const fieldName = prefix ? `${prefix} > ${this.formatFieldName(key)}` : this.formatFieldName(key);

        // Recursively compare objects (but not arrays)
        if (
          oldValue && newValue &&
          typeof oldValue === 'object' && typeof newValue === 'object' &&
          !Array.isArray(oldValue) && !Array.isArray(newValue)
        ) {
          compareFields(oldValue, newValue, fieldName);
        } else if (oldValue !== newValue) {
          messages.push(`${fieldName} changed from ${this.formatValue(oldValue)} to ${this.formatValue(newValue)}`);
        }
      }
    };

    if (eventType === 'CREATE') {
      if (afterData) {
        messages.push(`${recordType|| 'Record'} created successfully`);
      }
      return messages;
    }

    if (eventType === 'UPDATE') {
      compareFields(beforeData, afterData);
      if (messages.length === 0) messages.push('Record updated');
      return messages;
    }

    if (eventType === 'DELETE') {
      if (beforeData) {
        messages.push(`${recordType || 'Record'} deleted successfully`);
      }
      return messages;
    }

    // Other events
    messages.push(`${eventType} operation completed`);
    return messages;
  }

  /**
   * Format field name for display
   */
  private formatFieldName(fieldName: string): string {
    // Handle specific field mappings for better display names
    const fieldMappings: { [key: string]: string } = {
      'employeeId': 'Employee ID',
      'roleName': 'Role Name',
      'phoneNumber': 'Phone Number',
      'firstName': 'First Name',
      'lastName': 'Last Name',
      'isActive': 'Active Status',
      'orgId': 'Organization ID',
      'subOrgId': 'Sub Organization ID',
      'processId': 'Process ID',
      'roleId': 'Role ID'
    };

    // Check if we have a specific mapping
    if (fieldMappings[fieldName]) {
      return fieldMappings[fieldName];
    }

    // Default formatting for other fields
    return fieldName
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  /**
   * Format value for display (handles objects, null, undefined, arrays, dates)
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return 'null';
    }
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false';
    }
    if (typeof value === 'string') {
      return value;
    }
    if (typeof value === 'number') {
      return value.toString();
    }
    if (value instanceof Date) {
      return value.toISOString();
    }
    if (Array.isArray(value)) {
      return `[${value.map(v => this.formatValue(v)).join(', ')}]`;
    }
    if (typeof value === 'object') {
      // Try to show a short JSON representation
      const jsonStr = JSON.stringify(value);
      return jsonStr.length > 100 ? `${jsonStr.substring(0, 100)}...` : jsonStr;
    }
    return String(value);
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now().toString(36)}-${Math.random().toString(36).substring(2)}`;
  }

  /**
   * Map event type to severity
   */
  private mapSeverity(eventType: string): string {
    switch (eventType) {
      case 'DELETE':
        return 'HIGH';
      case 'ROLE_ASSIGNMENT':
        return 'MEDIUM';
      case 'LOGIN':
      case 'LOGOUT':
        return 'LOW';
      default:
        return 'MEDIUM';
    }
  }

  /**
   * Map record type to category
   */
  private mapCategory(recordType: string): string {
    switch (recordType.toLowerCase()) {
      case 'user':
        return 'USER';
      case 'role':
        return 'SECURITY';
      case 'system':
        return 'SYSTEM';
      default:
        return 'BUSINESS';
    }
  }

  /**
   * Initialize Kafka consumer on module init
   */
  async onModuleInit() {
    try {
      console.log('🔥 AUDIT HELPER - INITIALIZING KAFKA CONSUMER');

      await this.consumer.connect();
      const auditTopic = process.env.KAFKA_AUDIT_TOPIC || 'audit-history';
      await this.consumer.subscribe({ topic: auditTopic, fromBeginning: false });

      console.log(`🔥 AUDIT HELPER - SUBSCRIBED TO TOPIC: ${auditTopic}`);

      // Run the consumer
      await this.consumer.run({
        eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
          try {
            console.log(`🔥 AUDIT CONSUMER - MESSAGE RECEIVED FROM TOPIC: ${topic}`);

            const rawValue = message.value?.toString();
            if (!rawValue) {
              console.warn('⚠️ Empty audit message received');
              return;
            }

            const auditData = JSON.parse(rawValue);
            console.log(`🔥 AUDIT CONSUMER - PROCESSING MESSAGE:`, JSON.stringify(auditData, null, 2));

            // Store audit data in MongoDB
            await this.storeAuditInDatabase(auditData);

          } catch (err) {
            console.error('❌ Audit consumer message error:', err);
          }
        },
      });

      console.log('✅ AUDIT HELPER - KAFKA CONSUMER INITIALIZED SUCCESSFULLY');
    } catch (error) {
      console.error('❌ AUDIT HELPER - FAILED TO INITIALIZE KAFKA CONSUMER:', error);
    }
  }

  /**
   * Cleanup Kafka consumer on module destroy
   */
  async onModuleDestroy() {
    try {
      await this.consumer.disconnect();
      console.log('🛑 AUDIT HELPER - KAFKA CONSUMER DISCONNECTED');
    } catch (error) {
      console.error('❌ Error disconnecting audit consumer:', error);
    }
  }

  /**
   * Get audit logs collection by orgId
   */
  private async getAuditLogsCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('auditlogs', orgId || 'asp');
  }

  /**
   * Store audit data directly in MongoDB collection
   */
  private async storeAuditInDatabase(auditData: any): Promise<void> {
    try {
      console.log(`🔥 STORING AUDIT IN DATABASE:`, JSON.stringify(auditData, null, 2));

      // Validate required audit fields
      if (!auditData.auditId || !auditData.userId || !auditData.action) {
        console.warn('⚠️ Missing required audit fields:', auditData);
        return;
      }

      // Get audit logs collection
      const auditCollection = await this.getAuditLogsCollection();

      // Prepare audit document
      const auditDocument = {
        auditId: auditData.auditId,
        userId: auditData.userId,
        orgId: auditData.orgId || 'asp',
        userEmail: auditData.userEmail,
        action: auditData.action,
        entityType: auditData.entityType,
        entityId: auditData.entityId,
        message: auditData.message || [],
        beforeData: auditData.beforeData,
        afterData: auditData.afterData,
        severity: auditData.severity,
        category: auditData.category,
        timestamp: auditData.timestamp || new Date(),
        sessionId: auditData.sessionId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      console.log(`🔥 AUDIT DOCUMENT TO STORE:`, JSON.stringify(auditDocument, null, 2));

      // Insert audit document directly into MongoDB collection
      const result = await auditCollection.insertOne(auditDocument);

      console.log(`✅ AUDIT LOG STORED SUCCESSFULLY IN DATABASE: ${result.insertedId}`);

    } catch (error) {
      console.error('❌ Error storing audit in database:', error);

      // Fallback: Log to console if database storage fails
      console.log(`📊 FALLBACK AUDIT LOG: ${JSON.stringify(auditData, null, 2)}`);
    }
  }
}
