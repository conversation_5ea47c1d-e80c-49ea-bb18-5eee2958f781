import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { NotificationHistoryService } from '../services/notification-history.service';
import {
  GetNotificationHistoryInput,
  MarkNotificationReadInput,
  NotificationHistoryResponse,
  NotificationStatsResponse,
  MarkNotificationResponse
} from '../dto/notification-history-graphql.dto';
import { NotificationHistoryGraphQL } from '../entities/notification-history-graphql.entity';

@Resolver(() => NotificationHistoryGraphQL)
export class NotificationHistoryResolver {
  constructor(
    private readonly notificationHistoryService: NotificationHistoryService
  ) { }

  private mapToGraphQL(notification: any): NotificationHistoryGraphQL {
    return {
      _id: notification._id,
      notificationId: notification.notificationId,
      senderId: notification.senderId,
      userId: notification.userId,
      userEmail: notification.userEmail,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      channels: notification.channels,
      priority: notification.priority,
      status: notification.status,
      data: notification.data,
      metadata: notification.metadata,
      sentAt: notification.sentAt,
      deliveredAt: notification.deliveredAt,
      readAt: notification.readAt,
      orgId: notification.orgId,
      subOrgId: notification.subOrgId,
      processId: notification.processId,
      isRead: notification.isRead,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
      senderName: notification.senderName,
      senderEmail: notification.senderEmail,
      isRouted: notification.isRouted ?? false // Default to false if not set
    };
  }

  @Query(() => NotificationHistoryResponse, { name: 'notificationHistory' })
  async getNotificationHistory(
    @Args('input') input: GetNotificationHistoryInput,
    @Context() context: any
  ): Promise<NotificationHistoryResponse> {
    const userId = input.userId || context?.user?.userId || context?.userId;
    if (!userId) throw new Error('User ID is required');

    const orgId = input.orgId || context?.orgId;
    const subOrgId = input.subOrgId || context?.subOrgId;
    const processId = input.processId || context?.processId;

    const result = await this.notificationHistoryService.getNotificationHistory({
      ...input,
      userId,
      orgId,
      subOrgId,
      processId
    });

    return {
      ...result,
      notifications: result.notifications.map(n => this.mapToGraphQL(n))
    };
  }

  @Query(() => NotificationStatsResponse, { name: 'notificationStats' })
  async getNotificationStats(
    @Args('userId', { nullable: true }) userId?: string,
    @Context() context?: any
  ): Promise<NotificationStatsResponse> {
    const finalUserId = userId || context?.user?.userId || context?.userId;
    if (!finalUserId) throw new Error('User ID is required');

    const orgId = context?.orgId;
    const subOrgId = context?.subOrgId;
    const processId = context?.processId;

    return this.notificationHistoryService.getNotificationStats(
      finalUserId,
      orgId,
      subOrgId,
      processId
    );
  }

  @Mutation(() => MarkNotificationResponse, { name: 'markNotificationsAsRead' })
  async markNotificationsAsRead(
    @Args('input') input: MarkNotificationReadInput,
    @Context() context: any
  ): Promise<MarkNotificationResponse> {
    const userId = input.userId || context?.user?.userId || context?.userId;
    const orgId = context?.orgId;

    if (!userId) {
      throw new Error('User ID is required to mark notifications as read');
    }

    return this.notificationHistoryService.markNotificationsAsRead(
      { ...input, userId },
      orgId
    );
  }

  @Mutation(() => MarkNotificationResponse, { name: 'markAllNotificationsAsRead' })
  async markAllNotificationsAsRead(    
    @Context() context: any,
  ): Promise<MarkNotificationResponse> {
    const userId = context?.userId;
    const orgId = context?.orgId;
    const subOrgId = context?.suborgId;
    const processId = context?.processId;

    if (!userId) {
      throw new Error('❌ User ID is required to mark notifications as read.');
    }

    return this.notificationHistoryService.markAllNotificationsAsRead(      
      userId,
      orgId,     
    );
  }



  @Query(() => Number, { name: 'unreadNotificationCount' })
  async getUnreadNotificationCount(
    @Args('userId', { nullable: true }) userId?: string,
    @Context() context?: any
  ): Promise<number> {
    const finalUserId = userId || context?.user?.userId || context?.userId;
    if (!finalUserId) throw new Error('User ID is required');

    const orgId = context?.orgId;
    const subOrgId = context?.subOrgId;
    const processId = context?.processId;

    const stats = await this.notificationHistoryService.getNotificationStats(
      finalUserId,
      orgId,
      subOrgId,
      processId
    );

    return stats.unreadCount;
  }
}
