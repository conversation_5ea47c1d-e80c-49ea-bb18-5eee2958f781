import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, Document as MongooseDocument } from 'mongoose';
import { Cpt } from './entities/cpt.entity';
import { Specialty } from './entities/specialty.entity';
import { Icd } from './entities/icd.entity';
import { CptCode } from './entities/cpt-code.entity';
import { Diagnosis } from './entities/diagnosis.entity';
import { instanceToPlain } from 'class-transformer';

import {
  CreateCptInput,
  UpdateCptInput,
  PaginateCptArgs,
  PaginatedCptResponse,
} from './dto/cpt.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';

// Define Mongoose Document types
type CptDocument = Cpt & MongooseDocument;
type SpecialtyDocument = Specialty & MongooseDocument;
type IcdDocument = Icd & MongooseDocument;
type CptCodeDocument = CptCode & MongooseDocument;
type DiagnosisDocument = Diagnosis & MongooseDocument;

@Injectable()
export class MasterCptService {
  constructor(
    @InjectModel(Cpt.name)
    private masterCptModel: Model<CptDocument>,
    @InjectModel(Specialty.name)
    private specialtyModel: Model<SpecialtyDocument>,
    @InjectModel(Icd.name)
    private icdModel: Model<IcdDocument>,
    @InjectModel(CptCode.name)
    private cptCodeModel: Model<CptCodeDocument>,
    @InjectModel(Diagnosis.name)
    private diagnosisModel: Model<DiagnosisDocument>,
  ) { }

  private transformCptDocument(doc: any): CptDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Cannot transform null document',
      );
    }

    const plainDoc: Partial<Cpt> = {
      _id: doc._id || (doc.id ? new Types.ObjectId(doc.id) : new Types.ObjectId()),
      type: doc.type || '',
      values: doc.values || {},
      specialtyId: doc.specialtyId ? new Types.ObjectId(doc.specialtyId.toString()) : undefined,
      diagnosisId: doc.diagnosisId ? new Types.ObjectId(doc.diagnosisId.toString()) : undefined,
      cptCodeId: doc.cptCodeId ? new Types.ObjectId(doc.cptCodeId.toString()) : undefined,
      icdId: doc.icdId ? new Types.ObjectId(doc.icdId.toString()) : undefined,
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || undefined,
      updatedBy: doc.updatedBy || undefined,
    };

    if (doc.specialty) plainDoc.specialty = doc.specialty;
    if (doc.diagnosis) plainDoc.diagnosis = doc.diagnosis;
    if (doc.cptCode) plainDoc.cptCode = doc.cptCode;
    if (doc.icd) plainDoc.icd = doc.icd;

    return plainDoc as CptDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { type: searchRegex },
      { 'values.description': searchRegex }, // Assuming a common field
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ specialtyId: objectId });
      orConditions.push({ diagnosisId: objectId });
      orConditions.push({ cptCodeId: objectId });
      orConditions.push({ icdId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ?
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } :
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }

  async findAll(input: PaginateCptArgs): Promise<PaginatedCptResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Get a sample document to extract dynamic fields
      const sampleDoc = await this.masterCptModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        projection = selectedFields;
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v', 'values', 'flattenedValues'];
        projection = allFields.reduce((acc, field) => {
          if (!excluded.includes(field)) acc[field] = 1;
          return acc;
        }, {} as Record<string, number>);

        // Search in all available fields (except excluded ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = allFields.filter(field => !excludedFromSearch.includes(field));
      }

      // === Search Handling ===
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.masterCptModel.countDocuments(query);
      // === Fetch Data ===
      const specialty = await this.masterCptModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: specialty,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in MasterCptService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to fetch CPTs: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string) {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, 'Invalid ID format');
      }
      const doc = await this.masterCptModel.findById(id)
        .populate('specialty')
        .populate('diagnosis')
        .populate('cptCode')
        .populate('icd')
        .lean().exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT not found');
      }
      return doc;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in MasterCptService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to find CPT: ${(err as globalThis.Error).message}`);
    }
  }

  async create(input: CreateCptInput): Promise<Success> {
    try {
      const { specialtyId, diagnosisId, cptCodeId, icdId, createdBy } = input;

      if (!Types.ObjectId.isValid(specialtyId)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid Specialty ID format: ${specialtyId}`);
      }
      const specialty = await this.specialtyModel.findById(specialtyId).lean().exec();
      if (!specialty) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `Specialty not found with ID: ${specialtyId}`);
      }

      if (!Types.ObjectId.isValid(icdId)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid ICD ID format: ${icdId}`);
      }
      const icd = await this.icdModel.findById(icdId).lean().exec();
      if (!icd) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `ICD not found with ID: ${icdId}`);
      }

      if (!Types.ObjectId.isValid(cptCodeId)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid CPT Code ID format: ${cptCodeId}`);
      }
      const cptCodeDoc = await this.cptCodeModel.findById(cptCodeId).lean().exec();
      if (!cptCodeDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `CPT Code not found with ID: ${cptCodeId}`);
      }
      if (cptCodeDoc.specialtyId.toString() !== specialtyId) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `CPT Code's specialty (${cptCodeDoc.specialtyId}) does not match provided specialty (${specialtyId}).`);
      }

      if (!Types.ObjectId.isValid(diagnosisId)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid Diagnosis ID format: ${diagnosisId}`);
      }

      const diagnosisDoc = await this.diagnosisModel.findById(diagnosisId).lean().exec();

      if (!diagnosisDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `Diagnosis not found with ID: ${diagnosisId}`);
      }
      if (diagnosisDoc.icd.toString() !== icdId) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Diagnosis's ICD (${diagnosisDoc.icd || 'N/A'}) does not match provided ICD (${icdId}).`);
      }

      // Check for existing MasterCpt with same four IDs
      const existingCpt = await this.masterCptModel.findOne({
        specialtyId: specialtyId,
        diagnosisId: diagnosisId,
        cptCodeId: cptCodeId,
        icdId: icdId,
      }).lean().exec();

      if (existingCpt) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.ALREADY_EXISTS, ErrorType.CPT, 'A CPT with the same Specialty, Diagnosis, CPT Code, and ICD already exists.');
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const cptData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
      };

      delete cptData.flattenedValues;

      const newCpt = new this.masterCptModel(cptData);
      const savedDoc = await newCpt.save();
      const populatedDoc = await this.masterCptModel.findById(savedDoc._id)
        .populate('specialty')
        .populate('diagnosis')
        .populate('cptCode')
        .populate('icd')
        .lean().exec();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { cpt: this.transformCptDocument(populatedDoc as any) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in MasterCptService.create:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to create CPT: ${(err as globalThis.Error).message}`);
    }
  }

  async update(input: UpdateCptInput): Promise<Success> {
    try {
      const { id, specialtyId, diagnosisId, cptCodeId, icdId, flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, 'Invalid CPT ID format for update.');
      }

      const existingCpt = await this.masterCptModel.findById(id).lean().exec();
      if (!existingCpt) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT not found for update.');
      }

      const finalSpecialtyId = specialtyId || existingCpt.specialtyId?.toString();
      const finalIcdId = icdId || existingCpt.icdId?.toString();

      if (specialtyId) {
        if (!Types.ObjectId.isValid(specialtyId)) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid Specialty ID format: ${specialtyId}`);
        }
        const specialty = await this.specialtyModel.findById(specialtyId).lean().exec();
        if (!specialty) {
          throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `Specialty not found with ID: ${specialtyId}`);
        }
      }

      if (icdId) {
        if (!Types.ObjectId.isValid(icdId)) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid ICD ID format: ${icdId}`);
        }
        const icd = await this.icdModel.findById(icdId).lean().exec();
        if (!icd) {
          throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `ICD not found with ID: ${icdId}`);
        }
      }

      if (cptCodeId) {
        if (!Types.ObjectId.isValid(cptCodeId)) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid CPT Code ID format: ${cptCodeId}`);
        }
        const cptCodeDoc = await this.cptCodeModel.findById(cptCodeId).lean().exec();
        if (!cptCodeDoc) {
          throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `CPT Code not found with ID: ${cptCodeId}`);
        }
        if (cptCodeDoc.specialtyId.toString() !== finalSpecialtyId) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `CPT Code's specialty (${cptCodeDoc.specialtyId}) does not match effective specialty (${finalSpecialtyId}).`);
        }
      }

      if (diagnosisId) {
        if (!Types.ObjectId.isValid(diagnosisId)) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid Diagnosis ID format: ${diagnosisId}`);
        }
        const diagnosisDoc = await this.diagnosisModel.findById(diagnosisId).lean().exec();
        if (!diagnosisDoc) {
          throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `Diagnosis not found with ID: ${diagnosisId}`);
        }
        const diagnosisIcdId = diagnosisDoc.icd.toString() //diagnosisDoc.icd instanceof Types.ObjectId ? diagnosisDoc.icd.toString() : (diagnosisDoc.icd as any)?._id?.toString();       
        if (diagnosisIcdId !== finalIcdId) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Diagnosis's ICD (${diagnosisIcdId || 'N/A'}) does not match effective ICD (${finalIcdId}).`);
        }
      }

      const updatePayload: any = { ...updateData, updatedAt: new Date() };
      if (specialtyId) updatePayload.specialtyId = new Types.ObjectId(specialtyId);
      if (diagnosisId) updatePayload.diagnosisId = new Types.ObjectId(diagnosisId);
      if (cptCodeId) updatePayload.cptCodeId = new Types.ObjectId(cptCodeId);
      if (icdId) updatePayload.icdId = new Types.ObjectId(icdId);
      if (input.isActive !== undefined) updatePayload.isActive = input.isActive;

      const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.masterCptModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      // const updatedDoc = await this.masterCptModel
      //   .findByIdAndUpdate(id, { $set: updatePayload }, { new: true }) // Use $set for partial updates
      //   .populate('specialty')
      //   .populate('diagnosis')
      //   .populate('cptCode')
      //   .populate('icd')
      //   .lean()
      //   .exec();

      if (!updatedDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT not found during update process, or optimistic lock failed.');
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { cpt: this.transformCptDocument(updatedDoc as any) }, // Cast as it's lean
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in MasterCptService.update:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to update CPT: ${(err as globalThis.Error).message}`);
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CPT,
          'Invalid ID format',
        );
      }
      const deletedDoc = await this.masterCptModel.findByIdAndDelete(id).lean().exec();
      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.CPT,
          'CPT not found',
        );
      }
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { message: 'CPT deleted successfully' },
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in MasterCptService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        `Failed to delete CPT: ${(err as globalThis.Error).message}`,
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.masterCptModel.countDocuments().exec();
    } catch (err) {
      console.error('Unexpected error in MasterCptService.count:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        `Failed to count CPTs: ${(err as globalThis.Error).message}`,
      );
    }
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'type',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'specialtyId',
      'diagnosisId',
      'cptCodeId',
      'icdId'
    ];
  }

  getSearchableFields(): string[] {
    return ['type', 'values.description'];
  }
} 