import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { OrganisationSettings } from '../entities/organisation-settings.entity';
import { OrganisationSettingsService } from '../services/organisation-settings.service';
import { CreateOrganisationSettingsInput, UpdateOrganisationSettingsInput } from '../dto/organisation-settings.dto';
import { ConditionalPermissionsGuard, RequireConditionalPermission, createConditionalPermission, RequirePermission } from '@app/permissions';

@Resolver(() => OrganisationSettings)
export class OrganisationSettingsResolver {
  constructor(private readonly service: OrganisationSettingsService) {}

  @Mutation(() => OrganisationSettings)
  // @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   // Agent Portal - Main Organization
  //   createConditionalPermission(
  //     (input) => input.type === 'MAIN_ORGANISATION' || input.type === 'mainOrganisation',
  //     'Organizations',
  //     'Main Organization Settings',
  //     'Customize Settings'
  //   ),
  //   // Agent Portal - Sub Organization
  //   createConditionalPermission(
  //     (input) => input.type === 'SUB_ORGANISATION' || input.type === 'subOrganisation',
  //     'Organizations',
  //     'Sub Organization Settings',
  //     'Customize Settings'
  //   )
  // )
  // @RequirePermission(
  //   // Other Portal - Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async createOrganisationSettings(
    @Args('input') input: CreateOrganisationSettingsInput,
  ): Promise<OrganisationSettings> {
    return this.service.create(input);
  }

  @Query(() => [OrganisationSettings])
  // @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   // Agent Portal - Main Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'MAIN_ORGANISATION' || args.type === 'mainOrganisation',
  //     'Organizations',
  //     'Main Organization Settings',
  //     'Customize Settings'
  //   ),
  //   // Agent Portal - Sub Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'SUB_ORGANISATION' || args.type === 'subOrganisation',
  //     'Organizations',
  //     'Sub Organization Settings',
  //     'Customize Settings'
  //   )

  // )
  // @RequirePermission(
  //   // Other Portal - Organization Info View
  //   { module: 'organization', subModule: 'Organization Info', permission: 'View' },
  //   // Other Portal - Settings Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Settings Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async organisationSettingsList(
    @Args('type', { nullable: true }) type?: string,
  ): Promise<OrganisationSettings[]> {
    return this.service.findAll();
  }

  @Query(() => OrganisationSettings, { nullable: true })
  // @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   // Agent Portal - Main Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'MAIN_ORGANISATION' || args.type === 'mainOrganisation',
  //     'Organizations',
  //     'Main Organization Settings',
  //     'Customize Settings'
  //   ),
  //   // Agent Portal - Sub Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'SUB_ORGANISATION' || args.type === 'subOrganisation',
  //     'Organizations',
  //     'Sub Organization Settings',
  //     'Customize Settings'
  //   )
  // )
  // @RequirePermission(
  //   // Other Portal - Organization Info View
  //   { module: 'organization', subModule: 'Organization Info', permission: 'View' },
  //   // Other Portal - Settings Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Settings Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async organisationSettings(
    @Args('id', { type: () => ID }) id: string,
    @Args('type', { nullable: true }) type?: string,
  ): Promise<OrganisationSettings> {
    return this.service.findOne(id);
  }

  @Query(() => OrganisationSettings, { nullable: true })
  // @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   // Agent Portal - Main Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'MAIN_ORGANISATION' || args.type === 'mainOrganisation',
  //     'Organizations',
  //     'Main Organization Settings',
  //     'Customize Settings'
  //   ),
  //   // Agent Portal - Sub Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'SUB_ORGANISATION' || args.type === 'subOrganisation',
  //     'Organizations',
  //     'Sub Organization Settings',
  //     'Customize Settings'
  //   )
  // )
  // @RequirePermission(
  //   // Other Portal - Organization Info View
  //   { module: 'organization', subModule: 'Organization Info', permission: 'View' },
  //   // Other Portal - Settings Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Settings Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async organisationSettingsByOrganisationId(
    @Args('organisationId') organisationId: string,
    @Args('type', { nullable: true }) type?: string,
  ): Promise<OrganisationSettings | null> {
    return this.service.findByOrganisationId(organisationId);
  }

  @Mutation(() => OrganisationSettings)
  @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    // Agent Portal - Main Organization
    createConditionalPermission(
      (input) => input.type === 'MAIN_ORGANISATION' || input.type === 'mainOrganisation',
      'Organizations',
      'Main Organization Settings',
      'Customize Settings'
    ),
    // Agent Portal - Sub Organization
    createConditionalPermission(
      (input) => input.type === 'SUB_ORGANISATION' || input.type === 'subOrganisation',
      'Organizations',
      'Sub Organization Settings',
      'Customize Settings'
    )
  )
  // @RequirePermission(
  //   // Other Portal - Organization Info Edit
  //   { module: 'organization', subModule: 'Organization Info', permission: 'Edit' },
  //   // Other Portal - Settings Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Settings Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async updateOrganisationSettings(
    @Args('input') input: UpdateOrganisationSettingsInput,
  ): Promise<OrganisationSettings> {
    return this.service.updateByOrganisationId(input.id, input);
  }

  @Mutation(() => Boolean)
  // @UseGuards(ConditionalPermissionsGuard)
  // @RequireConditionalPermission(
  //   // Agent Portal - Main Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'MAIN_ORGANISATION' || args.type === 'mainOrganisation',
  //     'Organizations',
  //     'Main Organization Settings',
  //     'Customize Settings'
  //   ),
  //   // Agent Portal - Sub Organization
  //   createConditionalPermission(
  //     (args) => args.type === 'SUB_ORGANISATION' || args.type === 'subOrganisation',
  //     'Organizations',
  //     'Sub Organization Settings',
  //     'Customize Settings'
  //   )
  // )
  // @RequirePermission(
  //   // Other Portal - Organization Info Edit (for delete operations)
  //   { module: 'organization', subModule: 'Organization Info', permission: 'Edit' },
  //   // Other Portal - Settings Organization Login Portal
  //   { module: 'organization', subModule: 'Settings', permission: 'Organization Login Portal' },
  //   // Other Portal - Settings Access Rights
  //   { module: 'organization', subModule: 'Settings', permission: 'Access Rights' }
  // )
  async removeOrganisationSettings(
    @Args('id', { type: () => ID }) id: string,
    @Args('type', { nullable: true }) type?: string,
  ): Promise<boolean> {
    return this.service.remove(id);
  }
} 