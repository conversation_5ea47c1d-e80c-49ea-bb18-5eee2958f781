import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { User, UserSchema } from './entities/user.entity';
import { UserService } from './services/user.service';
import { UserResolver } from './resolvers/user.resolver';
import { Role, RoleSchema } from './entities/role.entity';
import { UserProcessAssignment, UserProcessAssignmentSchema } from './entities/user-process-assignment.entity';
import { Process, ProcessSchema } from './entities/process.entity';
import { OrganisationRole, OrganisationRoleSchema } from './entities/organisation-role.entity';
import { OrganisationRoleService } from './services/organisation-role.service';
import { OrganisationRoleResolver } from './resolvers/organisation-role.resolver';
import { ProcessRoleHierarchy, ProcessRoleHierarchySchema } from './entities/process-role-hierarchy.entity';
import { OrganisationUserResolver } from './resolvers/organisation-user.resolver';
import { OrganisationUserService } from './services/organisation-user.service';
import { OrganisationUser, OrganisationUserSchema } from './entities/organisation-user.entity';
import {UserNotificationToken, UserNotificationTokenSchema} from './entities/user-notification-token.entity';
import { AuditModule } from 'libs/audit/audit.module';
import {
  PermissionsModule,
  PermissionsGuard,
  ConditionalPermissionsGuard,
  RedisCacheService
} from '@app/permissions';
import { Reflector } from '@nestjs/core';
import { DatabaseModule, MongoConnectionService } from '@app/db';

// Create PermissionsGuard factory for UserModule
const userModulePermissionsGuardFactory = {
  provide: PermissionsGuard,
  useFactory: (
    reflector: Reflector,
    UserProcessAssignmentModel: any,
    RoleModel: any,
    OrganisationRoleModel: any,
    cacheService: RedisCacheService,
    mongoConnectionService: MongoConnectionService
  ) => {
    return new PermissionsGuard(
      reflector,
      async (userId, orgId, subOrgId, processId) => {
        // Build query dynamically to handle null values for global roles
        const query: any = { userId };

        if (orgId !== null) query.orgId = orgId;
        else query.organisationId = null;

        if (subOrgId !== null) query.subOrganisationId = subOrgId;
        else query.subOrganisationId = null;

        if (processId !== null) query.processId = processId;
        else query.processId = null;

        const result = await UserProcessAssignmentModel.findOne(query);
        return result;
      },
      {
        getSystemRole: async (roleId) => {
          const role = await RoleModel.findById(roleId);
          return role;
        },
        getOrganisationRole: async (roleId) => {
          const role = await OrganisationRoleModel.findById(roleId);
          return role;
        },
      },
      process.env.JWT_SECRET ?? 'fallback-secret',
      cacheService,
      mongoConnectionService
    );
  },
  inject: [
    Reflector,
    getModelToken(UserProcessAssignment.name),
    getModelToken(Role.name),
    getModelToken(OrganisationRole.name),
    RedisCacheService,
    MongoConnectionService,
  ],
};

@Module({
  imports: [
    DatabaseModule,
    PermissionsModule,
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Role.name, schema: RoleSchema },
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Process.name, schema: ProcessSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },
      { name: ProcessRoleHierarchy.name, schema: ProcessRoleHierarchySchema },
      {name:OrganisationUser.name, schema: OrganisationUserSchema},
      {name:UserNotificationToken.name, schema:UserNotificationTokenSchema}
    ]),
    AuditModule,
  ],
  providers: [
    UserService,
    UserResolver,
    OrganisationRoleService,
    OrganisationRoleResolver,
    OrganisationUserResolver,
    OrganisationUserService,
    userModulePermissionsGuardFactory,
    ConditionalPermissionsGuard,
  ],
  exports: [UserService, MongooseModule, OrganisationRoleService],
})
export class UserModule {}