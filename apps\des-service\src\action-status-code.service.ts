import { Injectable, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ActionCode, StatusCode, ActionStatusCodeMap } from './entities/action-status-code.entity';
import { BaseResponse } from './dto/base.response.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { CreateActionCodeInput, UpdateActionCodeInput, CreateStatusCodeInput, UpdateStatusCodeInput, CreateActionStatusCodeMapInput, UpdateActionStatusCodeMapInput, FindAllArgs, PaginatedActionCodesResponse, PaginatedStatusCodesResponse, PaginatedActionStatusCodeMapsResponse, FindAllAactions } from './dto/action-status-code.dto';
// import { ProcessService } from '../../identity/src/services/process.service';
// import { Process } from '../../identity/src/entities/process.entity';
import { instanceToPlain } from 'class-transformer';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class ActionStatusCodeService {
  constructor(
    @InjectModel(ActionCode.name) private readonly actionCodeModel: Model<ActionCode>,
    @InjectModel(StatusCode.name) private readonly statusCodeModel: Model<StatusCode>,
    @InjectModel(ActionStatusCodeMap.name) private readonly actionStatusCodeMapModel: Model<ActionStatusCodeMap>,
    private readonly mongoConnectionService: MongoConnectionService
  ) { }

  // private async populateProcess(item: any): Promise<any> {
  //   if (item && item.processId) {
  //     try {
  //       const process = await this.processService.findOne(item.processId);
  //       return { ...item, process: { id: process.id, name: process.name } };
  //     } catch (error) {
  //       // Process not found, return item without process
  //       return item;
  //     }
  //   }
  //   return item;
  // }

  // private buildResponse(items: any[], total: number, page: number, limit: number) {
  //   const totalItems = total;
  //   const totalPages = Math.ceil(totalItems / limit);
  //   const hasNext = page < totalPages;
  //   const hasPrev = page > 1;
  //   return {
  //     items,
  //     pagination: {
  //       page,
  //       limit,
  //       total,
  //       totalItems,
  //       totalPages,
  //       hasNext,
  //       hasPrev,
  //     },
  //   };
  // }

  // ===== PROCESS CONSTANT =====
  // getProcesses is removed

  // ===== STATUS CODE CRUD =====
  // Helper to get process collection
  private async getProcessCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('processes', orgId);
  }

  async createStatusCode(input: CreateStatusCodeInput, userId: string, orgId?: string): Promise<StatusCode> {
    try {
      const processCollection = await this.getProcessCollection(orgId);
      const process = await processCollection.findOne({ _id: typeof input.processId === 'string' ? new (require('mongodb').ObjectId)(input.processId) : input.processId });
      if (!process) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid processId');
      }

      const newStatusCode = new this.statusCodeModel({
        ...input,
        createdBy: userId,
        isActive: true
      });

      const savedStatusCode = await newStatusCode.save();
      return this.findStatusCodeById(savedStatusCode._id.toString());
    } catch (error) {
      if (error && error.code === 11000) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.ALREADY_EXISTS,
          ErrorType.VALIDATION,
          'StatusCode with this code already exists.'
        );
      }
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to create status code');
    }
  }

  async findAllStatusCodes(args: FindAllArgs): Promise<PaginatedStatusCodesResponse> {
    try {
      const { page = 1, limit = 10, search, filters, sortBy, sortOrder = 'asc' } = args;
      const query: any = {};

      // Search functionality
      if (search) {
        query.$or = [
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Filter functionality
      if (filters) {
        const parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : filters;
        Object.assign(query, parsedFilters);
      }

      // Sorting
      const sort: any = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // Pagination
      const skip = (page - 1) * limit;
      const [items, total] = await Promise.all([
        this.statusCodeModel.find(query).sort(sort).skip(skip).limit(limit).lean(),
        this.statusCodeModel.countDocuments(query)
      ]);

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch status codes');
    }
  }

  async findStatusCodeById(id: string): Promise<StatusCode> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid ID format');
      }
      const statusCode = await this.statusCodeModel.findById(id).lean();
      if (!statusCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'StatusCode not found');
      }
      return statusCode;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch status code');
    }
  }

  async updateStatusCode(input: UpdateStatusCodeInput, userId: string, orgId?: string): Promise<StatusCode> {
    try {
      const { id, ...updateData } = input;
      const statusCode = await this.statusCodeModel.findById(id);
      if (!statusCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'StatusCode not found');
      }

      if (updateData.processId) {
        const processCollection = await this.getProcessCollection(orgId);
        const process = await processCollection.findOne({ _id: typeof updateData.processId === 'string' ? new (require('mongodb').ObjectId)(updateData.processId) : updateData.processId });
        if (!process) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid processId');
        }
      }

      const updatedStatusCode = await this.statusCodeModel.findByIdAndUpdate(
        id,
        { ...updateData, updatedBy: userId },
        { new: true }
      );

      if (!updatedStatusCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'StatusCode not found during update');
      }

      return updatedStatusCode as StatusCode;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to update status code');
    }
  }

  async deleteStatusCode(id: string): Promise<BaseResponse> {
    try {
      const statusCode = await this.statusCodeModel.findById(id);
      if (!statusCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'StatusCode not found');
      }

      // Check if status code is used in any maps
      const mapCount = await this.actionStatusCodeMapModel.countDocuments({ statusCodeId: id });
      if (mapCount > 0) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Cannot delete status code that is used in maps');
      }

      await this.statusCodeModel.findByIdAndDelete(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'StatusCode deleted successfully' });
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to delete status code');
    }
  }

  // ===== ACTION CODE CRUD =====
  async createActionCode(input: CreateActionCodeInput, userId: string) {
    try {
      const statusCode = await this.statusCodeModel.findById(input.statusCodeId);
      if (!statusCode) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid statusCodeId');
      }
      const newActionCode = new this.actionCodeModel({ ...input, createdBy: userId, isActive: true });
      const savedActionCode = await newActionCode.save();
      return this.findActionCodeById(savedActionCode._id.toString());
    } catch (error) {
      if (error && error.code === 11000) {
        console.log('Error creating ActionCode:', error);
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.ALREADY_EXISTS,//'ActionCode with this code already exists.',//ResponseCode.ALREADY_EXISTS,
          ErrorType.VALIDATION,
          'ActionCode with this code already exists.'
        );
      }else{
      // if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to create action code');
      }}
  }
  
  async findAllActionCodes(args: FindAllArgs): Promise<PaginatedActionCodesResponse> {
    try {
      const { page = 1, limit = 10, search, filters, sort:sortBy, sortOrder = 'asc' } = args;
      const query: any = {};

      // Search functionality
      if (search) {
        query.$or = [
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Filter functionality
      if (filters) {
        const parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : filters;
        Object.assign(query, parsedFilters);
      }

      // Sorting
      const sort: any = {};
      if (sortBy) {
        sort[sortBy.field] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // Pagination
      const skip = (page - 1) * limit;
      const [items, total] = await Promise.all([
        this.actionCodeModel.find(query).sort(sort).skip(skip).limit(limit).populate('statusCodeId').lean(),
        this.actionCodeModel.countDocuments(query)
      ]);

      return {
        items,
        pagination: {
          page,
          limit,
          total,
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch action codes');
    }
  }

  async findActionCodeById(id: string): Promise<ActionCode> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid ID format');
      }
      const actionCode = await this.actionCodeModel.findById(id).populate('statusCodeId').lean();
      if (!actionCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionCode not found');
      }
      return actionCode;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch action code');
    }
  }

  async updateActionCode(input: UpdateActionCodeInput, userId: string): Promise<ActionCode> {
    try {
      const { id, ...updateData } = input;
      const actionCode = await this.actionCodeModel.findById(id);
      if (!actionCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionCode not found');
      }

      if (updateData.statusCodeId) {
        const statusCode = await this.statusCodeModel.findById(updateData.statusCodeId);
        if (!statusCode) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid statusCodeId');
        }
      }

      const updatedActionCode = await this.actionCodeModel.findByIdAndUpdate(
        id,
        { ...updateData, updatedBy: userId },
        { new: true }
      ).populate('statusCodeId');

      if (!updatedActionCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionCode not found during update');
      }

      return updatedActionCode as ActionCode;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to update action code');
    }
  }

  async deleteActionCode(id: string): Promise<BaseResponse> {
    try {
      const actionCode = await this.actionCodeModel.findById(id);
      if (!actionCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionCode not found');
      }

      // Check if action code is used in any maps
      const mapCount = await this.actionStatusCodeMapModel.countDocuments({ actionCodeId: id });
      if (mapCount > 0) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Cannot delete action code that is used in maps');
      }

      await this.actionCodeModel.findByIdAndDelete(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'ActionCode deleted successfully' });
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to delete action code');
    }
  }

  // ===== MAP CRUD =====
  async createActionStatusCodeMap(input: CreateActionStatusCodeMapInput, userId: string, orgId?: string): Promise<ActionStatusCodeMap> {
    try {
      const { processId, actionCodeId, statusCodeId, templateId } = input;

      // Validate process
      const processCollection = await this.getProcessCollection(orgId);
      const process = await processCollection.findOne({ _id: typeof processId === 'string' ? new (require('mongodb').ObjectId)(processId) : processId });
      if (!process) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, `Invalid processId: ${processId}`);
      }

      // Validate action code and status code
      const [actionCode, statusCode] = await Promise.all([
        this.actionCodeModel.findById(actionCodeId).lean(),
        this.statusCodeModel.findById(statusCodeId).lean(),
      ]);

      if (!actionCode) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, `Invalid actionCodeId: ${actionCodeId}`);
      }
      if (!statusCode) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, `Invalid statusCodeId: ${statusCodeId}`);
      }

      // Validate process matches
      if (statusCode.processId !== processId) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'The processId of the status code does not match the provided processId.');
      }

      // Validate action code status code matches
      const populatedActionCode = await this.actionCodeModel.findById(actionCodeId).populate('statusCodeId').lean();
      if (!populatedActionCode || !populatedActionCode.statusCodeId || (populatedActionCode.statusCodeId as any)._id.toString() !== statusCodeId) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'The provided statusCodeId does not match the statusCodeId associated with the actionCodeId.');
      }

      // Check for existing map
      const existingMap = await this.actionStatusCodeMapModel.findOne({ processId, actionCodeId, statusCodeId, templateId });
      if (existingMap) {
        throw new Error(HttpStatus.CONFLICT, ResponseCode.ALREADY_EXISTS, ErrorType.VALIDATION, 'A map with these parameters already exists');
      }

      // Prepare input data
      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const mapData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
        createdBy: userId,
        isActive: true,
      };

      delete mapData.flattenedValues;

      const newMap = new this.actionStatusCodeMapModel(mapData);

      const savedMap = await newMap.save();
      if (!savedMap) {
        throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, 'Failed to save action status code map');
      }

      const result = await this.findActionStatusCodeMapById(savedMap._id.toString());
      return result;
    } catch (error) {
      if (error && error.code === 11000) {
        // Duplicate key error (MongoDB)
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.ALREADY_EXISTS,
          ErrorType.VALIDATION,
          'An ActionStatusCodeMap with these parameters already exists.'
        );
      }
      console.error('Error in createActionStatusCodeMap:', error);
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to create action status code map');
    }
  }

  async findAllActionStatusCodeMaps(args: FindAllAactions): Promise<PaginatedActionStatusCodeMapsResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = args;

      const query: any = {};

      // Get a sample document to extract dynamic fields
      const sampleDoc = await this.actionStatusCodeMapModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        projection = selectedFields;
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v', 'values', 'flattenedValues'];
        projection = allFields.reduce((acc, field) => {
          if (!excluded.includes(field)) acc[field] = 1;
          return acc;
        }, {} as Record<string, number>);

        // Search in all available fields (except excluded ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = allFields.filter(field => !excludedFromSearch.includes(field));
      }

      // === Search Handling ===
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        let parsed: Record<string, any> = {};
        if (typeof filters === 'string') {
          const parsedObj = JSON.parse(filters);
          parsed = (parsedObj && typeof parsedObj === 'object' && 'filters' in parsedObj && typeof parsedObj.filters === 'object')
            ? parsedObj.filters
            : parsedObj;
        } else if (typeof filters === 'object' && filters !== null) {
          parsed = ('filters' in filters && typeof (filters as any).filters === 'object')
            ? (filters as any).filters
            : filters;
        }

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.actionStatusCodeMapModel.countDocuments(query);
      // === Fetch Data ===
      const specialty = await this.actionStatusCodeMapModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: specialty,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch action status code maps');
    }
  }

  async findActionStatusCodeMapById(id: string){
    try {
      const map = await this.actionStatusCodeMapModel
        .findById(id)
        // .populate({
        //   path: 'processId',
        //   model: 'Process',
        //   select: '_id name code'
        // })
        // .populate({
        //   path: 'actionCodeId',
        //   model: 'ActionCode',
        //   select: '_id code statusCodeId'
        // })
        // .populate({
        //   path: 'statusCodeId',
        //   model: 'StatusCode',
        //   select: '_id code processId'
        // })
        .lean()
        .exec();

      if (!map) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'Action status code map not found');
      }

      return map;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to fetch action status code map');
    }
  }

  async updateActionStatusCodeMap(input: UpdateActionStatusCodeMapInput, userId: string, orgId?: string): Promise<ActionStatusCodeMap> {
    try {
      const { id,flattenedValues, ...updateData } = input;
      const map = await this.actionStatusCodeMapModel.findById(id);
      if (!map) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionStatusCodeMap not found');
      }

      // Validate process if provided
      if (updateData.processId) {
        const processCollection = await this.getProcessCollection(orgId);
        const process = await processCollection.findOne({ _id: typeof updateData.processId === 'string' ? new (require('mongodb').ObjectId)(updateData.processId) : updateData.processId });
        if (!process) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid processId');
        }
      }

      // Validate action code if provided
      if (updateData.actionCodeId) {
        const actionCode = await this.actionCodeModel.findById(updateData.actionCodeId);
        if (!actionCode) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid actionCodeId');
        }
      }

      // Validate status code if provided
      if (updateData.statusCodeId) {
        const statusCode = await this.statusCodeModel.findById(updateData.statusCodeId);
        if (!statusCode) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid statusCodeId');
        }
      }

      const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.actionStatusCodeMapModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      const updatedMap = await this.actionStatusCodeMapModel.findByIdAndUpdate(
        id,
        { ...updateData, updatedBy: userId },
        { new: true }
      )
        .populate('processId')
        .populate('actionCodeId')
        .populate('statusCodeId');

      if (!updatedMap) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionStatusCodeMap not found during update');
      }

      return updatedMap;
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to update action status code map');
    }
  }

  async deleteActionStatusCodeMap(id: string): Promise<BaseResponse> {
    try {
      const map = await this.actionStatusCodeMapModel.findById(id);
      if (!map) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'ActionStatusCodeMap not found');
      }

      await this.actionStatusCodeMapModel.findByIdAndDelete(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'ActionStatusCodeMap deleted successfully' });
    } catch (error) {
      if (error instanceof Error) throw error;
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'Failed to delete action status code map');
    }
  }
}
