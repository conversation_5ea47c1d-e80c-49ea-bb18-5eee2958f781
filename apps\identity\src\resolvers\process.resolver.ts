import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { ProcessService } from '../services/process.service';
import { Process } from '../entities/process.entity';
import { CreateProcessInput, UpdateProcessInput, PaginatedProcessesResponse, FindAllArgs } from './../dto/process.dto';
// import { FindAllArgs } from '../../../des-service/src/dto/action-status-code.dto';
// import { BaseResponse } from '../../../des-service/src/dto/base.response.dto';
import { Success, Error as AppError, HttpStatus, ResponseCode, ErrorType } from '@app/error';
import { BaseResponse } from '../dto/base.response.dto';
import { RequirePermission } from '@app/permissions';

@Resolver(() => Process)
export class ProcessResolver {
  constructor(private readonly processService: ProcessService) {}

  @Mutation(() => Process)
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  createProcess(@Args('input') input: CreateProcessInput) {
    return this.processService.create(input);
  }

  @Query(() => BaseResponse, { name: 'processes' })
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  async findAllProcesses(@Args() args: FindAllArgs) {
    try {
        const result = await this.processService.findAll(args);
        return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, result);
    } catch (error: any) {
        if (error.response && error.response.responseCode) {
            return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
        }
        return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }

  @Query(() => Process, { name: 'process' })
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  findOneProcess(@Args('id', { type: () => ID }) id: string) {
    return this.processService.findOne(id);
  }

  @Mutation(() => Process)
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  updateProcess(@Args('input') input: UpdateProcessInput) {
    return this.processService.update(input);
  }

  @Mutation(() => Process)
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Process Settings', permission: 'Enable/Disable process' }
  // )
  removeProcess(@Args('id', { type: () => ID }) id: string) {
    return this.processService.remove(id);
  }
} 