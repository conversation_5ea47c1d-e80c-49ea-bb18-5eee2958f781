import { RemoteGraphQLDataSource } from '@apollo/gateway';
import { ApolloAuthContext } from '../types';
import { GatewayGraphQLRequest } from '@apollo/server-gateway-interface';
import { HEADER_KEYS } from '../config/vars';
import { empty } from '@app/functions';
import { validateRequest } from '../../application/middleware/validateRequest';
import { ErrorType, Error, ResponseCode, HttpStatus } from '@app/error';

export default class AuthenticatedDataSources extends RemoteGraphQLDataSource<ApolloAuthContext> {
  constructor(
    config:
      | (Partial<RemoteGraphQLDataSource<Record<string, unknown>>> & object)
      | undefined,
  ) {
    super(config);
  }

  async willSendRequest({
    request,
    context,
  }: {
    request: GatewayGraphQLRequest;
    context: ApolloAuthContext;
  }) {
    if (empty(context)) return;
    const headers =
      (context.req?.headers as Record<string, string | undefined>) ?? {};
    let tokens = context.req?.headers.authorization;
    const token = tokens ? tokens.replace('Bearer ', '') : '';
    const orgId = context.req?.headers.orgId  as string;
    if (!empty(orgId)) {
      request.http?.headers.set('orgId', orgId);
      return;
    }
    for (const [key, value] of Object.entries(headers)) {
      if (
        HEADER_KEYS.AUTHENTICATED_DATA_SOURCE.includes(key) &&
        typeof value === 'string'
      ) {
        request.http?.headers.set(key, value);
      }
    }
    if (!token)
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.LOGIN,
      );
    const result = await validateRequest(token); 


    if( result && result.orgId) {
      request.http?.headers.set('orgid', result.orgId);
    } 
    
    if (!empty(result)) {
      request.http?.headers.set('userid', result.userId);
    }
     else {
     
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.UNAUTHORIZED,
        ErrorType.LOGIN,
      );
    }
  }
}
