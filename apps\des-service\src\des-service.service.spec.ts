import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { HttpException } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { DesServiceService } from './des-service.service';
import { Form } from './entities/form.entity';
import { Template } from './entities/template.entity';
import { CreateFormInput, UpdateFormInput, GetFormVersionInput } from './dto/form.dto';
import { CreateTemplateInput, UpdateTemplateInput, FindAllTemplatesArgs } from './dto/template.dto';

// Mock @app/error module
class MockSuccess {
  constructor(
    public code: number,
    public message: string,
    public type: string,
    public data: any
  ) {}
}

const mockHttpStatus = { OK: 200, CREATED: 201, NOT_FOUND: 404 };
const mockResponseCode = { SUCCESS: 'SUCCESS', USER_NOT_FOUND: 'USER_NOT_FOUND' };
const mockErrorType = { FORM: 'FORM' };

jest.mock('@app/error', () => ({
  Success: MockSuccess,
  HttpStatus: mockHttpStatus,
  ResponseCode: mockResponseCode,
  ErrorType: mockErrorType,
}));

describe('DesServiceService', () => {
  let service: DesServiceService;
  let formModel: Model<Form>;
  let templateModel: Model<Template>;

  // Mock data
  const mockObjectId = new Types.ObjectId();
  const mockForm = {
    _id: mockObjectId,
    name: 'Test Form',
    description: 'Test Description',
    formId: 'test-form-001',
    fields: [{ type: 'text', label: 'Name' }],
    currentVersion: 1,
    status: 'active',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'test-user',
    save: jest.fn().mockResolvedValue(this),
    toObject: jest.fn().mockReturnValue({
      _id: mockObjectId,
      name: 'Test Form',
      description: 'Test Description',
      formId: 'test-form-001',
      fields: [{ type: 'text', label: 'Name' }],
      currentVersion: 1,
      status: 'active',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'test-user',
    }),
  };

  const mockTemplate = {
    _id: mockObjectId,
    name: 'Test Template',
    description: 'Test Template Description',
    templateId: 'test-template-001',
    value: [{ type: 'text', label: 'Name' }],
    status: 'active',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'test-user',
    save: jest.fn().mockResolvedValue(this),
    toObject: jest.fn().mockReturnValue({
      _id: mockObjectId,
      name: 'Test Template',
      description: 'Test Template Description',
      templateId: 'test-template-001',
      value: [{ type: 'text', label: 'Name' }],
      status: 'active',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'test-user',
    }),
  };

  // Mock model methods
  const mockFormModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
  };

  const mockTemplateModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findOne: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DesServiceService,
        {
          provide: getModelToken('Form'),
          useValue: mockFormModel,
        },
        {
          provide: getModelToken('Template'),
          useValue: mockTemplateModel,
        },
      ],
    }).compile();

    service = module.get<DesServiceService>(DesServiceService);
    formModel = module.get<Model<Form>>(getModelToken('Form'));
    templateModel = module.get<Model<Template>>(getModelToken('Template'));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Form Operations', () => {
    describe('findAllForms', () => {
      it('should return paginated forms with default parameters', async () => {
        const mockForms = [mockForm];
        const mockTotal = 1;

        mockFormModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue(mockForms),
              }),
            }),
          }),
        });
        mockFormModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockTotal),
        });

        const result = await service.findAllForms();

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.message).toBe(ResponseCode.SUCCESS);
        expect(result.data.forms).toEqual(mockForms);
        expect(result.data.pagination).toEqual({
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        });
      });

      it('should handle search functionality', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          search: 'test',
        };

        mockFormModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue([mockForm]),
              }),
            }),
          }),
        });
        mockFormModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllForms(args);

        expect(mockFormModel.find).toHaveBeenCalledWith({
          isActive: true,
          $or: [
            { name: { $regex: 'test', $options: 'i' } },
            { description: { $regex: 'test', $options: 'i' } },
            { formId: { $regex: 'test', $options: 'i' } },
            { status: { $regex: 'test', $options: 'i' } },
          ],
        });
      });

      it('should handle filters', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          filters: { status: 'active', name: 'test' },
        };

        mockFormModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue([mockForm]),
              }),
            }),
          }),
        });
        mockFormModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllForms(args);

        expect(mockFormModel.find).toHaveBeenCalledWith({
          isActive: true,
          status: { $regex: 'active', $options: 'i' },
          name: { $regex: 'test', $options: 'i' },
        });
      });

      it('should handle sorting', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          sort: { field: 'name', ascending: true },
        };

        const mockSortChain = {
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockForm]),
            }),
          }),
        };

        mockFormModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue(mockSortChain),
        });
        mockFormModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllForms(args);

        expect(mockFormModel.find().sort).toHaveBeenCalledWith({ name: 1 });
      });

      it('should handle errors', async () => {
        mockFormModel.find.mockImplementation(() => {
          throw new Error('Database error');
        });

        await expect(service.findAllForms()).rejects.toThrow(HttpException);
      });
    });

    describe('findFormById', () => {
      it('should return a form by ID', async () => {
        mockFormModel.findById.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockForm),
        });

        const result = await service.findFormById(mockObjectId.toString());

        expect(result).toBeInstanceOf(Success);
        expect(result.data.form).toEqual(mockForm);
        expect(mockFormModel.findById).toHaveBeenCalledWith(mockObjectId.toString());
      });

      it('should throw HttpException when form not found', async () => {
        mockFormModel.findById.mockReturnValue({
          exec: jest.fn().mockResolvedValue(null),
        });

        await expect(service.findFormById('nonexistent-id')).rejects.toThrow(HttpException);
      });

      it('should handle database errors', async () => {
        mockFormModel.findById.mockReturnValue({
          exec: jest.fn().mockRejectedValue(new Error('Database error')),
        });

        await expect(service.findFormById(mockObjectId.toString())).rejects.toThrow(HttpException);
      });
    });

    describe('createForm', () => {
      it('should create a new form successfully', async () => {
        const createFormInput: CreateFormInput = {
          name: 'Test Form',
          description: 'Test Description',
          formId: 'test-form-001',
          fields: [{ type: 'text', label: 'Name' }],
          status: 'active',
          createdBy: 'test-user',
        };

        // Mock the constructor and save method
        const mockFormInstance = {
          ...mockForm,
          save: jest.fn().mockResolvedValue(mockForm),
        };

        // Mock the model constructor
        (formModel as any).mockImplementation(() => mockFormInstance);
        mockFormModel.constructor = jest.fn().mockReturnValue(mockFormInstance);

        // Create a spy for the service's formModel
        const formModelSpy = jest.spyOn(service as any, 'formModel', 'get').mockReturnValue({
          ...mockFormModel,
          constructor: jest.fn().mockImplementation(() => mockFormInstance),
        });

        const result = await service.createForm(createFormInput);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.CREATED);
        expect(result.data.form).toBeDefined();

        formModelSpy.mockRestore();
      });

      it('should handle errors during form creation', async () => {
        const createFormInput: CreateFormInput = {
          name: 'Test Form',
          description: 'Test Description',
          templateId: 'test-template-001',
          fields: [{ type: 'text', label: 'Name' }],
          createdBy: 'test-user',
        };

        const mockFormInstance = {
          save: jest.fn().mockRejectedValue(new Error('Database error')),
        };

        const formModelSpy = jest.spyOn(service as any, 'formModel', 'get').mockReturnValue({
          ...mockFormModel,
          constructor: jest.fn().mockImplementation(() => mockFormInstance),
        });

        await expect(service.createForm(createFormInput)).rejects.toThrow();

        formModelSpy.mockRestore();
      });
    });

    describe('updateForm', () => {
      it('should update a form successfully', async () => {
        const updateFormInput: UpdateFormInput = {
          id: mockObjectId.toString(),
          name: 'Updated Form',
          value: { type: 'text', label: 'Updated Name' },
        };

        mockFormModel.findById.mockResolvedValue(mockForm);
        mockFormModel.findByIdAndUpdate.mockResolvedValue({
          ...mockForm,
          name: 'Updated Form',
          description: 'Updated Description',
        });

        const result = await service.updateForm(updateFormInput);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(mockFormModel.findById).toHaveBeenCalledWith(updateFormInput.id);
        expect(mockFormModel.findByIdAndUpdate).toHaveBeenCalledWith(
          updateFormInput.id,
          updateFormInput,
          { new: true }
        );
      });

      it('should throw error when form not found for update', async () => {
        const updateFormInput: UpdateFormInput = {
          id: 'nonexistent-id',
          name: 'Updated Form',
        };

        mockFormModel.findById.mockResolvedValue(null);

        await expect(service.updateForm(updateFormInput)).rejects.toThrow();
      });
    });

    describe('deleteForm', () => {
      it('should delete a form successfully', async () => {
        mockTemplateModel.findById.mockResolvedValue(mockForm);
        mockFormModel.findByIdAndDelete.mockResolvedValue(mockForm);

        const result = await service.deleteForm(mockObjectId.toString());

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.message).toBe('Form and all versions deleted successfully');
      });

      it('should throw error when form not found for deletion', async () => {
        mockTemplateModel.findById.mockResolvedValue(null);

        await expect(service.deleteForm('nonexistent-id')).rejects.toThrow();
      });
    });

    describe('getForm', () => {
      it('should get form successfully', async () => {
        const getFormVersionInput: GetFormVersionInput = {
          formId: mockObjectId.toString(),
        };

        mockFormModel.findById.mockResolvedValue(mockForm);

        const result = await service.getForm(getFormVersionInput);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.form).toEqual(mockForm);
      });

      it('should throw error when form not found', async () => {
        const getFormVersionInput: GetFormVersionInput = {
          formId: 'nonexistent-id',
        };

        mockFormModel.findById.mockResolvedValue(null);

        await expect(service.getForm(getFormVersionInput)).rejects.toThrow();
      });
    });
  });

  describe('Template Operations', () => {
    describe('createTemplate', () => {
      it('should create a new template successfully', async () => {
        const createTemplateInput: CreateTemplateInput = {
          name: 'Test Template',
          description: 'Test Template Description',
          templateId: 'test-template-001',
          value: [{ type: 'text', label: 'Name' }],
          status: 'active',
          createdBy: 'test-user',
        };

        mockTemplateModel.findOne.mockResolvedValue(null); // No existing template

        const mockTemplateInstance = {
          ...mockTemplate,
          save: jest.fn().mockResolvedValue(mockTemplate),
        };

        const templateModelSpy = jest.spyOn(service as any, 'templateModel', 'get').mockReturnValue({
          ...mockTemplateModel,
          constructor: jest.fn().mockImplementation(() => mockTemplateInstance),
          findOne: mockTemplateModel.findOne,
        });

        const result = await service.createTemplate(createTemplateInput);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.CREATED);
        expect(result.data?.template).toBeDefined();

        templateModelSpy.mockRestore();
      });

      it('should throw conflict error when template ID already exists', async () => {
        const createTemplateInput: CreateTemplateInput = {
          name: 'Test Template',
          description: 'Test Template Description',
          templateId: 'existing-template-001',
          value: [{ type: 'text', label: 'Name' }],
          status: 'active',
          createdBy: 'test-user',
        };

        mockTemplateModel.findOne.mockResolvedValue(mockTemplate); // Existing template

        await expect(service.createTemplate(createTemplateInput)).rejects.toThrow(HttpException);
      });
    });

    describe('findAllTemplates', () => {
      it('should return paginated templates with default parameters', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
        };

        const mockTemplates = [mockTemplate];
        const mockTotal = 1;

        mockTemplateModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue(mockTemplates),
              }),
            }),
          }),
        });
        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockTotal),
        });

        const result = await service.findAllTemplates(args);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.templates).toEqual(mockTemplates);
        expect(result.data?.pagination).toEqual({
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        });
      });

      it('should handle search functionality for templates', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          search: 'test',
        };

        mockTemplateModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue([mockTemplate]),
              }),
            }),
          }),
        });
        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllTemplates(args);

        expect(mockTemplateModel.find).toHaveBeenCalledWith({
          isActive: true,
          $or: [
            { name: { $regex: 'test', $options: 'i' } },
            { description: { $regex: 'test', $options: 'i' } },
            { templateId: { $regex: 'test', $options: 'i' } },
            { status: { $regex: 'test', $options: 'i' } },
          ],
        });
      });

      it('should handle template filters', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          filters: { status: 'active', name: 'test' },
        };

        mockTemplateModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                exec: jest.fn().mockResolvedValue([mockTemplate]),
              }),
            }),
          }),
        });
        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllTemplates(args);

        expect(mockTemplateModel.find).toHaveBeenCalledWith({
          isActive: true,
          status: { $regex: 'active', $options: 'i' },
          name: { $regex: 'test', $options: 'i' },
        });
      });

      it('should handle template sorting', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          sort: { field: 'name', ascending: false },
        };

        const mockSortChain = {
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockTemplate]),
            }),
          }),
        };

        mockTemplateModel.find.mockReturnValue({
          sort: jest.fn().mockReturnValue(mockSortChain),
        });
        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(1),
        });

        await service.findAllTemplates(args);

        expect(mockTemplateModel.find().sort).toHaveBeenCalledWith({ name: -1 });
      });

      it('should handle errors in findAllTemplates', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
        };

        mockTemplateModel.find.mockImplementation(() => {
          throw new Error('Database error');
        });

        await expect(service.findAllTemplates(args)).rejects.toThrow(HttpException);
      });
    });

    describe('findTemplateById', () => {
      it('should return a template by ID', async () => {
        mockTemplateModel.findById.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockTemplate),
        });

        const result = await service.findTemplateById(mockObjectId.toString());

        expect(result).toBeInstanceOf(Success);
        expect(result.data?.template).toEqual(mockTemplate);
        expect(mockTemplateModel.findById).toHaveBeenCalledWith(mockObjectId.toString());
      });

      it('should throw HttpException when template not found', async () => {
        mockTemplateModel.findById.mockReturnValue({
          exec: jest.fn().mockResolvedValue(null),
        });

        await expect(service.findTemplateById('nonexistent-id')).rejects.toThrow(HttpException);
      });

      it('should handle database errors', async () => {
        mockTemplateModel.findById.mockReturnValue({
          exec: jest.fn().mockRejectedValue(new Error('Database error')),
        });

        await expect(service.findTemplateById(mockObjectId.toString())).rejects.toThrow(HttpException);
      });
    });

    describe('updateTemplate', () => {
      it('should update a template successfully', async () => {
        const updateTemplateInput: UpdateTemplateInput = {
          id: mockObjectId.toString(),
          name: 'Updated Template',
          description: 'Updated Description',
        };

        mockTemplateModel.findById.mockResolvedValue(mockTemplate);
        mockTemplateModel.findByIdAndUpdate.mockResolvedValue({
          ...mockTemplate,
          name: 'Updated Template',
          description: 'Updated Description',
        });

        const result = await service.updateTemplate(updateTemplateInput);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(mockTemplateModel.findById).toHaveBeenCalledWith(updateTemplateInput.id);
        expect(mockTemplateModel.findByIdAndUpdate).toHaveBeenCalledWith(
          updateTemplateInput.id,
          { name: 'Updated Template', description: 'Updated Description' },
          { new: true }
        );
      });

      it('should throw error when template not found for update', async () => {
        const updateTemplateInput: UpdateTemplateInput = {
          id: 'nonexistent-id',
          name: 'Updated Template',
        };

        mockTemplateModel.findById.mockResolvedValue(null);

        await expect(service.updateTemplate(updateTemplateInput)).rejects.toThrow(HttpException);
      });

      it('should handle update errors', async () => {
        const updateTemplateInput: UpdateTemplateInput = {
          id: mockObjectId.toString(),
          name: 'Updated Template',
        };

        mockTemplateModel.findById.mockResolvedValue(mockTemplate);
        mockTemplateModel.findByIdAndUpdate.mockRejectedValue(new Error('Database error'));

        await expect(service.updateTemplate(updateTemplateInput)).rejects.toThrow(HttpException);
      });
    });

    describe('deleteTemplate', () => {
      it('should soft delete a template successfully', async () => {
        mockTemplateModel.findById.mockResolvedValue(mockTemplate);
        mockTemplateModel.findByIdAndUpdate.mockResolvedValue({
          ...mockTemplate,
          isActive: false,
        });

        const result = await service.deleteTemplate(mockObjectId.toString());

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.message).toBe('Template deleted successfully');
        expect(mockTemplateModel.findByIdAndUpdate).toHaveBeenCalledWith(
          mockObjectId.toString(),
          { isActive: false }
        );
      });

      it('should throw error when template not found for deletion', async () => {
        mockTemplateModel.findById.mockResolvedValue(null);

        await expect(service.deleteTemplate('nonexistent-id')).rejects.toThrow(HttpException);
      });

      it('should handle deletion errors', async () => {
        mockTemplateModel.findById.mockResolvedValue(mockTemplate);
        mockTemplateModel.findByIdAndUpdate.mockRejectedValue(new Error('Database error'));

        await expect(service.deleteTemplate(mockObjectId.toString())).rejects.toThrow(HttpException);
      });
    });

    describe('getTemplateCount', () => {
      it('should return template count without filters', async () => {
        const mockCount = 5;

        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockCount),
        });

        const result = await service.getTemplateCount();

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.count).toBe(mockCount);
        expect(mockTemplateModel.countDocuments).toHaveBeenCalledWith({ isActive: true });
      });

      it('should return template count with filters', async () => {
        const mockCount = 3;
        const filters = { status: 'active', name: 'test' };

        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockCount),
        });

        const result = await service.getTemplateCount(filters);

        expect(result).toBeInstanceOf(Success);
        expect(result.code).toBe(HttpStatus.OK);
        expect(result.data?.count).toBe(mockCount);
        expect(mockTemplateModel.countDocuments).toHaveBeenCalledWith({
          isActive: true,
          status: 'active',
          name: 'test',
        });
      });

      it('should handle count errors', async () => {
        mockTemplateModel.countDocuments.mockReturnValue({
          exec: jest.fn().mockRejectedValue(new Error('Database error')),
        });

        await expect(service.getTemplateCount()).rejects.toThrow(HttpException);
      });
    });
  });
});
