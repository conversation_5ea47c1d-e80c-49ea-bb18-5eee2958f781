import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { Document, Types } from 'mongoose';
@ObjectType()
@Schema({ timestamps: true })
export class ProviderHistory extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String)
  @Prop({ required: true, ref: 'ProviderTicket' })
  providerId: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  comments: string;

  @Field()
  @Prop({ required: true })
  follow_date: string;

  @Field({ nullable: true })
  @Prop({ default: null })
  exception_type?: string;

  @Field({ nullable: true })
  @Prop({ type: Number, default: null })
  touch_count?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object, default: null })
  values?: Record<string, any>;

  @Field({ nullable: true })
  @Prop({ default: null })
  start_time?: string;

  @Field({ nullable: true })
  @Prop({ default: null })
  end_time?: string;

  @Field({ nullable: true })
  @Prop({ default: null })
  updated_by?: string;

  @Field(() => Boolean)
  @Prop({ required: true })
  status: boolean;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}

export const ProviderHistorySchema = SchemaFactory.createForClass(ProviderHistory);
