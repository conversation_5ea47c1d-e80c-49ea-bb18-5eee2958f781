#!/bin/bash

APP_NAME=$1
CLASS_NAME="${APP_NAME^}"  # Capitalize first letter
ENTITY_NAME="User"
ENTITY_FILE="user.entities"

if [ -z "$APP_NAME" ]; then
  echo "Usage: ./generate-microservice.sh <app-name>"
  exit 1
fi

# Step 1: Generate Nest app
nest g app $APP_NAME

# Step 2: Change directory to the new app
cd apps/$APP_NAME/src || exit

# Step 3: Create supporting folders
mkdir -p dto entities interfaces

# Step 4: Create DTO
cat > dto/create-${APP_NAME}.dto.ts <<EOL
import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class Create${CLASS_NAME}Dto {
  @Field()
  name: string;
}
EOL

# Step 5: Create Entity
cat > entities/${ENTITY_FILE}.ts <<EOL
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Schema } from 'mongoose';

@ObjectType()
export class ${ENTITY_NAME} {
  @Field(() => ID)
  id: string;

  @Field()
  name: string;
}

export const ${ENTITY_NAME}Schema = new Schema({
  name: { type: String, required: true },
});
EOL

# Step 6: Create Interface
cat > interfaces/${APP_NAME}.interface.ts <<EOL
export interface I${CLASS_NAME} {
  id: string;
  name: string;
}
EOL

# Step 7: Create Resolver
cat > ${APP_NAME}.resolver.ts <<EOL
import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { ${ENTITY_NAME} } from './entities/${ENTITY_FILE}';
import { Create${CLASS_NAME}Dto } from './dto/create-${APP_NAME}.dto';

@Resolver(() => ${ENTITY_NAME})
export class ${CLASS_NAME}Resolver {
  @Query(() => [${ENTITY_NAME}])
  getAll${CLASS_NAME}() {
    return [];
  }

  @Mutation(() => ${ENTITY_NAME})
  create${CLASS_NAME}(@Args('input') input: Create${CLASS_NAME}Dto): ${ENTITY_NAME} {
    return { id: '1', name: input.name };
  }
}
EOL

# Step 8: Overwrite the default module with Federation/Mongoose version
cat > ${APP_NAME}.module.ts <<EOL
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import {
  ApolloFederationDriver,
  ApolloFederationDriverConfig,
} from '@nestjs/apollo';
import { ${CLASS_NAME}Controller } from './${APP_NAME}.controller';
import { ${CLASS_NAME}Service } from './${APP_NAME}.service';
import { ${CLASS_NAME}Resolver } from './${APP_NAME}.resolver';
import { ${ENTITY_NAME}Schema } from './entities/${ENTITY_FILE}';
import { DatabaseModule } from '@app/db';

@Module({
  imports: [
    DatabaseModule,
    MongooseModule.forFeature([{ name: '${ENTITY_NAME}', schema: ${ENTITY_NAME}Schema }]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req }) => ({
        query: req?.body?.query,
      }),
    }),
  ],
  controllers: [${CLASS_NAME}Controller],
  providers: [${CLASS_NAME}Service, ${CLASS_NAME}Resolver],
})
export class ${CLASS_NAME}Module {}
EOL


echo " Microservice '$APP_NAME' generated and customized:"
echo " DTO, entity, interface, resolver"
echo " Modified module with Mongoose + GraphQL Federation"
