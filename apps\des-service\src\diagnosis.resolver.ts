import { Resolver, Query, Mutation, Args, ID, Context, } from '@nestjs/graphql';
import { DiagnosisService } from './diagnosis.service';
import { Diagnosis } from './entities/diagnosis.entity';
import {
  CreateDiagnosisInput,
  UpdateDiagnosisInput,
  PaginateDiagnosisArgs,
  PaginatedDiagnosisResponse,
  GetDiagnosisInput,
  SuccessResponse,
} from './dto/diagnosis.dto';
import { HttpStatus, ErrorType, ResponseCode } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { RequirePermission } from '@app/permissions';
import { UseGuards } from '@nestjs/common';

@Resolver(() => Diagnosis)
export class DiagnosisResolver {
  constructor(private readonly diagnosisService: DiagnosisService) {}

  @Query(() => PaginatedDiagnosisResponse, { name: 'diagnoses' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getDiagnosesWithPagination(
    @Args('input', { nullable: true }) input?: GetDiagnosisInput,
    @Context() context?: any
  ) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters;

    // Get the authorization token from the context if available
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.diagnosisService.findAll({
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      selectedFields,
    });
  }

  @Query(() => Diagnosis, { name: 'diagnosis' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getDiagnosisById(@Args('id') id: string) {
    return this.diagnosisService.findById(id);
  }

  @Query(() => Number, { name: 'countDiagnoses' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async countDiagnoses() {
    return this.diagnosisService.count();
  }

  @Query(() => [String], { name: 'diagnosisSortFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getDiagnosisSortFields() {
    return this.diagnosisService.getAvailableSortFields();
  }

  @Query(() => [String], { name: 'diagnosisSearchFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getDiagnosisSearchFields() {
    return this.diagnosisService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Add' })
  async createDiagnosis(
    @Args('input') input: CreateDiagnosisInput,
    @Context() context: any
  ) {
    const authHeader = context.req?.headers?.authorization ?? '';
    let userId: string | undefined;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        userId = typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
      } catch (jwtError) {
        console.error('JWT verification failed for Diagnosis creation:', jwtError);
      }
    }
    if (userId) {
      input.createdBy = userId;
    }

    const result = await this.diagnosisService.create(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Edit' })
  async updateDiagnosis(@Args('input') input: UpdateDiagnosisInput) {
    const result = await this.diagnosisService.update(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Delete' })
  async deleteDiagnosis(@Args('id') id: string) {
    const result = await this.diagnosisService.delete(id);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }
} 