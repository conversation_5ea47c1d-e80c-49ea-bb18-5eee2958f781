import {
  Resolver,
  Query,
  Mutation,
  Args,
  ID,
  ObjectType,
  Field,
  Context
} from '@nestjs/graphql';
import { ProviderHistoryService } from '../services/provider_history.service';
import { ProviderHistory } from '../entities/provider_history';
import {
  CreateProviderHistoryInput,
  UpdateProviderHistoryInput,
  PaginateProviderHistoryArgs,
  ProviderHistoryResponse
} from '../dto/provider_history.dto';
import {
  BaseResponse, 
} from '../dto/base.response.dto';


@Resolver(() => ProviderHistory)
export class ProviderHistoryResolver {
  constructor(private readonly providerHistoryService: ProviderHistoryService) { }

  @Query(() => ProviderHistoryResponse, { name: 'providerHistories' })
  async getAllProviderHistories(
    @Context() context: any,
    @Args('input', { nullable: true }) input?: PaginateProviderHistoryArgs
  ) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters || {};

    return this.providerHistoryService.findAll(
      search,
      selectedFields,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      userId,
      orgId
    );
  }

  @Query(() => ProviderHistory, { name: 'providerHistory', nullable: true })
  async getProviderHistory(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerHistoryService.findById(id, orgId, userId);
  }

  @Mutation(() => BaseResponse)
  async createProviderHistory(
    @Args('input') input: CreateProviderHistoryInput,
    @Context() context: any
  ) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerHistoryService.create(input, orgId);
  }

  @Mutation(() => BaseResponse)
  async updateProviderHistory(
    @Args('input') input: UpdateProviderHistoryInput,
    @Context() context: any
  ) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerHistoryService.update(input, orgId);
  }

  @Mutation(() => BaseResponse)
  async deleteProviderHistory(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerHistoryService.delete(id, orgId);
  }
}
