import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Icd } from './icd.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'diagnoses',
  strict: false, 
})
export class Diagnosis extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => Icd)
  @Prop({ type: Types.ObjectId, ref: 'Icd', required: true })
  icd: Types.ObjectId | Icd;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Prop({ type: Object, default: {} })
  values: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @Prop({ index: true })
  templateId?: string;
  
  @Field(() => Boolean)
  @Prop({ default: true })
  isActive: boolean;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  updatedBy?: string;
}

export const DiagnosisSchema = SchemaFactory.createForClass(Diagnosis);

// Add indexes
DiagnosisSchema.index({ name: 'text', 'values.description': 'text' });
DiagnosisSchema.index({ icd: 1 });
DiagnosisSchema.index({ code: 1 }, { unique: true }); 
DiagnosisSchema.index({ createdAt: -1 });
DiagnosisSchema.index({ updatedAt: -1 }); 