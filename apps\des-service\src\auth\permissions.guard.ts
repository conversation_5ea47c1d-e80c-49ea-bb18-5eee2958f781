// This file is not used - we use the permissions library instead
// See libs/permissions for the actual implementation

// function normalizePermission(p: RequiredPermission): RequiredPermission {
//   return {
//     module: p.module,
//     subModule: p.subModule,
//     permission: p.permission,
//   };
// }

// @Injectable()
// export class PermissionsGuard implements CanActivate {
//   constructor(
//     private reflector: Reflector,
//     @InjectModel(UserProcessAssignment.name)
//     private userProcessAssignmentModel: Model<UserProcessAssignment>,
//     @InjectModel(Role.name)
//     private roleModel: Model<Role>,
//     @InjectModel(OrganisationRole.name)
//     private organisationRoleModel: Model<OrganisationRole>,
//   ) {}

//   async canActivate(context: ExecutionContext): Promise<boolean> {
//     const requiredPermissions =
//       this.reflector.getAllAndOverride<RequiredPermission[]>(
//         REQUIRE_PERMISSION_KEY,
//         [context.getHandler(), context.getClass()]
//       );

//     const ctx = GqlExecutionContext.create(context);
//     const gqlContext = ctx.getContext();
//     const request = gqlContext.req || gqlContext.request;

//     if (!requiredPermissions || requiredPermissions.length === 0) {
//       const authHeader = request?.headers?.authorization ?? '';
//       if (authHeader) {
//         const token = authHeader.replace('Bearer ', '');
//         try {
//           const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret') as {
//             userId: string;
//             role?: string;
//           };
//           const roleFromToken = decoded.role;

//           if (roleFromToken) {
//             gqlContext.roleName = roleFromToken;
//             request.headers.rolename = roleFromToken;

//             if (gqlContext.res) {
//               gqlContext.res.setHeader('roleName', roleFromToken);
//             }
//           }
//         } catch (err) {
//           console.log('[PermissionsGuard] JWT decode failed:', err.message);
//         }
//       }

//       return true;
//     }

//     const authHeader = request?.headers?.authorization ?? '';
//     if (!authHeader) {
//       throw new ForbiddenException('Authorization header missing');
//     }

//     const token = authHeader.replace('Bearer ', '');
//     let userId: string;
//     let roleIdFromToken: string | undefined;

//     try {
//       const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret') as {
//         userId: string;
//         roleId?: string;
//       };
//       userId = decoded.userId;
//       roleIdFromToken = decoded.roleId;
//     } catch (err) {
//       throw new ForbiddenException('Invalid token');
//     }

//     const orgId = request?.headers?.orgid;
//     const subOrgId = request?.headers?.suborgid;
//     const processId = request?.headers?.processid;
//     const roleIdFromHeader = request?.headers?.roleid;

//     let role: any;
//     let roleName: string | undefined;

//     if (orgId && subOrgId && processId) {
//       const assignment = await this.userProcessAssignmentModel.findOne({
//         userId,
//         organisationId: orgId,
//         subOrganisationId: subOrgId,
//         processId,
//       });

//       if (!assignment) {
//         throw new ForbiddenException('No role assignment found');
//       }

//       role = await this.roleModel.findById(assignment.roleId);
//       roleName = role?.key;
//     } else if (roleIdFromToken || roleIdFromHeader) {
//       const roleId = roleIdFromToken || roleIdFromHeader;
//       role = await this.organisationRoleModel.findById(roleId);
//       roleName = role?.name;
//       console.log('[PermissionsGuard] Loaded org role:', roleName);
//     } else {
//       console.warn('[PermissionsGuard] Missing context');
//       throw new ForbiddenException(
//         'Missing context: provide either (orgid, suborgid, processid) or roleId'
//       );
//     }

//     if (!role) {
//       throw new ForbiddenException('Role not found');
//     }

//     const permissionsJson = role.permissions || [];

//     const hasAtLeastOne = requiredPermissions.some((required) => {
//       const normalized = normalizePermission(required);
//       return this.checkPermission(permissionsJson, normalized);
//     });

//     if (!hasAtLeastOne) {
//       throw new ForbiddenException('Permission denied');
//     }

//     gqlContext.roleName = roleName;
//     request.headers.rolename = roleName;

//     if (gqlContext.res) {
//       gqlContext.res.setHeader('roleName', roleName);
//     }

//     return true;
//   }

//   checkPermission(permissionsJson: any[], requiredPermission: RequiredPermission): boolean {
//     for (const module of permissionsJson) {
//       if (module.moduleName === requiredPermission.module) {
//         for (const subModule of module.subModules || []) {
//           if (subModule.displayName === requiredPermission.subModule) {
//             for (const perm of subModule.permissions || []) {
//               if (
//                 perm.displayName === requiredPermission.permission &&
//                 perm.isEnabled === true
//               ) {
//                 return true;
//               }
//             }
//           }
//         }
//       }
//     }
//     return false;
//   }
// }