import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class ProcessRoleHierarchy extends Document {
  @Prop({ required: true })
  orgId: string;

  @Prop({ required: true })
  subOrgId: string;

  @Prop({
    type: [
      {
        processId: { type: String, required: true },
        isActive: { type: Boolean, default: true },
        roles: {
          operations: {
            manager_to_supervisors: { type: Object, default: {} },
            supervisor_to_agents: { type: Object, default: {} },
          },
          audit: {
            manager_to_supervisors: { type: Object, default: {} },
            supervisor_to_agents: { type: Object, default: {} },
          },
          management: {
            manager_to_supervisors: { type: Object, default: {} },
          },
        },
      },
    ],
    default: [],
  })
  process: any[];

  @Prop({ type: Array, default: [] })
  sla: any[];
}

export const ProcessRoleHierarchySchema = SchemaFactory.createForClass(ProcessRoleHierarchy); 