import { Injectable } from '@nestjs/common';
import { Collection, Document } from 'mongodb';
import { MongoConnectionService } from '@app/db';

/**
 * Base service that provides common database access functionality
 * All services should extend this to get consistent orgId handling
 */
@Injectable()
export abstract class BaseDatabaseService {
  constructor(protected readonly mongoConnectionService: MongoConnectionService) {}

  /**
   * Get a collection from the appropriate database based on orgId
   * @param orgId - Organization ID. If not provided or empty, uses master DB
   * @param collectionName - Name of the collection to access
   * @returns Promise resolving to the collection
   */
  protected async getCollection<T extends Document = Document>(
    orgId?: string,
    collectionName: string = 'users'
  ): Promise<Collection<T>> {
    return this.mongoConnectionService.getCollectionByOrgId<T>(collectionName,orgId );
  }

  /**
   * Get database instance based on orgId
   * @param orgId - Organization ID. If not provided or empty, returns master DB
   * @returns Promise resolving to the appropriate database
   */
  protected async getDatabase(orgId?: string) {
    return this.mongoConnectionService.getDatabaseByOrgId(orgId);
  }

  /**
   * Check if a tenant database exists
   * @param orgId - Organization ID to check
   * @returns Promise resolving to boolean indicating if tenant exists
   */
  protected async tenantExists(orgId: string): Promise<boolean> {
    return this.mongoConnectionService.tenantExists(orgId);
  }

  /**
   * Get master database collection (for backward compatibility)
   * @param collectionName - Name of the collection to access
   * @returns Promise resolving to the collection
   */
  protected async getMasterCollection<T extends Document = Document>(
    collectionName: string
  ): Promise<Collection<T>> {
    return this.mongoConnectionService.getMasterCollection<T>(collectionName);
  }

  /**
   * Validate orgId and return normalized version
   * @param orgId - Organization ID to validate
   * @returns Normalized orgId or empty string for master DB
   */
  protected normalizeOrgId(orgId?: string): string {
    return orgId && orgId.trim() !== '' ? orgId.trim() : '';
  }

  /**
   * Check if the given orgId represents a tenant (not master DB)
   * @param orgId - Organization ID to check
   * @returns True if orgId represents a tenant, false if master DB
   */
  protected isTenant(orgId?: string): boolean {
    return this.normalizeOrgId(orgId) !== '';
  }
} 