import { Resolver, Query, Args, Mutation, ObjectType, Field, Int, Context } from '@nestjs/graphql';
import { ImportConfigurationService } from '../services/import-configuration.service';
import { ImportConfiguration } from '../entities/import-configuration.entity';
import { CreateImportConfigurationInput, UpdateImportConfigurationInput, ImportConfigurationResponse } from '../dto/import.dto';
import * as jwt from 'jsonwebtoken';

import { UseGuards } from '@nestjs/common';
import { RequirePermission } from '@app/permissions';

@ObjectType()
class ImportConfigurationPaginationInfo {
  @Field(() => Int)
  page: number;
  @Field(() => Int)
  limit: number;
  @Field(() => Int)
  total: number;
  @Field(() => Int)
  totalPages: number;
  @Field()
  hasNext: boolean;
  @Field()
  hasPrev: boolean;
}

@ObjectType()
class ImportConfigurationPagination {
  @Field(() => [ImportConfiguration])
  items: ImportConfiguration[];
  @Field(() => ImportConfigurationPaginationInfo)
  pagination: ImportConfigurationPaginationInfo;
}

@Resolver(() => ImportConfiguration)
export class ImportConfigurationResolver {
  constructor(
    private readonly importConfigurationService: ImportConfigurationService
  ) {}

  private extractUserIdFromContext(context: any): string {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    if (!userId) {
      throw new Error('User authentication required');
    }

    return userId;
  }

  @Mutation(() => ImportConfigurationResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'All' }
  // )
  async createImportConfiguration(
    @Args('input') input: CreateImportConfigurationInput,
    @Context() context: any
  ): Promise<ImportConfigurationResponse> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.createConfiguration(userId, input);
  }

  @Query(() => ImportConfiguration)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async getImportConfiguration(
    @Args('configurationId') configurationId: string,
    @Context() context: any
  ): Promise<ImportConfiguration> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.getConfigurationById(configurationId, userId);
  }

  @Query(() => ImportConfiguration)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async getImportConfigurationByTemplateId(
    @Args('templateId') templateId: string,
    @Context() context: any
  ): Promise<ImportConfiguration> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.getConfigurationByTemplateId(templateId, userId);
  }

  @Query(() => ImportConfigurationPagination)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async getUserImportConfigurations(
    @Args('page', { type: () => Int, nullable: true }) page?: number,
    @Args('limit', { type: () => Int, nullable: true }) limit?: number,
    @Args('search', { nullable: true }) search?: string,
    @Args('collectionName', { nullable: true }) collectionName?: string,
    @Args('isActive', { nullable: true }) isActive?: boolean,
    @Context() context?: any
  ): Promise<ImportConfigurationPagination> {
    const userId = this.extractUserIdFromContext(context);

    const result = await this.importConfigurationService.getUserConfigurations(userId, {
      page,
      limit,
      search,
      collectionName,
      isActive
    });

    return {
      items: result.items,
      pagination: result.pagination
    };
  }

  @Query(() => [ImportConfiguration])
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async getImportConfigurationsByCollection(
    @Args('collectionName') collectionName: string,
    @Context() context: any
  ): Promise<ImportConfiguration[]> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.getConfigurationsByCollection(userId, collectionName);
  }

  @Mutation(() => ImportConfiguration)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'All' }
  // )
  async updateImportConfiguration(
    @Args('input') input: UpdateImportConfigurationInput,
    @Context() context: any
  ): Promise<ImportConfiguration> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.updateConfiguration(userId, input);
  }

  @Mutation(() => Boolean)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'Delete' }
  // )
  async deleteImportConfiguration(
    @Args('configurationId') configurationId: string,
    @Context() context: any
  ): Promise<boolean> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.deleteConfiguration(configurationId, userId);
  }

  @Query(() => ImportConfiguration)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async validateImportConfiguration(
    @Args('configurationId') configurationId: string,
    @Context() context: any
  ): Promise<ImportConfiguration> {
    const userId = this.extractUserIdFromContext(context);
    return this.importConfigurationService.validateConfiguration(configurationId, userId);
  }
}
