import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';

const defaultTransform = function (doc: any, ret: any) {
    ret.id = ret._id;
    delete ret._id;
    delete ret.__v;
    return ret;
};

@ObjectType()
@Schema({
    timestamps: true,
    collection: 'status_codes',
   /// toJSON: { virtuals: true, transform: defaultTransform },
})
export class StatusCode extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field(() => ID)
    @Prop({ required: true })
    processId: string;
    
    @Field()
    @Prop({ required: true })
    code: string;

    // @Field({ nullable: true })
    // @Prop({ index: true })
    // templateId?: string;

    // @Field(() => GraphQLJSON, { nullable: true })
    // @Prop({ type: Object })
    // values?: Record<string, any>;
    
    @Field({ nullable: true })
    @Prop()
    createdBy?: string;

    @Field({ nullable: true })
    @Prop()
    updatedBy?: string;

    @Field({ nullable: true })
    @Prop({ default: true })
    isActive?: boolean;

    // @Field(() => GraphQLJSON, { nullable: true })
    // process?: any;
}



@ObjectType()
@Schema({
    timestamps: true,
    collection: 'action_codes',
    toJSON: { virtuals: true, transform: defaultTransform },
})
export class ActionCode extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field(() => StatusCode, { nullable: true })
    @Prop({ type: Types.ObjectId, ref: 'StatusCode', required: true, index: true })
    statusCodeId: Types.ObjectId;

    @Field()
    @Prop({ required: true })
    code: string;

    // @Field({ nullable: true })
    // @Prop({ index: true })
    // templateId?: string;

    // @Field(() => GraphQLJSON, { nullable: true })
    // @Prop({ type: Object })
    // values?: Record<string, any>;

    @Field({ nullable: true })
    @Prop()
    createdBy?: string;

    @Field({ nullable: true })
    @Prop()
    updatedBy?: string;

    @Field({ nullable: true })
    @Prop({ default: true })
    isActive?: boolean;

    @Field(() => GraphQLJSON, { nullable: true })
    process?: any;
}

@ObjectType()
@Schema({
    timestamps: true,
    collection: 'action_status_code_maps',
    toJSON: { virtuals: true, transform: defaultTransform },
    strict: false,
})
export class ActionStatusCodeMap extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field(() => ID)
    @Prop({ required: true, index: true })
    processId: string;

    @Field(() => ActionCode, { nullable: true })
    @Prop({ type: Types.ObjectId, ref: 'ActionCode', required: true, index: true })
    actionCodeId: Types.ObjectId;

    @Field(() => StatusCode, { nullable: true })
    @Prop({ type: Types.ObjectId, ref: 'StatusCode', required: true, index: true })
    statusCodeId: Types.ObjectId;

    @Field({ nullable: true })
    @Prop({ index: true })
    templateId?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @Prop({ type: Object })
    values?: Record<string, any>;

    @Field({ nullable: true })
    @Prop()
    createdBy?: string;

    @Field({ nullable: true })
    @Prop()
    updatedBy?: string;

    @Field({ nullable: true })
    @Prop({ default: true })
    isActive?: boolean;

    @Field(() => GraphQLJSON, { nullable: true })
    process?: any;
}

export const StatusCodeSchema = SchemaFactory.createForClass(StatusCode);
StatusCodeSchema.index({ processId: 1, code: 1 }, { unique: true });

export const ActionCodeSchema = SchemaFactory.createForClass(ActionCode);
ActionCodeSchema.index({ statusCodeId: 1, code: 1 }, { unique: true });

export const ActionStatusCodeMapSchema = SchemaFactory.createForClass(ActionStatusCodeMap);
ActionStatusCodeMapSchema.index({ processId: 1, actionCodeId: 1, statusCodeId: 1 }, { unique: true }); 