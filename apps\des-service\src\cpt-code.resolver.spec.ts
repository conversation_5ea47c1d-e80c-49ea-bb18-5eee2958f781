import { Test, TestingModule } from '@nestjs/testing';
import { CptCodeResolver } from './cpt-code.resolver';
import { CptCodeService } from './cpt-code.service';
import { CptCode } from './entities/cpt-code.entity';
import { CreateCptCodeInput, UpdateCptCodeInput, PaginateCptCodeArgs } from './dto/cpt-code.dto';
import { HttpStatus, ErrorType } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { Types } from 'mongoose';
import { Success, ResponseCode, Error as GQLError } from '@app/error';

jest.mock('jsonwebtoken');

describe('CptCodeResolver', () => {
  let resolver: CptCodeResolver;
  let service: CptCodeService;

  const mockCptCodeService = {
    findById: jest.fn(),
    findAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    getAvailableSortFields: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CptCodeResolver,
        {
          provide: CptCodeService,
          useValue: mockCptCodeService,
        },
      ],
    }).compile();

    resolver = module.get<CptCodeResolver>(CptCodeResolver);
    service = module.get<CptCodeService>(CptCodeService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('cptCode', () => {
    it('should return a CPT code when found', async () => {
      const mockCptCode = {
        id: '1',
        code: '12345',
        values: { key: 'value' },
        specialtyId: 'specialty1',
        toJSON: () => ({
          id: '1',
          code: '12345',
          values: { key: 'value' },
          specialtyId: 'specialty1'
        }),
      };

      mockCptCodeService.findById.mockResolvedValue(mockCptCode);

      const result = await resolver.cptCode('1');

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('CPT code fetched successfully.');
      expect(result.type).toBe('15_FETCH_SUCCESS');
      expect(result.data).toEqual({
        id: '1',
        code: '12345',
        values: { key: 'value' },
        specialtyId: 'specialty1'
      });
    });

//    it('should handle not found error', async () => {
//   const error = new GQLError(
//     HttpStatus.NOT_FOUND,
//     ResponseCode.NOT_FOUND,
//     ErrorType.CPT,
//     'CPT code not found'
//   );
//   mockCptCodeService.findById.mockRejectedValue(error);

//   const result = await resolver.cptCode('999');

//   expect(result.code).toBe(HttpStatus.NOT_FOUND);
//   expect(result.message).toBe('CPT code not found');
//   expect(result.type).toBe('15_NOT_FOUND');
//   expect(result.data).toBeNull();
// });

    it('should handle service error', async () => {
      mockCptCodeService.findById.mockRejectedValue({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Service error',
        type: ErrorType.CPT
      });

      const result = await resolver.cptCode('1');

      expect(result.code).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.message).toBe('Service error');
      expect(result.type).toBe(ErrorType.CPT + '_FETCH_ERROR');
      expect(result.data).toBeNull();
    });
  });

  describe('cptCodes', () => {
    it('should return paginated CPT codes', async () => {
      const mockPaginatedData = {
        items: [
          {
            id: '1',
            code: '12345',
            values: { key: 'value1' },
            specialtyId: 'specialty1',
            toJSON: () => ({
              id: '1',
              code: '12345',
              values: { key: 'value1' },
              specialtyId: 'specialty1'
            }),
          },
          {
            id: '2',
            code: '67890',
            values: { key: 'value2' },
            specialtyId: 'specialty2',
            toJSON: () => ({
              id: '2',
              code: '67890',
              values: { key: 'value2' },
              specialtyId: 'specialty2'
            }),
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
        },
      };

      mockCptCodeService.findAll.mockResolvedValue(mockPaginatedData);

      const args: PaginateCptCodeArgs = { page: 1, limit: 10 };
      const result = await resolver.cptCodes(args);

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('CPT codes fetched successfully.');
      expect(result.type).toBe('CPT_CODES_FETCH_SUCCESS');
      expect(result.data).toEqual(mockPaginatedData);
    });

    it('should handle service error', async () => {
      const args = { page: 1, limit: 10 };
      const error = {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Service error',
        type: ErrorType.CPT
      };
      jest.spyOn(service, 'findAll').mockRejectedValue(error);

      const result = await resolver.cptCodes(args);

      expect(result.code).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.message).toBe('Service error');
      expect(result.type).toBe(ErrorType.CPT);
      expect(result.data).toBeNull();
    });
  });

  describe('createCptCode', () => {
    it('should create a CPT code with user ID from token', async () => {
      const mockToken = 'valid-token';
      const mockDecodedToken = { userId: 'user123' };
      const mockContext = {
        req: {
          headers: {
            authorization: `Bearer ${mockToken}`,
          },
        },
      };

      (jwt.verify as jest.Mock).mockReturnValue(mockDecodedToken);

      const createInput: CreateCptCodeInput = {
        code: '12345',
        values: { key: 'value' },
        specialtyId: new Types.ObjectId().toString(),
        templateId: new Types.ObjectId().toString()
      };

      const mockResponse = {
        code: HttpStatus.CREATED,
        message: 'CPT code created successfully',
        type: ErrorType.CPT.toString() + '_CREATE_SUCCESS',
        data: { id: '1', ...createInput },
      };

      mockCptCodeService.create.mockResolvedValue(mockResponse);

      const result = await resolver.createCptCode(createInput, mockContext);

      expect(result).toEqual(mockResponse);
      expect(mockCptCodeService.create).toHaveBeenCalledWith({
        ...createInput,
        createdBy: 'user123',
      });
    });

    it('should handle JWT verification failure', async () => {
      const mockContext = {
        req: {
          headers: {
            authorization: 'Bearer invalid-token',
          },
        },
      };

      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const createInput: CreateCptCodeInput = {
        code: '12345',
        values: { key: 'value' },
        specialtyId: new Types.ObjectId().toString(),
        templateId: new Types.ObjectId().toString()
      };

      const mockResponse = {
        code: HttpStatus.CREATED,
        message: 'CPT code created successfully',
        type: ErrorType.CPT.toString() + '_CREATE_SUCCESS',
        data: { id: '1', ...createInput },
      };

      mockCptCodeService.create.mockResolvedValue(mockResponse);

      const result = await resolver.createCptCode(createInput, mockContext);

      expect(result).toEqual(mockResponse);
      expect(mockCptCodeService.create).toHaveBeenCalledWith(createInput);
    });
  });

  describe('updateCptCode', () => {
    it('should update a CPT code', async () => {
      const updateInput: UpdateCptCodeInput = {
        id: '1',
        code: 'updated123',
        values: { key: 'updatedValue' },
        isActive: true
      };

      const mockResponse = {
        code: HttpStatus.OK,
        message: 'CPT code updated successfully',
        type: ErrorType.CPT.toString() + '_UPDATE_SUCCESS',
        data: { ...updateInput },
      };

      mockCptCodeService.update.mockResolvedValue(mockResponse);

      const result = await resolver.updateCptCode(updateInput);

      expect(result).toEqual(mockResponse);
      expect(mockCptCodeService.update).toHaveBeenCalledWith(updateInput);
    });
  });

  describe('deleteCptCode', () => {
    it('should delete a CPT code', async () => {
      const mockResponse = {
        code: HttpStatus.OK,
        message: 'CPT code deleted successfully',
        type: ErrorType.CPT.toString() + '_DELETE_SUCCESS',
        data: { id: '1' },
      };

      mockCptCodeService.delete.mockResolvedValue(mockResponse);

      const result = await resolver.deleteCptCode('1');

      expect(result).toEqual(mockResponse);
      expect(mockCptCodeService.delete).toHaveBeenCalledWith('1');
    });
  });

  describe('cptCodeCount', () => {
    it('should return the count of CPT codes', async () => {
      mockCptCodeService.count.mockResolvedValue(5);

      const result = await resolver.cptCodeCount();

      expect(result).toBe(5);
      expect(mockCptCodeService.count).toHaveBeenCalled();
    });
  });

  describe('cptCodeSortFields', () => {
    it('should return available sort fields', async () => {
      const mockFields = ['code', 'specialtyId', 'createdAt'];
      mockCptCodeService.getAvailableSortFields.mockReturnValue(mockFields);

      const result = resolver.cptCodeSortFields();

      expect(result).toEqual(mockFields);
      expect(mockCptCodeService.getAvailableSortFields).toHaveBeenCalled();
    });
  });
}); 