import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'roles',
})
export class Role extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true, unique: true })
  name: string;

  @Field(() => [Object], { nullable: true })
  @Prop({ type: Array, default: [] })
  permissions: any[]; // Will be structured as per permission template

  @Field({ nullable: true })
  @Prop({ default: true })
  isActive: boolean;

  // @Field({ nullable: true })
  // @Prop({ required: false })
  // entity?: string;

  @Field({ nullable: true })
  @Prop({ required: false })
  category?: string;

  @Field({ nullable: true })
  @Prop({ required: true, unique: true })
  key: string;

  @Field({ nullable: true })
  updatedAt?: Date;
}

export const RoleSchema = SchemaFactory.createForClass(Role);

// Static seeding method (to be called from a script or on app bootstrap)
export async function seedDefaultRoles(RoleModel: any, permissionTemplate: any) {
  const defaultRoles = [
    { key: 'agent', name: 'Agent' },
    { key: 'supervisor', name: 'Supervisor' },
    { key: 'manager', name: 'Manager' },
    { key: 'admin', name: 'Admin' },
    { key: 'qc-agent', name: 'QC Agent' },
    { key: 'qc-supervisor', name: 'QC Supervisor' },
    { key: 'qc-manager', name: 'QC Manager' },
    { key: 'sub-admin', name: 'Sub Admin' },
  ];
  for (const { key, name } of defaultRoles) {
    // Try to find by key first, then by name
    let role = await RoleModel.findOne({ key });
    // if (!role) {
    //   role = await RoleModel.findOne({ name });
    // }
    if (role) {
      // Update missing key or name if needed
      let needsUpdate = false;
      if (role.key !== key) {
        role.key = key;
        needsUpdate = true;
      }
      if (role.name !== name) {
        role.name = name;
        needsUpdate = true;
      }
      if (needsUpdate) {
        await role.save();
      }
      continue;
    }
    // If not found, create new
    let category = '';
    if (["agent", "supervisor", "manager"].includes(key)) category = "Operation";
    else if (["qc-agent", "qc-supervisor", "qc-manager"].includes(key)) category = "Audit";
    else if (["admin", "sub-admin"].includes(key)) category = "Management";
    await RoleModel.create({
      key,
      name,
      permissions: JSON.parse(JSON.stringify(permissionTemplate)),
      isActive: true,
      category,
    });
  }
} 