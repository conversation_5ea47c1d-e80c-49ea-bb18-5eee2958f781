import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CptCodeService } from './cpt-code.service';
import { CptCode } from './entities/cpt-code.entity';
import { Specialty } from './entities/specialty.entity';
import { Error, Success, HttpStatus, ResponseCode, ErrorType } from '@app/error';
import { CreateCptCodeInput, UpdateCptCodeInput, PaginateCptCodeArgs } from './dto/cpt-code.dto';


describe('CptCodeService', () => {
  let service: CptCodeService;
  let cptCodeModel: Model<CptCode>;
  let specialtyModel: Model<Specialty>;

  const mockCptCode = {
    _id: new Types.ObjectId(),
    code: '12345',
    values: { description: 'Test CPT Code' },
    specialtyId: new Types.ObjectId(),
    templateId: new Types.ObjectId(),
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    toObject: function() { return this; }
  };

  const mockSpecialty = {
    _id: new Types.ObjectId(),
    name: 'Test Specialty',
    isActive: true,
    toObject: function() { return this; }
  };

  beforeEach(async () => {

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CptCodeService,
        {
          provide: getModelToken(CptCode.name),
          useValue: {
            new: jest.fn().mockResolvedValue(mockCptCode),
            constructor: jest.fn().mockResolvedValue(mockCptCode),
            find: jest.fn(),
            findById: jest.fn(),
            findOne: jest.fn(),
            findByIdAndUpdate: jest.fn(),
            findByIdAndDelete: jest.fn(),
            create: jest.fn(),
            countDocuments: jest.fn(),
            exec: jest.fn(),
            save: jest.fn().mockResolvedValue(mockCptCode),
            lean: jest.fn().mockReturnThis(),
            skip: jest.fn().mockReturnThis(),
            limit: jest.fn().mockReturnThis(),
            sort: jest.fn().mockReturnThis(),
          },
        },
        {
          provide: getModelToken(Specialty.name),
          useValue: {
            findById: jest.fn().mockReturnValue({
              lean: jest.fn().mockReturnThis(),
              exec: jest.fn().mockResolvedValue(mockSpecialty)
            }),
          },
        },
      ],
    }).compile();

    service = module.get<CptCodeService>(CptCodeService);
    cptCodeModel = module.get<Model<CptCode>>(getModelToken(CptCode.name));
    specialtyModel = module.get<Model<Specialty>>(getModelToken(Specialty.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated CPT codes', async () => {
      const args: PaginateCptCodeArgs = {
        page: 1,
        limit: 10,
        sortBy: 'code',
        sortOrder: 'asc'
      };

      const mockFind = jest.fn().mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([mockCptCode])
      });

      jest.spyOn(cptCodeModel, 'find').mockReturnValue(mockFind() as any);
      jest.spyOn(cptCodeModel, 'countDocuments').mockResolvedValue(1);

      const result = await service.findAll(args);
      expect(result.items).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });

    it('should handle empty results in findAll', async () => {
      const args: PaginateCptCodeArgs = {
        page: 1,
        limit: 10
      };

      const mockFind = jest.fn().mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([])
      });

      jest.spyOn(cptCodeModel, 'find').mockReturnValue(mockFind() as any);
      jest.spyOn(cptCodeModel, 'countDocuments').mockResolvedValue(0);

      const result = await service.findAll(args);
      expect(result.items).toHaveLength(0);
      expect(result.pagination.total).toBe(0);
    });

    it('should handle search term in findAll', async () => {
      const args: PaginateCptCodeArgs = {
        page: 1,
        limit: 10,
        search: 'test'
      };

      const mockFind = jest.fn().mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([mockCptCode])
      });

      jest.spyOn(cptCodeModel, 'find').mockReturnValue(mockFind() as any);
      jest.spyOn(cptCodeModel, 'countDocuments').mockResolvedValue(1);

      const result = await service.findAll(args);
      expect(result.items).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });

    it('should handle database errors in findAll', async () => {
      const args: PaginateCptCodeArgs = {
        page: 1,
        limit: 10
      };

      jest.spyOn(cptCodeModel, 'find').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.findAll(args)).rejects.toThrow(Error);
    });
  });

  describe('findById', () => {
    it('should return a CPT code by id', async () => {
      jest.spyOn(cptCodeModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCptCode)
      } as any);

      const result = await service.findById(mockCptCode._id.toString());
      expect(result).toBeDefined();
      expect(result._id.toString()).toBe(mockCptCode._id.toString());
    });

    it('should throw error when CPT code not found', async () => {
      jest.spyOn(cptCodeModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      const expectedError = new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.CPT,
        'CPT code not found'
      );

      await expect(service.findById(mockCptCode._id.toString())).rejects.toThrow(expectedError);
    });

    it('should throw error for invalid id format', async () => {
      const expectedError = new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.CPT,
        'Invalid ID format'
      );

      await expect(service.findById('invalid-id')).rejects.toThrow(expectedError);
    });

    it('should handle database errors in findById', async () => {
      jest.spyOn(cptCodeModel, 'findById').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.findById(mockCptCode._id.toString())).rejects.toThrow(Error);
    });
  });

  describe('findByCode', () => {
    it('should return a CPT code by code', async () => {
      jest.spyOn(cptCodeModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCptCode)
      } as any);

      const result = await service.findByCode('12345');
      expect(result).toBeDefined();
      expect(result?.code).toBe('12345');
    });

    it('should return null when CPT code not found', async () => {
      jest.spyOn(cptCodeModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      const result = await service.findByCode('nonexistent');
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    const createInput: CreateCptCodeInput = {
      code: '12345',
      values: { description: 'Test CPT Code' },
      specialtyId: new Types.ObjectId().toString(),
      templateId: new Types.ObjectId().toString(),
      isActive: true
    };

  //  it('should create a new CPT code', async () => {
  //     // Mock findByCode to return null (no existing code)
  //     jest.spyOn(service, 'findByCode').mockResolvedValue(null);

  //     // Mock specialty findById
  //     jest.spyOn(specialtyModel, 'findById').mockReturnValue({
  //       lean: jest.fn().mockReturnThis(),
  //       exec: jest.fn().mockResolvedValue(mockSpecialty)
  //     } as any);

  //     // Mock the create method
  //     jest.spyOn(cptCodeModel, 'create').mockResolvedValue(mockCptCode as any);

  //     const result = await service.create(createInput);
  //     expect(result).toBeInstanceOf(Success);
  //     const data = result.data as { cptCode: CptCode };
  //     expect(data.cptCode).toBeDefined();
  //     expect(data.cptCode.code).toBe(createInput.code);
  //   });

    it('should throw error when CPT code already exists', async () => {
      jest.spyOn(cptCodeModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCptCode)
      } as any);

      const expectedError = new Error(
        HttpStatus.CONFLICT,
        ResponseCode.DUPLICATE_ENTRY,
        ErrorType.CPT,
        'CPT code already exists'
      );

      await expect(service.create(createInput)).rejects.toThrow(expectedError);
    });

    it('should throw error when specialty not found', async () => {
      jest.spyOn(cptCodeModel, 'findOne').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      jest.spyOn(specialtyModel, 'findById').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      const expectedError = new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.CPT,
        'Specialty not found'
      );

      await expect(service.create(createInput)).rejects.toThrow(expectedError);
    });

    it('should throw error for invalid specialty id format', async () => {
      const invalidInput = {
        ...createInput,
        specialtyId: 'invalid-id'
      };

      const expectedError = new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.CPT,
        'Invalid Specialty ID format'
      );

      await expect(service.create(invalidInput)).rejects.toThrow(expectedError);
    });

    it('should handle database errors in create', async () => {
      jest.spyOn(cptCodeModel, 'findOne').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.create(createInput)).rejects.toThrow(Error);
    });
  });

  describe('update', () => {
    const updateInput: UpdateCptCodeInput = {
      id: new Types.ObjectId().toString(),
      code: '54321',
      values: { description: 'Updated CPT Code' }
    };

    it('should update a CPT code', async () => {
      // Mock findByCode to return null (no duplicate code)
      jest.spyOn(service, 'findByCode').mockResolvedValue(null);

      // Mock findByIdAndUpdate
      const mockUpdatedDoc = {
        ...mockCptCode,
        ...updateInput,
        toObject: () => ({ ...mockCptCode, ...updateInput })
      };
      jest.spyOn(cptCodeModel, 'findByIdAndUpdate').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockUpdatedDoc)
      } as any);

      const result = await service.update(updateInput);
      expect(result).toBeInstanceOf(Success);
      expect(result.data?.cptCode).toBeDefined();
    });

    it('should throw error when CPT code not found', async () => {
      jest.spyOn(cptCodeModel, 'findByIdAndUpdate').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      const expectedError = new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.CPT,
        'CPT code not found'
      );

      await expect(service.update(updateInput)).rejects.toThrow(expectedError);
    });

    it('should throw error for invalid id format', async () => {
      const invalidInput = {
        ...updateInput,
        id: 'invalid-id'
      };

      const expectedError = new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.CPT,
        'Invalid ID format'
      );

      await expect(service.update(invalidInput)).rejects.toThrow(expectedError);
    });

    it('should handle database errors in update', async () => {
      jest.spyOn(cptCodeModel, 'findByIdAndUpdate').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.update(updateInput)).rejects.toThrow(Error);
    });
  });

  describe('delete', () => {
    it('should delete a CPT code', async () => {
      jest.spyOn(cptCodeModel, 'findByIdAndDelete').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(mockCptCode)
      } as any);

      const result = await service.delete(mockCptCode._id.toString());
      expect(result).toBeInstanceOf(Success);
      expect(result.data?.message).toBe('CPT code deleted successfully.');
    });

    it('should throw error when CPT code not found', async () => {
      jest.spyOn(cptCodeModel, 'findByIdAndDelete').mockReturnValue({
        lean: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue(null)
      } as any);

      const expectedError = new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.CPT,
        'CPT code not found'
      );

      await expect(service.delete(mockCptCode._id.toString())).rejects.toThrow(expectedError);
    });

    it('should throw error for invalid id format', async () => {
      const expectedError = new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.CPT,
        'Invalid ID format'
      );

      await expect(service.delete('invalid-id')).rejects.toThrow(expectedError);
    });

    it('should handle database errors in delete', async () => {
      jest.spyOn(cptCodeModel, 'findByIdAndDelete').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.delete(mockCptCode._id.toString())).rejects.toThrow(Error);
    });
  });

  describe('count', () => {
    it('should return total count of CPT codes', async () => {
      jest.spyOn(cptCodeModel, 'countDocuments').mockReturnValue({
        exec: jest.fn().mockResolvedValue(10)
      } as any);

      const result = await service.count();
      expect(result).toBe(10);
    });

    it('should handle database errors in count', async () => {
      jest.spyOn(cptCodeModel, 'countDocuments').mockImplementation(() => {
        throw new Error(
          HttpStatus.INTERNAL_SERVER_ERROR,
          ResponseCode.INTERNAL_SERVER_ERROR,
          ErrorType.CPT,
          'Database error'
        );
      });

      await expect(service.count()).rejects.toThrow(Error);
    });
  });

  describe('getAvailableSortFields', () => {
    it('should return array of sort fields', () => {
      const fields = service.getAvailableSortFields();
      expect(Array.isArray(fields)).toBe(true);
      expect(fields).toContain('code');
      expect(fields).toContain('isActive');
      expect(fields).toContain('createdAt');
      expect(fields).toContain('updatedAt');
    });
  });
}); 