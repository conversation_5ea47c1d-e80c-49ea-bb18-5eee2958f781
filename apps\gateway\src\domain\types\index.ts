import {
  ServiceEndpointDefinition,
  RemoteGraphQLDataSource,
} from '@apollo/gateway';
import { IncomingMessage } from 'http';

type clientCredentials = {
  clientId: string;
  clientSecret: string;
};

type BMGatewayConfig = typeof module & {
  clientCredentials: clientCredentials;
  subgraphs: Array<ServiceEndpointDefinition>;
};

export type DataSourceTypes = {
  [name: string]: {
    dataSourceClass: typeof RemoteGraphQLDataSource;
  };
};

export interface ApolloAuthContext extends Record<string, unknown> {
  authenticated?: boolean;
  userId?: string;
  req?: IncomingMessage;
}

export { BMGatewayConfig as GatewayConfig };
