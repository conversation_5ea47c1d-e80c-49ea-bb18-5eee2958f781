import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog } from '../entities/audit-log.entity';
import {
  AuditLogConnection,
  AuditLogQueryInput,
  AuditStatsType,
  AuditLogFilterInput,
  AuditLogSortInput,
  SortOrder
} from '../dto/audit-log.dto';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    @InjectModel(AuditLog.name) private auditModel: Model<AuditLog>,
  ) { }

  /**
   * Get audit logs with pagination, search, filter, and sorting
   */
  async getAuditLogs(queryInput: AuditLogQueryInput): Promise<AuditLogConnection> {
    try {
      const { page, limit, search, filters, sortBy, sortOrder } = queryInput;
      const skip = (page - 1) * limit;

      // Build query
      const query = this.buildQuery(search, filters);

      // Build sort, default to newest first if no sort specified
      const sortObj = sortBy ? this.buildSort(sortBy, sortOrder) : { createdAt: -1 };

      // Execute queries
      const [data, total] = await Promise.all([
        this.auditModel
          .find(query)
          .sort(sortObj)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.auditModel.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: data.map(this.mapAuditLog),
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    } catch (error) {
      this.logger.error('Failed to get audit logs:', error);
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStats(filter?: AuditLogFilterInput): Promise<AuditStatsType> {
    try {
      const query = filter ? this.buildQuery(undefined, filter) : {};

      const [totalLogs, actionStats, userStats, severityStats, categoryStats] = await Promise.all([
        this.auditModel.countDocuments(query),
        this.getActionStats(query),
        this.getUserStats(query),
        this.getSeverityStats(query),
        this.getCategoryStats(query),
      ]);

      return {
        totalLogs,
        actionStats,
        userStats,
        severityStats,
        categoryStats,
      };
    } catch (error) {
      this.logger.error('Failed to get audit stats:', error);
      throw error;
    }
  }

  /**
   * Create a test audit log
   */
  async createTestAuditLog(): Promise<boolean> {
    try {
      const testLog = new this.auditModel({
        auditId: `test-${Date.now()}`,
        userId: 'test-user',
        orgId: 'test-org',
        action: 'TEST',
        entityType: 'SYSTEM',
        description: 'Test audit log entry',
        timestamp: new Date(),
        severity: 'LOW',
        category: 'SYSTEM',
      });

      await testLog.save();
      this.logger.log('Test audit log created successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to create test audit log:', error);
      return false;
    }
  }

  /**
   * Check if audit logs are being created
   */
  async checkAuditLogs(): Promise<{
    totalCount: number;
    recentLogs: any[];
    lastLogTime: Date | null;
    isWorking: boolean;
  }> {
    try {
      const totalCount = await this.auditModel.countDocuments();
      const recentLogs = await this.auditModel
        .find()
        .sort({ timestamp: -1 })
        .limit(5)
        .lean();

      const lastLogTime = recentLogs.length > 0 ? recentLogs[0].timestamp : null;

      return {
        totalCount,
        recentLogs,
        lastLogTime,
        isWorking: totalCount > 0,
      };
    } catch (error) {
      this.logger.error('Failed to check audit logs:', error);
      return {
        totalCount: 0,
        recentLogs: [],
        lastLogTime: null,
        isWorking: false,
      };
    }
  }

  // Helper methods
  // Update buildQuery to accept filters as Record<string, any>
  private buildQuery(search?: string, filters?: Record<string, any> | string): any {
    const query: any = {};

    // Search across multiple fields
    if (search) {
      query.$or = [
        { description: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } },
        { entityType: { $regex: search, $options: 'i' } },
        { action: { $regex: search, $options: 'i' } },
      ];
    }

    // Apply flexible filters (object or JSON string)
    if (filters) {
      let parsed: Record<string, any>;
      if (typeof filters === 'string') {
        try {
          parsed = JSON.parse(filters);
        } catch (e) {
          this.logger.error('Failed to parse filters string:', e);
          parsed = {};
        }
      } else {
        parsed = filters;
      }
      const globalFrom = parsed.from;
      const globalTo = parsed.to;
      if (globalFrom || globalTo) {
        query.createdAt = {};
        if (globalFrom) query.createdAt.$gte = new Date(globalFrom);
        if (globalTo) {
          const toDate = new Date(globalTo);
          toDate.setDate(toDate.getDate() + 1); // include the whole day
          query.createdAt.$lt = toDate;
        }
        // delete parsed.from;
        // delete parsed.to;
      }
      Object.entries(parsed).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Range filter: { from: '', to: '' }
          if (key === 'from' || key === 'to') {
            return;
          }
          if (
            typeof value === 'object' &&
            value !== null &&
            Object.prototype.hasOwnProperty.call(value, 'from') &&
            Object.prototype.hasOwnProperty.call(value, 'to')
          ) {
            const { from, to } = value;
            // If both are empty, skip
            if ((from === '' || from === undefined || from === null) && (to === '' || to === undefined || to === null)) {
              return;
            }
            // Build range query
            query[key] = {};
            if (from && from !== '') query[key]['$gte'] = new Date(from);
            if (to && to !== '') query[key]['$lte'] = new Date(to);
            // If only one is set, only that bound is used
          }
          // Special handling for date fields (legacy single value)
          else if (["createdAt", "updatedAt", "timestamp"].includes(key) && typeof value === "string") {
            const start = new Date(value);
            const end = new Date(start);
            end.setDate(start.getDate() + 1);
            query[key] = { $gte: start, $lt: end };
          } else if (typeof value === 'string') {
            query[key] = { $regex: value, $options: 'i' };
          } else {
            query[key] = value;
          }
        }
      });
    }

    return query;
  }

  // Update buildSort to accept sortBy and sortOrder
  private buildSort(sortBy?: string, sortOrder?: string): any {
    if (!sortBy) return { timestamp: -1 };
    const order = sortOrder && sortOrder.toLowerCase() === 'asc' ? 1 : -1;
    return { [sortBy]: order };
  }

  private mapAuditLog(log: any): any {
    return {
      id: log._id.toString(),
      auditId: log.auditId,
      userId: log.userId,
      orgId: log.orgId,
      userEmail: log.userEmail,
      action: log.action,
      entityType: log.entityType,
      entityId: log.entityId,
      message: log.message || [],
      beforeData: log.beforeData ? JSON.stringify(log.beforeData) : null,
      afterData: log.afterData ? JSON.stringify(log.afterData) : null,
      severity: log.severity,
      category: log.category,
      timestamp: log.timestamp,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      sessionId: log.sessionId,
      createdAt: log.createdAt,
      updatedAt: log.updatedAt,
    };
  }

  private async getActionStats(query: any): Promise<any[]> {
    return this.auditModel.aggregate([
      { $match: query },
      { $group: { _id: '$action', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      { $project: { action: '$_id', count: 1, _id: 0 } },
    ]);
  }

  private async getUserStats(query: any): Promise<any[]> {
    return this.auditModel.aggregate([
      { $match: query },
      { $group: { _id: { userId: '$userId', userEmail: '$userEmail' }, count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 },
      { $project: { userId: '$_id.userId', userEmail: '$_id.userEmail', count: 1, _id: 0 } },
    ]);
  }

  private async getSeverityStats(query: any): Promise<any[]> {
    return this.auditModel.aggregate([
      { $match: query },
      { $group: { _id: '$severity', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { severity: '$_id', count: 1, _id: 0 } },
    ]);
  }

  private async getCategoryStats(query: any): Promise<any[]> {
    return this.auditModel.aggregate([
      { $match: query },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $project: { category: '$_id', count: 1, _id: 0 } },
    ]);
  }
}
