import { ArgsType, Field, InputType, ObjectType, Int, ID } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsBoolean, IsMongoId } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { GridTemplate } from '../entities/grid_template.entity';

// ===== CREATE, UPDATE, DELETE DTOs =====

@InputType()
export class CreateGridTemplateInput {
  @Field()
  @IsString()
  name: string;

  @Field(() => GraphQLJSON)
  grid_fields: Record<string, any>[];

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  user_id?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;


  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;
}

@InputType()
export class UpdateGridTemplateInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  grid_fields?: Record<string, any>[];

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  user_id?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;


  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;
}

// ===== PAGINATION AND RESPONSE DTOs =====

@ObjectType()
export class GridTemplatePaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;
}

@ObjectType()
export class GridTemplatesResponse {
  @Field(() => [GridTemplate])
  gridTemplates: GridTemplate[];

  @Field(() => GridTemplatePaginationMeta)
  pagination: GridTemplatePaginationMeta;
}

// ===== FILTER AND SORT INPUT DTOs =====

@InputType()
export class GridTemplateFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  user_id?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

@InputType()
export class GridTemplateSortInput {
  @Field()
  @IsString()
  field: string;

  @Field({ defaultValue: false })
  @IsBoolean()
  ascending: boolean;
}

// ===== MAIN QUERY ARGS DTO =====

@ArgsType()
export class FindAllGridTemplatesArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GridTemplateFilterInput, { nullable: true })
  @IsOptional()
  filters?: GridTemplateFilterInput;

  @Field(() => GridTemplateSortInput, { nullable: true })
  @IsOptional()
  sort?: GridTemplateSortInput;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

}
