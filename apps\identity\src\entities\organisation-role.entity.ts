import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';

export enum OrganisationType {
  MAIN_ORGANISATION = 'mainOrganisation',
  SUB_ORGANISATION = 'subOrganisation',
}

registerEnumType(OrganisationType, {
  name: 'OrganisationType',
});

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'organisation_roles',
})
export class OrganisationRole extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  name: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Array, default: [] })
  permissions: any[];

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Field({ nullable: true })
  updatedAt?: Date;

  @Field(() => OrganisationType)
  @Prop({ type: String, enum: OrganisationType, required: true })
  type: OrganisationType;

  @Field({ nullable: true })
  createdAt?: Date;
}

export const OrganisationRoleSchema = SchemaFactory.createForClass(OrganisationRole);
OrganisationRoleSchema.index({ name: 1, type: 1 }, { unique: true }); 