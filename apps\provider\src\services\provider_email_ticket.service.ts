import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';
import {
  CreateProviderEmailTicketInput,
  UpdateProviderEmailTicketInput,
  ReplyToEmailTicketInput,
} from '../dto/provider_email_ticket.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { MongoConnectionService } from '@app/db';
import { EmailTicket, TicketMessage, Attachment } from '../entities/provider_email_ticket';
import { BaseResponse } from '../dto/base.response.dto';
import { EmailService, KafkaService } from '@app/email';
import { NotificationService, NotificationType, NotificationPriority, NotificationChannel, NotificationPayload, } from '@app/notification';
import { ObjectId, UpdateFilter } from 'mongodb';
import moment from 'moment';

@Injectable()
export class ProviderEmailTicketService {
  constructor(
    private readonly mongoConnectionService: MongoConnectionService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
    private readonly kafkaService: KafkaService
  ) { }

  private async getProviderEmailTicketCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId(
      'email_tickets',
      orgId
    );
  }

  private async getProcessRoleHierarchyCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId(
      'process_role_hierarchies',
    );
  }


  private async getDeviceTokenForUser(userId: string): Promise<string | null> {
    try {
      const collection = await this.mongoConnectionService.getCollectionByOrgId(
        'user_notification_tokens',
      );

      const tokenRecord = await collection.findOne({ userId });

      if (!tokenRecord || !tokenRecord.token) {
        return null;
      }

      return tokenRecord.token;
    } catch (err) {
      console.error(`Error retrieving device token for user ${userId}:`, err);
      return null;
    }
  }


  private async getOrganisationUsers(orgId?: string): Promise<string[]> {
    console.log('🔍 Fetching organisation users with role Manager or Supervisor via hierarchy...');

    if (!orgId) {
      console.warn('⚠️ orgId is required.');
      return [];
    }

    // Step 1: Get user_process_assignments collection
    const assignmentCollection = await this.mongoConnectionService.getCollectionByOrgId('user_process_assignments');
    const assignment = await assignmentCollection.findOne({ orgId });

    if (!assignment?.subOrganisationId) {
      console.warn('⚠️ No subOrganisationId found in user_process_assignments.');
      return [];
    }

    const subOrgId = assignment.subOrganisationId;

    // Step 2: Get processrolehierarchies collection
    const hierarchyCollection = await this.mongoConnectionService.getCollectionByOrgId('processrolehierarchies');
    const hierarchies = await hierarchyCollection.find({ subOrgId }).toArray();

    const userIdSet = new Set<string>();

    for (const hierarchy of hierarchies) {
      if (!Array.isArray(hierarchy.process)) continue;

      for (const process of hierarchy.process) {
        const ops = process?.roles?.operations;
        if (!ops?.manager_to_supervisors) continue;

        // Add manager IDs (keys)
        for (const managerId of Object.keys(ops.manager_to_supervisors)) {
          userIdSet.add(managerId);
        }

        // Add supervisor IDs (values)
        for (const supervisors of Object.values(ops.manager_to_supervisors)) {
          if (Array.isArray(supervisors)) {
            for (const supervisorId of supervisors) {
              if (typeof supervisorId === 'string') {
                userIdSet.add(supervisorId);
              }
            }
          }
        }
      }
    }

    const userIds = Array.from(userIdSet);
    console.log(`✅ Found ${userIds.length} users: ${userIds.join(', ')}`);

    return userIds;
  }


  private async getManagersAndSupervisors(subOrgId: string, processId: string): Promise<string[]> {
    try {
      const hierarchyCollection = await this.getProcessRoleHierarchyCollection();

      const hierarchy = await hierarchyCollection.findOne({
        subOrgId,
        "process.processId": processId
      });

      if (!hierarchy || !hierarchy.process || !Array.isArray(hierarchy.process)) {
        console.log('No hierarchy found for orgId:', 'subOrgId:', subOrgId, 'processId:', processId);
        return [];
      }

      const processObj = hierarchy.process.find((p: any) =>
        p.processId.toString() === processId.toString()
      );

      if (!processObj || !processObj.roles) {
        console.log('No process roles found for processId:', processId);
        return [];
      }

      const userIds = new Set<string>();

      // Get operations managers and supervisors
      const operations = processObj.roles.operations || {};
      const managerToSupervisors = operations.manager_to_supervisors || {};
      const supervisorToAgents = operations.supervisor_to_agents || {};

      // Add all managers
      Object.keys(managerToSupervisors).forEach(managerId => {
        userIds.add(managerId);
      });

      // Add all supervisors
      Object.keys(supervisorToAgents).forEach(supervisorId => {
        userIds.add(supervisorId);
      });

      const result = Array.from(userIds);
      console.log(`Found ${result.length} managers/supervisors for notification:`, result);
      return result;
    } catch (error) {
      console.error('Error getting managers and supervisors:', error);
      return [];
    }
  }

  // FIX ME: change separte function org user reterview
  private async getUserEmail(userId: string): Promise<string | undefined> {
    const userCollection = await this.mongoConnectionService.getCollectionByOrgId('users');
    const user = await userCollection.findOne({ _id: new ObjectId(userId) });

    if (user?.email) return user.email;

    const orgUserCollection = await this.mongoConnectionService.getCollectionByOrgId('organisation_users');
    const orgUser = await orgUserCollection.findOne({ _id: new ObjectId(userId) });

    return orgUser?.email;
  }



  async findAll(
    search?: string,
    selectedFields?: Record<string, any>, // optional field projection
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page = 1,
    limit = 10,
    userId?: string,
    orgId?: string,
    roleName?: string,
  ): Promise<{
    providerEmailTickets: any[];
    pagination: {
      page: number;
      limit: number;
      total?: number;
      totalItems: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {

    const userCollection = await this.getProviderEmailTicketCollection(orgId);
    const sampleDoc = await userCollection.findOne();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

    const excludedFields = [
      '__v', '_id', 'values', 'flattenedValues', 'token', 'secretKey',
      'qrcode', 'attempt', 'dailyOtpAttempts', 'lastOtpAttemptDate'
    ];

    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
      searchableFields = Object.keys(selectedFields).filter(
        key => selectedFields[key] === 1 && !excludedFields.includes(key)
      );
    } else {
      projection = allFields.reduce((acc, key) => {
        if (!excludedFields.includes(key)) acc[key] = 1;
        return acc;
      }, {} as Record<string, number>);
      searchableFields = allFields.filter(field => !excludedFields.includes(field));
    }

    let query: any = {};
    if (roleName === 'qc-agent') {
      query.assignedToAgent = userId;
    }
    if (roleName === 'agent') {
      query.assignedToAgent = userId
    }

    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i',
      };

      query.$or = searchableFields.map(field => ({ [field]: regex }));

      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: new Types.ObjectId(searchTerm) });
      }

      if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
        const searchDate = new Date(searchTerm);
        if (!isNaN(searchDate.getTime())) {
          const start = new Date(searchDate); start.setHours(0, 0, 0, 0);
          const end = new Date(searchDate); end.setHours(23, 59, 59, 999);
          query.$or.push(
            { createdAt: { $gte: start, $lte: end } },
            { updatedAt: { $gte: start, $lte: end } }
          );
        }
      }
    }

    let parsedFilters: Record<string, any> = {};
    const typeMapping: Record<string, number> = {
      ticket: 2,
      email: 1,
    };

    if (filters) {
      try {
        parsedFilters =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters?.filters || filters;

        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value !== undefined && value !== '') {
            if (key === 'type') {
              const mappedValue = typeMapping[value.toLowerCase()];
              if (mappedValue !== undefined) {
                query.type = mappedValue;
              }
              continue; // ✅ Prevents overwriting with regex
            }

            if (key === 'updatedAt' || key === 'createdAt') {
              const date = moment(value, 'DD-MM-YYYY').startOf('day').toDate();
              const nextDate = moment(value, 'DD-MM-YYYY').endOf('day').toDate();
              query[key] = { $gte: date, $lte: nextDate };
              continue; // ✅ Prevents overwriting with regex
            }

            // ✅ Default case: case-insensitive regex
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i',
                }
                : value;
          }
        }
      } catch (err) {
        console.warn('Invalid filters provided. Skipping filters.', err);
      }
    }


    const sort: Record<string, 1 | -1> = {};
    sort[sortBy || 'createdAt'] = sortOrder === 'desc' ? 1 : -1;

    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;



    const totalItems = await userCollection.countDocuments(query);
    const providerEmailTickets = await userCollection
      .find(query)
      .project(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .toArray();

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      providerEmailTickets,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems, // always a number, never null
        totalItems: totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async findById(id: string, orgId?: string): Promise<EmailTicket> {
    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Invalid ID format'
      );
    }

    const userCollection = await this.getProviderEmailTicketCollection(orgId);
    const doc = await userCollection.findOne({ _id: new Types.ObjectId(id) });

    if (!doc) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider email ticket not found'
      );
    }

    if (!Array.isArray(doc.messages)) {
      doc.messages = [];
    }

    return doc as EmailTicket;
  }
  async create(
    input: CreateProviderEmailTicketInput,
    orgId?: string,
    userId?: string,
    subOrgId?: string,
    processId?: string
  ): Promise<BaseResponse> {
    try {
      const userCollection = await this.getProviderEmailTicketCollection(orgId);
      const now = new Date();

      // Generate conversationId early to avoid post-insert update
      const insertedId = new ObjectId();
      const conversationId = insertedId.toString();

      const insertPayload: any = {
        ...input,
        _id: insertedId,
        conversationId,
        isAllocated: false,
        createdAt: now,
        updatedAt: now,
        created_by: userId,
        status: 'pending',
        values: input.values || [],
        type: input.type,
        ticket_type: input.ticket_type,
        ticket_process: input.ticket_process,
        messages: [],
      };
      delete insertPayload.createdBy;

      await userCollection.insertOne(insertPayload);

      let managers: string[] = [];    
        managers = await this.getOrganisationUsers(orgId);
     

      await Promise.all(
        managers.map(async (managerId) => {
          try {
            const [deviceTokens, email] = await Promise.all([
              this.getDeviceTokenForUser(managerId),
              this.getUserEmail(managerId),
            ]);

            const payload: NotificationPayload = {
              userId: managerId,
              senderId: userId || '',
              type: NotificationType.SOURCE,
              title: 'New Ticket Assigned',
              message: `A new ticket "${input.subject}" has been created and requires attention`,
              priority: NotificationPriority.HIGH,
              channels: [NotificationChannel.IN_APP],
              isRouted: true,
              data: {
                ticketId: conversationId,
                subject: input.subject,
                ticket_type: input.type,
                ticketType: input.ticket_type,
                status: 'pending',
                createdBy: userId,
                role: 'manager_supervisor',
                notificationId: new ObjectId().toString(),
                deviceTokens: Array.isArray(deviceTokens) ? deviceTokens : [deviceTokens],
              },
              metadata: {
                ticketId: conversationId,
                ticketType: input.ticket_type,
                subject: input.subject,
                orgId: orgId || '',
                subOrgId: subOrgId || '',
                processId: input.ticket_process || processId?.toString() || '',
              },
            };

            await this.kafkaService.publishToKafka(payload, 'notification');

            if (email) {
              const html = `
              <html>
                <head>
                  <style>
                    body { font-family: Arial, sans-serif; background-color: #f4f6f9; padding: 20px; color: #333; }
                    .container { background: #fff; padding: 20px; border-radius: 8px; max-width: 600px; margin: auto; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); }
                    .btn { margin-top: 20px; display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; }
                    .footer { font-size: 12px; color: #888; text-align: center; margin-top: 30px; }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <h2>🩺 New Health Ticket Created</h2>
                    <p>Dear <strong>Manager</strong>,</p>
                    <p>A new healthcare support ticket has been created:</p>
                    <ul>
                      <li><strong>Subject:</strong> ${input.subject}</li>
                      <li><strong>Type:</strong> Ticket</li>
                      <li><strong>Priority:</strong> ${input.priority || 'Normal'}</li>
                      <li><strong>Status:</strong> Pending</li>
                    </ul>
                    <p>Please log in to the portal to review and take action.</p>
                    <a href="https://devrcmgenie.asprcmsolutions.com" class="btn">Open Ticket</a>
                    <div class="footer">This is an automated message from the Healthcare Support System.</div>
                  </div>
                </body>
              </html>
            `;
              await this.emailService.sendEmail(email, html, '🩺 New Ticket Created');
            }
          } catch (err) {
            console.error(`❌ Failed to notify manager ${managerId}:`, err);
          }
        })
      );

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { insertedId }
      );
    } catch (error: any) {
      console.error('❌ Failed to create provider email ticket:', error);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to create provider email ticket: ${error.message}`
      );
    }
  }

  async replyToEmailThread(
    conversationId: string,
    input: ReplyToEmailTicketInput,
    orgId?: string,
    userId?: string,
    subOrgId?: string,
    processId?: string
  ): Promise<BaseResponse> {
    try {
      console.log('Fetching ticket...');
      const collection = await this.getProviderEmailTicketCollection(orgId);
      const ticket = await collection.findOne({ conversationId });

      if (!ticket) {
        console.error(`Ticket not found for conversationId: ${conversationId}`);
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          `No thread found for conversationId: ${conversationId}`
        );
      }

      const now = new Date();
      const cleanBody = input.body?.replace(/<\/?[^>]+(>|$)/g, '') || '';
      const ticketId = ticket._id.toString();
      const emailTemplate = this.generateEmailTemplate(ticket.subject, cleanBody);

      const replyMessage: TicketMessage = {
        from: input.from,
        to: input.to,
        body: cleanBody,
        message: input.message,
        attachments: this.mapAttachmentsWithValidation(input.attachments),
        messageId: input.messageId,
        date: now,
      };

      console.log('Appending reply to ticket...');
      await collection.updateOne(
        { conversationId },
        { $push: { messages: replyMessage as any }, $set: { updatedAt: now } }
      );

      const status = ticket.status?.toLowerCase();
      const isPushToClient = input.pushNotification === 'client';

      const userEmailCache = new Map<string, string>();
      const deviceTokenCache = new Map<string, string | string[] | null>();

      const shouldSendEmail = async () => {
        if (!input.messageId || !input.to) return;
        const payload = { ...replyMessage, conversationId, timestamp: now.toISOString(), cleanBody };
        console.log('Publishing email reply to Kafka...');
        return this.kafkaService.publishToKafka(payload, 'email-replies');
      };

      const shouldManagersSendEmail = async (id: string) => {
        if (!id || id === userId) return;
        let email = userEmailCache.get(id);
        if (email === undefined) {
          console.log(`Fetching email for user: ${id}`);
          email = await this.getUserEmail(id);
          userEmailCache.set(id, email || '');
        }
        if (!email) return;
        console.log(`Sending email to manager: ${email}`);
        return this.emailService.sendEmail(email, emailTemplate, '🩺 New Ticket Reply');
      };

      const sendPushNotification = async (id: string, role: string) => {
        if (!id || id === userId) return;
        let token = deviceTokenCache.get(id);
        if (token === undefined) {
          console.log(`Fetching device token for user: ${id}`);
          token = await this.getDeviceTokenForUser(id);
          deviceTokenCache.set(id, token);
        }

        const payload: NotificationPayload = {
          userId: id,
          senderId: userId || '',
          type: NotificationType.SOURCE,
          title: `Reply Received for Ticket: ${ticket.subject}`,
          message: cleanBody,
          priority: NotificationPriority.MEDIUM,
          channels: [NotificationChannel.IN_APP],
          data: {
            ticketId,
            conversationId,
            subject: ticket.subject,
            role,
            notificationId: new ObjectId().toString(),
            deviceTokens: Array.isArray(token) ? token : [token],
          },
          isRouted: true,
          metadata: { ticketId, ticketType: ticket.ticket_type, subject: ticket.subject, orgId, subOrgId, processId },
        };

        console.log(`Sending push notification to user ${id} with role ${role}`);
        return this.kafkaService.publishToKafka(payload, 'notification');
      };

      const lead = ticket.assignedToLead;
      const agent = ticket.assignedToAgent;
      const createdBy = ticket.created_by;

      const pushAndEmail = async (id: string, role: string) => {
        console.log(`Pushing and emailing to user ${id} with role ${role}`);
        await Promise.all([
          shouldManagersSendEmail(id),
          sendPushNotification(id, role),
        ]);
      };

      if (status === 'pending') {
        console.log('Handling PENDING ticket...');
        if (ticket.type === 1) {
          if (isPushToClient) await shouldSendEmail();
          await Promise.all([
            sendPushNotification(createdBy, 'client'),
            shouldManagersSendEmail(createdBy),
          ]);
        } else if (ticket.type === 2) {
          if (isPushToClient) {
            await sendPushNotification(createdBy, 'client');
          } else {
            await Promise.all([
              shouldManagersSendEmail(createdBy),
              sendPushNotification(createdBy, 'client'),
            ]);
          }
        }
      } else if (status === 'open') {
        console.log('Handling OPEN ticket...');
        if (isPushToClient) {
          if (ticket.type === 1) {
            await shouldSendEmail();
            await Promise.all([
              pushAndEmail(lead, 'lead'),
              pushAndEmail(agent, 'agent'),
            ]);
          } else if (ticket.type === 2) {
            await Promise.all([
              sendPushNotification(createdBy, 'client'),
              pushAndEmail(lead, 'lead'),
              pushAndEmail(agent, 'agent'),
            ]);
            return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.PROVIDER, { conversationId });
          }
        } else if (lead || agent) {
          await shouldSendEmail();
          if (lead === agent) {
            await pushAndEmail(lead, 'lead_agent');
          } else {
            await Promise.all([
              pushAndEmail(lead, 'lead'),
              pushAndEmail(agent, 'agent'),
            ]);
          }
          return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.PROVIDER, { conversationId });
        }
      }

      if (orgId) {
        console.log('Pushing notifications to organization managers...');
        const managers = await this.getOrganisationUsers(orgId);
        await Promise.all(managers.map(id => pushAndEmail(id, 'manager_supervisor')));
      }

      console.log('Reply successfully processed.');
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.PROVIDER, { conversationId });

    } catch (error: any) {
      console.error(`Error in replyToEmailThread: ${error.message}`);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Reply to thread failed: ${error.message}`
      );
    }
  }


  private generateEmailTemplate(subject: string, body: string): string {
    return `
  <div style="font-family: Arial, sans-serif; background-color: #f4f6f8; padding: 20px;">
    <table style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px;">
      <tr>
        <td style="background-color: #1976d2; color: white; padding: 16px 24px; text-align: center;">
          <h2 style="margin: 0;">🩺 Ticket Reply Notification</h2>
        </td>
      </tr>
      <tr>
        <td style="padding: 24px;">
          <p style="font-size: 16px;">A new reply has been added to the ticket titled <strong>${subject}</strong>.</p>
          <div style="background-color: #f1f1f1; padding: 12px 16px; margin: 16px 0;">
            <p style="margin: 0;">${body}</p>
          </div>
          <a style="padding: 10px 20px; background-color: #1976d2; color: white; text-decoration: none; border-radius: 4px;">View Ticket</a>
        </td>
      </tr>
      <tr>
        <td style="background-color: #fafafa; text-align: center; padding: 12px; font-size: 12px; color: #999;">
          This is an automated message from the Healthcare Support System.
        </td>
      </tr>
    </table>
  </div>`;
  }


  async update(
    input: UpdateProviderEmailTicketInput,
    orgId?: string,
    userId?: string,
    subOrgId?: string,
    processId?: string
  ): Promise<BaseResponse> {
    const { id, messages = [], ...updateData } = input;

    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Invalid ID format'
      );
    }
    const collection = await this.getProviderEmailTicketCollection(orgId);
    const existingTicket = await collection.findOne({ _id: new Types.ObjectId(id) });

    if (!existingTicket) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider email ticket not found'
      );
    }
    const previousStatus = existingTicket.status;
    const updateQuery: any = {
      $set: {
        ...updateData,
        updatedAt: new Date(),
        updatedBy: userId,
      },
    };

    if (messages.length) {
      updateQuery.$push = { messages: { $each: messages } };
    }

    const result = await collection.findOneAndUpdate(
      { _id: new Types.ObjectId(id) },
      updateQuery,
      { returnDocument: 'after' }
    );

    if (!result) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider email ticket not found after update'
      );
    }
    const ticket = result;
    const ticketId = ticket._id.toString();
    const createdBy = ticket.created_by;
    const conversationId = ticket.conversationId;
    const status = ticket.status?.toLowerCase();
    const isPushToClient = input.pushNotification === 'client';

    const userEmailCache = new Map<string, string>();
    const deviceTokenCache = new Map<string, string | string[] | null>();

    const sendEmailTo = async (targetUserId: string, subject: string, body: string) => {
      if (!targetUserId || targetUserId === userId) return;
      let email = userEmailCache.get(targetUserId);
      if (email === undefined) {
        email = await this.getUserEmail(targetUserId);
        userEmailCache.set(targetUserId, email || '');
      }
      if (email) {
        await this.emailService.sendEmail(email, body, subject);
      }
    };

    const sendPushNotification = async (targetUserId: string, role: string, message: string) => {
      if (!targetUserId) return;
      let token = deviceTokenCache.get(targetUserId);
      if (token === undefined) {
        token = await this.getDeviceTokenForUser(targetUserId);
        deviceTokenCache.set(targetUserId, token);
      }

      const payload: NotificationPayload = {
        userId: targetUserId,
        senderId: userId || '',
        type: NotificationType.CUSTOM,
        title: `Ticket Update: ${ticket.subject}`,
        message,
        priority: NotificationPriority.MEDIUM,
        channels: [NotificationChannel.IN_APP],
        data: {
          ticketId,
          conversationId,
          subject: ticket.subject,
          role,
          notificationId: new ObjectId().toString(),
          deviceTokens: Array.isArray(token) ? token : [token],
        },
        metadata: { ticketId, ticketType: ticket.ticket_type, subject: ticket.subject, orgId, subOrgId, processId },
      };

      await this.kafkaService.publishToKafka(payload, 'notification');
    };

    const notifyManagersOfStatusChange = async (
      action: 'closed' | 'reopen',
    ) => {
      const subjectMap = {
        closed: '📬 Ticket Closed',
        reopen: '📬 Ticket Reopened',
      };
      const bodyMap = {
        closed: `The ticket "${ticket.subject}" has been securely closed.`,
        reopen: `The ticket "${ticket.subject}" has been reopened. Please proceed with care – no PHI included.`,
      };

      const managers = await this.getOrganisationUsers(orgId);

      await Promise.all([
        ...managers.map((id) =>
          sendEmailTo(id, subjectMap[action], bodyMap[action])
        ),
        ...managers.map((id) =>
          sendPushNotification(id, 'manager_supervisor', bodyMap[action])
        ),
      ]);
    };

    // HIPAA-compliant Notification Logic
    if (status === 'closed' || status === 'reopen') {
      const replyMessage = messages?.[0]?.reason || 'New update on your ticket.';

      if (ticket.type === 1) {
        await Promise.all([
          sendPushNotification(createdBy, 'client', replyMessage),
          sendEmailTo(createdBy, '🩺 Ticket Update', replyMessage),
        ]);
      } else if (ticket.type === 2) {
        if (isPushToClient) {
          await sendPushNotification(createdBy, 'client', replyMessage);
        } else {
          await notifyManagersOfStatusChange(status as 'closed' | 'reopen');
        }
      }
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.PROVIDER,
      { providerEmailTicket: ticket }
    );
  }


  async delete(id: string, orgId?: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Invalid ID format'
      );
    }

    const userCollection = await this.getProviderEmailTicketCollection(orgId);
    const result = await userCollection.findOneAndDelete({ _id: new Types.ObjectId(id) });

    if (!result?.value) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider email ticket not found'
      );
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.PROVIDER,
      { message: 'Provider email ticket deleted successfully' }
    );
  }

  /**
   * Maps and validates attachments from input to proper Attachment objects
   * @param attachments - Array of attachment inputs
   * @returns Array of validated Attachment objects or undefined
   */
  private mapAttachmentsWithValidation(attachments?: any[]): Attachment[] | undefined {
    if (!attachments || !Array.isArray(attachments)) {
      return undefined;
    }

    return attachments.map(att => {
      try {
        // Validate required attachment properties
        if (!att.fileName || !att.url || !att.type) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.PROVIDER,
            'Missing required attachment properties: fileName, url, or type'
          );
        }

        // Validate that uploadedAt is a valid date string
        const uploadedDate = new Date(att.uploadedAt);
        if (isNaN(uploadedDate.getTime())) {
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.PROVIDER,
            `Invalid date format for uploadedAt: ${att.uploadedAt}`
          );
        }

        return {
          fileName: att.fileName,
          url: att.url,
          type: att.type,
          uploadedAt: uploadedDate,
        } as Attachment;
      } catch (error) {
        // Log the error and provide a fallback attachment
        console.warn(`Error processing attachment ${att?.fileName || 'unknown'}:`, error.message);
        return {
          fileName: att?.fileName || 'unknown',
          url: att?.url || '',
          type: att?.type || 'unknown',
          uploadedAt: new Date(), // Fallback to current date
        } as Attachment;
      }
    });
  }
}