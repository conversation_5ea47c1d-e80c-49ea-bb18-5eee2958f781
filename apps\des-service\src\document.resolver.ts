import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { DocumentService } from './document.service';
import { DocumentEntity } from './entities/document.entity';
import { FindAllDocumentsArgs, CreateDocumentInput, UpdateDocumentInput } from './dto/document.dto';
import { BaseResponse } from './dto/base.response.dto';
import {
  ErrorType,
  HttpStatus,
  ResponseCode,
  Success,
  Error
} from '@app/error';

@Resolver(() => DocumentEntity)
export class DocumentResolver {
  constructor(private readonly documentService: DocumentService) {}

  @Query(() => BaseResponse, { name: 'documents' })
  async getDocuments(@Args() args: FindAllDocumentsArgs): Promise<BaseResponse> {
    try {
      const result = await this.documentService.findAll(args);
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DOCUMENT,
        { ...result }
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      return new Error(
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.responseCode || ResponseCode.INTERNAL_SERVER_ERROR,
        error.errorType || ErrorType.DOCUMENT,
        error.message || 'An unexpected error occurred while fetching documents.'
      ).toJSON() as unknown as BaseResponse;
    }
  }

  @Query(() => BaseResponse, { name: 'document' })
  async getDocument(@Args('id', { type: () => ID }) id: string): Promise<BaseResponse> {
    try {
      const document = await this.documentService.findById(id);
      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DOCUMENT,
        { document }
      ).toJSON() as unknown as BaseResponse;
    } catch (error) {
      return new Error(
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.responseCode || ResponseCode.INTERNAL_SERVER_ERROR,
        error.errorType || ErrorType.DOCUMENT,
        error.message || 'An unexpected error occurred while fetching the document.'
      ).toJSON() as unknown as BaseResponse;
    }
  }

  @Mutation(() => BaseResponse)
  async createDocument(@Args('input') input: CreateDocumentInput, @Context() context: any): Promise<BaseResponse> {
    try {
      return await this.documentService.create(input, context);
    } catch (error) {
      return new Error(
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.responseCode || ResponseCode.INTERNAL_SERVER_ERROR,
        error.errorType || ErrorType.DOCUMENT,
        error.message || 'An unexpected error occurred while creating the document.'
      ).toJSON() as unknown as BaseResponse;
    }
  }

  @Mutation(() => BaseResponse)
  async updateDocument(@Args('input') input: UpdateDocumentInput, @Context() context: any): Promise<BaseResponse> {
    try {
      return await this.documentService.update(input, context);
    } catch (error) {
      return new Error(
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.responseCode || ResponseCode.INTERNAL_SERVER_ERROR,
        error.errorType || ErrorType.DOCUMENT,
        error.message || 'An unexpected error occurred while updating the document.'
      ).toJSON() as unknown as BaseResponse;
    }
  }

  @Mutation(() => BaseResponse)  
  async deleteDocument(@Args('id', { type: () => ID }) id: string): Promise<BaseResponse> {
    try {
      return await this.documentService.delete(id);
    } catch (error) {
      return new Error(
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
        error.responseCode || ResponseCode.INTERNAL_SERVER_ERROR,
        error.errorType || ErrorType.DOCUMENT,
        error.message || 'An unexpected error occurred while deleting the document.'
      ).toJSON() as unknown as BaseResponse;
    }
  }
} 