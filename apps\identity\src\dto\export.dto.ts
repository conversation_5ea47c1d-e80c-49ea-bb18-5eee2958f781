import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsString, IsArray, ArrayNotEmpty, IsOptional, IsIn } from 'class-validator';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class StartExportInput {
  @Field()
  @IsString()
  collection: string;

  @Field(() => [String])
  @IsArray()
  @ArrayNotEmpty()
  fields: string[];

  @Field()
  @IsString()
  createdBy: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  orgId?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  selectedRow?: string[];

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['CSV', 'JSON'])
  fileType?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;
}

@ObjectType()
export class ExportTaskResponse {
  @Field()
  message: string;

  @Field()
  taskId: string;
} 