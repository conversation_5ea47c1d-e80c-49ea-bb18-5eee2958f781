import { InputType, Field, ID, ObjectType, Int, Directive } from '@nestjs/graphql';
import {
  IsOptional,
  IsString,
  IsMongoId,
  IsIn,
  IsInt,
  Min,
  Max,
  ValidateNested,
} from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { Type } from 'class-transformer';
import { ProviderEntryType } from '../entities/provider.entity'; // updated import path
@InputType()
export class TicketValueInput {
  @Field()
  @IsString()
  field: string;

  @Field()
  @IsString()
  value: string;
}


@InputType()
export class AttachmentInput {
  @Field()
  @IsString()
  fileName: string;

  @Field()
  @IsString()
  url: string;

  @Field()
  @IsString()
  uploadedAt: string; // or use Date if you're parsing to Date

  @Field()
  @IsString()
  type: string;
}

@InputType()
export class TicketMessageInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  from: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  to?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  body?: string;

  @Field(() => Date)
  date: Date;

  @Field(() => [AttachmentInput], { nullable: true })
  @IsOptional()
  attachments?: AttachmentInput[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  messageId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  reason?: string;

}

@InputType()
export class CreateProviderEmailTicketInput {


  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  to?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  from?: string;

  @Field()
  @IsString()
  subject: string;

  @Field(() => ProviderEntryType, { nullable: true })
  @IsOptional()
  type?: ProviderEntryType;

  @Field({ nullable: true })
  @IsOptional()
  ticket_type?: string;

  @Field({ nullable: true })
  @IsOptional()
  ticket_process?: string;

  // FIX ME: change
  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['low', 'medium', 'high', 'urgent'])
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  @Field()
  @IsString()
  createdBy: string;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  description?: string;

  @Field(() => [TicketMessageInput], { nullable: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TicketMessageInput)
  messages?: TicketMessageInput[];

  @Field(() => String, { nullable: true })
  @IsOptional()
  conversationId?: string;

  @Field(() => GraphQLJSON)
  values: Record<string, any>;



}




@InputType()
export class ReplyToEmailTicketInput {

  @Field(() => ID)
  @IsMongoId()
  @IsString()
  conversationId: string;

  @Field({ nullable: true })
  @IsOptional()
  from?: string;

  @Field({ nullable: true })
  @IsOptional()
  to?: string;

  @Field({ nullable: true })
  @IsOptional()
  body?: string;

  @Field({ nullable: true })
  @IsOptional()
  message?: string;

  @Field(() => [AttachmentInput], { nullable: true })
  @IsOptional()
  attachments?: AttachmentInput[];

  @Field({ nullable: true })
  @IsOptional()
  messageId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  pushNotification?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  reason?: string;
}


@InputType()
export class UpdateProviderEmailTicketInput {
  @Field(() => ID)
  @IsMongoId()
  @IsString()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  subject?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['Open', 'Closed', 'In Progress'])
  status?: 'Open' | 'Closed' | 'In Progress';

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['low', 'medium', 'high', 'urgent'])
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  @Field(() => [TicketMessageInput], { nullable: true })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => TicketMessageInput)
  messages?: TicketMessageInput[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => [TicketValueInput], { nullable: true })
  @IsOptional()
  values?: TicketValueInput[];

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  messageId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  pushNotification?: string;
}

@InputType()
export class PaginateProviderEmailTicketArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;
}

@InputType()
export class PaginateProviderTicketArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;
}

@ObjectType()
@Directive('@shareable')
export class PaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class ProviderEmailTicketResponse {
  @Field(() => [GraphQLJSON])
  providerEmailTickets: any;

  @Field(() => PaginationMeta)
  pagination: PaginationMeta;
}
