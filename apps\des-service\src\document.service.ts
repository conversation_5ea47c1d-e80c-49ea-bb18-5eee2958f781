import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as jwt from 'jsonwebtoken';
import { DocumentEntity } from './entities/document.entity';
import { FindAllDocumentsArgs, DocumentsResponse, CreateDocumentInput, UpdateDocumentInput } from './dto/document.dto';
import { BaseResponse } from './dto/base.response.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class DocumentService {
  constructor(
    @InjectModel(DocumentEntity.name) private readonly documentModel: Model<DocumentEntity>,
    private readonly mongoConnectionService: MongoConnectionService
  ) { }

  /**
   * Common method to get document collection with proper error handling
   * @param orgId - Organization ID, defaults to empty string if not provided
   * @returns Promise resolving to the document collection
  //  */
  // private async getDocumentCollection(orgId?: string) {
  //   return await this.mongoConnectionService.getCollectionByOrgId(orgId, 'documents');
  // }

  /**
   * Common method to get tenant database with proper error handling
   * @param orgId - Organization ID
   * @returns Promise resolving to the tenant database
   */
  private async getTenantDatabase(orgId?: string) {
    return await this.mongoConnectionService.getDatabaseByOrgId(orgId);
  }

  private getUserIdFromToken(context: any): string | undefined {
    const authHeader = context.req?.headers?.authorization ?? '';
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        return typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
      } catch (jwtError) {
        console.error('JWT verification failed:', jwtError);
        return undefined;
      }
    }
    return undefined;
  }
  
  private transformDocument(doc: any): DocumentEntity {
    if (!doc) {
      return doc;
    }
    const transformed = doc.toObject ? doc.toObject() : { ...doc };
    if (transformed._id) {
      transformed.id = transformed._id.toString();
      delete transformed._id;
    }
    if (transformed.__v) {
      delete transformed.__v;
    }
    if (typeof transformed.values === 'string') {
      try {
        transformed.values = JSON.parse(transformed.values);
      } catch (e) {
        // Keep it as a string if it's not valid JSON
      }
    }
    return transformed as DocumentEntity;
  }

  async findAll(args: FindAllDocumentsArgs): Promise<DocumentsResponse> {
    try {
      const { page = 1, limit = 10, search, filters, sort } = args;
      const skip = (page - 1) * limit;

      let query: any = {};
console.log("DocumentService initialized",ErrorType.DOCUMENT);
      if (search && search.trim()) {
        const searchTerm = search.trim();
        const searchRegex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        query.$or = [
          { documentName: searchRegex },
          { documentType: searchRegex },
        ];
      }

      if (filters) {
        if (filters.documentName) {
            query.documentName = { $regex: filters.documentName, $options: 'i' };
        }
        if (filters.documentType) {
            query.documentType = { $regex: filters.documentType, $options: 'i' };
        }
      }

      let sortQuery: any = { createdAt: -1 }; 
      if (sort && sort.field) {
        sortQuery = { [sort.field]: sort.ascending ? 1 : -1 };
      }

      const documentsDocs = await this.documentModel
        .find(query)
        .sort(sortQuery)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec();

      const documents = documentsDocs.map(d => this.transformDocument(d));
      const total = await this.documentModel.countDocuments(query).exec();
      
      return this.buildResponse(documents, total, page, limit);

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DOCUMENT,
        `Failed to retrieve documents: ${error.message}`,
      );
    }
  }

  async findById(id: string): Promise<DocumentEntity> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.DOCUMENT,
          'Invalid ID format',
        );
      }

      const document = await this.documentModel.findById(id).lean().exec();
      if (!document) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.DOCUMENT,
          `Document with ID ${id} not found`,
        );
      }

      return this.transformDocument(document);

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DOCUMENT,
        `Error finding document: ${error.message}`,
      );
    }
  }

  async create(input: CreateDocumentInput, context: any): Promise<BaseResponse> {
    try {
      const userId = this.getUserIdFromToken(context);

      const documentData = {
        ...input,
        values: typeof input.values === 'object' ? JSON.stringify(input.values) : input.values,
        isActive: true,
        createdBy: userId,
      };

      const newDocument = new this.documentModel(documentData);
      await newDocument.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.DOCUMENT,
        { document: this.transformDocument(newDocument.toObject()) },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DOCUMENT,
        `Failed to create document: ${error.message}`,
      );
    }
  }

  async update(input: UpdateDocumentInput, context: any): Promise<BaseResponse> {
    try {
      const { id, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.DOCUMENT,
          'Invalid ID format',
        );
      }

      const document = await this.documentModel.findById(id);
      if (!document) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.DOCUMENT,
          'Document not found',
        );
      }
      
      const userId = this.getUserIdFromToken(context);

      const processedUpdateData: any = { ...updateData };
      if (processedUpdateData.values) {
        processedUpdateData.values = typeof processedUpdateData.values === 'object'
          ? JSON.stringify(processedUpdateData.values)
          : processedUpdateData.values;
      }

      if (userId) {
        processedUpdateData.updatedBy = userId;
      }

      const updatedDocument = await this.documentModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      if (!updatedDocument) {
        throw new Error(
            HttpStatus.NOT_FOUND,
            ResponseCode.NOT_FOUND,
            ErrorType.DOCUMENT,
            'Document not found after update',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DOCUMENT,
        { document: this.transformDocument(updatedDocument.toObject()) },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DOCUMENT,
        `Failed to update document: ${error.message}`,
      );
    }
  }
 
  async delete(id: string): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.DOCUMENT,
          'Invalid ID format',
        );
      }

      const deletedDocument = await this.documentModel.findByIdAndDelete(id).exec();

      if (!deletedDocument) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.DOCUMENT,
          'Document not found',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.DOCUMENT,
        { message: 'Document deleted successfully' },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.DOCUMENT,
        `Failed to delete document: ${error.message}`,
      );
    }
  }

  private buildResponse(documents: any[], total: number, page: number, limit: number): DocumentsResponse {
    const totalItems = total;
    const totalPages = Math.ceil(totalItems / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      documents,
      pagination: {
        page,
        limit,
        total,
        totalItems,
        totalPages,
        hasNext,
        hasPrev,
      },
    };
  }
} 