import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
// import { ImportFailedRow } from './import-failed-row.entity';

@ObjectType()
export class StatusLog {
  @Field()
  status: string;
  @Field()
  message: string;
  @Field()
  timestamp: Date;
}

@ObjectType()
export class FailedRowInfo {
  @Field(() => String)
  row: string;
  @Field(() => String)
  error: string;
  @Field(() => Int, { nullable: true })
  rowNumber?: number;
}

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'taskqueue',
})
export class TaskQueue extends Document {
  @Field(() => ID)
  @Prop({ required: true, unique: true })
  taskId: string;

  @Field()
  @Prop({ required: true })
  type: 'import' | 'export';

  // Common fields
  @Field({ nullable: true })
  @Prop() name?: string;

  @Field({ nullable: true })
  @Prop() createdBy?: string;

  @Field({ nullable: true })
  @Prop() createdByEmail?: string;

  @Field({ nullable: true })
  @Prop() orgId?: string;

  @Field({ nullable: true })
  @Prop() status?: string;
  
  @Field(() => [StatusLog], { nullable: true })
  @Prop({
    type: [
      {
        status: { type: String },
        message: { type: String },
        timestamp: { type: Date },
      },
    ],
    default: [],
  })
  statusLog?: StatusLog[];

  @Field(() => [FailedRowInfo], { nullable: true })
  @Prop({ type: [Object], default: [] }) failedRows?: FailedRowInfo[];

   @Field(() => [String], { nullable: true })
  @Prop() importedIds?: string[];

  @Field(() => Int, { nullable: true })
  @Prop() unchangedCount?: number;

  @Field({ nullable: true })
  @Prop() auditLogUrl?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object, default: null })
  backupUrls?: Record<string, {
    url: string;
    format: string;
    recordCount: number;
    timestamp: Date;
    status: string;
  }>;

  @Field({ nullable: true })
  @Prop()
  createdAt?: Date;

  @Field({ nullable: true })
  @Prop()
  updatedAt?: Date;

  // Import-specific fields
  // @Field(() => GraphQLJSON, { nullable: true })
  // @Prop({ type: Object })
  // mapping?: Record<string, string>;
  // @Field({ nullable: true })
  // @Prop() filePath?: string;
  // @Field({ nullable: true })
  // @Prop() dbName?: string;

  // Export-specific fields
  @Field({ nullable: true })
  @Prop() collectionName?: string;

  @Field(() => [String], { nullable: true })
  @Prop([String]) fields?: string[];

  @Field({ nullable: true })
  @Prop() fileType?: string;

  @Field({ nullable: true })
  @Prop() downloadUrl?: string;

  @Field(() => [String], { nullable: true })
  @Prop([String]) selectedRow?: string[];
  @Prop() file?: Buffer;

  // @Field(() => [String], { nullable: true })
  // @Prop([String]) uniqueFields?: string[];

  @Field({ nullable: true })
  @Prop() importMode?: string; // 'upsert', 'update', or 'create'

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'ImportConfiguration' })
  configurationId?: Types.ObjectId; // Reference to ImportConfiguration

  @Field({ nullable: true })
  @Prop({ default: 0 }) insertedCount?: number;

  @Field({ nullable: true })
  @Prop({ default: 0 }) updatedCount?: number;

  @Field({ nullable: true })
  @Prop({ default: 0 }) failedCount?: number;

  @Field(() => Int, { nullable: true })
  @Prop({ default: 0 }) totalRecords?: number; // Total records from Excel/CSV file

  @Field({ nullable: true })
  @Prop() isStopOnError?: boolean; // Stop on first error flag

  @Field({ nullable: true })
  @Prop() backupUrl?: string; // URL to backup file in S3/bucket

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object }) affected?: Record<string, any>; // Affected collections and their backup URLs

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object }) filters?: Record<string, any>; // Export filters

  @Field({ nullable: true })
  @Prop() sortBy?: string;

  @Field({ nullable: true })
  @Prop() sortOrder?: string;

  // Expiration configuration
  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object }) expires?: {
    importUrl?: Date;
    backupUrl?: Date;
    exportUrl?: Date;
    auditUrl?: Date;
  };

}

export const TaskQueueSchema = SchemaFactory.createForClass(TaskQueue); 