import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { OrganisationProcessMapping } from '../entities/organisation-process-mapping.entity';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class OrganisationProcessMappingService {
  constructor(
    @InjectModel(OrganisationProcessMapping.name)
    private mappingModel: Model<OrganisationProcessMapping>,
    private readonly mongoConnectionService: MongoConnectionService
  ) {}

  async list(orgId?: string, subOrgId?: string) {
    const filter: any = {};
    if (orgId) filter.organisationId = orgId;
    if (subOrgId) filter.subOrganisationId = subOrgId;
    return this.mappingModel.find(filter).lean();
  }

  async updateIsActive(id: string, isActive: boolean, userContext?: { subOrgId?: string; processId?: string }) {
    // 1. Check if mapping exists
    const mapping = await this.mappingModel.findById(id).lean();
    if (!mapping) {
      throw new Error(`Mapping with ID ${id} not found`);
    }

    // 2. Check if user is trying to disable their own process
    if (userContext && !isActive) {
      const { subOrgId, processId } = userContext;
      if (subOrgId && processId &&
          mapping.subOrganisationId.toString() === subOrgId.toString() &&
          mapping.processId.toString() === processId.toString()) {
        throw new Error(`You can't disable your own process`);
      }
    }

    // 3. Get subOrganisationId and organisationId
    const { subOrganisationId, organisationId, processName } = mapping;

    console.log("processName", processName,isActive);
    
    // 4. Create template if processName is "Provider Credentialing" and isActive is true
    if (processName === 'Provider Credentialing' && isActive) {
      await this.createProviderCredentialTemplate(organisationId, subOrganisationId);
    }

    // 5. Update the mapping
    return await this.mappingModel.findByIdAndUpdate(id, { isActive }, { new: true }).lean();
  }

  private async createProviderCredentialTemplate(organisationId: string, subOrganisationId: string) {
    try {
      // Get template collection for the org
      const templateCollection = await this.mongoConnectionService.getCollectionByOrgId('templates',);

      // Check if template exists for subOrganisationId and key
      const key = 'provider-credential-tickets';
      const docType = 'Default';
      let template = await templateCollection.findOne({ subOrganisationId, key });
console.log("template", template);

      // If not exist, create new template
      if (!template) {
        console.log(`🔧 Creating Provider Credential template for subOrg: ${subOrganisationId}`);

        const defaultTemplate = await templateCollection.findOne({ key, docType });
        console.log("defaultTemplate", defaultTemplate);
        
        const newTemplate = {
          key,
          name: 'Provider Credential Tickets',
          version: 1,
          view_summary: defaultTemplate?.view_summary,
          fields: defaultTemplate?.fields,
          description: '',
          type: 'provider_credentials',
          organisationId,
          subOrganisationId,
          docType: 'Custom',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const insertResult = await templateCollection.insertOne(newTemplate);
        template = await templateCollection.findOne({ _id: insertResult.insertedId });

        console.log(`✅ Provider Credential template created with ID: ${insertResult.insertedId}`);
      } else {
        console.log(`✅ Provider Credential template already exists for subOrg: ${subOrganisationId}`);
      }
    } catch (error) {
      console.error(`❌ Error creating Provider Credential template:`, error);
      // Don't throw error here - template creation failure shouldn't block the main operation
    }
  }
} 