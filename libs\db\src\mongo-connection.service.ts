import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { MongoClient, Db, Collection, Document } from 'mongodb';

@Injectable()
export class MongoConnectionService implements OnModuleDestroy {
  private readonly tenantDbs: Map<string, Db> = new Map();
  private readonly collections: Map<string, Collection<any>> = new Map();
  private masterDb: Db;
  private masterClient: MongoClient;
  private readonly clients: Map<string, MongoClient> = new Map();

  private readonly baseUri = 'mongodb+srv://karthikyoki999:<EMAIL>';
  private readonly defaultOptions = '?retryWrites=true&w=majority';

  /**
   * Connect to the master DB (e.g., `asp`)
   */
  async getMasterDb(): Promise<Db> {
    if (this.masterDb) return this.masterDb;

    this.masterClient = new MongoClient(`${this.baseUri}/asp${this.defaultOptions}`);
    await this.masterClient.connect();
    this.masterDb = this.masterClient.db('asp');
    console.log('Connected to master DB');
    return this.masterDb;
  }

  /**
   * Get tenant DB by ID (e.g., `client123`)
   * If orgId is not provided or empty, returns master DB
   */
  async getTenantDb(orgId?: string, collectionName?: string): Promise<Db> {
    if (!orgId || orgId.trim() === '') {
      // If orgId is not provided, return master DB
      return this.getMasterDb();
    }
    
    if (this.tenantDbs.has(orgId)) return this.tenantDbs.get(orgId)!;

    const client = new MongoClient(`${this.baseUri}/${orgId}${this.defaultOptions}`);
    await client.connect();
    const db = client.db(orgId);

    this.clients.set(orgId, client);
    this.tenantDbs.set(orgId, db);
    console.log(`Connected to tenant DB: ${orgId}`);

    return db;
  }

  /**
   * Get a specific collection from a tenant DB or master DB based on orgId
   */
  async getCollection<T extends Document = Document>(
    orgId: string,
    collectionName: string,
  ): Promise<Collection<T>> {
    const key = `${orgId}:${collectionName}`;
    if (this.collections.has(key)) {
      return this.collections.get(key) as Collection<T>;
    }

    const db = await this.getTenantDb(orgId);
    const collection = db.collection<T>(collectionName);
    this.collections.set(key, collection);
    return collection;
  }

  /**
   * Enhanced collection access method that handles fallback to master DB
   */
async getCollectionByOrgId<T extends Document = Document>(
  collectionName: string,
  orgId?: string,
): Promise<Collection<T>> {
  const resolvedOrgId = orgId && orgId.trim() !== '' ? orgId.trim() : '';
  return this.getCollection<T>(resolvedOrgId, collectionName);
}

  /**
   * Get a specific collection from the master DB
   * @deprecated Use getCollectionByOrgId instead
   */
  async getMasterCollection<T extends Document = Document>(
    collectionName: string,
  ): Promise<Collection<T>> {
    const key = `master:${collectionName}`;
    if (this.collections.has(key)) {
      return this.collections.get(key) as Collection<T>;
    }

    const db = await this.getMasterDb();
    const collection = db.collection<T>(collectionName);
    this.collections.set(key, collection);
    return collection;
  }

  /**
   * Get database instance based on orgId
   */
  async getDatabaseByOrgId(orgId?: string): Promise<Db> {
    return this.getTenantDb(orgId);
  }

  /**
   * Check if a tenant database exists
   */
  async tenantExists(orgId: string): Promise<boolean> {
    try {
      const db = await this.getTenantDb(orgId);
      await db.listCollections().toArray();
      return true;
    } catch (error) {
      console.warn(`Tenant database ${orgId} does not exist or is not accessible:`, error.message);
      return false;
    }
  }

  /**
   * Cleanup all Mongo connections
   */
  async onModuleDestroy() {
    console.log('Cleaning up MongoDB connections...');
    for (const client of this.clients.values()) {
      await client.close();
    }
    if (this.masterClient) {
      await this.masterClient.close();
    }
  }
}
