import { Types } from 'mongoose';

export interface IProvider {
  _id: Types.ObjectId;
  providerId: string;
  credentialType: string;
  values: any;
  attachments: any;
  status: string;
  expiryDate: Date;
  uploadedBy: string;
  ticketId: string;

  createdAt: Date;
  updatedAt: Date;
}

export interface ProviderResponse {
  message: string;
  code: number;
  type: string;
  data?: Record<string, any>;
}

export interface PaginatedProviderResponse {
  items: IProvider[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface QueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  filters?: Record<string, any>;
  sort?: {
    field: string;
    ascending: boolean;
  };
}
