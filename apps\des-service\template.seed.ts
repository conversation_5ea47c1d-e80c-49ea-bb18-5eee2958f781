import mongoose from 'mongoose';
// import { TemplateSchema } from './src/entities/template.entity'; // REMOVE this import

const MONGO_URI = "mongodb+srv://karthikyoki999:<EMAIL>/asp?retryWrites=true&w=majority/asprcm2";

// Define a minimal schema for seeding
const templateSchema = new mongoose.Schema({
  key: { type: String, required: true, unique: true },
  name: { type: String, required: true, unique: true },
  version: { type: Number, default: 0 },
  view_summary: { type: Object, default: { in_grid: [], default: [] } },
  fields: { type: Object, default: {} },
  description: String,
  type: { type: String, required: true, enum: ['masters', 'organization', 'tickets', 'exceptions', 'payer', 'cpt', 'provider-credential-tickets'] },
  organisationId: String,
  subOrganisationId: String,
  useTemplate: { type: Boolean, default: false },
  docType: { type: String, enum: ['Default', 'Custom'], default: 'Custom' },
  isActive: { type: Boolean, default: true },
}, { timestamps: true });

const Template = mongoose.model('Template', templateSchema, 'templates');

// Add this type for raw template seeds
interface RawTemplateSeed {
  key: string;
  name: string;
  version: number;
  view_summary?: any;
  fields: any;
  description?: string;
  type?: 'Master' |  'provider_credentials';
  organisationId?: string;
  subOrganisationId?: string;
  useTemplate?: boolean;
  docType?: 'Default' | 'Custom';
  isActive?: boolean;
  [key: string]: any;
}

// Helper to convert view_summary keys to in_grid/default (from inGrid/default)
function normalizeViewSummary(view_summary) {
  if (!view_summary) return { in_grid: [], default: [] };
  return {
    in_grid: view_summary.inGrid || view_summary.in_grid || [],
    default: view_summary.default || [],
  };
}

// Robust, idempotent seeding function for templates
export async function seedDefaultTemplates(TemplateModel, templateSeeds) {
  for (const seed of templateSeeds) {
    const { key, name, type, fields, organisationId, subOrganisationId, ...rest } = seed;
    let template = await TemplateModel.findOne({ key });
    if (template) {
      let needsUpdate = false;
      if (template.name !== name) {
        template.name = name;
        needsUpdate = true;
      }
      if (template.type !== type) {
        template.type = type;
        needsUpdate = true;
      }
      if (JSON.stringify(template.fields) !== JSON.stringify(fields)) {
        template.fields = fields;
        needsUpdate = true;
      }
      if (type === 'organization') {
        if (template.organisationId !== organisationId) {
          template.organisationId = organisationId;
          needsUpdate = true;
        }
        if (template.subOrganisationId !== subOrganisationId) {
          template.subOrganisationId = subOrganisationId;
          needsUpdate = true;
        }
      }
      for (const [k, v] of Object.entries(rest)) {
        if (template[k] !== v) {
          template[k] = v;
          needsUpdate = true;
        }
      }
      if (needsUpdate) {
        await template.save();
      }
      continue;
    }
    const newTemplate = {
      key,
      name,
      type,
      fields,
      ...rest,
    };
    if (type === 'organization') {
      newTemplate.organisationId = organisationId;
      newTemplate.subOrganisationId = subOrganisationId;
    }
    await TemplateModel.create(newTemplate);
  }
}

async function seedTemplates() {
  await mongoose.connect(MONGO_URI);

  // All master templates from your JSON (add more as needed)
  const rawTemplates: RawTemplateSeed[] =
  [
  {
    key: 'status-code',
    name: 'Add Status Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-cbbb8e4f-9870-479d-a7a8-5b5921b61097","name":"Step 1","sections":[{"id":"3a4146fa-5586-4d96-8de7-b4d243bad2e5","name":"Add Status Code","fields":[{"id":"field-2da04912-d199-4cf4-94ad-abf8b398d25d","label":"Process","placeholder":"","field_type":"select","required":true},{"id":"field-9b67d810-b1a5-4377-b9c7-181513122216","label":"Status Code","placeholder":"","field_type":"text","required":true}]}]}]',
    description: 'Add Status Code Form',
  },
  {
    key: 'action-code',
    name: 'Action Code Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-e7bd8f35-3237-4cab-9c16-9b2e3668836a","name":"Step 1","sections":[{"id":"420ac0cd-59f1-4ad3-aeb8-757b3b804571","name":"Add Action Code","fields":[{"id":"field-5a60fe9a-67b1-4f98-8204-74a059f8b458","label":"Status Code","placeholder":"","field_type":"select","required":true},{"id":"field-86ac9bc1-4fae-4a34-834b-241b78360355","label":"Action Code","placeholder":"","field_type":"text","required":true}]}]}]',
    description: 'Add Action Code',
  },
  {
    "key": "provider",
    "name": "Provider Form",
    "version": 1,
    "type": "Master",
    "flattening_version": 1,
    "view_summary": {
      "inGrid": [
        "fullName",
        "firstName",
        "lastName",
        "email",
        "nUCCGrouping",
        "providerType",
        "medicarePTAN",
        "languagesSpoken"
      ],
      "default": [
        "fullName",
        "firstName",
        "lastName",
        "email",
        "nUCCGrouping",
        "providerType",
        "medicarePTAN",
        "medicaidProvider",
        "languagesSpoken"
      ]
    },
    "fields": "[{\"id\":\"step-001\",\"name\":\"Step 1\",\"sections\":[{\"id\":\"section-basic-info\",\"name\":\"Basic Information\",\"fields\":[{\"id\":\"providerImage\",\"label\":\"Provider Image\",\"field_type\":\"file_upload\",\"position\":1}]}]}]",
    "description": "Form for capturing provider details, credentials, CAQH, NPI, and checklist validation."
  },

  {
    key: 'payer',
    name: 'Payer Form',
    version: 1,
    type: "Master",
    
    view_summary: { inGrid: ['payerName', 'payerType', 'claimSubmissionMethod'], default: ['payerName', 'payerType', 'claimSubmissionMethod'] },
    fields: '[{"id":"step-001","name":"Step 1","sections":[{"id":"section-basic-info","name":"Basic Information","fields":[{"id":"payerName","label":"Payer Name","field_type":"text","show_in_grid":true,"is_default":true,"required":true},{"id":"payerType","label":"Payer Type","field_type":"text","show_in_grid":true,"is_default":true},{"id":"claimSubmissionMethod","label":"Claim Submission Method","field_type":"select","options":[{"id":"option_1750157536918","value":"EDI"},{"id":"option_1750415131482","value":"EDI A"},{"id":"option_1750415132841","value":"EDI B"},{"id":"option_1750415133921","value":"EDI C"},{"id":"option_1750415134722","value":"EDI D"},{"id":"option_1750415135721","value":"EDI E"}],"show_in_grid":true,"is_default":true,"visibleIf":"","logicConditions":[]}]},{"id":"section-location","name":"Location","fields":[{"id":"locationGrid","label":"Location Grid","field_type":"grid","columns":[{"id":"col_payerName","name":"Payer Name","fieldType":"string"},{"id":"col_state","name":"State","fieldType":"string","position":1,"globalFieldId":"6853a4d54e9a543ff64b0a9e"},{"id":"col_website","name":"Website","fieldType":"string"},{"id":"col_contactNumber","name":"Contact Number","fieldType":"string"},{"id":"col_processType","name":"process","fieldType":"global_select","position":4,"globalFieldId":"6853ca9801e02247261b41ec"},{"id":"col_contactEmail","name":"Contact Email","fieldType":"string"},{"id":"col_action","name":"Action","fieldType":"string"}],"rows":[]}]},{"id":"section-appeal-timelines","name":"Appeal Timelines","fields":[{"id":"appealTimelineGrid","label":"Appeal Timelines Grid","field_type":"grid","columns":[{"id":"col_denialType","name":"Denial Type","fieldType":"string"},{"id":"col_appealWindow","name":"Typical Appeal Window","fieldType":"string"},{"id":"col_state","name":"State","fieldType":"global_select","position":2,"globalFieldId":"6853a4d54e9a543ff64b0a9e"},{"id":"col_action","name":"Action","fieldType":"string"}],"rows":[]}]},{"id":"section-credentialing","name":"Credentialing","fields":[{"id":"credentialingGrid","label":"Credentialing Grid","field_type":"grid","columns":[{"id":"col_specialty","name":"Specialty","fieldType":"string"},{"id":"col_state","name":"State","fieldType":"global_select","position":1,"globalFieldId":"6853a4d54e9a543ff64b0a9e"},{"id":"col_link","name":"Link","fieldType":"string"},{"id":"col_action","name":"Action","fieldType":"string"}],"rows":[]}]},{"id":"section-manual-forms","name":"Manual Forms","fields":[{"id":"manualFormsGrid","label":"Manual Forms Grid","field_type":"grid","columns":[{"id":"col_type","name":"Type","fieldType":"string"},{"id":"col_version","name":"Version","fieldType":"string"},{"id":"col_document","name":"Document","fieldType":"string","position":2},{"id":"col_createdDate","name":"Created Date","fieldType":"date","position":3},{"id":"col_versionHistory","name":"Version History","fieldType":"string"},{"id":"col_specialty","name":"Specialty","fieldType":"string"},{"id":"col_state","name":"State","fieldType":"global_select","position":6,"globalFieldId":"6853a4d54e9a543ff64b0a9e"},{"id":"col_action","name":"Action","fieldType":"string"}],"rows":[]}]}]}]',
    description: 'Form for maintaining payer metadata including locations, credentialing, appeals, and documents.',
  },
  // ... add the rest of your templates here, using the same structure ...
  // The following are additional templates from your JSON sample:
  // (For brevity, only the structure is shown. The actual fields and view_summary should be filled in as per your JSON.)
  {
    key: 'provider-credential-tickets',
    name: 'Provider Credential Tickets Form',
    version: 1,
    type: "provider_credentials",
    docType: 'Default',
    
    view_summary: { inGrid: ['groupNPI', 'tIN', 'providerNPI', 'payorName'], default: ['groupNPI', 'tIN', 'providerNPI', 'payorName'] },
    fields: '[{"id":"step-dfcb5a6e-2434-47aa-8020-5d0051d53e9a","name":"Details","sections":[{"id":"bc508f0c-7a77-4554-8a68-4f49f53fd870","name":"  Request Information","fields":[{"id":"field-2d22d054-ce33-4852-8abb-b518a99be2ac","label":"Enrollement Type","placeholder":"Select Enrollement Type","field_type":"select","globals_name":"Select","options":[{"id":"f9ca8c63-4a1a-481f-8f15-2c90da7a869b","value":"option 1"},{"id":"7ddedf2f-8b0d-4c3a-8a22-8a4f7999735d","value":"option 2"}],"is_import":true},{"id":"field-a6cc0fb9-c91b-4498-85d4-8d66500dcc76","label":"Job Request","placeholder":"Select Job Request","field_type":"select","globals_name":"Select","options":[{"id":"d7497d82-405d-43b8-8d30-3ee2da25dc4a","value":"option 1"},{"id":"26499e2a-fbba-4609-8bcc-5bf419e4945d","value":"option 2"}],"is_import":true}]},{"id":"49374dfd-89dc-474a-85ac-1cbd52897df5","name":"Group  Information","fields":[{"id":"field-a9e145b6-7111-4854-8da7-aa4eb16d54fa","label":"Group NPI","placeholder":"Enter Group NPI","field_type":"text","globals_name":"Text","options":[],"required":true,"position":1,"prefillFieldIds":["field-63a7431e-27fd-47f8-96d6-852ed4b1b479","field-4eec4cac-da3c-4e3c-82a6-c3927cf6be55","field-b0649b02-4d44-41b3-af37-8ec600258abe"],"show_in_grid":true,"only_in_grid":false,"is_default":true,"is_import":true},{"field_type":"text","id":"field-63a7431e-27fd-47f8-96d6-852ed4b1b479","identifierType":"686e0e9ac6b121903986f987","label":"Client Name","options":[],"prefilled":true,"sectionId":"eb764660-5d6f-4dbd-84e2-e2a33be5c0bd","stepId":"step-32bd3f7e-a1e0-4bbf-92dd-37c5137500fa","sourceFieldId":"field-a9e145b6-7111-4854-8da7-aa4eb16d54fa","globals_name":"","placeholder":"Enter client name","required":true,"position":2},{"field_type":"text","id":"field-4eec4cac-da3c-4e3c-82a6-c3927cf6be55","identifierType":"686e0e9ac6b121903986f987","label":"Address 1","options":[],"prefilled":true,"sectionId":"eb764660-5d6f-4dbd-84e2-e2a33be5c0bd","stepId":"step-32bd3f7e-a1e0-4bbf-92dd-37c5137500fa","sourceFieldId":"field-a9e145b6-7111-4854-8da7-aa4eb16d54fa","globals_name":"","placeholder":"Enter address line 1","position":3},{"id":"field-f510af46-23ed-40db-b4b9-9cb79a95a546","label":"TIN","placeholder":"Select TIN","field_type":"select","globals_name":"Select","options":[{"id":"a8df45ff-e6da-45ce-9d32-523c1d20ad99","value":"option 1"},{"id":"1ed702ad-1614-430a-b3b2-83cf32ad97ce","value":"option 2"}],"position":4,"show_in_grid":true,"is_default":true,"is_import":true},{"field_type":"global_select","id":"field-b0649b02-4d44-41b3-af37-8ec600258abe","identifierType":"686e0e9ac6b121903986f987","label":"State/Province","options":[],"prefilled":true,"sectionId":"eb764660-5d6f-4dbd-84e2-e2a33be5c0bd","stepId":"step-32bd3f7e-a1e0-4bbf-92dd-37c5137500fa","sourceFieldId":"field-a9e145b6-7111-4854-8da7-aa4eb16d54fa","globals_name":"State","placeholder":"Select State","position":5}]},{"id":"e1ee1b80-f458-43cf-97a1-26f971963f90","name":"Provider  Information","fields":[{"id":"field-6f242b42-7d59-4249-93b4-44f50013aa18","label":"Provider NPI","placeholder":"Enter Provider NPI","field_type":"text","globals_name":"Text","options":[],"required":true,"position":1,"prefillFieldIds":["firstName","middleName","suffix","lastName","fullName","naicsGrouping","providerType"],"show_in_grid":true,"is_default":true,"is_import":true},{"field_type":"text","id":"fullName","identifierType":"686b5f4c761db80d63c63dc8","label":"Full Name","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","required":true,"position":2},{"field_type":"text","id":"firstName","identifierType":"686b5f4c761db80d63c63dc8","label":"First Name","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":3},{"field_type":"text","id":"middleName","identifierType":"686b5f4c761db80d63c63dc8","label":"Middle Name","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":4},{"field_type":"text","id":"lastName","identifierType":"686b5f4c761db80d63c63dc8","label":"Last Name","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":5},{"field_type":"text","id":"suffix","identifierType":"686b5f4c761db80d63c63dc8","label":"Suffix","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":6},{"field_type":"text","id":"naicsGrouping","identifierType":"686b5f4c761db80d63c63dc8","label":"NUCC Grouping","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":7},{"field_type":"text","id":"providerType","identifierType":"686b5f4c761db80d63c63dc8","label":"Provider Type","options":[],"prefilled":true,"sectionId":"section-basic-info","stepId":"step-001","sourceFieldId":"field-6f242b42-7d59-4249-93b4-44f50013aa18","globals_name":"","position":8},{"id":"field-7757d7f5-c300-4fa7-9989-d77e8c26a4ac","label":"Provider Qualifications","placeholder":"Select Provider Qualifications","field_type":"select","globals_name":"Select","options":[{"id":"a18f21b9-45d8-46e5-8bcd-8365f6cd9530","value":"option 1"},{"id":"5b620fa1-6b28-4b63-af96-f53fa86bfc35","value":"option 2"}],"position":9,"is_import":true}]},{"id":"0ae3c9ad-f25d-43f1-b2d6-090fbd350f30","name":"Payor  Information","fields":[{"id":"field-8ddd247d-cf0a-4d97-a7fc-43ecd57a2284","label":"Payor Name","placeholder":"Select Payor Name","field_type":"select","globals_name":"Select","options":[{"id":"5cb3fda8-696c-46c7-b89e-82d39c86feb6","value":"option 1"},{"id":"1f532409-9b90-4608-89c4-d1187ba213ac","value":"option 2"}],"show_in_grid":true,"is_default":true,"is_import":true},{"id":"field-d6ce2225-c0df-4152-8190-46e6855c14ca","label":"Payor State","placeholder":"Select State","field_type":"global_select","globals_name":"State","options":[],"is_import":true}]}]},{"id":"step-14073e50-f8f8-4448-9b0f-bb15d552dc51","name":"Application Document","sections":[{"id":"e2105489-bb02-474d-ad90-d89ab2b1d762","name":"Form & Documentation Metadata","fields":[{"id":"field-a728aee9-7bc1-4eaa-a5df-c18bc723f17b","label":"Payor Credentialing  Link","placeholder":"Enter Payor Credentialing  Link","field_type":"text","globals_name":"Text","options":[]},{"id":"field-6e12887c-3b2d-4537-98ce-790e3ca0686f","label":"Table Grid field","placeholder":"Enter Table Grid","field_type":"grid","globals_name":"Table Grid","options":[],"columns":[{"name":"Form Type","fieldType":"select","id":"col_1751957665263","position":0},{"name":"Name","fieldType":"text","id":"col_1751957674303"},{"name":"Document","fieldType":"text","id":"col_1751957683455"}],"rows":[]}]},{"id":"18fb415b-aa19-4f45-937a-1df02ad089f3","name":"Worked Document Details","fields":[{"id":"field-67e36f88-5425-41d8-868e-24555c21a6a2","label":"Table Grid field","placeholder":"Enter Table Grid","field_type":"grid","globals_name":"Table Grid","options":[],"columns":[{"name":"Document Type","fieldType":"select","id":"col_1751957720311","position":0,"options":[{"id":"opt_1751957779495","value":"Option 1"},{"id":"opt_1751957780695","value":"Option 2"}]},{"name":"Document Name","fieldType":"text","id":"col_1751957730607"},{"name":"Uploaded Document","fieldType":"file_upload","id":"col_1751957764847"},{"name":"Uploaded Date","fieldType":"date","id":"col_1751957836999"},{"name":"Comments","fieldType":"textarea","id":"col_1751957856863"}],"rows":[]},{"id":"field-782bdac5-810d-48d1-8a3e-4f3acad32aa2","label":"Credentialing ETA","placeholder":"Enter Credentialing ETA","field_type":"text","globals_name":"Text","options":[]}]},{"id":"919a981e-0ee3-4619-a289-e6e1e87d4a73","name":"Submission Details","fields":[{"id":"field-1f130690-2adf-4092-bef9-2a25a8cdb583","label":"Submission method","placeholder":"Select Submission method","field_type":"select","globals_name":"Select","options":[{"id":"1faf11ec-dda2-4d81-b79a-c8b5724084be","value":"option 1"},{"id":"caa9d069-04d1-4f2e-8083-569f0c177183","value":"option 2"}]}]},{"id":"eff031fa-4088-4c6d-b75e-72e7d309a9ee","name":"Followup Details","fields":[{"id":"field-2393ac43-d8a4-4ff1-9eb9-2c8fa1ea9d33","label":"Followup Method","placeholder":"Select Followup Method","field_type":"select","globals_name":"Select","options":[{"id":"972f4e75-bce5-4fb6-a1bf-8cf952af08c3","value":"option 1"},{"id":"1e3d0667-3332-4a6b-ab85-92705f958879","value":"option 2"}]}]},{"id":"16c0be53-339c-454d-8857-a836a8f4888f","name":"Contract Document","fields":[{"id":"field-eb8a508c-2d64-4c5f-aa10-0459e3b710ef","label":"Table Grid field","placeholder":"Enter Table Grid","field_type":"grid","globals_name":"Table Grid","options":[],"columns":[{"name":"Doc Type","fieldType":"select","options":[{"id":"opt_1751958088927","value":"Option 1"}],"id":"col_1751958090255"},{"name":"Document Name","fieldType":"text","id":"col_1751958099879"},{"name":"Uploaded Date","fieldType":"date","id":"col_1751958114767"}],"rows":[]}]}]},{"id":"step-13d9003d-8b01-4aeb-99d2-532a4b7d11e6","name":"Status & Comments","sections":[{"id":"62bf4761-95c4-4c40-9ec7-d2cd3c326df9","name":"Ticket Action","fields":[{"id":"field-7326eb7a-79d4-42b5-a6a1-8fc4c8d9f5f3","label":"Action Code","placeholder":"Enter Action Code","field_type":"number","globals_name":"Number","options":[]},{"id":"field-9f4d9bfa-841b-4035-a6a1-8fc4c8d9f5f3","label":"Status Code","placeholder":"Enter Status Code","field_type":"number","globals_name":"Number","options":[]},{"id":"field-57f3309b-8210-4a8d-a011-a5131dd99d8e","label":"Follow Up (In Days)","placeholder":"Enter Follow Up (In Days)","field_type":"text","globals_name":"Text","options":[]},{"id":"field-2a606cd6-9310-4c98-af3a-5524cc0ab93a","label":"Upload Document","placeholder":"Upload Document","field_type":"file_upload","globals_name":"File Upload","options":[]},{"id":"field-02e826ab-3394-4fa0-8718-0dbd20d69e7d","label":"Comments","placeholder":"Enter Comments","field_type":"textarea","globals_name":"Text Area","options":[]}]},{"id":"f25bda71-c5e1-4b3a-959c-e86483331288","name":"Contract Fee Details","fields":[{"id":"field-c8a9045c-a3dc-4b57-88f2-f2175adbe5a7","label":"Table Grid field","placeholder":"Enter Table Grid","field_type":"grid","globals_name":"Table Grid","options":[],"columns":[{"name":"Payor Name","fieldType":"text","id":"col_1751958371279"},{"name":"State","fieldType":"global_select","globalFieldId":"6853a4d54e9a543ff64b0a9e","id":"col_1751958392015"},{"name":"Provider Qualifications","fieldType":"text","id":"col_1751958427143"},{"name":"ICD","fieldType":"text","id":"col_1751958435247"},{"name":"Diagnosis Code ","fieldType":"number","id":"col_1751958445015"},{"name":"CPT Code","fieldType":"number","id":"col_1751958456103"},{"name":"Speciality","fieldType":"text","id":"col_1751958465552"},{"name":"Units Allowed","fieldType":"text","id":"col_1751958473799"},{"name":"Billing Frequency","fieldType":"text","id":"col_1751958481279"},{"name":"Unit Value","fieldType":"text","id":"col_1751958488823"},{"name":"Unit Type","fieldType":"select","id":"col_1751958500311","position":10},{"name":"Modifiers","fieldType":"text","id":"col_1751958650496"},{"name":" POS","fieldType":"text","id":"col_1751958657855"},{"name":"Rate","fieldType":"text","id":"col_1751958666367"}],"rows":[]}]}]}]'
    , description: 'Provider Credential Tickets'
  },
  {
    key: 'exception',
    name: 'Exception Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: ['process', 'exception', 'documentRequired', 'followUpInDays', 'status'], default: ['process', 'exception', 'documentRequired', 'followUpInDays', 'status'] },
    fields: '[{"id":"step-873733e4-162b-4931-a886-74befcc12f8d","name":"Step 1","sections":[{"id":"15a86e5f-2039-496d-bb16-83681118f8b7","name":"Add Exception Type","fields":[{"id":"field-a92eb673-8963-4cb3-abb6-e9c2656b9944","label":"Process","placeholder":"select Select","field_type":"select","options":[],"position":1,"show_in_grid":true,"is_default":true},{"id":"field-0591ec9e-e635-46a3-a2d8-cf7205aca71a","label":"Exception","placeholder":"","field_type":"text","options":[],"show_in_grid":true,"is_default":true,"position":2},{"id":"field-a4725f29-4145-4af0-b87b-fc35c5e4f2f3","label":"Document Required","placeholder":"","field_type":"select","options":[{"id":"e4f1feec-1761-4877-8434-5bd787a10ca1","value":"Yes"},{"id":"9c4dcfcb-9502-43a7-be9a-1a23406fea11","value":"No"}],"filter":false,"show_in_grid":true,"is_default":true,"position":3},{"id":"field-14162f48-4071-4abe-96a0-7bf5db5e12f2","label":"Follow Up (In Days)","placeholder":"","field_type":"number","options":[],"show_in_grid":true,"is_default":true,"position":4},{"id":"field-625351a0-98ab-4b3d-b365-a38063455c90","label":"Status","placeholder":"","field_type":"select","options":[{"id":"7d38ad77-ca2b-4673-8309-990f1f53b9aa","value":"Active"},{"id":"99298507-07e6-4632-854c-00022e8e30e4","value":"Inactive"}],"show_in_grid":true,"is_default":true,"position":5}]}]}]'
    , description: ''
  },
  {
    key: 'icd-code',
    name: 'Icd Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-fb49a7b5-6433-457e-959d-7142bce858f1","name":"Step 1","sections":[{"id":"3cdb7b16-ea8b-4a5b-87d6-1e3daa73cf97","name":"Add ICD","fields":[{"id":"field-ed80cba6-0a40-4910-99c0-064482e82990","label":"ICD","placeholder":"","field_type":"text","options":[]},{"id":"field-24539b0a-21d6-48f9-a4b2-dc576840388f","label":"Status","placeholder":"Enter Select","field_type":"select","options":[{"id":"option_1750153306227","value":"Active"},{"id":"option_1750153310211","value":"InActive"}]}]}]}]',
    description: 'icd-form',
  },
  {
    key: 'diagnois-code',
    name: 'diagnosis Code Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-7ace12c5-db9a-4a05-b641-06f701781a02","name":"Step 1","sections":[{"id":"e38992fe-bc76-45c3-b026-dc1b53b2539b","name":"Add Diagnosis Code","fields":[{"id":"field-1c0dcfe0-84ee-4f06-95f5-12da9d098226","label":"ICD","placeholder":"","field_type":"select","options":[]},{"id":"field-ca46639b-6200-4405-a6a1-bdb5cf664c21","label":"Diagnosis Code","placeholder":"","field_type":"text","options":[]},{"id":"field-3551b8e7-f3fe-4ddc-a57c-dc2d6ef44cac","label":"Diagnosis Code Description","placeholder":"","field_type":"text","options":[]},{"id":"field-e75db6e8-87e3-4ca6-954b-a7de25494cb8","label":"Status","placeholder":"Enter Select","field_type":"select","options":[{"id":"be4215eb-f81d-4580-a14e-bb9cd281908d","value":"Active"},{"id":"f4019884-f37e-4028-8864-7ab3e4f4ea94","value":"Inactive"}]}]}]}]',
    description: 'diagnosis-code-form',
  },
  {
    key: 'action-status-code',
    name: 'Action Code Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: {
      inGrid: [
        "process",
        "statusCode", 
        "actionCode",
        "documentsRequired",
        "followUpInDays"
      ],
      default: [
        "process",
        "statusCode",
        "actionCode", 
        "documentsRequired",
        "followUpInDays"
      ]
    },
    fields: '[{"id":"step-3be9671f-764e-4bda-b2f6-2ad6f4fff1a2","name":"Step 1","sections":[{"id":"795f3e99-3198-46ed-a978-53a1514ae79c","name":"Create Action & Status Code","fields":[{"id":"field-7f072c20-5cfa-4d75-898a-6ea9fce7f4f7","label":"Process","placeholder":"","field_type":"global_select","globals_name":"process","options":[],"position":1,"show_in_grid":true,"is_default":true,"required":true},{"id":"field-86f133b5-b408-414f-97fb-8c650643731d","label":"Contract Fee","placeholder":"","field_type":"select","globals_name":"Select","options":[{"id":"9ba60a64-9193-4ede-9e8e-70707714be59","value":"Yes"},{"id":"2aa53581-94b9-4dda-a06f-8e8a3d3dc6e7","value":"No"}],"position":2,"visibleIf":"{field-7f072c20-5cfa-4d75-898a-6ea9fce7f4f7} = \'Provider Credentialing\'","logicConditions":[{"fieldId":"field-7f072c20-5cfa-4d75-898a-6ea9fce7f4f7","operator":"===","value":"Provider Credentialing","multiSelectLogic":"OR"}],"logicJoinType":"OR"},{"id":"field-75ec0d27-1be9-479a-9423-50af1d3f0ccc","label":"Collection","placeholder":"","field_type":"select","globals_name":"Select","options":[{"id":"a95d7abe-e621-4e0c-9af7-6b6ca4add176","value":"Yes"},{"id":"1b556a32-627e-42b8-89bd-527389afc0b1","value":"No"}],"position":3,"visibleIf":"{field-7f072c20-5cfa-4d75-898a-6ea9fce7f4f7} = \'Account Receivable\'","logicConditions":[{"fieldId":"field-7f072c20-5cfa-4d75-898a-6ea9fce7f4f7","operator":"===","value":"Account Receivable","multiSelectLogic":"OR"}],"logicJoinType":"OR"},{"id":"field-332789c0-87ff-4747-9569-5bea308565aa","label":"Status Code","placeholder":"","field_type":"select","show_in_grid":true,"is_default":true,"position":4,"required":true},{"id":"field-16206f35-c119-48ba-84d7-6159d4e63fde","label":"Action Code","placeholder":"","field_type":"select","show_in_grid":true,"is_default":true,"position":5,"required":true},{"id":"field-44ab5545-325d-4932-ad89-af7e2ba03bd9","label":"Documents Required","placeholder":"","field_type":"select","options":[{"id":"option_1749885281485","value":"Yes"},{"id":"option_1749885286165","value":"No"}],"show_in_grid":true,"is_default":true,"position":6},{"id":"field-85690c1c-3911-4c0a-a804-66c49c59993e","label":"Follow Up (In Days)","placeholder":"","field_type":"number","show_in_grid":true,"is_default":true,"position":7},{"id":"field-70fea124-69d7-4093-a25d-cc8910aff770","label":"Active","placeholder":"","field_type":"select","options":[{"id":"option_1749885707690","value":"Active"},{"id":"option_1749885712410","value":"Inactive"}],"position":8}]}]}]',
    description: 'Add Action Code'
  },  {
    key: 'cpt-code',
    name: 'cpt-code-form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-61d2cf38-2474-42b0-a232-f95b01810194","name":"Step 1","sections":[{"id":"e2478c25-c6d5-43fd-b281-589e31de5682","name":"Add CPT","fields":[{"id":"field-fe1f7ab1-0800-4df3-98f1-0f7bd86cbe6a","label":"CPT","placeholder":"","field_type":"text","options":[]},{"id":"field-1f98b751-d04a-42bd-9aaa-3a246f127b7e","label":"CPT Code Description","placeholder":"","field_type":"text","options":[]},{"id":"field-0f02df42-a916-427b-b1f6-d3d687118ab4","label":"Status","placeholder":"","field_type":"select","options":[{"id":"37a4cd50-b5de-468a-8917-e2b30601f13f","value":"Active"},{"id":"ad84da6c-ce0f-4206-8c69-a05619bcba2b","value":"Inactive"}]},{"id":"field-7437afd6-6991-4363-9ef2-c77d6526b946","label":"Speciality","placeholder":"","field_type":"select","options":[]}]}]}]',
    description: 'cpt-code-form',
  },
  {
    key: 'cpt',
    name: 'CPT Form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: ['iCD', 'diagnosisCode', 'diagnosisCodeDescription', 'speciality', 'cPTCode', 'cPTCodeDescription', 'mUELimit', 'billingFrequency', 'unitValue', 'unitType', 'status'], default: ['iCD', 'diagnosisCode', 'diagnosisCodeDescription', 'speciality', 'cPTCode', 'cPTCodeDescription', 'mUELimit', 'billingFrequency', 'unitValue', 'unitType', 'status'] },
    fields: '[{"id":"step-e3e64eb8-22c1-4386-871f-125004a97105","name":"Step 1","sections":[{"id":"8bd7b024-0ce5-4a34-9e9b-c394b223798d","name":"Add CPT Dictionary","fields":[{"id":"field-b1b14ff4-6ca9-4bf0-a986-2a9d3c6d2cf4","label":"ICD","placeholder":"","field_type":"select","options":[],"show_in_grid":true,"position":1,"is_default":true,"required":true},{"id":"field-12d51386-24b3-48e1-abbc-44f077655713","label":"Diagnosis Code","placeholder":"","field_type":"select","options":[],"show_in_grid":true,"position":2,"is_default":true,"required":true},{"id":"field-9c3ed693-e4a9-4196-8fcc-ab83db9f2695","label":"Diagnosis Code Description","placeholder":"","field_type":"text","options":[],"show_in_grid":true,"position":3,"is_default":true},{"id":"field-b8244a7d-c7ee-4adc-858e-53240f4bf0f9","label":"Speciality","placeholder":"","field_type":"select","options":[],"show_in_grid":true,"position":4,"is_default":true,"required":true},{"id":"field-f9302235-a550-4ce9-8b92-eef5290e5aa3","label":"CPT Code","placeholder":"Enter Select","field_type":"select","options":[],"show_in_grid":true,"position":5,"is_default":true,"required":true},{"id":"field-8e8b0e7f-d664-4064-adeb-d8d9a64128db","label":"CPT Code Description","placeholder":"","field_type":"text","options":[],"show_in_grid":true,"position":6,"is_default":true},{"id":"field-8098a453-1ac3-4a63-a0fc-23c3aa39ff22","label":"MUE Limit","placeholder":"","field_type":"number","options":[],"show_in_grid":true,"position":7,"is_default":true},{"id":"field-0178008d-f56f-42d8-ace7-39f6787fa463","label":"Billing Frequency","placeholder":"","field_type":"select","options":[],"show_in_grid":true,"position":8,"is_default":true},{"id":"field-9894d4e4-9911-402b-bddf-c7e6779fff5e","label":"Unit Value","placeholder":"Enter Select","field_type":"select","options":[],"show_in_grid":true,"position":9,"is_default":true},{"id":"field-01b91f9d-8875-4191-a53a-44c3ab52bff0","label":"Unit Type","placeholder":"","field_type":"select","options":[],"show_in_grid":true,"position":10,"is_default":true},{"id":"field-faae5831-4468-40de-8e8b-ee98cb71636f","label":"Status","placeholder":"","field_type":"select","options":[{"id":"26e4f39e-d544-4d1d-a67c-b54ba433780c","value":"Active"},{"id":"cdd800b3-d5b8-433a-8364-44abba1bb1c7","value":"Inactive"}],"show_in_grid":true,"position":11,"is_default":true},{"id":"field-8b819200-259e-448b-9935-dc197934c5ea","label":"Notes","placeholder":"Enter Text Area","field_type":"textarea","options":[],"position":12}]}]}]',
    description: 'CPT Form',
  },
  {
    key: 'speciality',
    name: 'speciality-form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: { inGrid: [], default: [] },
    fields: '[{"id":"step-38654e61-c476-4bae-9f47-a6afeb688e4e","name":"Step 1","sections":[{"id":"01cccf7d-7761-4dde-a958-aa30b7855d0c","name":"Add Speciality","fields":[{"id":"field-2d1707fb-22e4-4872-9238-9fb3937fd883","label":"Speciality","placeholder":"Enter Text","field_type":"text","options":[]},{"id":"field-432e6189-2448-49a3-a565-efe12c821110","label":"Description","placeholder":"","field_type":"text","options":[]},{"id":"field-065df362-0099-431d-bade-e4719daa1cf7","label":"Status","placeholder":"","field_type":"select","options":[{"id":"f553d81b-3eed-49b8-96eb-93e4a95bd839","value":"Active"},{"id":"1c44b386-538b-4437-9125-b2a50d12ca6f","value":"Inactive"}]}]}]}]'
    , description: 'speciality-form'
  },
  {
    key: 'organization',
    name: 'organization-form',
    version: 1,
    type: "Master",
    docType: "Default",
    
    view_summary: {
      "inGrid": [
        "clientImage",
        "clientName",
        "legalBusinessName",
        "clientOnBoardingDate",
        "clientId",
        "clientEmail",
        "telephone",
        "fax",
        "address1",
        "zipPostalCode"
      ],
      "default": [
        "clientImage",
        "clientName",
        "legalBusinessName",
        "clientOnBoardingDate",
        "clientId",
        "clientEmail",
        "telephone",
        "fax",
        "address1",
        "zipPostalCode"
      ]
    },
    fields: "[{\"id\":\"step-32bd3f7e-a1e0-4bbf-92dd-37c5137500fa\",\"name\":\"Step 1\",\"sections\":[{\"id\":\"eb764660-5d6f-4dbd-84e2-e2a33be5c0bd\",\"name\":\"Basic Information\",\"fields\":[{\"id\":\"field-3f33e117-4b6d-4527-a710-b6e859d5e438\",\"label\":\"Client Image\",\"placeholder\":\"Choose client logo/image\",\"field_type\":\"file_upload\",\"position\":1,\"show_in_grid\":true,\"is_default\":true},{\"id\":\"field-63a7431e-27fd-47f8-96d6-852ed4b1b479\",\"label\":\"Client Name\",\"placeholder\":\"Enter client name\",\"field_type\":\"text\",\"position\":2,\"filter\":true,\"show_in_grid\":true,\"is_default\":true,\"required\":true},{\"id\":\"field-fddc0d20-6329-4e86-94f1-83960096dad6\",\"label\":\"Legal Business Name\",\"placeholder\":\"Enter legal business name\",\"field_type\":\"text\",\"show_in_grid\":true,\"is_default\":true,\"filter\":true,\"position\":3},{\"id\":\"field-8a85746b-10eb-4b19-bf2d-14f1246cedd7\",\"label\":\"Client OnBoarding date\",\"placeholder\":\"Select onboarding date\",\"field_type\":\"date\",\"show_in_grid\":true,\"is_default\":true,\"position\":4},{\"id\":\"field-54631d3a-9158-4e45-bdc2-0a30223d074e\",\"label\":\"Client Id\",\"placeholder\":\"Enter unique client ID\",\"field_type\":\"text\",\"filter\":true,\"show_in_grid\":true,\"is_default\":true,\"position\":5},{\"id\":\"field-f2c98936-5799-4a91-9c24-447d992347ca\",\"label\":\"Client Email\",\"placeholder\":\"Enter official email\",\"field_type\":\"email\",\"filter\":true,\"show_in_grid\":true,\"is_default\":true,\"required\":true,\"position\":6},{\"id\":\"field-3077dfd9-9bf7-4c86-a4c6-794170a6f964\",\"label\":\"Telephone\",\"placeholder\":\"Enter phone number\",\"field_type\":\"phone\",\"filter\":true,\"show_in_grid\":true,\"is_default\":true,\"position\":7},{\"id\":\"field-f486b6b3-a2ab-41fb-aef0-062726fb4512\",\"label\":\"Fax\",\"placeholder\":\"Enter fax number (optional)\",\"field_type\":\"phone\",\"only_in_custom_form\":false,\"position\":8,\"only_in_grid\":false,\"show_in_grid\":true,\"is_default\":true},{\"id\":\"field-4eec4cac-da3c-4e3c-82a6-c3927cf6be55\",\"label\":\"Address 1\",\"placeholder\":\"Enter address line 1\",\"field_type\":\"text\",\"filter\":true,\"show_in_grid\":true,\"is_default\":true,\"only_in_grid\":false,\"position\":9},{\"id\":\"field-3503dd5e-1379-4820-8bf6-4fb0afbc954c\",\"label\":\"Address 2\",\"placeholder\":\"Enter address line 2 (optional)\",\"field_type\":\"text\",\"only_in_grid\":false,\"show_in_grid\":false,\"is_default\":false,\"position\":10,\"only_in_custom_form\":true},{\"id\":\"field-d187624c-983c-431d-80c8-e3710ce7dc94\",\"label\":\"Country\",\"placeholder\":\"Select Country\",\"field_type\":\"global_select\",\"globals_name\":\"Country\",\"options\":[],\"position\":11},{\"id\":\"field-8588ca0b-5ad2-4936-afac-9b01dd8ac5d1\",\"label\":\"County\",\"placeholder\":\"Select County\",\"field_type\":\"global_select\",\"globals_name\":\"County\",\"options\":[],\"position\":12},{\"id\":\"field-b0649b02-4d44-41b3-af37-8ec600258abe\",\"label\":\"State/Province\",\"placeholder\":\"Select State\",\"field_type\":\"global_select\",\"globals_name\":\"State\",\"options\":[],\"position\":13},{\"id\":\"field-c1ebd950-88d5-467c-9135-fd47e7ceb24a\",\"label\":\"City\",\"placeholder\":\"Select City\",\"field_type\":\"global_select\",\"globals_name\":\"City\",\"options\":[],\"position\":14},{\"id\":\"field-0224e9a4-fe1f-4ee1-a556-8cc4dff21497\",\"label\":\"Zip/Postal Code\",\"placeholder\":\"Enter ZIP or postal code\",\"field_type\":\"text\",\"show_in_grid\":true,\"is_default\":true,\"filter\":true,\"position\":15}]},{\"id\":\"f7e7b861-70e0-49a9-af5d-6439683f27e8\",\"name\":\"Point of Contact\",\"fields\":[{\"id\":\"field-0d241fea-23f9-42d9-928b-65546991180e\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"columns\":[{\"name\":\"Name\",\"fieldType\":\"string\",\"id\":\"col_1749806877036\"},{\"name\":\"Email ID\",\"fieldType\":\"string\",\"id\":\"col_1749806888635\"},{\"name\":\"Mobile Phone\",\"fieldType\":\"string\",\"id\":\"col_1749806901146\"},{\"name\":\"Is Primary\",\"fieldType\":\"toggle\",\"id\":\"col_1749806914931\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1749806924955\"}],\"rows\":[],\"position\":1}]}]}]",

    description: 'organization-form'
  },
  {
    key: 'sub-organization',
    name: 'Sub organization form',
    version: 1,
   type: "Master",
    docType: "Default",
    
    view_summary: {
      "inGrid": [
        "clientName",
        "legalBusinessName",
        "doingBusinessAsDBA",
        "taxIdentificationNumberTINEIN",
        "medicaidGroupID",
        "fiscalYearDateEnd"
      ],
      "default": [
        "clientName",
        "legalBusinessName",
        "doingBusinessAsDBA",
        "taxIdentificationNumberTINEIN",
        "fiscalYearDateEnd"
      ]
    },
    fields: "[{\"id\":\"step-001\",\"name\":\"Step 1\",\"sections\":[{\"id\":\"section-basic-info\",\"name\":\"Basic Information\",\"fields\":[{\"id\":\"clientImage\",\"label\":\"Client Image\",\"field_type\":\"file_upload\",\"placeholder\":\"Choose client image/logo\",\"position\":1},{\"id\":\"clientName\",\"label\":\"Client Name\",\"field_type\":\"text\",\"placeholder\":\"Enter client name\",\"show_in_grid\":true,\"is_default\":true,\"position\":2,\"required\":true},{\"id\":\"legalBusinessName\",\"label\":\"Legal Business Name\",\"field_type\":\"text\",\"placeholder\":\"Enter full legal name of business\",\"show_in_grid\":true,\"is_default\":true,\"position\":3},{\"id\":\"clientEmail\",\"label\":\"Client Email\",\"field_type\":\"email\",\"placeholder\":\"Enter official email\",\"position\":4,\"required\":true},{\"id\":\"dba\",\"label\":\"Doing Business As (DBA)\",\"field_type\":\"text\",\"placeholder\":\"Enter DBA name\",\"show_in_grid\":true,\"is_default\":true,\"position\":5},{\"id\":\"taxId\",\"label\":\"Tax Identification Number (TIN/EIN)\",\"field_type\":\"number\",\"placeholder\":\"Enter TIN or EIN\",\"required\":false,\"filter\":false,\"show_in_grid\":true,\"is_default\":true,\"position\":6},{\"id\":\"organizationType\",\"label\":\"Organization Type\",\"field_type\":\"select\",\"placeholder\":\"Select organization type\",\"options\":[{\"id\":\"option_1750311232856\",\"value\":\"Clinic\"},{\"id\":\"option_1750311256495\",\"value\":\"Hospital\"},{\"id\":\"option_1750311260590\",\"value\":\"Home Health Agency\"},{\"id\":\"option_1750311270519\",\"value\":\"Skilled Nursing Facility (SNF)\"},{\"id\":\"option_1750311277150\",\"value\":\"Ambulatory Surgical Center (ASC)\"},{\"id\":\"option_1750311284661\",\"value\":\"Federally Qualified Health Center (FQHC)\"},{\"id\":\"option_1750311291582\",\"value\":\"Rural Health Clinic (RHC)\"},{\"id\":\"option_1750311294917\",\"value\":\"Urgent Care Center\"},{\"id\":\"option_1750311303124\",\"value\":\"Behavioral Health Facility\"},{\"id\":\"option_1750311308870\",\"value\":\"Laboratory\"},{\"id\":\"option_1750311314220\",\"value\":\"Imaging Center\"},{\"id\":\"option_1750311319653\",\"value\":\"Pharmacy\"},{\"id\":\"option_1750311325461\",\"value\":\"Durable Medical Equipment Supplier (DME)\"},{\"id\":\"option_1750311330037\",\"value\":\"Hospice\"},{\"id\":\"option_1750311334868\",\"value\":\"Independent Diagnostic Testing Facility (IDTF)\"},{\"id\":\"option_1750311339836\",\"value\":\"Mobile Unit\"},{\"id\":\"option_1750311347948\",\"value\":\"Telehealth Provider\"},{\"id\":\"option_1750311356124\",\"value\":\"Telehealth Provider\"},{\"id\":\"option_1750311356370\",\"value\":\"Academic Medical Center\"},{\"id\":\"option_1750311356691\",\"value\":\"Group Practice\"}],\"position\":7},{\"id\":\"incorporationDate\",\"label\":\"Incorporation Date\",\"field_type\":\"date\",\"placeholder\":\"Select incorporation date\",\"position\":8},{\"id\":\"stateOfIncorporation\",\"label\":\"State of Incorporation\",\"field_type\":\"select\",\"placeholder\":\"Select state\",\"position\":9,\"global\":false,\"options\":[{\"id\":\"option_1751269742192\",\"value\":\"California\"},{\"id\":\"option_1751269745016\",\"value\":\"Texas\"},{\"id\":\"option_1751269745912\",\"value\":\"Flordia\"}]},{\"id\":\"medicaidGroupId\",\"label\":\"Medicaid Group ID\",\"field_type\":\"text\",\"placeholder\":\"Enter Medicaid Group ID\",\"position\":11,\"only_in_grid\":true,\"show_in_grid\":false,\"is_default\":false},{\"id\":\"cliaNumber\",\"label\":\"CLIA Number\",\"field_type\":\"text\",\"placeholder\":\"Enter CLIA number\",\"position\":12},{\"id\":\"naicsCode\",\"label\":\"NAICS Code\",\"field_type\":\"text\",\"placeholder\":\"Enter NAICS code\",\"position\":13},{\"id\":\"practiceCode\",\"label\":\"Practice Code\",\"field_type\":\"text\",\"placeholder\":\"Enter practice code\",\"position\":14},{\"id\":\"specialty\",\"label\":\"Specialty\",\"field_type\":\"select\",\"placeholder\":\"Select specialty\",\"options\":[{\"id\":\"option_1750326681585\",\"value\":\"Cardiology\"},{\"id\":\"option_1750326774407\",\"value\":\"Neurology\"},{\"id\":\"option_1750326782456\",\"value\":\"Oncology\"},{\"id\":\"option_1750326793863\",\"value\":\"Orthopedics\"},{\"id\":\"option_1750326802528\",\"value\":\"Pediatrics\"},{\"id\":\"option_1750326826183\",\"value\":\"Gynecology\"},{\"id\":\"option_1750326829255\",\"value\":\"Dermatology\"},{\"id\":\"option_1750326843689\",\"value\":\"Psychiatry\"},{\"id\":\"option_1750326847319\",\"value\":\"Gastroenterology\"},{\"id\":\"option_1750326862465\",\"value\":\"Pulmonology\"}],\"position\":15},{\"id\":\"field-a1cb0353-5185-4ecc-bc78-79bb60805f93\",\"label\":\"Languages offered\",\"placeholder\":\"select Multi-Select\",\"field_type\":\"multiselect\",\"options\":[{\"id\":\"86abce7e-c1c4-4e1b-bfd4-a02c9755b186\",\"value\":\"English\"},{\"id\":\"dab023d0-df3d-4de2-9b53-b2a6f3e313a2\",\"value\":\"Spanish\"},{\"id\":\"option_1750759822340\",\"value\":\"Chinese\"},{\"id\":\"option_1750759823668\",\"value\":\"Tagalog\"},{\"id\":\"option_1750759824356\",\"value\":\"Vietnamese\"},{\"id\":\"option_1750759824874\",\"value\":\"Arabic\"},{\"id\":\"option_1750759825202\",\"value\":\"French\"},{\"id\":\"option_1750759825435\",\"value\":\"Korean\"},{\"id\":\"option_1750759825636\",\"value\":\"Russian\"},{\"id\":\"option_1750759825852\",\"value\":\"Portuguese\"},{\"id\":\"option_1750759826068\",\"value\":\"German\"},{\"id\":\"option_1750759826348\",\"value\":\"Hindi\"},{\"id\":\"option_1750759826731\",\"value\":\"Japanese\"},{\"id\":\"option_1750759827027\",\"value\":\"Polish\"},{\"id\":\"option_1750759827354\",\"value\":\"Persian\"}],\"required\":true,\"show_in_grid\":false,\"is_default\":false,\"global\":true,\"position\":16},{\"id\":\"field-04a1278e-dfc2-4455-8134-1bf2d0c62500\",\"label\":\"Timezone\",\"placeholder\":\"select Select\",\"field_type\":\"select\",\"options\":[{\"id\":\"189f417e-25f2-49cb-ba47-8a315aaa4d39\",\"value\":\"Eastern Time\"},{\"id\":\"ffe256d9-ef3c-4413-a6b7-b189d09bd49e\",\"value\":\"Central Time\"},{\"id\":\"option_1750760033516\",\"value\":\"Mountain Time\"},{\"id\":\"option_1750760036155\",\"value\":\"Pacific Time\"},{\"id\":\"option_1750760037411\",\"value\":\"Alaska Time\"},{\"id\":\"option_1750760037946\",\"value\":\"Hawaii-Aleutian Time\"},{\"id\":\"option_1750760038283\",\"value\":\"Atlantic Time (Puerto Rico)\"},{\"id\":\"option_1750760038530\",\"value\":\"Samoa Time (American Samoa)\"},{\"id\":\"option_1750760040889\",\"value\":\"Chamorro Time (Guam, CNMI)\"}],\"filter\":false,\"global\":true,\"position\":17},{\"id\":\"field-5b9db211-fd67-4d86-a4fe-798acaa702a0\",\"label\":\"Fiscal Year Date End\",\"placeholder\":\"Enter Date\",\"field_type\":\"date\",\"options\":[],\"show_in_grid\":true,\"is_default\":true,\"position\":18},{\"id\":\"field-7940b67c-6cce-44eb-b593-094c861b5360\",\"label\":\"Telehealth/Telemedicine Delivery Method\",\"placeholder\":\"select Select\",\"field_type\":\"select\",\"options\":[{\"id\":\"c70bbba2-a047-414f-a0a5-10adf98276d6\",\"value\":\"Audio Only\"},{\"id\":\"7dbb0cb8-6a3f-4b1e-b8e2-2c7f8c92ed45\",\"value\":\"Digital Only\"},{\"id\":\"option_1750760301045\",\"value\":\"Both Audio & Digital\"}],\"visibleIf\":\"{field-f21a4a1f-bf31-4d80-9574-6def8124dfac} contains 'Yes'\",\"logicConditions\":[{\"fieldId\":\"field-f21a4a1f-bf31-4d80-9574-6def8124dfac\",\"operator\":\"===\",\"value\":\"Yes\",\"multiSelectLogic\":\"OR\"}],\"logicJoinType\":\"OR\",\"position\":20},{\"id\":\"field-3bfc5d5b-1400-4aec-b7ac-aac3524ff737\",\"label\":\"Providing Telehealth / Telemedicine Services\",\"placeholder\":\"Select\",\"field_type\":\"checkboxes\",\"globals_name\":\"Checkboxes\",\"options\":[{\"id\":\"380fbd51-f48f-4945-aeca-a8d0ea6defa9\",\"value\":\"Yes\"},{\"id\":\"c4952c3d-3d69-450e-abe5-0ae266c6eb60\",\"value\":\"No\"}]}]},{\"id\":\"f17d6607-633b-4fe4-aed5-587f014f64ea\",\"name\":\"Taxonomy Details\",\"fields\":[{\"id\":\"field-35a98804-76d1-49fc-8517-8d28a921cdae\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"Taxonomy Code\",\"fieldType\":\"string\",\"id\":\"col_1750310493275\"},{\"name\":\"Taxonomy Description\",\"fieldType\":\"string\",\"id\":\"col_1750310500475\"},{\"name\":\"Primary Taxonomy\",\"fieldType\":\"string\",\"id\":\"col_1750310508387\"},{\"name\":\"Effective Date\",\"fieldType\":\"date\",\"id\":\"col_1750310516369\"},{\"name\":\"Taxonomy Type\",\"fieldType\":\"string\",\"id\":\"col_1750310531746\",\"options\":[{\"id\":\"opt_1751103934749\",\"value\":\"Option 1\"},{\"id\":\"opt_1751103936723\",\"value\":\"Option 2\"}]},{\"name\":\"Associated NPI\",\"fieldType\":\"string\",\"id\":\"col_1750310631692\"},{\"name\":\"State Licensure Match Required\",\"fieldType\":\"string\",\"id\":\"col_1750310644109\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1750310653988\"}],\"rows\":[]}]},{\"id\":\"ce3dbe47-9164-46ba-a547-90140d01b580\",\"name\":\"NPI Details\",\"fields\":[{\"id\":\"field-917bb934-19d2-4385-8c01-ffdd5cf4d8ae\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"NPI Number\",\"fieldType\":\"string\",\"id\":\"col_1750310678339\"},{\"name\":\"NPI Type\",\"fieldType\":\"string\",\"id\":\"col_1750310685066\"},{\"name\":\"Entity Name\",\"fieldType\":\"string\",\"id\":\"col_1750310694643\"},{\"name\":\"NPI Status\",\"fieldType\":\"select\",\"id\":\"col_1750310702315\",\"options\":[{\"id\":\"opt_1751270030937\",\"value\":\"Active\"},{\"id\":\"opt_1751270037064\",\"value\":\"In-Active\"}]},{\"name\":\"Enumeration Date\",\"fieldType\":\"string\",\"id\":\"col_1750310710139\"},{\"name\":\"Deactivation Date\",\"fieldType\":\"string\",\"id\":\"col_1750310716426\"},{\"name\":\"NPI Registry Link\",\"fieldType\":\"string\",\"id\":\"col_1750310724106\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1750310730666\"}],\"rows\":[]}]},{\"id\":\"section-contact-info\",\"name\":\"Contact Information\",\"fields\":[{\"id\":\"telephone\",\"label\":\"Telephone\",\"field_type\":\"phone\",\"placeholder\":\"Enter phone number\",\"position\":1},{\"id\":\"fax\",\"label\":\"Fax\",\"field_type\":\"phone\",\"placeholder\":\"Enter fax number (optional)\",\"position\":2},{\"id\":\"address1\",\"label\":\"Address\",\"field_type\":\"text\",\"placeholder\":\"Enter address line 1\",\"position\":3},{\"id\":\"address2\",\"label\":\"Address 2\",\"field_type\":\"text\",\"placeholder\":\"Enter address line 2 (optional)\",\"position\":4},{\"id\":\"field-5a063160-ae20-4276-967d-b1d327c0f8ff\",\"label\":\"Country\",\"placeholder\":\"Select Country\",\"field_type\":\"global_select\",\"globals_name\":\"Country\",\"options\":[],\"position\":5},{\"id\":\"field-4e32ce5d-028d-419d-959d-1898c4d9f395\",\"label\":\"County\",\"placeholder\":\"Select County\",\"field_type\":\"global_select\",\"globals_name\":\"County\",\"options\":[],\"position\":6},{\"id\":\"field-ac5e6e45-1da1-44ff-84dc-226d6fd50fd8\",\"label\":\"State/Province\",\"placeholder\":\"Select State/province\",\"field_type\":\"global_select\",\"globals_name\":\"State\",\"options\":[],\"position\":7},{\"id\":\"field-4d24f03f-df2b-4f95-a7d1-a220322a46eb\",\"label\":\"City\",\"placeholder\":\"Select City\",\"field_type\":\"global_select\",\"globals_name\":\"City\",\"options\":[],\"position\":8},{\"id\":\"zipPostalCode\",\"label\":\"Zip/Postal Code\",\"field_type\":\"text\",\"placeholder\":\"Enter postal/ZIP code\",\"position\":9}]},{\"id\":\"84370e17-0343-48e0-bc45-ca5442b80ca4\",\"name\":\"Ownership Type\",\"fields\":[{\"id\":\"field-5d31f130-af41-47cb-918e-2059a1790366\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"Field Name\",\"fieldType\":\"string\",\"id\":\"col_1750310748217\"},{\"name\":\"Owner Name\",\"fieldType\":\"string\",\"id\":\"col_1750310755089\"},{\"name\":\"Owner Type\",\"fieldType\":\"string\",\"id\":\"col_1750310762544\"},{\"name\":\"Ownership Percentage\",\"fieldType\":\"string\",\"id\":\"col_1750310768783\"},{\"name\":\"Tax ID (If Entity)\",\"fieldType\":\"string\",\"id\":\"col_1750310777320\"},{\"name\":\"SSN (If Individual)\",\"fieldType\":\"string\",\"id\":\"col_1750310787817\"},{\"name\":\"Date of Birth (If Individual)\",\"fieldType\":\"date\",\"id\":\"col_1750310795326\",\"position\":6},{\"name\":\"Place of Birth\",\"fieldType\":\"date\",\"id\":\"col_1751872625779\",\"position\":7},{\"name\":\"Driver's License Attachment\",\"fieldType\":\"string\",\"id\":\"col_1751872638280\"},{\"name\":\"Ownership Type\",\"fieldType\":\"string\",\"id\":\"col_1751872657896\"},{\"name\":\"Relationship to Entity\",\"fieldType\":\"string\",\"id\":\"col_1751872670000\"},{\"name\":\"Start Date of Ownership\",\"fieldType\":\"string\",\"id\":\"col_1751872679144\"},{\"name\":\"End Date (If Applicable)\",\"fieldType\":\"string\",\"id\":\"col_1751872699784\"},{\"name\":\"Is Managing Employee?\",\"fieldType\":\"string\",\"options\":[{\"id\":\"opt_1751872738840\",\"value\":\"Yes\"},{\"id\":\"opt_1751872742847\",\"value\":\"No\"}],\"id\":\"col_1751872746816\"},{\"name\":\"Is Authorized Official?\",\"fieldType\":\"string\",\"options\":[{\"id\":\"opt_1751872764791\",\"value\":\"Yes\"},{\"id\":\"opt_1751872768543\",\"value\":\"No\"}],\"id\":\"col_1751872772855\"},{\"name\":\"CMS Disclosures Required (5% More)?\",\"fieldType\":\"string\",\"id\":\"col_1751872792647\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1751872805255\"}],\"rows\":[]}]},{\"id\":\"section-point-of-contact\",\"name\":\"Point of Contact\",\"fields\":[{\"id\":\"pointOfContactGrid\",\"label\":\"Point of Contact\",\"field_type\":\"grid\",\"columns\":[{\"id\":\"col_name\",\"name\":\"Title\",\"fieldType\":\"string\"},{\"id\":\"col_email\",\"name\":\"Name\",\"fieldType\":\"string\"},{\"id\":\"col_mobile\",\"name\":\"Email ID\",\"fieldType\":\"string\"},{\"id\":\"col_primary\",\"name\":\"Mobile Number\",\"fieldType\":\"string\"},{\"name\":\"IsPrimary\",\"fieldType\":\"select\",\"id\":\"col_1750319189259\",\"options\":[{\"id\":\"opt_1751872981774\",\"value\":\"Yes\"},{\"id\":\"opt_1751872985654\",\"value\":\"No\"}]},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1750319208889\"}],\"rows\":[]}]},{\"id\":\"section-authorization-mapping\",\"name\":\"Automation Mapping\",\"fields\":[{\"id\":\"authorizationMapping\",\"label\":\"Authorization Mapping\",\"field_type\":\"grid\",\"columns\":[{\"name\":\"Name\",\"fieldType\":\"string\",\"id\":\"col_1750164416453\"},{\"name\":\"Email ID\",\"fieldType\":\"string\",\"id\":\"col_1750164559603\"},{\"name\":\"process\",\"fieldType\":\"global_select\",\"id\":\"col_1750164571203\",\"position\":2,\"globalFieldId\":\"6853ca9801e02247261b41ec\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1750319080428\"}],\"rows\":[]}]},{\"id\":\"29d87f5a-9aa8-407d-98a9-03b86d8364aa\",\"name\":\"Practice Location(s)\",\"fields\":[{\"id\":\"field-8e2a1d16-5d4a-49e9-9b2b-f8dcb1fe3f10\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"Practice Location Name\",\"fieldType\":\"string\",\"id\":\"col_1750311706113\"},{\"name\":\"Location Type\",\"fieldType\":\"string\",\"id\":\"col_1750311714839\"},{\"name\":\"Phone \",\"fieldType\":\"string\",\"id\":\"col_1750311720943\"},{\"name\":\"Fax\",\"fieldType\":\"string\",\"id\":\"col_1750311728734\"},{\"name\":\"Location NPI\",\"fieldType\":\"string\",\"id\":\"col_1750311736447\"},{\"name\":\"Email for Location\",\"fieldType\":\"string\",\"id\":\"col_1750311743751\"},{\"name\":\"City\",\"fieldType\":\"string\",\"id\":\"col_1750311750471\"},{\"name\":\"State\",\"fieldType\":\"string\",\"id\":\"col_1750311764470\"},{\"name\":\"Zip\",\"fieldType\":\"string\",\"id\":\"col_1750311774590\"},{\"name\":\"Address\",\"fieldType\":\"text\",\"id\":\"col_1750311836628\"},{\"name\":\"Filling under TIN / NPI\",\"fieldType\":\"text\",\"id\":\"col_1751873323781\"},{\"name\":\"TIN\",\"fieldType\":\"text\",\"id\":\"col_1751873333460\"},{\"name\":\"Is Primary\",\"fieldType\":\"select\",\"options\":[{\"id\":\"opt_1751873345524\",\"value\":\"Yes\"},{\"id\":\"opt_1751873349580\",\"value\":\"No\"}],\"id\":\"col_1751873352980\"}],\"rows\":[]}]},{\"id\":\"section-credentialing\",\"name\":\"Credentialing Contact\",\"fields\":[{\"id\":\"field-70dc601b-aa91-4e31-b6b5-90e071d89523\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"Contact Name\",\"fieldType\":\"text\",\"id\":\"col_1750324326471\"},{\"name\":\"Title\",\"fieldType\":\"text\",\"id\":\"col_1750324338893\"},{\"name\":\"Phone Number\",\"fieldType\":\"text\",\"id\":\"col_1750324345559\"},{\"name\":\"Email Address\",\"fieldType\":\"text\",\"id\":\"col_1750324352646\"},{\"name\":\"Alternate Contact\",\"fieldType\":\"text\",\"id\":\"col_1750324361550\"},{\"name\":\"Action\",\"fieldType\":\"text\",\"id\":\"col_1750324371950\"}],\"rows\":[]}]},{\"id\":\"section-licenses\",\"name\":\"Licenses & Accreditation\",\"fields\":[{\"id\":\"licenseGrid\",\"label\":\"Licenses\",\"field_type\":\"grid\",\"columns\":[{\"id\":\"col_state\",\"name\":\"State\",\"fieldType\":\"string\"},{\"id\":\"col_license\",\"name\":\"State Business License Number\",\"fieldType\":\"string\"},{\"name\":\"Expiry Date\",\"fieldType\":\"date\",\"id\":\"col_1751873479572\",\"position\":2},{\"name\":\"Version\",\"fieldType\":\"string\",\"id\":\"col_1751873489916\"},{\"name\":\"Accreditation Body\",\"fieldType\":\"string\",\"id\":\"col_1751873500379\"},{\"name\":\"Accreditation Date & Expiry\",\"fieldType\":\"string\",\"id\":\"col_1751873510379\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1751873519411\"}],\"rows\":[]}]},{\"id\":\"25349846-45f5-4695-a2b3-51d3daf01368\",\"name\":\"Documents to Upload\",\"fields\":[{\"id\":\"field-9c1a96b4-0baf-4239-95c8-49060b540010\",\"label\":\"Table Grid field\",\"placeholder\":\"Enter Table Grid\",\"field_type\":\"grid\",\"options\":[],\"columns\":[{\"name\":\"Document Type\",\"fieldType\":\"string\",\"id\":\"col_1750315699388\"},{\"name\":\"Expiration Date\",\"fieldType\":\"date\",\"id\":\"col_1750315761082\",\"position\":1},{\"name\":\"Reminder Days\",\"fieldType\":\"string\",\"id\":\"col_1750315772327\"},{\"name\":\"Renew By\",\"fieldType\":\"date\",\"id\":\"col_1750315785808\",\"position\":3},{\"name\":\"Issue Date\",\"fieldType\":\"string\",\"id\":\"col_1750315793864\"},{\"name\":\"Last Updated Date\",\"fieldType\":\"date\",\"id\":\"col_1750315803751\",\"position\":5},{\"name\":\"Version\",\"fieldType\":\"string\",\"id\":\"col_1750315810583\"},{\"name\":\"Status\",\"fieldType\":\"string\",\"id\":\"col_1750315817855\"},{\"name\":\"Action\",\"fieldType\":\"string\",\"id\":\"col_1750315845446\"}],\"rows\":[]}]},{\"id\":\"section-logins\",\"name\":\"Other Login Details\",\"fields\":[{\"id\":\"field-92fc361c-19c7-4cdf-b6f2-5f91e1da4b15\",\"label\":\"Name\",\"placeholder\":\"Enter Name\",\"field_type\":\"text\",\"options\":[],\"position\":1},{\"id\":\"loginLink\",\"label\":\"Link\",\"field_type\":\"text\",\"placeholder\":\"Enter login URL\",\"position\":2},{\"id\":\"loginUsername\",\"label\":\"Username\",\"field_type\":\"text\",\"placeholder\":\"Enter login username\",\"position\":3},{\"id\":\"loginPassword\",\"label\":\"Password\",\"field_type\":\"password\",\"position\":4}]},{\"id\":\"f2e2018b-a709-4408-8a1e-37e48e828ba7\",\"name\":\"Operating Hours\",\"fields\":[{\"id\":\"field-3def358e-d6e1-4b00-94e7-81a40ee47b49\",\"label\":\"Day\",\"placeholder\":\"Select Day\",\"field_type\":\"select\",\"options\":[{\"id\":\"e7009b7b-8575-48f3-bf09-4c676904d65e\",\"value\":\"Sunday\"},{\"id\":\"1c293a4f-7a0f-4344-93c6-d87e18127f16\",\"value\":\"Monday\"},{\"id\":\"option_1750316097931\",\"value\":\"Tuesday\"},{\"id\":\"option_1750316103204\",\"value\":\"Wednesday\"},{\"id\":\"option_1750316108859\",\"value\":\"Thursday\"},{\"id\":\"option_1750316114083\",\"value\":\"Firday\"},{\"id\":\"option_1750316118859\",\"value\":\"Saturday\"}],\"position\":1},{\"id\":\"field-ed704e52-12b0-4385-8430-90e4e90bd717\",\"label\":\"Start Time\",\"placeholder\":\"Select Start Time\",\"field_type\":\"time\",\"globals_name\":\"Time\",\"options\":[]},{\"id\":\"field-9e7c4a08-ce56-455f-b572-0db6b99d0b5d\",\"label\":\"End Time\",\"placeholder\":\"Select End Time\",\"field_type\":\"time\",\"globals_name\":\"Time\",\"options\":[]}]},{\"id\":\"section-document-checklist\",\"name\":\"Document Checklist\",\"fields\":[{\"id\":\"field-7fbf3bca-4154-4893-b650-a4b7f72d531e\",\"label\":\"\",\"placeholder\":\"Enter Checkboxes\",\"field_type\":\"checkboxes\",\"options\":[{\"id\":\"c315f7b8-a119-4152-8601-94ce96bed473\",\"value\":\"Group Name\"},{\"id\":\"37a75da8-5714-4b7a-8e0b-d94e7d0f81d6\",\"value\":\"Tax ID\"},{\"id\":\"option_1750141986874\",\"value\":\"NPI\"},{\"id\":\"option_1750142077122\",\"value\":\"Group W9 Form\"},{\"id\":\"option_1750142086514\",\"value\":\"Start date of the Group\"},{\"id\":\"option_1750142095882\",\"value\":\"Group's Practice/Billing and Mailing address along with Ph and Fax\"},{\"id\":\"option_1750142104786\",\"value\":\"Voided check/Bank letter to complete EFT\"},{\"id\":\"option_1750142105490\",\"value\":\"Owner Information (group owner's name, % of ownership, SSN, DOB and Home address)\"},{\"id\":\"option_1750142121546\",\"value\":\"CP575/IRS document (This is for Group enrollment)\"},{\"id\":\"option_1750142129690\",\"value\":\"Group Professional liability insurance, if any\"},{\"id\":\"option_1750142137355\",\"value\":\"Payer list (To start enrollment applications with those Payers)\"}]}]}]}]",

    description: 'organization-form'
  }
];

  // Templates are ready to seed

  await seedDefaultTemplates(Template, rawTemplates);

  console.log('✅ Template seeding completed successfully!');
  await mongoose.disconnect();
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedTemplates().catch((err) => {
    console.error('❌ Seeding failed:', err);
    process.exit(1);
  });
}

// Export for use in other files
export { seedTemplates };