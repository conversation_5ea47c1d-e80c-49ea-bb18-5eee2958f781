import { RemoteGraphQLDataSource } from '@apollo/gateway';
import { DataSourceTypes } from '../types';
import OpenDataSource from './openDataSource';
import AuthenticatedDataSources from './authenticatedDataSource';

const dataSources: DataSourceTypes = {
  identity: {
    dataSourceClass: OpenDataSource,
  },
  'des-service': {
    dataSourceClass: OpenDataSource,
  },
  'provider': {
    dataSourceClass: AuthenticatedDataSources,
  },
  'notification-service': {
    dataSourceClass: AuthenticatedDataSources,
  }
};

export function getDataSource(
  name: string,
  url?: string,
): RemoteGraphQLDataSource {
  try {
    const dataSourceEntry = dataSources[name];
    if (!dataSourceEntry) {
      throw new Error(`Data source '${name}' not found`);
    }
    const { dataSourceClass } = dataSourceEntry;
    const dataSource: RemoteGraphQLDataSource = new dataSourceClass({ url });
    return dataSource;
  } catch (error) {
    console.error(`Failed to create data source for '${name}':`, error);
    return new OpenDataSource({ url });
  }
}
