import { Resolver, Query, Mutation, Args, ID, Info, Context } from '@nestjs/graphql';
import { DesServiceService } from './des-service.service';
import {
  CreateTemplateInput,
  UpdateTemplateInput,
  FindAllTemplatesArgs,
  FindAllTemplatesResponse,
  CloneTemplateInput,
  CreateTemplateVersionInput
} from './dto/template.dto';
import { BaseResponse } from './dto/base.response.dto';
import { Template } from './entities/template.entity';
import * as jwt from 'jsonwebtoken';
import { RequirePermission } from '@app/permissions';
import { ProviderEmailTicketInput } from './dto/template.dto';
import { TemplateSeedingService } from './template-seeding.service';

@Resolver(() => Template)
export class DesServiceResolver {
  constructor(
    private readonly desServiceService: DesServiceService,
    private readonly templateSeedingService: TemplateSeedingService
  ) { }

  // ==================== TEMPLATE OPERATIONS ====================

  /**
   * Fetches all templates with dynamic search, filter, and sort
   * @param args Query arguments including pagination, search, filters, and sort
   * @returns A BaseResponse containing templates and pagination info
   */
  @Query(() => BaseResponse, { name: 'templates' })
  // @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'View Template' })
  async getTemplates(@Args() args: FindAllTemplatesArgs, @Context() context: any) {
    const orgID = context.req.headers.orgid ?? '';
    const subOrgId = context.req.headers.suborgid ?? '';
    console.log("headers", context.req.headers);
    
    return this.desServiceService.findAllTemplates(args,orgID,subOrgId);
  }

  /**
   * Extracts selected fields from GraphQL info object for templates query
   * @param info GraphQL info object
   * @returns Array of selected field names
   */
  private extractSelectedFields(info: any): string[] {
    try {
      const selections: any[] = info.fieldNodes[0]?.selectionSet?.selections || [];
      const fields: string[] = [];

      for (const selection of selections) {
        if (selection.kind === 'Field') {
          if (selection.name.value === 'templates') {
            // Extract fields from templates selection
            const templateSelections: any[] = selection.selectionSet?.selections || [];
            for (const templateSelection of templateSelections) {
              if (templateSelection.kind === 'Field') {
                fields.push(templateSelection.name.value);
              }
            }
          }
        }
      }

      // Always include essential fields for functionality
      const essentialFields = ['_id', 'name', 'version', 'isActive', 'createdAt', 'updatedAt'];
      const allFields = [...new Set([...essentialFields, ...fields])];

      console.log('Extracted fields for templates query:', allFields);
      return allFields;
    } catch (error) {
      console.error('Error extracting fields:', error);
      // Return all essential fields if extraction fails
      return ['_id', 'name', 'version', 'isActive', 'createdAt', 'updatedAt', 'description', 'fields', 'view_summary', 'type'];
    }
  }

  /**
   * Fetches a template by its ID
   * @param id The template's ID
   * @returns A BaseResponse containing the template
   */
  @Query(() => BaseResponse, { name: 'template' })
  // @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'View Template' })
  async getTemplate(@Args('id', { type: () => ID }) id: string, @Context() context: any) {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
    return this.desServiceService.findTemplateById(id);
  }

  /**
   * Creates a new template
   * @param input Template creation data
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Create Template' })
  async createTemplate(@Args('input') input: CreateTemplateInput, @Context() context: any) {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    console.log("decoded",decoded);
    
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
    return this.desServiceService.createTemplate(input, userId);
  }
  /**
   * Updates an existing template
   * @param input Template update data
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Edit Template' })
  async updateTemplate(@Args('input') input: UpdateTemplateInput, @Context() context: any) {
    console.log("input",context);
    
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    console.log("decoded",decoded);
    
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
    return this.desServiceService.updateTemplate(input, userId);
  }

  /**
   * Soft deletes a template (sets isActive to false)
   * @param id The template's ID
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Delete Template' })
  async deleteTemplate(@Args('id', { type: () => ID }) id: string, @Context('user') user: any) {
    return this.desServiceService.deleteTemplate(id);
  }

  /**
   * Clones a template by its ID
   * @param input CloneTemplateInput (id, organisationId, subOrganisationId)
   * @returns A BaseResponse containing the cloned template
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Create Template' })
  async cloneTemplate(@Args('input') input: CloneTemplateInput,@Context() context: any) {
    console.log("input",context);
    
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    console.log("decoded",decoded);
    
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
    return this.desServiceService.cloneTemplate(input.id, input.organisationId, input.subOrganisationId,userId, input.name);
  }

  /**
   * Create a new version of an existing template
   * Updates current template to isActive: false and creates new template with viewSummary and fields from old version
   * @param input Create template version input data (currentId, oldVersionId, name, description)
   * @param context Request context
   * @returns A BaseResponse containing the new template version
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Create Template' })
  async createTemplateVersion(@Args('input') input: CreateTemplateVersionInput, @Context() context: any) {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.desServiceService.createTemplateVersion(input, userId);
  }

  /**
   * Gets count of templates with optional filters
   * @param filters Optional filters to apply
   * @returns A BaseResponse containing the count
   */
  @Query(() => BaseResponse, { name: 'templateCount' })
  async getTemplateCount(@Context('user') user: any, @Args('filters', { type: () => String, nullable: true }) filters?: string) {
    const parsedFilters = filters ? JSON.parse(filters) : undefined;
    return this.desServiceService.getTemplateCount(parsedFilters);
  }


    @Mutation(() => BaseResponse)
    async n8bncreate(
      @Args('input') input: ProviderEmailTicketInput,
      @Context() context: any
    ) {      
      return this.desServiceService.n8bncreate(input);
    }
  /**
   * Seeds default templates into the database
   * @returns A BaseResponse indicating success or failure
   */
  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Des Form', permission: 'Create Template' })
  async seedTemplates(@Context() context: any) {
    try {
      const result = await this.templateSeedingService.manualSeed();

      return {
        success: result.success,
        message: result.message,
        code: result.success ? 200 : 500,
        data: { templatesSeeded: result.count }
      };
    } catch (error) {
      return {
        success: false,
        message: `Template seeding failed: ${error.message}`,
        code: 500,
        data: null
      };
    }
  }
}