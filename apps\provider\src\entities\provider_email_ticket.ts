import { ObjectType, Field, ID, Directive } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { ProviderEntryType } from './provider.entity';

@Schema({ timestamps: true })
@ObjectType()
export class EmailTicket extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  subject: string;


  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  ticket_type?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  from?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  to?: string;
  
 // FIX ME: change number
  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  priority?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  description?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  ticket_process?: string;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  created_by?: string;

  @Field(() => [TicketMessage], { nullable: true })
  @Prop({ type: [Object], default: [] })
  messages?: TicketMessage[];

  @Field(() => GraphQLJSON)
  @Prop({ type: Object, required: true })
  values: Record<string, any>;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  conversationId?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  status?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  ticketId?: string;

  @Field(() => ProviderEntryType, { nullable: true })
  @Prop({ type: String, enum: ProviderEntryType, default: ProviderEntryType.EMAIL })
  type?: ProviderEntryType;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  assignedToLead?: string;

  @Field(() => String, { nullable: true })
  @Prop({ default: null })
  assignedToAgent?: string;
}

@ObjectType()
export class TicketValue {
  @Field()
  field: string;

  @Field()
  value: string;
}

@ObjectType()
@Directive('@shareable')
export class Attachment {
  @Field()
  fileName: string;

  @Field()
  url: string;

  @Field()
  uploadedAt: Date;

  @Field()
  type: string;
}

@ObjectType()
export class TicketMessage {
  @Field({ nullable: true })
  from?: string;

  @Field({ nullable: true })
  to?: string;

  @Field({ nullable: true })
  body?: string;

  @Field(() => Date, { nullable: true })
  date?: Date;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => [Attachment], { nullable: true })
  attachments?: Attachment[];

  @Field(() => String, { nullable: true })
  messageId?: string;

  @Field(() => String, { nullable: true })
  reason?: string
}

export const EmailTicketSchema = SchemaFactory.createForClass(EmailTicket);
