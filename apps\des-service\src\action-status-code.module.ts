import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ActionCode, ActionCodeSchema, StatusCode, StatusCodeSchema, ActionStatusCodeMap, ActionStatusCodeMapSchema } from './entities/action-status-code.entity';
import { ActionStatusCodeService } from './action-status-code.service';
import { ActionStatusCodeResolver } from './action-status-code.resolver';
// import { ProcessModule } from 'apps/identity/src/process.module';
// import { ProcessModule } from '../identity/src/process.module';

@Module({
  imports: [
    // ProcessModule,
    MongooseModule.forFeature([
      { name: ActionCode.name, schema: ActionCodeSchema },
      { name: StatusCode.name, schema: StatusCodeSchema },
      { name: ActionStatusCodeMap.name, schema: ActionStatusCodeMapSchema },
    ]),
  ],
  providers: [ActionStatusCodeService, ActionStatusCodeResolver],
  exports: [ActionStatusCodeService],
})
export class ActionStatusCodeModule {} 