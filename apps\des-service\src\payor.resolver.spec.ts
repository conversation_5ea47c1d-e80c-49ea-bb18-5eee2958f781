import { Test, TestingModule } from '@nestjs/testing';
import { PayerResolver } from './payer.resolver';
import { PayerService } from './payer.service';
import { FindAllPayersArgs, PayersResponse, CreatePayerInput, UpdatePayerInput } from './dto/payer.dto';
import { BaseResponse } from './dto/base.response.dto';
import { Types } from 'mongoose';
import { HttpStatus } from '@nestjs/common';

describe('PayorResolver', () => {
  let resolver: PayerResolver;
  let payerService: PayerService;

  // Mock data
  const mockObjectId = new Types.ObjectId();
  const mockPayor = {
    _id: mockObjectId,
    templateId: 'test-template-001',
    values: JSON.stringify({
      sections: [
        {
          fields: [
            { label: 'PayorName', value: 'Test Payor', filter: true },
            { label: 'City', value: 'New York', filter: true }
          ]
        }
      ]
    }),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPayersResponse: PayersResponse = {
    payers: [mockPayor as any],
    pagination: {
      page: 1,
      limit: 10,
      total: 1,
      totalItems: 1,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  };

  const mockBaseResponse: BaseResponse = {
    message: 'Success',
    code: HttpStatus.OK,
    type: 'SUCCESS',
    data: { payer: mockPayor },
  };

  // Mock service
  const mockPayerService = {
    findAll: jest.fn(),
    findById: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayerResolver,
        {
          provide: PayerService,
          useValue: mockPayerService,
        },
      ],
    }).compile();

    resolver = module.get<PayerResolver>(PayerResolver);
    payerService = module.get<PayerService>(PayerService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('getPayers', () => {
    it('should return paginated payers with default parameters', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
      };

      mockPayerService.findAll.mockResolvedValue(mockPayersResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(mockPayersResponse);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should handle search parameters', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
        search: 'test payer',
      };

      mockPayerService.findAll.mockResolvedValue(mockPayersResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(mockPayersResponse);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should handle filter parameters', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
        filters: {
          templateId: 'test-template',
          fieldFilters: [
            { path: 'sections.fields.PayerName', value: 'Test Payer' }
          ],
        },
      };

      mockPayerService.findAll.mockResolvedValue(mockPayersResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(mockPayersResponse);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should handle sort parameters', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
        sort: {
          field: 'templateId',
          ascending: true,
        },
      };

      mockPayerService.findAll.mockResolvedValue(mockPayersResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(mockPayersResponse);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should handle complex query with all parameters', async () => {
      const args: FindAllPayersArgs = {
        page: 2,
        limit: 5,
        search: 'test',
        filters: {
          templateId: 'template-001',
          fieldFilters: [
            { path: 'sections.fields.City', value: 'New York' }
          ],
        },
        sort: {
          field: 'values.PayerName',
          ascending: false,
        },
      };

      mockPayerService.findAll.mockResolvedValue(mockPayersResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(mockPayersResponse);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should propagate service errors', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
      };

      const error = new Error('Service error');
      mockPayerService.findAll.mockRejectedValue(error);

      await expect(resolver.getPayers(args)).rejects.toThrow(error);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });

    it('should handle empty results', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
      };

      const emptyResponse: PayersResponse = {
        payers: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalItems: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      };

      mockPayerService.findAll.mockResolvedValue(emptyResponse);

      const result = await resolver.getPayers(args);

      expect(result).toEqual(emptyResponse);
      expect(result.payers).toHaveLength(0);
      expect(mockPayerService.findAll).toHaveBeenCalledWith(args);
    });
  });

  describe('getPayer', () => {
    it('should return a payer by ID', async () => {
      const id = mockObjectId.toString();
      mockPayerService.findById.mockResolvedValue(mockBaseResponse);

      const result = await resolver.getPayer(id);

      expect(result).toEqual(mockBaseResponse);
      expect(mockPayerService.findById).toHaveBeenCalledWith(id);
    });

    it('should propagate service errors', async () => {
      const id = mockObjectId.toString();
      const error = new Error('Service error');
      mockPayerService.findById.mockRejectedValue(error);

      await expect(resolver.getPayer(id)).rejects.toThrow(error);
      expect(mockPayerService.findById).toHaveBeenCalledWith(id);
    });
  });

  describe('createPayer', () => {
    it('should create a new payer', async () => {
      const input: CreatePayerInput = {
        templateId: 'test-template-001',
        values: { PayerName: 'Test Payer', City: 'New York' },
      };

      const createResponse: BaseResponse = {
        message: 'Payer created successfully',
        code: HttpStatus.CREATED,
        type: 'SUCCESS',
        data: { payer: mockPayor },
      };

      mockPayerService.create.mockResolvedValue(createResponse);

      const result = await resolver.createPayer(input);

      expect(result).toEqual(createResponse);
      expect(mockPayerService.create).toHaveBeenCalledWith(input);
    });

    it('should propagate service errors', async () => {
      const input: CreatePayerInput = {
        templateId: 'test-template-001',
        values: { PayerName: 'Test Payer' },
      };

      const error = new Error('Service error');
      mockPayerService.create.mockRejectedValue(error);

      await expect(resolver.createPayer(input)).rejects.toThrow(error);
      expect(mockPayerService.create).toHaveBeenCalledWith(input);
    });
  });

  describe('updatePayer', () => {
    it('should update a payer', async () => {
      const input: UpdatePayerInput = {
        id: mockObjectId.toString(),
        templateId: 'updated-template',
        values: { PayerName: 'Updated Payer' },
      };

      const updateResponse: BaseResponse = {
        message: 'Payer updated successfully',
        code: HttpStatus.OK,
        type: 'SUCCESS',
        data: { payer: { ...mockPayor, templateId: 'updated-template' } },
      };

      mockPayerService.update.mockResolvedValue(updateResponse);

      const result = await resolver.updatePayer(input);

      expect(result).toEqual(updateResponse);
      expect(mockPayerService.update).toHaveBeenCalledWith(input);
    });

    it('should propagate service errors', async () => {
      const input: UpdatePayerInput = {
        id: mockObjectId.toString(),
        templateId: 'updated-template',
      };

      const error = new Error('Service error');
      mockPayerService.update.mockRejectedValue(error);

      await expect(resolver.updatePayer(input)).rejects.toThrow(error);
      expect(mockPayerService.update).toHaveBeenCalledWith(input);
    });
  });

  describe('deletePayer', () => {
    it('should delete a payer', async () => {
      const id = mockObjectId.toString();
      const deleteResponse: BaseResponse = {
        message: 'Payer deleted successfully',
        code: HttpStatus.OK,
        type: 'SUCCESS',
        data: { message: 'Payer deleted successfully' },
      };

      mockPayerService.delete.mockResolvedValue(deleteResponse);

      const result = await resolver.deletePayer(id);

      expect(result).toEqual(deleteResponse);
      expect(mockPayerService.delete).toHaveBeenCalledWith(id);
    });

    it('should propagate service errors', async () => {
      const id = mockObjectId.toString();
      const error = new Error('Service error');
      mockPayerService.delete.mockRejectedValue(error);

      await expect(resolver.deletePayer(id)).rejects.toThrow(error);
      expect(mockPayerService.delete).toHaveBeenCalledWith(id);
    });
  });
});
