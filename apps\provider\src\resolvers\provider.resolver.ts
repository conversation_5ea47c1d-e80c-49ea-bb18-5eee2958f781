import { Resolver, Query, Mutation, Args, ID, ObjectType, Field, Context } from '@nestjs/graphql';
import { ProviderService } from '../services/provider.service';
import { ProviderTicket } from '../entities/provider.entity';
import {
  CreateProviderTicketInput,
  UpdateProviderTicketInput,
  PaginateProviderTicketArgs,
  ProviderResponse
} from '../dto/provider_ticket.dto';
import { BaseResponse } from '../dto/base.response.dto';
import { RequirePermission } from '@app/permissions';


@Resolver(() => ProviderTicket)
export class ProviderResolver {
  constructor(private readonly providerService: ProviderService) { }

  @Query(() => ProviderResponse, { name: 'providerTickets' })
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'View' },)
  async getAllProviderTickets(@Args('input') input: PaginateProviderTicketArgs, @Context() context: any) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const roleName = context?.roleName;

    const filters: Record<string, any> = input?.filters || {};

    return this.providerService.findAll(
      search,
      selectedFields,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      userId,
      orgId,
      roleName,
    );
  }



  
  @Query(() => ProviderTicket, { name: 'providerTicket', nullable: true })
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'View' },

  )
  async getProviderTicket(@Args('id', { type: () => ID }) id: string, @Context() context: any,) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerService.findById(id, orgId, userId);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'Allocate' },

  )
  async createProviderTicket(@Args('input') input: CreateProviderTicketInput, @Context() context: any) {
    const userId = context.req?.headers?.userid;
    const orgId = context.req?.headers?.orgid;
    return this.providerService.create(input, orgId, userId);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'Edit' },

  )
  async updateProviderTicket(@Args('input') input: UpdateProviderTicketInput, @Context() context: any) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerService.update(input, orgId, userId);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'Delete' },
  )
  async deleteProviderTicket(@Args('id', { type: () => ID }) id: string, @Context() context: any,) {
    const orgId = context?.orgId;
    const userId = context?.userId;
    return this.providerService.delete(id, orgId, userId);
  }


  @Mutation(() => BaseResponse)
  async getNpiInformations(
    @Args('type', { type: () => [String], nullable: true }) type: string[],
    @Context() context: any,
  ): Promise<BaseResponse> {
    const orgId = context?.orgId;
    const userId = context?.userId;
    const suborgid = context?.subOrgId || context?.suborgid;

    const response = await this.providerService.npiInformations(
      orgId,
      userId,
      suborgid,
      type,
    );

    return {
      message: 'Match found',
      code: 200,
      type: 'SUCCESS',
      data: response,
    };
  }

    @Mutation(() => BaseResponse)
  async getProviderInformations(
    @Args('type', { type: () => [String], nullable: true }) type: string[],
    @Context() context: any,
  ): Promise<BaseResponse> {
    const orgId = context?.orgId;
    const userId = context?.userId;
    const suborgid = context?.subOrgId || context?.suborgid;

    const response = await this.providerService.providerInformations(
      orgId,
      userId,
      suborgid,
      type,
    );

    return {
      message: 'Match found',
      code: 200,
      type: 'SUCCESS',
      data: response,
    };
  }
}
