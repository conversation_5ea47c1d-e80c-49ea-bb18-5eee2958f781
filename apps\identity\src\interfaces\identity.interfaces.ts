export interface OtpVerificationResult {
  qrcode: string;
  secret_code: string;
  message: string;
}

export interface OtpVerificationInput {
  email: string;
  otp: string;
}

export interface TOtpVerificationResult {
  token: string;
  user_id: string;
  message: string;
}

export interface Secret {
  ascii: string;
  hex: string;
  base32: string;
  otpauth_url: string;
}

export interface DecodedToken {
  oid: any;
  preferred_username?: string;
  email?: string;
  upn?: string;
  name?: string;
  given_name?: string;
  unique_name?: string;
  roles?: string[] | string;
  role?: string[] | string;
  groups?: string[] | string;
  wids?: string[];
}

export interface OtpSend {
  message: any;
  is2FAenabled: boolean;
  bypass2FA:boolean
}

export interface OtpResponse {
  token: string;
  user_id: string;
}
