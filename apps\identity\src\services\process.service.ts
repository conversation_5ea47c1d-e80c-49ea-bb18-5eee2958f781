import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Process } from '../entities/process.entity';
import { CreateProcessInput, FindAllArgs, UpdateProcessInput } from './../dto/process.dto';
// import { FindAllArgs } from '../../../des-service/src/dto/action-status-code.dto';
import { Error, ErrorType, HttpStatus, ResponseCode } from '@app/error';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class ProcessService {
    constructor(
        @InjectModel(Process.name) private readonly processModel: Model<Process>,
        private readonly mongoConnectionService: MongoConnectionService,
    ) { }

    private async getProcessCollection(orgId?: string) {
        return this.mongoConnectionService.getCollectionByOrgId('processes', orgId);
    }

    async create(input: CreateProcessInput, orgId?: string): Promise<any> {
        const processCollection = await this.getProcessCollection(orgId);
        const result = await processCollection.insertOne(input);
        return processCollection.findOne({ _id: result.insertedId });
    }

    async findAll(args: FindAllArgs, orgId?: string) {
        const { page = 1, limit = 10, search, sortBy, sortOrder = 'desc' } = args;
        const skip = (page - 1) * limit;
        let query: any = {};

        if (search) {
            query.name = { $regex: search, $options: 'i' };
        }

        const sortOptions: any = sortBy ? { [sortBy]: sortOrder === 'asc' ? 1 : -1 } : { createdAt: -1 };
        const processCollection = await this.getProcessCollection(orgId);
        const [items, total] = await Promise.all([
            processCollection.find(query).sort(sortOptions).skip(skip).limit(limit).toArray(),
            processCollection.countDocuments(query),
        ]);

        return { items, total };
    }

    async findOne(id: string, orgId?: string): Promise<any> {
        const processCollection = await this.getProcessCollection(orgId);
        const process = await processCollection.findOne({ _id: typeof id === 'string' ? new (require('mongodb').ObjectId)(id) : id });
        if (!process) {
            throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'Process not found');
        }
        return process;
    }

    async update(input: UpdateProcessInput, orgId?: string): Promise<any> {
        const { id, ...updateData } = input;
        const processCollection = await this.getProcessCollection(orgId);
        const updated = await processCollection.findOneAndUpdate(
            { _id: typeof id === 'string' ? new (require('mongodb').ObjectId)(id) : id },
            { $set: updateData },
            { returnDocument: 'after' }
        );
        if (!updated || !updated.value) {
            throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'Process not found');
        }
        return updated.value;
    }

    async remove(id: string, orgId?: string): Promise<{ success: boolean }> {
        const processCollection = await this.getProcessCollection(orgId);
        const result = await processCollection.deleteOne({ _id: typeof id === 'string' ? new (require('mongodb').ObjectId)(id) : id });
        if (result.deletedCount === 0) {
            throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.VALIDATION, 'Process not found');
        }
        return { success: true };
    }
} 