import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { NotificationType, NotificationPriority, NotificationChannel, NotificationStatus } from '@app/notification';

@Schema({
  timestamps: true,
  collection: 'notification_history',
})
export class NotificationHistory extends Document {
  declare _id: Types.ObjectId;

  @Prop({ required: true })
  notificationId: string; // Unique notification ID

  @Prop({ type: String })
  senderId?: string;

  @Prop({ required: true, type: Types.ObjectId })
  userId: Types.ObjectId;

  @Prop()
  userEmail?: string;

  @Prop({ required: true, type: String, enum: NotificationType })
  type: NotificationType;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  message: string;

  @Prop({ required: true, type: [String], enum: NotificationChannel })
  channels: NotificationChannel[];

  @Prop({ required: true, type: String, enum: NotificationPriority, default: NotificationPriority.MEDIUM })
  priority: NotificationPriority;

  @Prop({ required: true, type: String, enum: NotificationStatus, default: NotificationStatus.PENDING })
  status: NotificationStatus;

  @Prop({ type: Object })
  data?: Record<string, any>;

  @Prop({ type: Object })
  metadata?: Record<string, any>;

  @Prop()
  sentAt?: Date;

  @Prop()
  deliveredAt?: Date;

  @Prop()
  isRouted?: boolean;

  @Prop()
  readAt?: Date;

  @Prop()
  orgId?: string;

  @Prop()
  subOrgId?: string;

  @Prop()
  processId?: string;

  @Prop({ default: false })
  isRead: boolean;

  createdAt: Date;

  updatedAt: Date;
}

export const NotificationHistorySchema = SchemaFactory.createForClass(NotificationHistory);

// Create indexes for better query performance
NotificationHistorySchema.index({ userId: 1, createdAt: -1 });
NotificationHistorySchema.index({ userId: 1, isRead: 1 });
NotificationHistorySchema.index({ notificationId: 1 }, { unique: true });
NotificationHistorySchema.index({ type: 1, userId: 1 });
