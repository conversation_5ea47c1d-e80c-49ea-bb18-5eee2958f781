import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import * as jwt from 'jsonwebtoken';
import { ActionStatusCodeService } from './action-status-code.service';
import { ActionCode, StatusCode, ActionStatusCodeMap } from './entities/action-status-code.entity';
import { CreateActionCodeInput, UpdateActionCodeInput, CreateStatusCodeInput, UpdateStatusCodeInput, CreateActionStatusCodeMapInput, UpdateActionStatusCodeMapInput, FindAllArgs, PaginatedActionCodesResponse, PaginatedStatusCodesResponse, PaginatedActionStatusCodeMapsResponse, FindAllAactions } from './dto/action-status-code.dto';
import { BaseResponse } from './dto/base.response.dto';
import { Success, Error as AppError, HttpStatus, ResponseCode, ErrorType } from '@app/error';
import { GraphQLJSON } from 'graphql-type-json';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { RequirePermission } from '@app/permissions';

@Resolver()
export class ActionStatusCodeResolver {
  constructor(private readonly actionStatusCodeService: ActionStatusCodeService) {}

  private getUserIdFromContext(context: any): string {
    const authHeader = context.req?.headers?.authorization ?? '';
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        const userId = typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
        if (userId) return userId;
      } catch (jwtError) {
        console.log("JWT verification failed:", jwtError);
        
        throw new AppError(HttpStatus.UNAUTHORIZED, ResponseCode.UNAUTHORIZED, ErrorType.AUTHENTICATION, 'Invalid or expired token.');
      }
    }
    throw new AppError(HttpStatus.UNAUTHORIZED, ResponseCode.UNAUTHORIZED, ErrorType.AUTHENTICATION, 'Authorization header missing or invalid.');
  }

  // ===== ActionCode Mutations and Queries =====
  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Add' }
  )
  async createActionCode(@Args('input') input: CreateActionCodeInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      console.log("Received input:", input,userId);
      
      const result = await this.actionStatusCodeService.createActionCode(input, userId);
      return new Success(HttpStatus.CREATED, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Query(() => PaginatedActionCodesResponse, { name: 'actionCodes' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findAllActionCodes(@Args() args: FindAllArgs) {
    try {
      const result = await this.actionStatusCodeService.findAllActionCodes(args);
      return result;
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }
  
  @Query(() => BaseResponse, { name: 'actionCode' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findActionCodeById(@Args('id', { type: () => ID }) id: string) {
    try {
      const result = await this.actionStatusCodeService.findActionCodeById(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }
  
  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Edit' }
  )
  async updateActionCode(@Args('input') input: UpdateActionCodeInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      const result = await this.actionStatusCodeService.updateActionCode(input, userId);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Delete' }
  )
  async deleteActionCode(@Args('id', { type: () => ID }) id: string) {
    try {
      await this.actionStatusCodeService.deleteActionCode(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'Action code deleted successfully' });
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }

  // ===== StatusCode Mutations and Queries =====
  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Add' }
  )
  async createStatusCode(@Args('input') input: CreateStatusCodeInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      const result = await this.actionStatusCodeService.createStatusCode(input, userId);
      return new Success(HttpStatus.CREATED, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Query(() => PaginatedStatusCodesResponse, { name: 'statusCodes' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findAllStatusCodes(@Args() args: FindAllArgs) {
    try {
      const result = await this.actionStatusCodeService.findAllStatusCodes(args);
      return result;
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }

  @Query(() => BaseResponse, { name: 'statusCode' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findStatusCodeById(@Args('id', { type: () => ID }) id: string) {
    try {
      const result = await this.actionStatusCodeService.findStatusCodeById(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }

  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Edit' }
  )
  async updateStatusCode(@Args('input') input: UpdateStatusCodeInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      const result = await this.actionStatusCodeService.updateStatusCode(input, userId);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Delete' }
  )
  async deleteStatusCode(@Args('id', { type: () => ID }) id: string) {
    try {
      await this.actionStatusCodeService.deleteStatusCode(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'Status code deleted successfully' });
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }
  
  // ===== ActionStatusCodeMap Mutations and Queries =====
  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Add' }
  )
  async createActionStatusCodeMap(@Args('input') input: CreateActionStatusCodeMapInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      const result = await this.actionStatusCodeService.createActionStatusCodeMap(input, userId);
      return new Success(HttpStatus.CREATED, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Query(() => PaginatedActionStatusCodeMapsResponse, { name: 'actionStatusCodeMaps' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findAllActionStatusCodeMaps(@Args() args: FindAllAactions) {
    try {
      const result = await this.actionStatusCodeService.findAllActionStatusCodeMaps(args);
      return result;
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        throw new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      throw new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }

  @Query(() => BaseResponse, { name: 'actionStatusCodeMap' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'View' }
  )
  async findActionStatusCodeMapById(@Args('id', { type: () => ID }) id: string) {
    try {
      const result = await this.actionStatusCodeService.findActionStatusCodeMapById(id);
      return {
        message: 'Action status code map retrieved successfully',
        code: HttpStatus.OK,
        type: ErrorType.ACTION_STATUS_CODE,
        data: result
      };
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return {
          message: error.response.message,
          code: error.response.responseCode,
          type: error.response.errorType,
          data: null
        };
      }
      return {
        message: error.message || 'An unexpected error occurred',
        code: ResponseCode.INTERNAL_SERVER_ERROR,
        type: ErrorType.UNKNOWN,
        data: null
      };
    }
  }

  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Edit' }
  )
  async updateActionStatusCodeMap(@Args('input') input: UpdateActionStatusCodeMapInput, @Context() context: any) {
    // try {
      const userId = this.getUserIdFromContext(context);
      const result = await this.actionStatusCodeService.updateActionStatusCodeMap(input, userId);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { data: result });
  }

  @Mutation(() => BaseResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'Masters', subModule: 'Action and Status', permission: 'Delete' }
  )
  async deleteActionStatusCodeMap(@Args('id', { type: () => ID }) id: string) {
    try {
      await this.actionStatusCodeService.deleteActionStatusCodeMap(id);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.ACTION_STATUS_CODE, { message: 'Action status code map deleted successfully' });
    } catch (error: any) {
      if (error.response && error.response.responseCode) {
        return new AppError(error.response.status, error.response.responseCode, error.response.errorType, error.response.message);
      }
      return new AppError(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.UNKNOWN, error.message || 'An unexpected error occurred');
    }
  }
} 