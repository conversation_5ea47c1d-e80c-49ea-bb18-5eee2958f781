import { Resolver, Query, Args, Mutation, Int, ObjectType, Field } from '@nestjs/graphql';
import { ImportService } from '../services/import.service';
import { TaskQueue } from '../entities/task.entity';
import { StartImportInput } from '../dto/import.dto';
import { ExportTaskResponse } from '../dto/export.dto';
import { RequirePermission } from '@app/permissions';
import { TaskExpirationService } from '../services/task-expiration.service';

@ObjectType()
class RollbackResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field(() => Int)
  revertedCount: number;

  @Field(() => Int)
  deletedCount: number;
}

@ObjectType()
class ExpiredTaskCleanupResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;

  @Field(() => Int)
  processedTasks: number;
}

@ObjectType()
class ImportPaginationInfo {
  @Field(() => Int)
  page: number;
  @Field(() => Int)
  limit: number;
  @Field(() => Int)
  total: number;
  @Field(() => Int)
  totalItems: number;
  @Field(() => Int)
  totalPages: number;
  @Field()
  hasNext: boolean;
  @Field()
  hasPrev: boolean;
}

@ObjectType()
class ImportPagination {
  @Field(() => [TaskQueue])
  items: TaskQueue[];
  @Field(() => ImportPaginationInfo)
  pagination: ImportPaginationInfo;
}

@ObjectType()
export class StartImportResponse {
  @Field()
  message: string;

  @Field()
  taskId: string;
}

@Resolver(() => TaskQueue)
export class ImportResolver {
  constructor(
    private readonly importService: ImportService,
    private readonly taskExpirationService: TaskExpirationService
  ) {}

  @Mutation(() => StartImportResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'All' }
  // )
  async startImport(@Args('input') input: StartImportInput): Promise<StartImportResponse> {
    const taskId = await this.importService.createImportTask({
      filePath: input.filePath,
      collectionName: input.collectionName,
      createdBy: input.createdBy,
      orgId: input.orgId,
      fileType: input.fileType,
      type: input.type,
      configurationId: input.templateId, // Map templateId to configurationId for backward compatibility
      isStopOnError: input.isStopOnError, // ✅ FIXED: Pass the isStopOnError parameter
    });
    console.log('Task ID:', taskId);
    
    return { message: 'Import initiated successfully. Check the Download Queue for status.', taskId };
  }

  @Query(() => [String], { nullable: true })
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async importStatusLog(@Args('taskId') taskId: string): Promise<any[] | null> {
    const task = await this.importService.getImportStatus(taskId);
    return task?.statusLog || null;
  }

  @Query(() => ImportPagination)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'View' }
  // )
  async allImports(
    @Args('page', { type: () => Int, nullable: true }) page?: number,
    @Args('limit', { type: () => Int, nullable: true }) limit?: number,
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('filters', { type: () => String, nullable: true }) filters?: string,
    @Args('sortBy', { type: () => String, nullable: true }) sortBy?: string,
    @Args('sortOrder', { type: () => String, nullable: true }) sortOrder?: 'asc' | 'desc',
    @Args('selectedFields', { type: () => String, nullable: true }) selectedFields?: string,
  ): Promise<ImportPagination> {
    let parsedFields: Record<string, number> | undefined = undefined;
    if (selectedFields) {
      try {
        parsedFields = JSON.parse(selectedFields);
      } catch {}
    }
    let parsedFilters: Record<string, any> | undefined = undefined;
    if (filters) {
      try {
        parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : filters;
      } catch {
        parsedFilters = undefined;
      }
    }
    const result = await this.importService.getAllImports({
      page,
      limit,
      search,
      filters: parsedFilters,
      sortBy,
      sortOrder,
      selectedFields: parsedFields,
    });
    return {
      items: result.items,
      pagination: result.pagination,
    };
  }

  @Mutation(() => Boolean)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'Delete' }
  // )
  async deleteImport(@Args('taskId') taskId: string): Promise<boolean> {
    return this.importService.deleteImportTask(taskId);
  }

  @Mutation(() => RollbackResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'Import', permission: 'All' }
  // )
  async rollbackImport(@Args('taskId') taskId: string): Promise<RollbackResponse> {
    const result = await this.importService.rollbackImport(taskId);
    return {
      success: result.success,
      message: result.message,
      revertedCount: result.restoredCount,
      deletedCount: result.deletedCount
    };
  }

  @Mutation(() => ExpiredTaskCleanupResponse)
  // @RequirePermission(
  //   { module: 'Tasks', subModule: 'Cleanup', permission: 'Execute' }
  // )
  async cleanupExpiredTasks(): Promise<ExpiredTaskCleanupResponse> {
    console.log('🧹 Manual cleanup of expired tasks triggered via GraphQL');
    const result = await this.taskExpirationService.runExpiredTaskCleanup();
    return {
      success: result.success,
      message: result.message,
      processedTasks: result.processedTasks || 0
    };
  }
}