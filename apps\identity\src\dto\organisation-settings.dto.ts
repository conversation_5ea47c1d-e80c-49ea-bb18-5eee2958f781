import { InputType, Field, ID } from '@nestjs/graphql';
import { PreferredCommunication } from '../entities/organisation-settings.entity';
import { OrganisationType } from '../entities/organisation-role.entity';

@InputType()
export class CreateOrganisationSettingsInput {
  @Field()
  organisationId: string;

  @Field(() => PreferredCommunication)
  preferredCommunication: PreferredCommunication;

  @Field()
  accessPortal: boolean;

  @Field()
  accessOrganisation: boolean;

  @Field()
  expiryTime: Date;

  @Field(() => OrganisationType, { nullable: true })
  type?: OrganisationType;
}

@InputType()
export class UpdateOrganisationSettingsInput {
  @Field(() => ID)
  id: string;

  @Field(() => PreferredCommunication, { nullable: true })
  preferredCommunication?: PreferredCommunication;

  @Field({ nullable: true })
  accessPortal?: boolean;

  @Field({ nullable: true })
  accessOrganisation?: boolean;

  @Field({ nullable: true })
  expiryTime?: Date;

  @Field(() => OrganisationType, { nullable: true })
  type?: OrganisationType;
}