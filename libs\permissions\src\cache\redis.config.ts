import { ConfigService } from '@nestjs/config';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  maxRetriesPerRequest: number;
  connectTimeout: number;
  commandTimeout: number;
  lazyConnect: boolean;
  retryStrategy?: (times: number) => number | null;
}

export const createRedisConfig = (configService: ConfigService): RedisConfig => {
  return {
    host: configService.get<string>('REDIS_HOST', 'localhost'),
    port: configService.get<number>('REDIS_PORT', 6379),
    password: configService.get<string>('REDIS_PASSWORD'),
    db: configService.get<number>('REDIS_PERMISSION_DB', 1),
    keyPrefix: configService.get<string>('REDIS_KEY_PREFIX', 'asp:'),
    maxRetriesPerRequest: 1,
    connectTimeout: 3000,
    commandTimeout: 2000,
    lazyConnect: true,
    retryStrategy: (times: number) => {
      if (times > 1) {
        console.warn('Redis connection failed, giving up');
        return null;
      }
      return 100; // 100ms delay
    },
  };
};

export const REDIS_CACHE_SETTINGS = {
  TTL: {
    USER_PERMISSION: 3600, // 1 hour
    ROLE: 86400, // 24 hours
    PERMISSION_CHECK: 300, // 5 minutes
  },
  PERFORMANCE: {
    BATCH_SIZE: 100,
    PIPELINE_THRESHOLD: 10,
    MAX_KEYS_PER_SCAN: 1000,
  },
  MONITORING: {
    HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
    STATS_RETENTION_DAYS: 7,
    ALERT_THRESHOLDS: {
      HIT_RATE_MIN: 80, // 80%
      RESPONSE_TIME_MAX: 100, // 100ms
      ERROR_RATE_MAX: 5, // 5%
    },
  },
};
