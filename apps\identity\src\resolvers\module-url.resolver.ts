import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { Injectable, UseGuards } from '@nestjs/common';
import { ModuleUrl } from '../entities/module-url.entity';
import { ModuleUrlService } from '../services/module-url.service';
import {
  CreateModuleUrlInput,
  UpdateModuleUrlInput,
  GetModuleUrlsArgs,
  GetModuleUrlByIdArgs,
  UpdateModuleUrlArgs,
  DeleteModuleUrlArgs,
  ModuleUrlPaginatedResponse
} from '../dto/module-url.dto';
import { RequirePermission } from '@app/permissions';
import { JwtAuthGuard } from '@app/permissions';

@Injectable()
@Resolver(() => ModuleUrl)
@UseGuards(JwtAuthGuard)
export class ModuleUrlResolver {
  constructor(private readonly moduleUrlService: ModuleUrlService) {}

  @Mutation(() => ModuleUrl)
  // @RequirePermission({
  //   module: 'Settings',
  //   subModule: 'URL Management',
  //   permission: 'Create'
  // })
  async createModuleUrl(
    @Args('input') input: CreateModuleUrlInput
  ): Promise<ModuleUrl> {
    console.log('🔗 Creating module URL:', {
      module: input.moduleName,
      collectionName: input.collectionName,
      updatedBy: input.updatedBy
    });

    return this.moduleUrlService.create(input);
  }

  @Query(() => ModuleUrlPaginatedResponse)
  // @RequirePermission({
  //   module: 'Settings',
  //   subModule: 'URL Management',
  //   permission: 'View'
  // })
  async getModuleUrls(@Args() args: GetModuleUrlsArgs): Promise<ModuleUrlPaginatedResponse> {
    console.log('🔍 Getting module URLs with filters:', args);

    return this.moduleUrlService.findAll(args);
  }

  @Query(() => ModuleUrl)
  // @RequirePermission({
  //   module: 'Settings',
  //   subModule: 'URL Management',
  //   permission: 'View'
  // })
  async getModuleUrlById(@Args() args: GetModuleUrlByIdArgs): Promise<ModuleUrl> {
    console.log('🔍 Getting module URL by ID:', args.id);
    
    return this.moduleUrlService.findById(args.id);
  }

  @Mutation(() => ModuleUrl)
  // @RequirePermission({
  //   module: 'Settings',
  //   subModule: 'URL Management',
  //   permission: 'Update'
  // })
  async updateModuleUrl(@Args() args: UpdateModuleUrlArgs): Promise<ModuleUrl> {
    console.log('✏️ Updating module URL:', {
      id: args.id,
      changes: args.input
    });
    
    return this.moduleUrlService.update(args.id, args.input);
  }

  @Mutation(() => Boolean)
  // @RequirePermission({
  //   module: 'Settings',
  //   subModule: 'URL Management',
  //   permission: 'Delete'
  // })
  async deleteModuleUrl(@Args() args: DeleteModuleUrlArgs): Promise<boolean> {
    console.log('🗑️ Deleting module URL:', args.id);
    
    return this.moduleUrlService.delete(args.id);
  }

}
