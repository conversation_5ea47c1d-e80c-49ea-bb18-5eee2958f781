import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import { GoogleAuth } from 'google-auth-library';
import { BaseResponse } from './dto/base.response.dto';
import { Success, Error, HttpStatus, ResponseCode, ErrorType } from '@app/error';


@Injectable()
export class FileUploadService {
  private storage: Storage;
  private bucketName: string;

  constructor() {
    this.bucketName = process.env.GCS_BUCKET_NAME || 'asp-rcm-dev-documents-bucket';
  }

  async generateV4UploadSignedUrl(filename: string, contentType: string, azureToken?: string): Promise<BaseResponse> {
    try {
      // let auth: GoogleAuth;

      // try {
      //   // Use adc.json for Workload Identity Federation
      //   const adcPath = path.join(__dirname, 'adc.json');
      //   const msftTokenPath = path.join(__dirname, 'msft-id-token.txt');

      //   if (!fs.existsSync(adcPath)) {
      //     throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.GENERAL, 'ADC credentials file not found');
      //   }

      //   // If Azure token is provided, write it to msft-id-token.txt
      //   if (azureToken) {
      //     fs.writeFileSync(msftTokenPath, azureToken);
      //   }

      //   // Read and modify the adc.json to use the correct path for msft-id-token.txt
      //   const adcConfig = JSON.parse(fs.readFileSync(adcPath, 'utf8'));
      //   adcConfig.credential_source.file = msftTokenPath;

      //   // Create a temporary modified adc.json
      //   const tempAdcPath = path.join(__dirname, 'temp-adc.json');
      //   fs.writeFileSync(tempAdcPath, JSON.stringify(adcConfig, null, 2));

      //   auth = new GoogleAuth({
      //     keyFile: tempAdcPath,
      //     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      //   });

      //   // Clean up temporary file after auth is created
      //   setTimeout(() => {
      //     try {
      //       fs.unlinkSync(tempAdcPath);
      //     } catch (cleanupError) {
      //       console.log('Failed to cleanup temp adc file:', cleanupError.message);
      //     }
      //   }, 1000);

      // } catch (adcError) {
      //   console.log('ADC authentication failed, trying default authentication:', adcError.message);
      //   // Fallback to default authentication (service account key or metadata server)
      //   auth = new GoogleAuth({
      //     scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      //   });
      // }

      // const client = await auth.getClient();
      this.storage = new Storage();

      const [url] = await this.storage
        .bucket(this.bucketName)
        .file(filename)
        .getSignedUrl({
          version: 'v4',
          action: 'write',
          expires: Date.now() + 15 * 60 * 1000, // 15 minutes
          // contentType,
        });
      console.log('Generated signed URL:', url);


      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.GENERAL, {
        message: 'Signed URL generated successfully',
        uploadUrl: url,
        filename,
      }).toJSON() as unknown as BaseResponse;
    } catch (error) {
      console.error('Error generating signed URL:', error);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.GENERAL, 'Could not generate signed URL');
    }
  }

  async generateV4ViewSignedUrl(filename: string): Promise<BaseResponse> {
    try {
      this.storage = new Storage();
      const [url] = await this.storage
        .bucket(this.bucketName)
        .file(filename)
        .getSignedUrl({
          version: 'v4',
          action: 'read',
          expires: Date.now() + 15 * 60 * 1000, // 15 minutes
        });
      console.log('Generated view signed URL:', url);
      return new Success(HttpStatus.OK, ResponseCode.SUCCESS, ErrorType.GENERAL, {
        message: 'Signed view URL generated successfully',
        viewUrl: url,
        filename,
      }).toJSON() as unknown as BaseResponse;
    } catch (error) {
      console.error('Error generating view signed URL:', error);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.GENERAL, 'Could not generate view signed URL');
    }
  }

  async makeFilePublic(filename: string): Promise<string> {
    this.storage = new Storage();
    const file = this.storage.bucket(this.bucketName).file(filename);
    await file.makePublic();
    return `https://storage.googleapis.com/${this.bucketName}/${filename}`;
  }
}
