import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { PayerService } from './payer.service';
import { Payer } from './entities/payer.entity';
import { FindAllPayersArgs, CreatePayerInput, UpdatePayerInput } from './dto/payer.dto';

describe('PayerService', () => {
  let service: PayerService;
  let payerModel: Model<Payer>;

  // Mock data
  const mockObjectId = new Types.ObjectId();
  const mockPayer = {
    _id: mockObjectId,
    templateId: 'test-template-001',
    values: JSON.stringify({
      sections: [
        {
          fields: [
            { label: 'PayerName', value: 'Test Payer', filter: true },
            { label: 'City', value: 'New York', filter: true }
          ]
        }
      ]
    }),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Mock model methods
  const mockPayerModel = {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    countDocuments: jest.fn(),
    aggregate: jest.fn(),
    exec: jest.fn(),
    save: jest.fn(),
    constructor: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayerService,
        {
          provide: getModelToken('Payor'),
          useValue: mockPayorModel,
        },
      ],
    }).compile();

    service = module.get<PayerService>(PayerService);
    payorModel = module.get<Model<Payer>>(getModelToken('Payer'));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated payors with default parameters', async () => {
      const mockPayors = [mockPayor];
      const mockTotal = 1;

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue(mockPayors),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockTotal),
      });

      const result = await service.findAll({});

      expect(result.payors).toEqual(mockPayors);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        totalItems: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
    });

    it('should handle search functionality', async () => {
      const args: FindAllPayersArgs = {
        page: 1,
        limit: 10,
        search: 'test',
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockPayor]),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.find).toHaveBeenCalledWith({
        $or: [
          { templateId: { $regex: 'test', $options: 'i' } },
          { values: { $regex: 'test', $options: 'i' } }
        ],
      });
    });

    it('should handle ObjectId search', async () => {
      const objectIdString = mockObjectId.toString();
      const args: FindAllPayersArgs = {
        search: objectIdString,
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockPayor]),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.find).toHaveBeenCalledWith({
        $or: expect.arrayContaining([
          { _id: objectIdString },
          { templateId: objectIdString }
        ]),
      });
    });

    it('should handle templateId filter', async () => {
      const args: FindAllPayersArgs = {
        filters: {
          templateId: 'test-template',
        },
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockPayor]),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.find).toHaveBeenCalledWith({
        templateId: { $regex: 'test-template', $options: 'i' },
      });
    });

    it('should handle field filters', async () => {
      const args: FindAllPayersArgs = {
        filters: {
          fieldFilters: [
            { path: 'sections.fields.PayorName', value: 'Test Payor' }
          ],
        },
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockPayor]),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.find).toHaveBeenCalledWith({
        values: {
          $regex: '"PayorName": *"[^"]*Test\\ Payor[^"]*"',
          $options: 'i'
        },
      });
    });

    it('should handle regular field sorting', async () => {
      const args: FindAllPayersArgs = {
        sort: { field: 'templateId', ascending: true },
      };

      const mockSortChain = {
        skip: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            exec: jest.fn().mockResolvedValue([mockPayor]),
          }),
        }),
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue(mockSortChain),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.find().sort).toHaveBeenCalledWith({ templateId: 1 });
    });

    it('should handle values field sorting with aggregation', async () => {
      const args: FindAllPayersArgs = {
        sort: { field: 'values.PayorName', ascending: false },
      };

      mockPayorModel.aggregate.mockReturnValue({
        exec: jest.fn().mockResolvedValue([mockPayor]),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(1),
      });

      await service.findAll(args);

      expect(mockPayorModel.aggregate).toHaveBeenCalledWith([
        { $match: {} },
        {
          $addFields: {
            parsedValues: {
              $cond: {
                if: { $type: "$values" },
                then: { $toObjectId: "$values" },
                else: "$values"
              }
            }
          }
        },
        { $sort: { 'parsedValues.PayorName': -1 } },
        { $skip: 0 },
        { $limit: 10 }
      ]);
    });

    it('should handle errors', async () => {
      mockPayorModel.find.mockImplementation(() => {
        throw new Error('Database error');
      });

      await expect(service.findAll({})).rejects.toThrow(HttpException);
    });

    it('should calculate pagination correctly', async () => {
      const args: FindAllPayersArgs = {
        page: 2,
        limit: 5,
      };

      mockPayorModel.find.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              exec: jest.fn().mockResolvedValue([mockPayor]),
            }),
          }),
        }),
      });
      mockPayorModel.countDocuments.mockReturnValue({
        exec: jest.fn().mockResolvedValue(12),
      });

      const result = await service.findAll(args);

      expect(result.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 12,
        totalItems: 12,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });
    });
  });

  describe('findById', () => {
    it('should return a payor by ID', async () => {
      mockPayorModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockPayor),
      });

      const result = await service.findById(mockObjectId.toString());

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.data?.payor).toEqual(mockPayor);
      expect(mockPayorModel.findById).toHaveBeenCalledWith(mockObjectId.toString());
    });

    it('should throw HttpException when payor not found', async () => {
      mockPayorModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.findById('nonexistent-id')).rejects.toThrow(HttpException);
    });

    it('should throw HttpException for invalid ID format', async () => {
      await expect(service.findById('invalid-id')).rejects.toThrow(HttpException);
    });
  });

  describe('create', () => {
    it('should create a new payor successfully', async () => {
      const createInput: CreatePayerInput = {
        templateId: 'test-template-001',
        values: { PayorName: 'Test Payor', City: 'New York' },
      };

      const mockPayorInstance = {
        ...mockPayor,
        save: jest.fn().mockResolvedValue(mockPayor),
      };

      // Mock the model constructor directly
      const originalPayorModel = (service as any).payorModel;
      (service as any).payorModel = jest.fn().mockImplementation(() => mockPayorInstance);

      const result = await service.create(createInput);

      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Payor created successfully');
      expect(result.data?.payor).toBeDefined();

      // Restore original model
      (service as any).payorModel = originalPayorModel;
    });

    it('should handle errors during payor creation', async () => {
      const createInput: CreatePayerInput = {
        templateId: 'test-template-001',
        values: { PayorName: 'Test Payor' },
      };

      const mockPayorInstance = {
        save: jest.fn().mockRejectedValue(new Error('Database error')),
      };

      // Mock the model constructor directly
      const originalPayorModel = (service as any).payorModel;
      (service as any).payorModel = jest.fn().mockImplementation(() => mockPayorInstance);

      await expect(service.create(createInput)).rejects.toThrow(HttpException);

      // Restore original model
      (service as any).payorModel = originalPayorModel;
    });
  });

  describe('update', () => {
    it('should update a payor successfully', async () => {
      const updateInput: UpdatePayerInput = {
        id: mockObjectId.toString(),
        templateId: 'updated-template',
        values: { PayorName: 'Updated Payor' },
      };

      mockPayorModel.findById.mockResolvedValue(mockPayor);
      mockPayorModel.findByIdAndUpdate.mockResolvedValue({
        ...mockPayor,
        templateId: 'updated-template',
      });

      const result = await service.update(updateInput);

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Payor updated successfully');
      expect(mockPayorModel.findById).toHaveBeenCalledWith(updateInput.id);
    });

    it('should throw error when payor not found for update', async () => {
      const updateInput: UpdatePayerInput = {
        id: 'nonexistent-id',
        templateId: 'updated-template',
      };

      mockPayorModel.findById.mockResolvedValue(null);

      await expect(service.update(updateInput)).rejects.toThrow(HttpException);
    });

    it('should throw error for invalid ID format', async () => {
      const updateInput: UpdatePayerInput = {
        id: 'invalid-id',
        templateId: 'updated-template',
      };

      await expect(service.update(updateInput)).rejects.toThrow(HttpException);
    });
  });

  describe('delete', () => {
    it('should delete a payor successfully', async () => {
      mockPayorModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockPayor),
      });

      const result = await service.delete(mockObjectId.toString());

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Payor deleted successfully');
      expect(mockPayorModel.findByIdAndDelete).toHaveBeenCalledWith(mockObjectId.toString());
    });

    it('should throw error when payor not found for deletion', async () => {
      mockPayorModel.findByIdAndDelete.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.delete('nonexistent-id')).rejects.toThrow(HttpException);
    });

    it('should throw error for invalid ID format', async () => {
      await expect(service.delete('invalid-id')).rejects.toThrow(HttpException);
    });
  });
});
