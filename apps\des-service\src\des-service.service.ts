import { Injectable, HttpException, HttpStatus as NestHttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Template } from './entities/template.entity';
import { CreateTemplateInput, UpdateTemplateInput, FindAllTemplatesArgs, ProviderEmailTicketInput, CreateTemplateVersionInput } from './dto/template.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { MongoConnectionService } from '@app/db';
import isEqual from 'lodash.isequal';
import { NotificationChannel, NotificationPayload, NotificationPriority, NotificationService, NotificationType } from '@app/notification';
import { BaseResponse } from 'apps/provider/src/dto/base.response.dto';
import { EmailService, KafkaService } from '@app/email';
import { ObjectId, UpdateFilter } from 'mongodb';
@Injectable()
export class DesServiceService {
  constructor(
    @InjectModel('Template') private readonly templateModel: Model<Template>,
    private readonly mongoConnectionService: MongoConnectionService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
    private readonly kafkaService: KafkaService
  ) { }




  private async getProviderEmailTicketCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId(
      'email_tickets',
      orgId
    );
  }

  private async getProcessRoleHierarchyCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId(
      'process_role_hierarchies',
    );
  }


  private async getDeviceTokenForUser(userId: string): Promise<string | null> {
    try {
      const collection = await this.mongoConnectionService.getCollectionByOrgId(
        'user_notification_tokens',
      );

      const tokenRecord = await collection.findOne({ userId });

      if (!tokenRecord || !tokenRecord.token) {
        return null;
      }

      return tokenRecord.token;
    } catch (err) {
      console.error(`Error retrieving device token for user ${userId}:`, err);
      return null;
    }
  }


  private async getOrganisationUsers(orgId?: string): Promise<string[]> {
    console.log('🔍 Fetching organisation users with role Manager or Supervisor via hierarchy...');

    if (!orgId) {
      console.warn('⚠️ orgId is required.');
      return [];
    }

    // Step 1: Get user_process_assignments collection
    const assignmentCollection = await this.mongoConnectionService.getCollectionByOrgId('user_process_assignments');
    const assignment = await assignmentCollection.findOne({ orgId });

    if (!assignment?.subOrganisationId) {
      console.warn('⚠️ No subOrganisationId found in user_process_assignments.');
      return [];
    }

    const subOrgId = assignment.subOrganisationId;

    // Step 2: Get processrolehierarchies collection
    const hierarchyCollection = await this.mongoConnectionService.getCollectionByOrgId('processrolehierarchies');
    const hierarchies = await hierarchyCollection.find({ subOrgId }).toArray();

    const userIdSet = new Set<string>();

    for (const hierarchy of hierarchies) {
      if (!Array.isArray(hierarchy.process)) continue;

      for (const process of hierarchy.process) {
        const ops = process?.roles?.operations;
        if (!ops?.manager_to_supervisors) continue;

        // Add manager IDs (keys)
        for (const managerId of Object.keys(ops.manager_to_supervisors)) {
          userIdSet.add(managerId);
        }

        // Add supervisor IDs (values)
        for (const supervisors of Object.values(ops.manager_to_supervisors)) {
          if (Array.isArray(supervisors)) {
            for (const supervisorId of supervisors) {
              if (typeof supervisorId === 'string') {
                userIdSet.add(supervisorId);
              }
            }
          }
        }
      }
    }

    const userIds = Array.from(userIdSet);
    console.log(`✅ Found ${userIds.length} users: ${userIds.join(', ')}`);

    return userIds;
  }


  private async getManagersAndSupervisors(subOrgId: string, processId: string): Promise<string[]> {
    try {
      const hierarchyCollection = await this.getProcessRoleHierarchyCollection();

      const hierarchy = await hierarchyCollection.findOne({
        subOrgId,
        "process.processId": processId
      });

      if (!hierarchy || !hierarchy.process || !Array.isArray(hierarchy.process)) {
        console.log('No hierarchy found for orgId:', 'subOrgId:', subOrgId, 'processId:', processId);
        return [];
      }

      const processObj = hierarchy.process.find((p: any) =>
        p.processId.toString() === processId.toString()
      );

      if (!processObj || !processObj.roles) {
        console.log('No process roles found for processId:', processId);
        return [];
      }

      const userIds = new Set<string>();

      // Get operations managers and supervisors
      const operations = processObj.roles.operations || {};
      const managerToSupervisors = operations.manager_to_supervisors || {};
      const supervisorToAgents = operations.supervisor_to_agents || {};

      // Add all managers
      Object.keys(managerToSupervisors).forEach(managerId => {
        userIds.add(managerId);
      });

      // Add all supervisors
      Object.keys(supervisorToAgents).forEach(supervisorId => {
        userIds.add(supervisorId);
      });

      const result = Array.from(userIds);
      console.log(`Found ${result.length} managers/supervisors for notification:`, result);
      return result;
    } catch (error) {
      console.error('Error getting managers and supervisors:', error);
      return [];
    }
  }

  private async getUserEmail(userId: string): Promise<string | undefined> {
    const userCollection = await this.mongoConnectionService.getCollectionByOrgId('users');


    const user = await userCollection.findOne({ _id: new ObjectId(userId) });
    return user?.email;
  }


  /**
   * Creates a new template
   * @param input Template creation data
   * @returns Success response with the created template
   */
  async createTemplate(input: CreateTemplateInput, userId: string) {
    try {
      let templateData: any = {
        ...input,
        isActive: true,
        fields: null,
        version: 0,
      };
      templateData.docType = 'Custom'; // Default docType for new templates
      // Handle useTemplate logic
      if (input.useTemplate && input.type) {
        const defaultTemplate = await this.templateModel.findOne({
          type: input.type,
          docType: "Default"
        });

        if (defaultTemplate) {
          console.log('Found default template:', defaultTemplate._id);
          // Copy view_summary and fields from default template
          templateData.view_summary = defaultTemplate.view_summary || {};
          templateData.version = 1;
          templateData.isActive = true;
          templateData.organisationId = input.organisationId;
          templateData.subOrganisationId = input.subOrganisationId;
          templateData.fields = defaultTemplate.fields || [];
          console.log('Copied view_summary and fields from default template');
        } else {
          throw new HttpException(
            `Default template not found`,
            NestHttpStatus.INTERNAL_SERVER_ERROR
          );
        }
      }

      const newTemplate = new this.templateModel(templateData);
      await newTemplate.save();

      // Create import configuration if template has isImport fields and userId is provided
      if (userId && input.view_summary?.isImport && input.view_summary.isImport.length > 0) {
        try {
          await this.createImportConfigurationForTemplate(newTemplate._id.toString(), userId, input, newTemplate.key);
        } catch (configError) {
          console.log('Failed to create import configuration:', configError);
          // Don't fail template creation if import config creation fails
        }
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { template: newTemplate },
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to create template: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Get import configurations collection
   */
  private async getImportConfigurationsCollection() {
    return this.mongoConnectionService.getCollectionByOrgId('import_configurations');
  }

  /**
   * Create import configuration for template
   */
  private async createImportConfigurationForTemplate(
    templateId: string,
    userId: string,
    templateInput: CreateTemplateInput,
    key: string
  ) {
    try {
      if (!templateInput.view_summary?.isImport || templateInput.view_summary.isImport.length === 0) {
        console.log('No isImport fields found in template, skipping import configuration creation');
        return;
      }

      const isImportFields = templateInput.view_summary.isImport;

      // Create default mapping JSON where each field maps to itself
      const defaultMappingJson: Record<string, string> = {};
      isImportFields.forEach((field: string) => {
        defaultMappingJson[field] = field;
      });

      const importConfigCollection = await this.getImportConfigurationsCollection();

      // Check if configuration already exists for this templateId and userId
      const existingConfig = await importConfigCollection.findOne({
        templateId
      });

      if (existingConfig) {
        console.log(`Import configuration already exists for templateId: ${templateId}`);
        return;
      }

      // Create new import configuration
      const importConfigData = {
        updatedBy: new Types.ObjectId(userId),
        organisationId: templateInput.organisationId,
        type: templateInput.type,
        templateId,
        collectionName: key, // You can customize this based on template type
        mappingJson: defaultMappingJson,
        templateFields: (templateInput as any).fields || [], // Store complete template structure
        requiredFields: [],
        uniqueFields: [],
        isActive: true,
        // orgId: templateInput.organisationId|| null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await importConfigCollection.insertOne(importConfigData);
      console.log(`Successfully created import configuration for templateId: ${templateId}`);

    } catch (error) {
      console.error('Error creating import configuration:', error);
      throw error;
    }
  }

  /**
   * Handle import configuration migration when template is updated
   */
  private async handleImportConfigurationMigration(
    oldTemplateId: string,
    newTemplateId: string,
    newTemplate: any,
    userId: string
  ) {
    try {
      const importConfigCollection = await this.getImportConfigurationsCollection();

      // Check if import configuration exists for old templateId
      const existingConfig = await importConfigCollection.findOne({
        templateId: oldTemplateId
      });

      if (!existingConfig) {
        console.log(`No existing import configuration found for old templateId: ${oldTemplateId}`);
        return;
      }

      console.log(`Found existing import configuration for old templateId: ${oldTemplateId}`);

      // Extract fields from new template
      const newFields = this.extractImportFieldsFromTemplate(newTemplate);

      // Compare and update mapping JSON
      const updatedMappingJson = this.compareAndUpdateMappingJson(
        existingConfig.mappingJson || {},
        newFields
      );

      // Create new import configuration for new template
      const newImportConfigData = {
        userId: new Types.ObjectId(userId),
        templateId: newTemplateId,
        collectionName: newTemplate.key || existingConfig.collectionName,
        mappingJson: updatedMappingJson,
        values: newTemplate.fields || [], // Store complete template structure
        requiredFields: existingConfig.requiredFields || [],
        uniqueFields: existingConfig.uniqueFields || [],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await importConfigCollection.insertOne(newImportConfigData);
      console.log(`Import configuration migrated from ${oldTemplateId} to ${newTemplateId}`);
      console.log('Updated mapping JSON:', JSON.stringify(updatedMappingJson, null, 2));

    } catch (error) {
      console.error('Error handling import configuration migration:', error);
      // Don't throw error to avoid failing template update
    }
  }

  /**
   * Extract import fields from template structure
   */
  private extractImportFieldsFromTemplate(template: any): string[] {
    const importFields: string[] = [];

    // Handle new template structure with fields array
    if (template.fields && Array.isArray(template.fields)) {
      template.fields.forEach((step: any) => {
        if (step.sections && Array.isArray(step.sections)) {
          step.sections.forEach((section: any) => {
            if (section.fields && Array.isArray(section.fields)) {
              section.fields.forEach((field: any) => {
                if (field.is_import === true && field.label) {
                  importFields.push(field.label);
                }
              });
            }
          });
        }
      });
    }

    // Handle old template structure with view_summary.isImport
    if (template.view_summary?.isImport && Array.isArray(template.view_summary.isImport)) {
      importFields.push(...template.view_summary.isImport);
    }

    return [...new Set(importFields)]; // Remove duplicates
  }

  /**
   * Compare old and new fields and update mapping JSON
   */
  private compareAndUpdateMappingJson(
    oldMappingJson: Record<string, string>,
    newFields: string[]
  ): Record<string, string> {
    const updatedMapping: Record<string, string> = {};

    // Process existing mappings
    Object.entries(oldMappingJson).forEach(([key, value]) => {
      // Only keep mappings for fields that still exist in new template
      if (newFields.includes(key)) {
        updatedMapping[key] = value;
      } else {
        console.log(`Removed field from mapping: ${key} (field no longer exists in new template)`);
      }
    });

    // Add new fields with default mapping (field name maps to itself)
    newFields.forEach(field => {
      if (!updatedMapping.hasOwnProperty(field)) {
        updatedMapping[field] = field;
        console.log(`Added new field to mapping: ${field} -> ${field}`);
      }
    });

    return updatedMapping;
  }

  /**
   * Retrieves all templates with dynamic search, filter, and sort
   * @param args Query arguments including pagination, search, filters, sort, and selectedFields
   * @returns Success response with templates and pagination info
   */
  async findAllTemplates(args: FindAllTemplatesArgs, orgId: string, subOrgId: string) {
    try {
      const { page = 1, limit = 10, search, filters, sort, selectedFields } = args;
      const skip = (page - 1) * limit;

      // Build dynamic query
      let query: any = {};
      if (filters?.type) {
        if (filters?.type !== "Master") {
          // query.organisationId = orgId;
          query.subOrganisationId = subOrgId;
        }
      }
      // Search across multiple string fields
      if (search) {
        const searchFields = ['name', 'description'];
        query.$or = searchFields.map(field => ({
          [field]: { $regex: search, $options: 'i' }
        }));
      }

      // Filters
      if (filters) {
        // Handle formType filter - use partial match with regex
        if (filters.formType !== null && filters.formType !== undefined) {
          query.type = { $regex: filters.formType, $options: 'i' };
        }

        Object.keys(filters).forEach(key => {
          const value = filters[key];
          if (value !== null && value !== undefined) {
            // Skip formType as it's already handled above
            if (key === 'formType') return;

            const stringFields = ['name', 'description', 'createdBy', 'updatedBy'];
            const booleanFields = ['status', 'isActive'];
            const numberFields = ['version'];
            const dateFields = ['createdAt', 'updatedAt'];

            if (stringFields.includes(key) && typeof value === 'string') {
              query[key] = { $regex: value, $options: 'i' };
            } else if (booleanFields.includes(key)) {
              query[key] = typeof value === 'string'
                ? ['true', '1'].includes(value.toLowerCase())
                : Boolean(value);
            } else if (numberFields.includes(key)) {
              const num = Number(value);
              if (!isNaN(num)) query[key] = num;
            } else if (dateFields.includes(key) && typeof value === 'string') {
              // Handle date filtering with partial matching
              const dateValue = value.trim();

              // If it's a full date (YYYY-MM-DD format), create a date range for that day
              if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
                const startDate = new Date(dateValue);
                const endDate = new Date(dateValue);
                endDate.setDate(endDate.getDate() + 1);

                query[key] = {
                  $gte: startDate,
                  $lt: endDate
                };
              }
              // If it's a partial date (like just year or year-month), create a range
              else if (/^\d{4}$/.test(dateValue)) {
                // Just year provided
                const startDate = new Date(`${dateValue}-01-01`);
                const endDate = new Date(`${dateValue}-12-31T23:59:59.999Z`);

                query[key] = {
                  $gte: startDate,
                  $lte: endDate
                };
              } else if (/^\d{4}-\d{2}$/.test(dateValue)) {
                // Year-month provided
                const startDate = new Date(`${dateValue}-01`);
                const endDate = new Date(`${dateValue}-31T23:59:59.999Z`);

                query[key] = {
                  $gte: startDate,
                  $lte: endDate
                };
              } else {
                // For any other string, try to match as a date string
                try {
                  const parsedDate = new Date(dateValue);
                  if (!isNaN(parsedDate.getTime())) {
                    query[key] = parsedDate;
                  }
                } catch (e) {
                  // If date parsing fails, ignore this filter
                  console.warn(`Invalid date format for ${key}: ${dateValue}`);
                }
              }
            } else {
              query[key] = value;
            }
          }
        });
      }

      // Extract sortBy and sortOrder if present (even if not in DTO)
      const sortBy = (args as any).sortBy;
      const sortOrder = (args as any).sortOrder;

      // Sorting
      let sortQuery: any = { createdAt: -1 };
      if (sortBy) {
        sortQuery = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };
      } else if (sort?.field) {
        sortQuery = { [sort.field]: sort.ascending ? 1 : -1 };
      }

      // Selected fields (projection)
      let projection: any = undefined;
      if (selectedFields?.length) {
        const validFields = Object.keys(this.templateModel.schema.paths);
        projection = selectedFields.reduce((acc, field) => {
          if (validFields.includes(field)) {
            acc[field] = 1;
          }
          return acc;
        }, {});
      }
      console.log("query", query);
      const [templates, total] = await Promise.all([
        this.templateModel.find(query)
          .select(projection)
          .sort(sortQuery)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.templateModel.countDocuments(query).exec()
      ]);

      const totalPages = Math.ceil(total / limit);

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        {
          templates,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        },
      );
    } catch (error) {
      throw new HttpException(
        `Failed to fetch templates: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  /**
   * Retrieves a template by its ID with previous version information
   * @param id The template's ID
   * @param selectedFields Optional array of fields to project from database
   * @returns Success response with the template and previous version info
   */
  async findTemplateById(id: string, selectedFields?: string[]) {
    try {
      // Build projection object for field selection
      let projection: any = {};
      if (selectedFields && selectedFields.length > 0) {
        selectedFields.forEach(field => {
          projection[field] = 1;
        });
      }

      const queryBuilder = this.templateModel.findById(id);

      // Apply field projection if specified
      if (Object.keys(projection).length > 0) {
        queryBuilder.select(projection);
      }

      const template = await queryBuilder.exec();
      if (!template) {
        throw new HttpException(
          `Template with ID ${id} not found`,
          NestHttpStatus.NOT_FOUND
        );
      }

      // Find version history if current version > 1
      let versionHistory: any[] = [];
      if (template.version > 1) {
        versionHistory = await this.templateModel.find({
          // name: template.name,
          docType: template.docType,
          key: template.key,
          type: template.type,
          // version: template.version - 1,
          // isActive: false
        }).select('_id version name createdAt').sort({ version: -1 }).exec();
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        {
          template,
          versionHistory: versionHistory.map(version => ({
            id: version._id,
            version: version.version,
            name: version.name,
            createdAt: version.createdAt
          }))
        },
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Error finding template: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Gets templates by docType, key, and type, returning only id and version
   * @param docType The document type
   * @param key The template key
   * @param type The template type
   * @returns Success response with templates containing only id and version
   */
  async getTemplatesByDocTypeKeyType(docType: string, key: string, type: string) {
    try {
      const templates = await this.templateModel.find({
        docType,
        key,
        type
      }).select('_id version name isActive').sort({ version: -1 }).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { templates },
      );
    } catch (error) {
      throw new HttpException(
        `Failed to fetch templates: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Updates a template
   * @param input Template update data
   * @returns Success response with the updated template
   */


  async updateTemplate(input: UpdateTemplateInput, userId: string) {
    try {
      const template = await this.templateModel.findById(input.id);
      if (!template) {
        throw new HttpException('Template not found', NestHttpStatus.NOT_FOUND);
      }

      const updateData: any = { ...input };
      delete updateData.id;

      const normalizeFields = (fields: any[]): any[] => {
        return fields.map(step => ({
          ...step,
          sections: step.sections.map(section => ({
            ...section,
            fields: section.fields.filter((field, index, self) =>
              index === self.findIndex(f => f.id === field.id)
            )
          }))
        }));
      };

      const hasFieldStructureChanged = (): boolean => {
        if (!input.fields) return false;
        try {
          const oldFieldsRaw = template.fields;
          const newFieldsRaw = input.fields;

          const oldFields = typeof oldFieldsRaw === 'string' ? JSON.parse(oldFieldsRaw) : oldFieldsRaw;
          const newFields = typeof newFieldsRaw === 'string' ? JSON.parse(newFieldsRaw) : newFieldsRaw;

          const oldNormalized = normalizeFields(oldFields);
          const newNormalized = normalizeFields(newFields);

          return !isEqual(oldNormalized, newNormalized);
        } catch (e) {
          console.error('Field comparison error:', e);
          return true;
        }
      };

      const hasViewSummaryChanged = (): boolean => {
        if (!input.view_summary) return false;

        const oldVS = template.view_summary || {};
        const newVS = input.view_summary;

        return !isEqual(oldVS, newVS);
      };



      const shouldCreateNewVersion = hasFieldStructureChanged() || hasViewSummaryChanged();

      let updatedTemplate;
      console.log("shouldCreateNewVersion:", updateData, template);

      if (shouldCreateNewVersion) {
        await this.templateModel.findByIdAndUpdate(input.id, { isActive: false });

        const newTemplateData = {
          ...template.toObject(),
          ...updateData,
          _id: undefined,
          name: template.name,
          docType: template.docType,

          version: template.version + 1,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        updatedTemplate = await new this.templateModel(newTemplateData).save();
      } else {
        updateData.updatedAt = new Date();
        updatedTemplate = await this.templateModel.findByIdAndUpdate(
          input.id,
          updateData,
          { isActive: true, new: true }
        );
      }

      // After update, handle import configuration migration
      if (userId && shouldCreateNewVersion) {
        await this.handleImportConfigurationMigration(
          template._id.toString(), // old templateId
          updatedTemplate._id.toString(), // new templateId
          updatedTemplate, // new template
          userId
        );
      }

      return new Success(
        shouldCreateNewVersion ? HttpStatus.CREATED : HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { template: updatedTemplate }
      );
    } catch (error) {
      if (error instanceof HttpException) throw error;

      throw new HttpException(
        `Failed to update template: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }



  /**
   * Soft deletes a template (sets isActive to false)
   * @param id The template's ID
   * @returns Success response
   */
  async deleteTemplate(id: string) {
    try {
      const template = await this.templateModel.findById(id);
      if (!template) {
        throw new HttpException(
          'Template not found',
          NestHttpStatus.NOT_FOUND
        );
      }

      await this.templateModel.findByIdAndDelete(id);

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { message: 'Template deleted successfully' },
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to delete template: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Gets count of templates with optional filters
   * @param filters Optional filters to apply
   * @returns Success response with count
   */
  async getTemplateCount(filters?: Record<string, any>) {
    try {
      let query: any = { isActive: true };

      if (filters) {
        Object.keys(filters).forEach(key => {
          if (filters[key] !== null && filters[key] !== undefined) {
            query[key] = filters[key];
          }
        });
      }

      const count = await this.templateModel.countDocuments(query).exec();

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { count },
      );
    } catch (error) {
      throw new HttpException(
        `Failed to get template count: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Clones a template by its ID, creating a new template with name as 'name-clone'.
   * @param id The template's ID to clone
   * @param organisationId New organisationId for the cloned template (from user)
   * @param subOrganisationId New subOrganisationId for the cloned template (from user)
   * @returns Success response with the cloned template
   */
  async cloneTemplate(id: string, organisationId: string, subOrganisationId: string, userId: string, name: string) {
    try {
      const template = await this.templateModel.findById(id);
      if (!template) {
        throw new HttpException(
          `Template with ID ${id} not found`,
          NestHttpStatus.NOT_FOUND
        );
      }

      // Check for existing template with same subOrganisationId and key
      const existingTemplate = await this.templateModel.findOne({
        subOrganisationId,
        key: template.key,
        isActive: true
      });

      // Set version based on existing template
      const newVersion = existingTemplate ? existingTemplate.version + 1 : 1;

      // If existing template found, set it to inactive
      if (existingTemplate) {
        await this.templateModel.findByIdAndUpdate(existingTemplate._id, { isActive: false });
      }

      const timestamp = Math.floor(Date.now() / 1000);
      const clonedData = {
        ...template.toObject(),
        _id: undefined,
        name: name,//`${template.name}-${timestamp}`,
        version: newVersion,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        docType: 'Custom', // Default docType for cloned templates
        organisationId,
        subOrganisationId,
      };
      const clonedTemplate = await new this.templateModel(clonedData).save();

      // Create import configuration if needed
      if (
        userId &&
        clonedTemplate.view_summary &&
        Array.isArray((clonedTemplate.view_summary as any).isImport) &&
        (clonedTemplate.view_summary as any).isImport.length > 0
      ) {
        try {
          await this.createImportConfigurationForTemplate(
            clonedTemplate._id.toString(),
            userId,
            clonedTemplate,
            clonedTemplate.key
          );
        } catch (configError) {
          console.log('Failed to create import configuration for cloned template:', configError);
          // Don't fail clone if import config creation fails
        }
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        { template: clonedTemplate },
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to clone template: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Create a new version of an existing template
   * Updates current template to isActive: false and creates new template with viewSummary and fields from old version
   * @param input Create template version input data (currentId, oldVersionId, name, description)
   * @param userId User ID for audit trail
   * @returns Success response with the new template version
   */
  async createTemplateVersion(input: CreateTemplateVersionInput, userId: string) {
    try {
      // Get the current template to deactivate
      const currentTemplate = await this.templateModel.findById(input.currentId);
      if (!currentTemplate) {
        throw new HttpException(
          `Current template with ID ${input.currentId} not found`,
          NestHttpStatus.NOT_FOUND
        );
      }

      // Get the old version template to copy from
      const oldVersionTemplate = await this.templateModel.findById(input.oldVersionId);
      if (!oldVersionTemplate) {
        throw new HttpException(
          `Old version template with ID ${input.oldVersionId} not found`,
          NestHttpStatus.NOT_FOUND
        );
      }

      // Update current template to isActive: false
      await this.templateModel.findByIdAndUpdate(
        input.currentId,
        {
          isActive: false,
          updatedAt: new Date()
        }
      );

      // Create new template with data from old version
      const newTemplateData = {
        name: input.name || currentTemplate.name,
        description: input.description || currentTemplate.description,
        type: currentTemplate.type,
        organisationId: currentTemplate.organisationId,
        subOrganisationId: currentTemplate.subOrganisationId,
        view_summary: oldVersionTemplate.view_summary,
        fields: oldVersionTemplate.fields,
        docType: currentTemplate.docType,
        key: currentTemplate.key,
        version: (currentTemplate.version || 0) + 1, // Increment version number
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const newTemplate = await new this.templateModel(newTemplateData).save();

      // Create import configuration if needed
      if (
        userId &&
        newTemplate.view_summary &&
        Array.isArray((newTemplate.view_summary as any).isImport) &&
        (newTemplate.view_summary as any).isImport.length > 0
      ) {
        try {
          await this.createImportConfigurationForTemplate(
            newTemplate._id.toString(),
            userId,
            newTemplate,
            newTemplate.key
          );
        } catch (configError) {
          console.log('Failed to create import configuration for new template version:', configError);
          // Don't fail version creation if import config creation fails
        }
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.FORM,
        {
          template: newTemplate,
          message: `New template version created successfully. Previous version (${input.currentId}) has been deactivated.`
        },
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to create template version: ${error.message}`,
        NestHttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async n8bncreate(
    input: ProviderEmailTicketInput,
    userId?: string,
  ): Promise<BaseResponse> {
    try {
      const orgId = input.orgId;
      const subOrgId = input.subOrgId;
      const processId = input.processId;
      const userCollection = await this.getProviderEmailTicketCollection(orgId);

      if (!input.tickectId) {
        throw new HttpException('Ticket IDs are required', NestHttpStatus.BAD_REQUEST);
      }

      for (const ticketId of input.tickectId) {
        const ticketObjectId = new ObjectId(ticketId);
        const ticketDoc = await userCollection.findOne({ _id: ticketObjectId });

        if (!ticketDoc) {
          console.warn(`⚠️ Ticket with ID ${ticketId} not found.`);
          continue;
        }

        const insertedId = ticketDoc._id;

        let managers: string[] = [];
      
          managers = await this.getOrganisationUsers(orgId);
        

        await Promise.all(
          managers.map(async (managerId) => {
            try {
              const [deviceTokens, email] = await Promise.all([
                this.getDeviceTokenForUser(managerId),
                this.getUserEmail(managerId),
              ]);

              const payload: NotificationPayload = {
                userId: managerId,
                senderId: userId || '',
                type: NotificationType.SOURCE,
                title: 'New Ticket Assigned',
                message: `A new ticket "${ticketDoc.subject || 'Untitled'}" has been created and requires attention`,
                priority: NotificationPriority.HIGH,
                channels: [NotificationChannel.IN_APP],
                isRouted: true,
                data: {
                  ticketId: ticketId,
                  subject: ticketDoc.subject || 'Untitled',
                  ticket_type: ticketDoc.type || '',
                  ticketType: ticketDoc.ticket_type || '',
                  status: 'pending',
                  createdBy: userId,
                  role: 'manager_supervisor',
                  notificationId: new ObjectId().toString(),
                  deviceTokens: Array.isArray(deviceTokens) ? deviceTokens : [deviceTokens],
                },
                metadata: {
                  ticketId: ticketId,
                  ticketType: ticketDoc.ticket_type || '',
                  subject: ticketDoc.subject || 'Untitled',
                  orgId: orgId || '',
                  subOrgId: subOrgId || '',
                  processId: ticketDoc.ticket_process || processId?.toString() || '',
                },
              };

              await this.kafkaService.publishToKafka(payload, 'notification');

              if (email) {
                const html = `
              <html>
                <head>
                  <style>
                    body { font-family: Arial, sans-serif; background-color: #f4f6f9; padding: 20px; color: #333; }
                    .container { background: #fff; padding: 20px; border-radius: 8px; max-width: 600px; margin: auto; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); }
                    .btn { margin-top: 20px; display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; }
                    .footer { font-size: 12px; color: #888; text-align: center; margin-top: 30px; }
                  </style>
                </head>
                <body>
                  <div class="container">
                    <h2>🩺 New Health Ticket Created</h2>
                    <p>Dear <strong>Manager</strong>,</p>
                    <p>A new healthcare support ticket has been created:</p>
                    <ul>
                      <li><strong>Subject:</strong> ${ticketDoc.subject || 'Untitled'}</li>
                      <li><strong>Type:</strong> ${ticketDoc.ticket_type || 'Ticket'}</li>
                      <li><strong>Priority:</strong> ${ticketDoc.priority || 'Normal'}</li>
                      <li><strong>Status:</strong> Pending</li>
                    </ul>
                    <p>Please log in to the portal to review and take action.</p>
                    <a href="https://devrcmgenie.asprcmsolutions.com" class="btn">Open Ticket</a>
                    <div class="footer">This is an automated message from the Healthcare Support System.</div>
                  </div>
                </body>
              </html>
            `;
                await this.emailService.sendEmail(email, html, '🩺 New Ticket Created');
              }
            } catch (err) {
              console.error(`❌ Failed to notify manager ${managerId}:`, err);
            }
          })
        );
      }

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { message: 'Provider email tickets processed successfully' }
      );
    } catch (error: any) {
      console.error('❌ Failed to create provider email ticket:', error);
      throw new HttpException(
        `Failed to create provider email ticket: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
