export function empty(value: any): boolean {
  // Checks Array
  if (Array.isArray(value)) {
    return value.length === 0;
  }

  // Checks Object
  if (value && typeof value === 'object') {
    if (Object?.keys(value).length === 0) return true;

    // If object has keys, then check whether the keys are actual object keys or prototypes
    for (const key in value) {
      if (value.hasOwnProperty(key)) return false;
    }
    return true;
  }
  return !value;
}
