# Permissions System with Redis Caching

This library provides a comprehensive permissions system with Redis-based caching for high-performance user permission checks.

## Features

- **Redis-based caching** for user permissions
- **User-specific cache invalidation**
- **Cache warming** for frequently accessed users
- **Performance monitoring** and statistics
- **Graceful fallback** when Red<PERSON> is unavailable
- **Batch operations** for improved performance

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ PermissionsGuard│───▶│ CacheManagerService│───▶│ RedisCacheService│
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       ▼
         │                       │              ┌─────────────────┐
         │                       │              │     Redis       │
         │                       │              │   (Cache Store) │
         │                       │              └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│   Database      │    │   Monitoring     │
│  (Fallback)     │    │   & Statistics   │
└─────────────────┘    └──────────────────┘
```

## Setup

### 1. Environment Variables

Copy the example environment file and configure Redis settings:

```bash
cp .env.redis.example .env
```

Required environment variables:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password_here
REDIS_PERMISSION_DB=1
REDIS_KEY_PREFIX=asp:
CACHE_WARMUP_ENABLED=true
```

### 2. Docker Setup

The system includes a Redis container in docker-compose.yml:

```bash
docker-compose up -d redis
```

### 3. Module Integration

The permissions system is automatically integrated into your NestJS application through the PermissionsGuard.

## Cache Key Structure

The system uses structured cache keys for efficient organization:

- **User Permissions**: `asp:perm:user:{userId}:{orgId}:{subOrgId}:{processId}`
- **User Index**: `asp:perm:user_index:{userId}` (for invalidation)
- **Role Cache**: `asp:perm:role:system:{roleId}`

## Performance Features

### Cache Warming
- Automatically warms cache for frequently accessed users
- Configurable batch size and intervals
- Reduces cold cache misses

### Batch Operations
- Pipeline Redis operations for better performance
- Batch cache invalidation
- Reduced network round trips

### Monitoring
- Real-time cache hit/miss statistics
- Performance metrics tracking
- Health check endpoints
- Automatic alerting for low performance

## Usage Examples

### Basic Permission Check
```typescript
// The PermissionsGuard automatically handles caching
@UseGuards(PermissionsGuard)
@RequirePermission([
  { module: 'Tickets', subModule: 'All Ticket', permission: 'View' }
])
async getTickets() {
  // Your method implementation
}
```

### Manual Cache Operations
```typescript
// Invalidate user cache when permissions change
await cacheManager.invalidateUserCache(userId);

// Get cache statistics
const stats = cacheManager.getCacheStats();
console.log(`Hit rate: ${stats.hitRate}%`);

// Check cache health
const health = await cacheManager.getCacheHealth();
```

## Cache TTL Settings

Default TTL values:
- **User Permissions**: 1 hour (3600 seconds)
- **Roles**: 24 hours (86400 seconds)
- **Permission Checks**: 5 minutes (300 seconds)

## Monitoring and Debugging

### Cache Statistics
The system provides detailed statistics:
- Hit/miss ratios
- Average response times
- Total requests
- Cache health status

### Debug Logging
Enable detailed logging by setting log level to debug:
```typescript
// Detailed cache operations are logged
[PermissionsGuard] ✅ Cache hit in 15ms for user: 12345
[PermissionsGuard] 👤 Cached role: Supervisor (Operation)
[PermissionsGuard] ✅ Permission granted from cache
```

### Health Checks
Monitor cache health:
```bash
# Check Redis connectivity
redis-cli ping

# Monitor cache statistics
# Access through your application's health endpoint
```

## Troubleshooting

### Common Issues

1. **Cache Miss Every Time**
   - Check Redis connectivity
   - Verify environment variables
   - Ensure proper context parameters (orgId, subOrgId, processId)

2. **High Memory Usage**
   - Adjust TTL values
   - Implement cache size limits
   - Monitor key expiration

3. **Slow Performance**
   - Check Redis server performance
   - Verify network connectivity
   - Consider Redis clustering for high load

### Debug Commands
```bash
# Check Redis keys
redis-cli keys "asp:perm:*"

# Monitor Redis operations
redis-cli monitor

# Check memory usage
redis-cli info memory
```

## Best Practices

1. **Cache Invalidation**: Always invalidate cache when user permissions change
2. **Error Handling**: The system gracefully falls back to database queries
3. **Monitoring**: Regularly monitor cache hit rates and performance
4. **Security**: Use Redis AUTH and secure network connections
5. **Scaling**: Consider Redis Cluster for high-availability setups

## Configuration Options

Customize cache behavior through environment variables:
- `CACHE_DEFAULT_TTL`: Default cache TTL in seconds
- `CACHE_BATCH_SIZE`: Batch operation size
- `CACHE_WARMUP_ENABLED`: Enable/disable cache warming
- `CACHE_MONITORING_ENABLED`: Enable/disable monitoring

## Performance Metrics

Expected performance improvements with Redis caching:
- **Cache Hit**: ~5-15ms response time
- **Cache Miss**: ~50-200ms (database query)
- **Hit Rate Target**: >80%
- **Memory Usage**: ~1-5MB per 1000 cached users
