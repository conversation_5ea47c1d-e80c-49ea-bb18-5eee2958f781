import { Injectable, Logger } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { NotificationHistory } from '../entities/notification-history.entity';
import {
  GetNotificationHistoryInput,
  MarkNotificationReadInput,
  NotificationHistoryResponse,
  NotificationStatsResponse,
  MarkNotificationResponse,
  NotificationTypeStats
} from '../dto/notification-history-graphql.dto';
import {

  NotificationStatus,
  NotificationPayload
} from '@app/notification';
import { MongoConnectionService } from '@app/db';
import { NotificationHistoryGraphQL } from '../entities/notification-history-graphql.entity';
import { ObjectId, UpdateFilter } from 'mongodb';

@Injectable()
export class NotificationHistoryService {
  private readonly logger = new Logger(NotificationHistoryService.name);

  constructor(
    private readonly mongoConnectionService: MongoConnectionService
  ) { }

  private async getNotificationCollection(orgId?: string) {
    return this.mongoConnectionService.getMasterCollection(
      'notification_history',     
    );
  }


  private async getorganisationuserCollection() {
    return this.mongoConnectionService.getCollectionByOrgId(
      'organisation_users',
    );
  }

  private async getuserCollection() {
    return this.mongoConnectionService.getCollectionByOrgId(
      'users',
    );
  }

  async saveNotification(payload: NotificationPayload, notificationId: string): Promise<NotificationHistory> {
    try {
      const modelCollection = await this.getNotificationCollection(payload.metadata?.orgId);
      const notification = await modelCollection.insertOne({
        notificationId,
        senderId: payload.senderId,
        userId: new Types.ObjectId(payload.userId),
        userEmail: payload.userEmail,
        type: payload.type,
        title: payload.title,
        message: payload.message,
        channels: payload.channels || [],
        priority: payload.priority,
        status: NotificationStatus.SENT,
        data: payload.data,
        metadata: payload.metadata,
        sentAt: new Date(),
        orgId: payload.metadata?.orgId,
        subOrgId: payload.metadata?.subOrgId,
        processId: payload.metadata?.processId,
        isRead: false
      });

      this.logger.log(`Notification saved to history: ${notificationId} for user ${payload.userId}`);

      return {
        _id: notification.insertedId,
        notificationId,
        userId: new Types.ObjectId(payload.userId),
        userEmail: payload.userEmail,
        type: payload.type,
        title: payload.title,
        message: payload.message,
        channels: payload.channels || [],
        priority: payload.priority,
        status: NotificationStatus.SENT,
        data: payload.data,
        metadata: payload.metadata,
        sentAt: new Date(),
        orgId: payload.metadata?.orgId,
        subOrgId: payload.metadata?.subOrgId,
        processId: payload.metadata?.processId,
        isRead: false
      } as NotificationHistory;
    } catch (error) {
      this.logger.error(`Failed to save notification to history: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getNotificationHistory(input: GetNotificationHistoryInput): Promise<NotificationHistoryResponse> {
    try {
      const {
        userId,
        types,
        isRead,
        status,
        page = 1,
        limit = 20,
        orgId,
      } = input;

      const collection = await this.getNotificationCollection();

      const query: any = {};
      if (userId) query.userId = userId;
      if (types?.length) query.type = { $in: types };
      if (typeof isRead === 'boolean') query.isRead = isRead;
      if (status) query.status = status;

      const skip = (page - 1) * limit;
      const total = await collection.countDocuments(query);

      const notifications = await collection
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray() as NotificationHistoryGraphQL[];  

      // Step 1: Extract senderIds
      const senderIds = [...new Set(notifications.map(n => n.senderId).filter(Boolean))];

      // Step 2: Fetch from both collections
      const [userCollection, orgUserCollection] = await Promise.all([
        this.getuserCollection(),
        this.getorganisationuserCollection()
      ]);

      const [users, orgUsers] = await Promise.all([
        userCollection.find({ _id: { $in: senderIds.map(id => new ObjectId(id)) } }).toArray(),
        orgUserCollection.find({ _id: { $in: senderIds.map(id => new ObjectId(id)) } }).toArray()
      ]);

      // Step 3: Create sender map
      const senderMap = new Map<string, any>();
      [...users, ...orgUsers].forEach(user => {
        senderMap.set(user._id.toString(), user);
      });

      // Step 4: Merge sender into notification
      const enrichedNotifications = notifications.map(n => {
        const sender = senderMap.get(n.senderId?.toString() ?? '');
        return {
          ...n,
          isRouted: typeof n.isRouted === 'boolean' ? n.isRouted : false,
          senderName: sender?.name || sender?.firstName || null,
          senderEmail: sender?.email || null,
        };
      });

      const unreadCount = await collection.countDocuments({ ...query, isRead: false });
      const totalPages = Math.ceil(total / limit);

      console.log("enrichedNotifications", enrichedNotifications)

      return {
        notifications: enrichedNotifications,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        unreadCount,
      };
    } catch (error) {
      this.logger.error(`Failed to get notification history: ${error.message}`, error.stack);
      throw error;
    }
  }


  async markNotificationsAsRead(
    input: MarkNotificationReadInput,
    orgId?: string
  ): Promise<MarkNotificationResponse> {
    try {
      const { notificationIds, userId } = input;
      const notificationObjectIds = notificationIds.map(
        (id) => (id)
      );
      const model = await this.getNotificationCollection();

      const query: any = {
        notificationId: { $in: notificationObjectIds },
        isRead: false,
      };
      if (userId) {
        query.userId = userId;
      }

      const result = await model.updateMany(query, {
        $set: {
          isRead: true,
          readAt: new Date(),
          status: NotificationStatus.READ,
        },
      });
      return {
        success: true,
        message: `Marked ${result.modifiedCount} notifications as read.`,
        affectedCount: result.modifiedCount,
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to mark notifications as read: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to mark notifications as read');
    }
  }

  async markAllNotificationsAsRead(userId?: string, orgId?: string ): Promise<MarkNotificationResponse> {
    try {
      
      const model = await this.getNotificationCollection();
      const query: any = { isRead: false };
   
      if (userId) query.userId = userId;
      if (orgId) query.orgId = orgId;

      const result = await model.updateMany(query, {
        $set: {
          isRead: true,
          readAt: new Date(),
          status: NotificationStatus.READ
        }
      });

      this.logger.log(`Marked all ${result.modifiedCount} notifications as read for user ${userId}`);
      return {
        success: true,
        message: `Successfully marked all ${result.modifiedCount} notifications as read`,
        affectedCount: result.modifiedCount
      };
    } catch (error) {
      this.logger.error(`Failed to mark all notifications as read: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getNotificationStats(
    userId: string,
    orgId?: string,
    subOrgId?: string,
    processId?: string
  ): Promise<NotificationStatsResponse> {
    try {
      const model = await this.getNotificationCollection();
      const baseQuery: any = { userId:userId };
      if (orgId) baseQuery.orgId = orgId;

      const totalNotifications = await model.countDocuments(baseQuery);
      const unreadCount = await model.countDocuments({ ...baseQuery, isRead: false });
      const readCount = totalNotifications - unreadCount;

      const typeStats = await model.aggregate([
        { $match: baseQuery },
        {
          $group: {
            _id: '$type',
            total: { $sum: 1 },
            unread: { $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] } },
            read: { $sum: { $cond: [{ $eq: ['$isRead', true] }, 1, 0] } }
          }
        },
        { $sort: { total: -1 } }
      ]).toArray();

      const byType: NotificationTypeStats[] = typeStats.map(stat => ({
        type: stat._id,
        total: stat.total,
        unread: stat.unread,
        read: stat.read
      }));

      return {
        totalNotifications,
        unreadCount,
        readCount,
        byType
      };
    } catch (error) {
      this.logger.error(`Failed to get notification stats: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteOldNotifications(daysOld: number = 30, orgId?: string): Promise<number> {
    try {
      const model = await this.getNotificationCollection(orgId);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await model.deleteMany({
        createdAt: { $lt: cutoffDate }
      });

      this.logger.log(`Deleted ${result.deletedCount} old notifications older than ${daysOld} days`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error(`Failed to delete old notifications: ${error.message}`, error.stack);
      throw error;
    }
  }
}
