# Redis-Based SelectHeader API Caching

## 🎯 Overview

This document describes the Redis-based caching implementation for the `selectHeader` GraphQL API, which provides dropdown/header selection data for organizations, sub-organizations, and processes.

## 🚀 Performance Benefits

### Before Caching
- **Response Time**: 50-200ms per request (database query)
- **Database Load**: High (frequent queries for dropdown data)
- **Scalability**: Limited by database performance

### After Caching
- **Cache Hit**: 5-15ms response time (85-95% faster)
- **Cache Miss**: 50-200ms (database query + cache storage)
- **Database Load**: Significantly reduced
- **Target Hit Rate**: >80%
- **Cache TTL**: 30 minutes

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GraphQL       │───▶│  UserResolver    │───▶│ RedisCacheService│
│  selectHeader   │    │  (with caching)  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       ▼
         │                       │              ┌─────────────────┐
         │                       │              │     Redis       │
         │                       │              │  (Header Cache) │
         │                       │              └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│   UserService   │    │   Cache Stats    │
│  (Database)     │    │  & Monitoring    │
└─────────────────┘    └──────────────────┘
```

## 🔑 Cache Key Structure

### Organizations
```
header:orgs:{userId}
```

### Sub-Organizations
```
header:suborgs:{userId}:{orgId}
```

### Processes
```
header:processes:{userId}:{orgId}:{subOrgId}
```

### User Index (for invalidation)
```
header:user_index:{userId}
```

## 📊 Cache Data Structure

Each cache entry contains:
```json
{
  "data": [
    {
      "id": "entity_id",
      "value": "display_name",
      "organisationId": "org_id", // for organizations
      "role": "user_role"         // for processes
    }
  ],
  "cachedAt": 1640995200000,
  "expiresAt": 1640997000000
}
```

## 🔧 Implementation Details

### Enhanced UserResolver

The `selectHeader` method now includes:
- **Cache-first approach**: Check Redis before database
- **Automatic caching**: Store results after database queries
- **Performance logging**: Track cache hits/misses and response times
- **Error handling**: Graceful fallback to database on cache failures

### Cache Methods Added

1. **Organizations**:
   - `cacheUserOrganizations(userId, data, ttl)`
   - `getUserOrganizations(userId)`

2. **Sub-Organizations**:
   - `cacheUserSubOrganizations(userId, orgId, data, ttl)`
   - `getUserSubOrganizations(userId, orgId)`

3. **Processes**:
   - `cacheUserProcesses(userId, orgId, subOrgId, data, ttl)`
   - `getUserProcesses(userId, orgId, subOrgId)`

4. **Cache Management**:
   - `invalidateUserHeaderCache(userId)`

## 🛠️ Usage Examples

### GraphQL Query
```graphql
query SelectHeader($name: String!, $userId: String!, $organisationId: String, $subOrganisationId: String) {
  selectHeader(
    name: $name
    userId: $userId
    organisationId: $organisationId
    subOrganisationId: $subOrganisationId
  )
}
```

### Variables Examples

**Get Organizations:**
```json
{
  "name": "ORGANISATION",
  "userId": "6878f8cd7c1ff7c564303704"
}
```

**Get Sub-Organizations:**
```json
{
  "name": "SUBORGANISATION",
  "userId": "6878f8cd7c1ff7c564303704",
  "organisationId": "01K0C9FZQPPJQ2ZBNFBCME7DQT"
}
```

**Get Processes:**
```json
{
  "name": "PROCESS",
  "userId": "6878f8cd7c1ff7c564303704",
  "organisationId": "01K0C9FZQPPJQ2ZBNFBCME7DQT",
  "subOrganisationId": "6879d34f3d5d5589878857af"
}
```

## 📈 Monitoring & Management

### Cache CLI Commands

```bash
# Start cache CLI
npm run cache:cli

# Check cache statistics
redis-cache> stats

# View user header cache
redis-cache> headers 6878f8cd7c1ff7c564303704

# Clear user header cache
redis-cache> clear-headers 6878f8cd7c1ff7c564303704

# Invalidate all user cache (permissions + headers)
redis-cache> invalidate 6878f8cd7c1ff7c564303704
```

### Test Header Cache
```bash
# Run comprehensive header cache tests
npm run cache:test-headers
```

## 🔍 Log Examples

### Cache Hit
```
✅ [SelectHeader] Organizations cache hit for user 6878f8cd7c1ff7c564303704 in 12ms
```

### Cache Miss
```
🔄 [SelectHeader] Organizations cache miss for user 6878f8cd7c1ff7c564303704 - querying database
💾 [SelectHeader] Organizations cached for user 6878f8cd7c1ff7c564303704 in 156ms
```

### Cache Invalidation
```
🗑️ [SelectHeader] Invalidated header cache for user: 6878f8cd7c1ff7c564303704
```

## ⚙️ Configuration

### Environment Variables
```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PERMISSION_DB=1
REDIS_KEY_PREFIX=asp:

# Cache Settings
CACHE_DEFAULT_TTL=1800  # 30 minutes for header cache
```

### TTL Settings
- **Organizations**: 30 minutes (1800 seconds)
- **Sub-Organizations**: 30 minutes (1800 seconds)
- **Processes**: 30 minutes (1800 seconds)
- **User Index**: 35 minutes (2100 seconds) - slightly longer for cleanup

## 🔄 Cache Invalidation Strategy

### When to Invalidate
1. **User assignment changes**: New org/sub-org/process assignments
2. **Role changes**: User role modifications
3. **Organization structure changes**: New orgs, sub-orgs, or processes
4. **Manual invalidation**: Via CLI or admin interface

### How to Invalidate
```typescript
// In your service when user assignments change
await this.cacheService.invalidateUserHeaderCache(userId);
```

## 🧪 Testing

### Automated Tests
```bash
# Test header cache functionality
npm run cache:test-headers
```

### Manual Testing
1. Make a selectHeader request (cache miss)
2. Make the same request again (cache hit)
3. Check response times in logs
4. Verify cache entries via CLI

## 🚨 Troubleshooting

### Common Issues

1. **Cache Always Missing**
   - Check Redis connectivity
   - Verify environment variables
   - Ensure RedisCacheService is injected

2. **High Memory Usage**
   - Monitor cache key count
   - Adjust TTL values
   - Implement cache size limits

3. **Slow Performance**
   - Check Redis server performance
   - Monitor network latency
   - Consider Redis clustering

### Debug Commands
```bash
# Check Redis connectivity
redis-cli ping

# Monitor cache operations
redis-cache> monitor

# Check specific user cache
redis-cache> headers <userId>
```

## 📋 Best Practices

1. **Cache Invalidation**: Always invalidate when underlying data changes
2. **Error Handling**: System gracefully falls back to database
3. **Monitoring**: Regularly check hit rates and performance
4. **TTL Management**: Balance between freshness and performance
5. **Memory Management**: Monitor Redis memory usage

## 🎯 Expected Results

After implementation, you should see:
- **85-95% faster response times** for cached requests
- **Reduced database load** for dropdown queries
- **Better user experience** with faster UI interactions
- **Improved scalability** for concurrent users
- **Detailed logging** for monitoring and debugging
