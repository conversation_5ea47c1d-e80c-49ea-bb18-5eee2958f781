import { Modu<PERSON> } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloGatewayDriver, ApolloGatewayDriverConfig } from '@nestjs/apollo';
import { IntrospectAndCompose } from '@apollo/gateway';
import { getSubgraphs } from './application/config';
import { getDataSource } from './domain/datasources';
import { ApolloServerPluginLandingPageLocalDefault } from '@apollo/server/plugin/landingPage/default';

@Module({
  imports: [
    GraphQLModule.forRoot<ApolloGatewayDriverConfig>({
      driver: ApolloGatewayDriver,
      server: {
        formatError: (error: any) => {
          return {
            message: error.message,
            code: error.extensions?.code ?? 'INTERNAL_SERVER_ERROR',
            type: error.extensions?.type ?? 'UNKNOWN',
            ...(error.extensions?.error && { error: error.extensions.error }),
          };
        },
        playground: false,
        plugins: [ApolloServerPluginLandingPageLocalDefault()],
      },
      gateway: {
        supergraphSdl: new IntrospectAndCompose({
          subgraphs: getSubgraphs(),
        }),
        buildService({ name, url }) {
          return getDataSource(name, url);
        },
      },
    }),
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
