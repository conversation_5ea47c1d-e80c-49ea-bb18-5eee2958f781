import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { Redis } from 'ioredis';
import { ConfigService } from '@nestjs/config';

export interface PermissionCacheData {
  userId: string;
  orgId: string;
  subOrgId: string;
  processId: string;
  roleId: string;
  roleName: string;
  roleKey: string;
  roleCategory?: string;
  permissions: any[];
  assignmentId: string;
  reportToUserId?: string;
  isActive?: boolean;
  cachedAt: number;
  expiresAt: number;
}

export interface OrgPermissionCacheData {
  roleKey: string;
  roleName: string;
  roleCategory?: string;
  isActive?: boolean;
  permissions: any[];
  cachedAt?: number;
  expiresAt?: number;
}

@Injectable()
export class RedisCacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisCacheService.name);
  private redis?: Redis;
  private readonly defaultTTL = 86400; // 24 hours in seconds (24 * 60 * 60)
  private readonly shortTTL = 3600; // 1 hour in seconds for short-lived cache
  private readonly headerTTL = 86400; // 24 hours for header cache (organizations, processes, etc.)
  // Cache key patterns
  private readonly KEYS = {
    USER_PERMISSION: 'perm:user:{userId}:{orgId}:{subOrgId}:{processId}',
    USER_ORG_PERMISSION: 'perm:user:{userId}:{orgId}',
    USER_PERMISSIONS_SET: 'perm:user:{userId}:*',
    // Header selection cache keys
    USER_ORGANIZATIONS: 'header:orgs:{userId}',
    USER_SUBORGANIZATIONS: 'header:suborgs:{userId}:{orgId}',
    USER_PROCESSES: 'header:processes:{userId}:{orgId}:{subOrgId}',
    USER_HEADER_INDEX: 'header:user_index:{userId}',
  };

  constructor(private readonly configService: ConfigService) { }

  /**
   * Get TTL configurations
   */
  getTTLConfig() {
    return {
      default: this.defaultTTL, // 24 hours
      short: this.shortTTL, // 1 hour
      header: this.headerTTL, // 24 hours
    };
  }

  /**
   * Set cache with 24-hour expiration (convenience method)
   */
  async setCacheWith24HourExpiry(key: string, data: any): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const cacheData = {
        ...data,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (this.defaultTTL * 1000),
      };

      await this.redis!.setex(key, this.defaultTTL, JSON.stringify(cacheData));
      console.log(`✅ Set 24-hour cache: ${key}`);
    } catch (error) {
      console.warn(`Failed to set 24-hour cache for ${key}:`, error.message);
    }
  }

  /**
   * Generic get method for cache
   */
  async get(key: string): Promise<string | null> {
    if (!this.isRedisAvailable()) return null;

    try {
      return await this.redis!.get(key);
    } catch (error) {
      console.warn(`Failed to get cache for key ${key}:`, error.message);
      return null;
    }
  }

  /**
   * Generic set method for cache
   */
  async set(key: string, value: string, ttlSeconds: number = this.defaultTTL): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      await this.redis!.setex(key, ttlSeconds, value);
    } catch (error) {
      console.warn(`Failed to set cache for key ${key}:`, error.message);
    }
  }

  async onModuleInit() {
    try {
      await this.initializeRedis();
    } catch (error) {
      console.warn('Redis initialization failed, continuing without cache:', error.message);
      // Don't throw - allow application to continue without Redis
    }
  }

  async onModuleDestroy() {
    if (this.redis) {
      try {
        await this.redis.quit();
        console.log('Redis connection closed');
      } catch (error) {
        console.warn('Error closing Redis connection:', error.message);
      }
    }
  }

  private async initializeRedis() {
    // Check if Redis configuration is provided
    const redisHost = this.configService.get<string>('REDIS_HOST');
    const redisPort = this.configService.get<number>('REDIS_PORT');

    if (!redisHost && !redisPort) {
      console.log('Redis configuration not found, skipping Redis initialization');
      return;
    }

    const config = {
      host: redisHost || 'localhost',
      port: redisPort || 6379,
      password: this.configService.get<string>('REDIS_PASSWORD'),
      db: this.configService.get<number>('REDIS_PERMISSION_DB', 1),
      keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:'),
      maxRetriesPerRequest: 1, // Minimal retries
      connectTimeout: 3000, // 3 second timeout
      commandTimeout: 2000, // 2 second command timeout
      lazyConnect: true,
      // Retry strategy - give up quickly if Redis is not available
      retryStrategy: (times: number) => {
        if (times > 1) {
          console.warn('Redis connection failed, giving up');
          return null; // Stop retrying
        }
        return 100; // 100ms delay
      },
    };

    this.redis = new Redis(config);

    // Event listeners with minimal logging
    this.redis.on('connect', () => {
      console.log('Redis connected successfully');
    });

    this.redis.on('error', (error) => {
      console.warn('Redis error (continuing without cache):', error.message);
      // Don't throw - just log and continue
    });

    this.redis.on('close', () => {
      console.warn('Redis connection closed');
    });

    try {
      // Try to connect with timeout
      const connectPromise = this.redis.connect();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis connection timeout')), 3000);
      });

      await Promise.race([connectPromise, timeoutPromise]);
      console.log('Redis cache service initialized successfully');
    } catch (error) {
      console.warn('Failed to connect to Redis, continuing without cache:', error.message);
      // Clean up the failed connection
      if (this.redis) {
        this.redis.removeAllListeners();
        this.redis = undefined;
      }
      // Don't throw - allow application to continue without Redis
    }
  }

  // Helper method to check if Redis is available
  private isRedisAvailable(): boolean {
    return this.redis !== undefined;
  }

  // Permission caching methods
  async cacheUserPermission(
    userId: string,
    orgId: string,
    subOrgId: string,
    processId: string,
    data: PermissionCacheData,
    ttl: number = this.defaultTTL
  ): Promise<void> {
    if (!this.isRedisAvailable()) {
      // Redis not available, skip caching
      return;
    }

    try {
      const key = this.formatKey(this.KEYS.USER_PERMISSION, {
        userId,
        orgId,
        subOrgId,
        processId,
      });

      const cacheData = {
        ...data,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (ttl * 1000),
      };

      // Use pipeline for better performance
      const pipeline = this.redis!.pipeline();
      pipeline.setex(key, ttl, JSON.stringify(cacheData));

      // Also cache user-specific index for invalidation
      const userIndexKey = `perm:user_index:${userId}`;
      pipeline.sadd(userIndexKey, key);
      pipeline.expire(userIndexKey, ttl + 300); // Keep index slightly longer

      await pipeline.exec();
      const hours = Math.round(ttl / 3600 * 10) / 10; // Round to 1 decimal place
      console.log(`✅ Cached user permission: ${key} (TTL: ${ttl}s / ${hours}h)`);
    } catch (error) {
      console.warn('Failed to cache user permission (continuing without cache):', error.message);
      // Don't throw - just log and continue
    }
  }


  // Permission caching methods
  async setOrgUserPermission(
    userId: string,
    orgId: string,
    data: OrgPermissionCacheData,
    ttl: number = this.defaultTTL,
  ): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const key = this.formatKey(this.KEYS.USER_ORG_PERMISSION, {
        userId,
        orgId,
      });

      const cacheData = {
        ...data,
        cachedAt: Date.now(),
        expiresAt: Date.now() + ttl * 1000,
      };

      const pipeline = this.redis!.pipeline();

      // Main permission cache
      pipeline.setex(key, ttl, JSON.stringify(cacheData));

      // User index for invalidation support
      const userIndexKey = `perm:user_index:${userId}`;
      pipeline.sadd(userIndexKey, key);
      pipeline.expire(userIndexKey, ttl + 300); // Keep index longer for async invalidation

      await pipeline.exec();

      console.log(`✅ Cached user permission: ${key} (TTL: ${ttl}s)`);
    } catch (error) {
      console.warn(
        `⚠️ Failed to cache user permission for ${userId}@${orgId}: ${error.message}`,
      );
      // Silent fail — do not interrupt the main flow
    }
  }


  async getUserPermission(
    userId: string,
    orgId: string,
    subOrgId: string,
    processId: string
  ): Promise<PermissionCacheData | null> {
    if (!this.isRedisAvailable()) {
      // Redis not available, return null (cache miss)
      return null;
    }

    try {
      const key = this.formatKey(this.KEYS.USER_PERMISSION, {
        userId,
        orgId,
        subOrgId,
        processId,
      });

      const cached = await this.redis!.get(key);
      if (!cached) {
        console.log(`❌ Cache miss for user permission: ${key}`);
        return null;
      }

      const data = JSON.parse(cached) as PermissionCacheData;

      // Check if cache is expired (additional safety check)
      if (data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Cache expired for user permission: ${key}`);
        await this.redis!.del(key);
        return null;
      }

      console.log(`✅ Cache hit for user permission: ${key} (expires in ${Math.round((data.expiresAt - Date.now()) / 1000)}s)`);
      return data;
    } catch (error) {
      console.warn('Failed to get user permission from cache (continuing without cache):', error.message);
      return null;
    }
  }


  async getOrgUserPermission(
    userId: string,
    orgId: string,
  ): Promise<OrgPermissionCacheData | null> {
    if (!this.isRedisAvailable()) {
      // Redis not available, return null (cache miss)
      return null;
    }
    try {
      const key = this.formatKey(this.KEYS.USER_ORG_PERMISSION, {
        userId,
        orgId
      });

      const cached = await this.redis!.get(key);
      if (!cached) {
        console.log(`❌miss for user permission: ${key}`);
        return null;
      }
      const data = JSON.parse(cached) as OrgPermissionCacheData;

      // Check if cache is expired (additional safety check)
      if (data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Cache expired for user permission: ${key}`);
        await this.redis!.del(key);
        return null;
      }

      const expiresInSeconds = data.expiresAt ? Math.round((data.expiresAt - Date.now()) / 1000) : 'unknown';
      console.log(`✅ Cache hit for user permission: ${key} (expires in ${expiresInSeconds}s)`);
      return data;
    } catch (error) {
      console.warn('Failed to get user permission from cache (continuing without cache):', error.message);
      return null;
    }
  }

  async invalidateRole(roleId: string): Promise<void> {
    if (!this.isRedisAvailable()) {
      return;
    }

    try {
      // Simple role invalidation
      const pattern = `perm:role:*:${roleId}`;
      const keys = await this.redis!.keys(pattern);

      if (keys.length > 0) {
        await this.redis!.del(...keys);
        console.log(`Invalidated role cache for roleId: ${roleId}`);
      }
    } catch (error) {
      console.warn('Failed to invalidate role cache (continuing without cache):', error.message);
    }
  }

  // Debug method to check cache keys for a user
  async debugUserCacheKeys(userId: string): Promise<any> {
    if (!this.isRedisAvailable()) {
      return { error: 'Redis not available' };
    }

    try {
      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');

      // Check user index
      const userIndexKey = `perm:user_index:${userId}`;
      const indexKeys = await this.redis!.smembers(userIndexKey);

      // Check pattern-based keys
      const pattern = `${keyPrefix}perm:user:${userId}:*`;
      const patternKeys = await this.redis!.keys(pattern);

      // Check header cache keys
      const headerPattern = `${keyPrefix}header:*${userId}*`;
      const headerKeys = await this.redis!.keys(headerPattern);

      return {
        userId,
        keyPrefix,
        userIndex: {
          key: userIndexKey,
          keys: indexKeys,
          count: indexKeys.length
        },
        patternSearch: {
          pattern,
          keys: patternKeys,
          count: patternKeys.length
        },
        headerCache: {
          pattern: headerPattern,
          keys: headerKeys,
          count: headerKeys.length
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        userId,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Utility methods
  private formatKey(pattern: string, params: Record<string, string>): string {
    let key = pattern;
    for (const [param, value] of Object.entries(params)) {
      key = key.replace(`{${param}}`, value);
    }
    return key;
  }

  // Health check
  async isHealthy(): Promise<boolean> {
    if (!this.isRedisAvailable()) {
      return false;
    }

    try {
      const result = await this.redis!.ping();
      return result === 'PONG';
    } catch (error) {
      console.warn('Redis health check failed:', error.message);
      return false;
    }
  }

  // User-specific cache invalidation
  async invalidateUserPermissions(userId: string): Promise<void> {
    if (!this.isRedisAvailable()) {
      console.log(`⚠️ Redis not available, skipping cache invalidation for user: ${userId}`);
      return;
    }

    try {
      const userIndexKey = `perm:user_index:${userId}`;
      console.log(`🔍 [invalidateUserPermissions] Checking user index: ${userIndexKey}`);

      const userKeys = await this.redis!.smembers(userIndexKey);
      console.log(`🔍 [invalidateUserPermissions] Found ${userKeys.length} keys in user index for user: ${userId}`);

      if (userKeys.length > 0) {
        console.log(`🔍 [invalidateUserPermissions] Keys to delete:`, userKeys);
        const pipeline = this.redis!.pipeline();
        userKeys.forEach(key => {
          // Redis client with keyPrefix handles prefixes automatically
          pipeline.del(key);
        });
        pipeline.del(userIndexKey);

        const results = await pipeline.exec();
        console.log(`🗑️ [invalidateUserPermissions] Pipeline results:`, results?.map(r => r[1]));
        console.log(`🗑️ Invalidated ${userKeys.length} cache entries for user: ${userId}`);
      } else {
        console.log(`ℹ️ [invalidateUserPermissions] No cached entries found for user: ${userId}`);

        // Fallback: try to find keys using pattern matching
        const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');
        const pattern = `${keyPrefix}perm:user:${userId}:*`;
        console.log(`🔍 [invalidateUserPermissions] Fallback: searching with pattern: ${pattern}`);

        const keys = await this.redis!.keys(pattern);
        if (keys.length > 0) {
          console.log(`🔍 [invalidateUserPermissions] Found ${keys.length} keys via pattern search:`, keys);
          await this.redis!.del(...keys);
          console.log(`🗑️ [invalidateUserPermissions] Deleted ${keys.length} keys via pattern search for user: ${userId}`);
        }
      }
    } catch (error) {
      console.warn(`❌ [invalidateUserPermissions] Failed to invalidate user permissions for ${userId}:`, error.message);
    }
  }

  // Batch cache user permissions for multiple contexts
  async batchCacheUserPermissions(
    entries: Array<{
      userId: string;
      orgId: string;
      subOrgId: string;
      processId: string;
      data: PermissionCacheData;
      ttl?: number;
    }>
  ): Promise<void> {
    if (!this.isRedisAvailable() || entries.length === 0) {
      return;
    }

    try {
      const pipeline = this.redis!.pipeline();

      entries.forEach(({ userId, orgId, subOrgId, processId, data, ttl = this.defaultTTL }) => {
        const key = this.formatKey(this.KEYS.USER_PERMISSION, {
          userId,
          orgId,
          subOrgId,
          processId,
        });

        const cacheData = {
          ...data,
          cachedAt: Date.now(),
          expiresAt: Date.now() + (ttl * 1000),
        };

        pipeline.setex(key, ttl, JSON.stringify(cacheData));

        // Update user index
        const userIndexKey = `perm:user_index:${userId}`;
        pipeline.sadd(userIndexKey, key);
        pipeline.expire(userIndexKey, ttl + 300);
      });

      await pipeline.exec();
      console.log(`✅ Batch cached ${entries.length} user permissions`);
    } catch (error) {
      console.warn('Failed to batch cache user permissions:', error.message);
    }
  }

  // Header selection caching methods
  async cacheUserOrganizations(userId: string, organizations: any[], ttl: number = this.headerTTL): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const key = this.formatKey(this.KEYS.USER_ORGANIZATIONS, { userId });
      const cacheData = {
        data: organizations,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (ttl * 1000),
      };

      const pipeline = this.redis!.pipeline();
      pipeline.setex(key, ttl, JSON.stringify(cacheData));

      // Update user header index
      const userIndexKey = this.formatKey(this.KEYS.USER_HEADER_INDEX, { userId });
      pipeline.sadd(userIndexKey, key);
      pipeline.expire(userIndexKey, ttl + 300);

      await pipeline.exec();
      console.log(`✅ Cached organizations for user: ${userId} (TTL: ${ttl}s)`);
    } catch (error) {
      console.warn(`Failed to cache organizations for user ${userId}:`, error.message);
    }
  }

  async getUserOrganizations(userId: string): Promise<any[] | null> {
    if (!this.isRedisAvailable()) return null;

    try {
      const key = this.formatKey(this.KEYS.USER_ORGANIZATIONS, { userId });
      const cached = await this.redis!.get(key);

      if (!cached) {
        console.log(`❌ Cache miss for user organizations: ${userId}`);
        return null;
      }

      const data = JSON.parse(cached);
      if (data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Cache expired for user organizations: ${userId}`);
        await this.redis!.del(key);
        return null;
      }

      console.log(`✅ Cache hit for user organizations: ${userId}`);
      return data.data;
    } catch (error) {
      console.warn(`Failed to get organizations from cache for user ${userId}:`, error.message);
      return null;
    }
  }

  async cacheUserSubOrganizations(userId: string, orgId: string, subOrganizations: any[], ttl: number = this.headerTTL): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const key = this.formatKey(this.KEYS.USER_SUBORGANIZATIONS, { userId, orgId });
      const cacheData = {
        data: subOrganizations,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (ttl * 1000),
      };

      const pipeline = this.redis!.pipeline();
      pipeline.setex(key, ttl, JSON.stringify(cacheData));

      // Update user header index
      const userIndexKey = this.formatKey(this.KEYS.USER_HEADER_INDEX, { userId });
      pipeline.sadd(userIndexKey, key);
      pipeline.expire(userIndexKey, ttl + 300);

      await pipeline.exec();
      console.log(`✅ Cached sub-organizations for user: ${userId}, org: ${orgId} (TTL: ${ttl}s)`);
    } catch (error) {
      console.warn(`Failed to cache sub-organizations for user ${userId}, org ${orgId}:`, error.message);
    }
  }

  async getUserSubOrganizations(userId: string, orgId: string): Promise<any[] | null> {
    if (!this.isRedisAvailable()) return null;

    try {
      const key = this.formatKey(this.KEYS.USER_SUBORGANIZATIONS, { userId, orgId });
      const cached = await this.redis!.get(key);

      if (!cached) {
        console.log(`❌ Cache miss for user sub-organizations: ${userId}, org: ${orgId}`);
        return null;
      }

      const data = JSON.parse(cached);
      if (data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Cache expired for user sub-organizations: ${userId}, org: ${orgId}`);
        await this.redis!.del(key);
        return null;
      }

      console.log(`✅ Cache hit for user sub-organizations: ${userId}, org: ${orgId}`);
      return data.data;
    } catch (error) {
      console.warn(`Failed to get sub-organizations from cache for user ${userId}, org ${orgId}:`, error.message);
      return null;
    }
  }

  async cacheUserProcesses(userId: string, orgId: string, subOrgId: string, processes: any[], ttl: number = this.headerTTL): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const key = this.formatKey(this.KEYS.USER_PROCESSES, { userId, orgId, subOrgId });
      const cacheData = {
        data: processes,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (ttl * 1000),
      };

      const pipeline = this.redis!.pipeline();
      pipeline.setex(key, ttl, JSON.stringify(cacheData));

      // Update user header index
      const userIndexKey = this.formatKey(this.KEYS.USER_HEADER_INDEX, { userId });
      pipeline.sadd(userIndexKey, key);
      pipeline.expire(userIndexKey, ttl + 300);

      await pipeline.exec();
      console.log(`✅ Cached processes for user: ${userId}, org: ${orgId}, subOrg: ${subOrgId} (TTL: ${ttl}s)`);
    } catch (error) {
      console.warn(`Failed to cache processes for user ${userId}, org ${orgId}, subOrg ${subOrgId}:`, error.message);
    }
  }

  async getUserProcesses(userId: string, orgId: string, subOrgId: string): Promise<any[] | null> {
    if (!this.isRedisAvailable()) return null;

    try {
      const key = this.formatKey(this.KEYS.USER_PROCESSES, { userId, orgId, subOrgId });
      const cached = await this.redis!.get(key);

      if (!cached) {
        console.log(`❌ Cache miss for user processes: ${userId}, org: ${orgId}, subOrg: ${subOrgId}`);
        return null;
      }

      const data = JSON.parse(cached);
      if (data.expiresAt && Date.now() > data.expiresAt) {
        console.log(`⏰ Cache expired for user processes: ${userId}, org: ${orgId}, subOrg: ${subOrgId}`);
        await this.redis!.del(key);
        return null;
      }

      console.log(`✅ Cache hit for user processes: ${userId}, org: ${orgId}, subOrg: ${subOrgId}`);
      return data.data;
    } catch (error) {
      console.warn(`Failed to get processes from cache for user ${userId}, org ${orgId}, subOrg ${subOrgId}:`, error.message);
      return null;
    }
  }

  // Invalidate all header cache for a user
  async invalidateUserHeaderCache(userId: string): Promise<void> {
    if (!this.isRedisAvailable()) return;

    try {
      const userIndexKey = this.formatKey(this.KEYS.USER_HEADER_INDEX, { userId });
      const userKeys = await this.redis!.smembers(userIndexKey);

      if (userKeys.length > 0) {
        const pipeline = this.redis!.pipeline();
        userKeys.forEach(key => pipeline.del(key));
        pipeline.del(userIndexKey);
        await pipeline.exec();
        console.log(`🗑️ Invalidated ${userKeys.length} header cache entries for user: ${userId}`);
      }
    } catch (error) {
      console.warn(`Failed to invalidate header cache for user ${userId}:`, error.message);
    }
  }


  

  // Get cache statistics
  async getCacheStats(): Promise<Record<string, string>> {
    if (!this.isRedisAvailable()) {
      return { status: 'Redis not available' };
    }

    try {
      const info = await this.redis!.info('memory');
      const keyCount = await this.redis!.dbsize();

      return {
        status: 'Connected',
        keyCount: keyCount.toString(),
        memoryInfo: info,
        uptime: Date.now().toString(),
      };
    } catch (error) {
      return { status: 'Error', error: error.message };
    }
  }

  async invalidateByPattern(pattern: string): Promise<number> {
    if (!this.isRedisAvailable()) {
      return 0;
    }

    try {
      // Account for Redis key prefix when searching for keys
      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');
      const searchPattern = pattern.startsWith(keyPrefix) ? pattern : `${keyPrefix}${pattern}`;

      console.log(`🔍 Searching for keys with pattern: ${searchPattern}`);
      const keys = await this.redis!.keys(searchPattern);

      if (keys.length === 0) {
        console.log(`🔍 No keys found matching pattern: ${searchPattern}`);
        return 0;
      }

      // Redis client with keyPrefix handles prefixes automatically, so we can delete keys directly
      const deletedCount = await this.redis!.del(...keys);
      console.log(`🗑️ Invalidated ${deletedCount} cache entries matching pattern: ${searchPattern}`);
      return deletedCount;
    } catch (error) {
      console.warn('Failed to invalidate by pattern (continuing without cache):', error.message);
      return 0;
    }
  }

  /**
   * Clean up expired cache entries manually
   * This is useful for maintenance or when Redis doesn't automatically clean up expired keys
   */
  async cleanupExpiredEntries(): Promise<{ cleaned: number; checked: number }> {
    if (!this.isRedisAvailable()) {
      return { cleaned: 0, checked: 0 };
    }

    try {
      const patterns = ['perm:*', 'header:*'];
      let totalCleaned = 0;
      let totalChecked = 0;

      for (const pattern of patterns) {
        const keys = await this.redis!.keys(pattern);
        totalChecked += keys.length;

        for (const key of keys) {
          try {
            const cached = await this.redis!.get(key);
            if (!cached) continue;

            const data = JSON.parse(cached);
            if (data.expiresAt && Date.now() > data.expiresAt) {
              await this.redis!.del(key);
              totalCleaned++;
              console.log(`🧹 Cleaned expired cache entry: ${key}`);
            }
          } catch (parseError) {
            // If we can't parse the data, it might be corrupted, so delete it
            await this.redis!.del(key);
            totalCleaned++;
            console.log(`🧹 Cleaned corrupted cache entry: ${key}`);
          }
        }
      }

      console.log(`🧹 Cache cleanup completed: ${totalCleaned} expired entries removed from ${totalChecked} checked`);
      return { cleaned: totalCleaned, checked: totalChecked };
    } catch (error) {
      console.warn('Failed to cleanup expired cache entries:', error.message);
      return { cleaned: 0, checked: 0 };
    }
  }

  /**
   * Get cache entries that will expire in the next 24 hours
   */
  async getExpiringEntries(hoursFromNow: number = 24): Promise<string[]> {
    if (!this.isRedisAvailable()) {
      return [];
    }

    try {
      const patterns = ['perm:*', 'header:*'];
      const expiringKeys: string[] = [];
      const cutoffTime = Date.now() + (hoursFromNow * 60 * 60 * 1000);

      for (const pattern of patterns) {
        const keys = await this.redis!.keys(pattern);

        for (const key of keys) {
          try {
            const cached = await this.redis!.get(key);
            if (!cached) continue;

            const data = JSON.parse(cached);
            if (data.expiresAt && data.expiresAt <= cutoffTime) {
              expiringKeys.push(key);
            }
          } catch (parseError) {
            // Skip corrupted entries
            continue;
          }
        }
      }

      return expiringKeys;
    } catch (error) {
      console.warn('Failed to get expiring cache entries:', error.message);
      return [];
    }
  }

  /**
   * Extend TTL for specific cache entries (useful for frequently accessed data)
   */
  async extendCacheTTL(key: string, additionalSeconds: number = this.defaultTTL): Promise<boolean> {
    if (!this.isRedisAvailable()) {
      return false;
    }

    try {
      const exists = await this.redis!.exists(key);
      if (!exists) {
        return false;
      }

      const result = await this.redis!.expire(key, additionalSeconds);
      if (result) {
        console.log(`⏰ Extended TTL for cache key: ${key} by ${additionalSeconds}s`);
      }
      return result === 1;
    } catch (error) {
      console.warn(`Failed to extend TTL for cache key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Clear entire Redis cache - use when permissions are updated
   * This is a "nuclear option" but ensures all cached permissions are refreshed
   */
  async clearEntireCache(): Promise<{ success: boolean; deletedKeys: number; message: string }> {
    if (!this.isRedisAvailable()) {
      return {
        success: false,
        deletedKeys: 0,
        message: 'Redis not available'
      };
    }

    try {
      console.log('🧹 [clearEntireCache] Starting complete Redis cache clear...');

      // Get all keys with the prefix
      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');
      const pattern = `${keyPrefix}*`;
      const keys = await this.redis!.keys(pattern);

      if (keys.length === 0) {
        console.log('🧹 [clearEntireCache] No keys found to delete');
        return {
          success: true,
          deletedKeys: 0,
          message: 'No keys found to delete'
        };
      }

      console.log(`🧹 [clearEntireCache] Found ${keys.length} keys to delete`);

      // Delete all keys in batches to avoid blocking Redis
      const batchSize = 1000;
      let totalDeleted = 0;

      for (let i = 0; i < keys.length; i += batchSize) {
        const batch = keys.slice(i, i + batchSize);
        const deleted = await this.redis!.del(...batch);
        totalDeleted += deleted;
        console.log(`🧹 [clearEntireCache] Deleted batch ${Math.floor(i/batchSize) + 1}: ${deleted} keys`);
      }

      console.log(`✅ [clearEntireCache] Successfully cleared entire Redis cache: ${totalDeleted} keys deleted`);

      return {
        success: true,
        deletedKeys: totalDeleted,
        message: `Successfully cleared ${totalDeleted} cache entries`
      };
    } catch (error) {
      console.error('❌ [clearEntireCache] Failed to clear Redis cache:', error.message);
      return {
        success: false,
        deletedKeys: 0,
        message: `Failed to clear cache: ${error.message}`
      };
    }
  }

  /**
   * Clear only permission-related cache keys - safer than clearing everything
   * This targets only permission and header cache, not Bull queues or other data
   */
  async clearPermissionCache(): Promise<{ success: boolean; deletedKeys: number; message: string; details: any }> {
    if (!this.isRedisAvailable()) {
      return {
        success: false,
        deletedKeys: 0,
        message: 'Redis not available',
        details: {}
      };
    }

    try {
      console.log('🧹 [clearPermissionCache] Starting permission cache clear...');

      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');

      // Define permission-related patterns
      const patterns = [
        `${keyPrefix}perm:*`,           // All permission cache
        `${keyPrefix}header:*`,         // All header cache (orgs, suborgs, processes)
        `${keyPrefix}roles:*`,          // Roles query cache
      ];

      let totalDeleted = 0;
      const details: any = {};

      for (const pattern of patterns) {
        console.log(`🔍 [clearPermissionCache] Searching for pattern: ${pattern}`);
        const keys = await this.redis!.keys(pattern);

        if (keys.length > 0) {
          console.log(`🔍 [clearPermissionCache] Found ${keys.length} keys for pattern: ${pattern}`);
          console.log(`🔍 [clearPermissionCache] Sample keys:`, keys.slice(0, 5));

          let deleted = 0;

          // Try bulk delete first (works for single Redis instance)
          try {
            console.log(`🔄 [clearPermissionCache] Attempting bulk delete for ${keys.length} keys...`);
            deleted = await this.redis!.del(...keys);
            console.log(`✅ [clearPermissionCache] Bulk delete successful: ${deleted} keys deleted`);
          } catch (bulkError) {
            console.log(`⚠️ [clearPermissionCache] Bulk delete failed (likely Redis Cluster): ${bulkError.message}`);
            console.log(`🔄 [clearPermissionCache] Falling back to individual key deletion...`);

            // Fallback: Delete keys one by one for Redis Cluster
            deleted = 0;
            const failedKeys: string[] = [];

            console.log(`🔄 [clearPermissionCache] Processing ${keys.length} keys individually...`);
            for (let i = 0; i < keys.length; i++) {
              const key = keys[i];
              try {
                console.log(`🔍 [clearPermissionCache] Attempting to delete key ${i + 1}/${keys.length}: ${key}`);
                const result = await this.redis!.del(key);
                console.log(`🔍 [clearPermissionCache] Delete result for ${key}: ${result}`);

                if (result > 0) {
                  deleted += result;
                  console.log(`✅ [clearPermissionCache] Successfully deleted key: ${key}`);
                } else {
                  console.log(`⚠️ [clearPermissionCache] Key not found or already deleted: ${key}`);
                  failedKeys.push(key);
                }
              } catch (keyError) {
                console.error(`❌ [clearPermissionCache] Failed to delete key ${key}:`, keyError.message);
                failedKeys.push(key);
              }
            }

            console.log(`🔍 [clearPermissionCache] Individual deletion summary: ${deleted} deleted, ${failedKeys.length} failed`);
            if (failedKeys.length > 0) {
              console.log(`🔍 [clearPermissionCache] Failed keys sample:`, failedKeys.slice(0, 3));
            }

            if (failedKeys.length > 0) {
              console.error(`❌ [clearPermissionCache] Failed to delete ${failedKeys.length} keys:`, failedKeys.slice(0, 3));

              // Third attempt: Try using UNLINK command (non-blocking delete)
              console.log(`🔄 [clearPermissionCache] Trying UNLINK command for failed keys...`);
              const stillFailedKeys: string[] = [];

              for (const key of failedKeys) {
                try {
                  const result = await this.redis!.unlink(key);
                  if (result > 0) {
                    deleted += result;
                    console.log(`✅ [clearPermissionCache] UNLINK successful for key: ${key}`);
                  } else {
                    stillFailedKeys.push(key);
                  }
                } catch (unlinkError) {
                  console.error(`❌ [clearPermissionCache] UNLINK also failed for key ${key}:`, unlinkError.message);
                  stillFailedKeys.push(key);
                }
              }

              // Fourth attempt: Try pipeline for remaining keys
              if (stillFailedKeys.length > 0) {
                console.log(`🔄 [clearPermissionCache] Trying pipeline for ${stillFailedKeys.length} remaining keys...`);
                try {
                  const pipeline = this.redis!.pipeline();
                  stillFailedKeys.forEach(key => pipeline.del(key));
                  const results = await pipeline.exec();
                  const pipelineDeleted = results?.filter(result => result && result[1] === 1).length || 0;
                  deleted += pipelineDeleted;
                  console.log(`✅ [clearPermissionCache] Pipeline deleted ${pipelineDeleted} additional keys`);
                } catch (pipelineError) {
                  console.error(`❌ [clearPermissionCache] Pipeline also failed:`, pipelineError.message);
                  console.log(`⚠️ [clearPermissionCache] ${stillFailedKeys.length} keys could not be deleted:`, stillFailedKeys.slice(0, 5));
                }
              }
            }
          }

          totalDeleted += deleted;
          details[pattern] = { found: keys.length, deleted };

          console.log(`🗑️ [clearPermissionCache] Final result: ${deleted}/${keys.length} keys deleted for pattern: ${pattern}`);
        } else {
          console.log(`ℹ️ [clearPermissionCache] No keys found for pattern: ${pattern}`);
          details[pattern] = { found: 0, deleted: 0 };
        }
      }

      console.log(`✅ [clearPermissionCache] Successfully cleared permission cache: ${totalDeleted} keys deleted`);

      return {
        success: true,
        deletedKeys: totalDeleted,
        message: `Successfully cleared ${totalDeleted} permission cache entries`,
        details
      };
    } catch (error) {
      console.error('❌ [clearPermissionCache] Failed to clear permission cache:', error.message);
      return {
        success: false,
        deletedKeys: 0,
        message: `Failed to clear permission cache: ${error.message}`,
        details: {}
      };
    }
  }

  /**
   * Force clear cache using Redis EVAL script - works with clusters
   */
  async clearPermissionCacheWithScript(): Promise<{ success: boolean; deletedKeys: number; message: string; details: any }> {
    if (!this.isRedisAvailable()) {
      return {
        success: false,
        deletedKeys: 0,
        message: 'Redis not available',
        details: {}
      };
    }

    try {
      console.log('🧹 [clearPermissionCacheWithScript] Starting script-based permission cache clear...');

      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');
      const patterns = [
        `${keyPrefix}perm:*`,
        `${keyPrefix}header:*`,
        `${keyPrefix}roles:*`,
      ];

      let totalDeleted = 0;
      const details: any = {};

      for (const pattern of patterns) {
        console.log(`🔍 [clearPermissionCacheWithScript] Processing pattern: ${pattern}`);

        // Use EVAL script to find and delete keys atomically
        const script = `
          local keys = redis.call('keys', ARGV[1])
          local deleted = 0
          for i=1,#keys do
            deleted = deleted + redis.call('del', keys[i])
          end
          return {#keys, deleted}
        `;

        try {
          const result = await this.redis!.eval(script, 0, pattern) as [number, number];
          const [found, deleted] = result;

          totalDeleted += deleted;
          details[pattern] = { found, deleted };

          console.log(`✅ [clearPermissionCacheWithScript] Pattern ${pattern}: found ${found}, deleted ${deleted}`);
        } catch (scriptError) {
          console.error(`❌ [clearPermissionCacheWithScript] Script failed for pattern ${pattern}:`, scriptError.message);
          details[pattern] = { found: 0, deleted: 0 };
        }
      }

      console.log(`✅ [clearPermissionCacheWithScript] Successfully cleared permission cache: ${totalDeleted} keys deleted`);

      return {
        success: totalDeleted > 0,
        deletedKeys: totalDeleted,
        message: `Successfully cleared ${totalDeleted} permission cache entries using script`,
        details
      };
    } catch (error) {
      console.error('❌ [clearPermissionCacheWithScript] Failed to clear permission cache:', error.message);
      return {
        success: false,
        deletedKeys: 0,
        message: `Failed to clear permission cache with script: ${error.message}`,
        details: {}
      };
    }
  }

  /**
   * Alternative cache clearing method using Redis pipeline - better for clusters
   */
  async clearPermissionCacheWithPipeline(): Promise<{ success: boolean; deletedKeys: number; message: string; details: any }> {
    if (!this.isRedisAvailable()) {
      return {
        success: false,
        deletedKeys: 0,
        message: 'Redis not available',
        details: {}
      };
    }

    try {
      console.log('🧹 [clearPermissionCacheWithPipeline] Starting pipeline-based permission cache clear...');

      const keyPrefix = this.configService.get<string>('REDIS_KEY_PREFIX', 'asp:');
      const patterns = [
        `${keyPrefix}perm:*`,
        `${keyPrefix}header:*`,
        `${keyPrefix}roles:*`,
      ];

      let totalDeleted = 0;
      const details: any = {};

      for (const pattern of patterns) {
        console.log(`🔍 [clearPermissionCacheWithPipeline] Searching for pattern: ${pattern}`);
        const keys = await this.redis!.keys(pattern);

        if (keys.length > 0) {
          console.log(`🔍 [clearPermissionCacheWithPipeline] Found ${keys.length} keys for pattern: ${pattern}`);

          // Use pipeline for better performance
          const pipeline = this.redis!.pipeline();
          keys.forEach(key => pipeline.del(key));

          try {
            const results = await pipeline.exec();
            const deleted = results?.filter(result => result && result[1] === 1).length || 0;
            totalDeleted += deleted;
            details[pattern] = { found: keys.length, deleted };
            console.log(`✅ [clearPermissionCacheWithPipeline] Pipeline deleted ${deleted} keys for pattern: ${pattern}`);
          } catch (pipelineError) {
            console.error(`❌ [clearPermissionCacheWithPipeline] Pipeline failed for pattern ${pattern}:`, pipelineError.message);
            details[pattern] = { found: keys.length, deleted: 0 };
          }
        } else {
          console.log(`ℹ️ [clearPermissionCacheWithPipeline] No keys found for pattern: ${pattern}`);
          details[pattern] = { found: 0, deleted: 0 };
        }
      }

      console.log(`✅ [clearPermissionCacheWithPipeline] Successfully cleared permission cache: ${totalDeleted} keys deleted`);

      return {
        success: totalDeleted > 0,
        deletedKeys: totalDeleted,
        message: `Successfully cleared ${totalDeleted} permission cache entries using pipeline`,
        details
      };
    } catch (error) {
      console.error('❌ [clearPermissionCacheWithPipeline] Failed to clear permission cache:', error.message);
      return {
        success: false,
        deletedKeys: 0,
        message: `Failed to clear permission cache with pipeline: ${error.message}`,
        details: {}
      };
    }
  }
}
