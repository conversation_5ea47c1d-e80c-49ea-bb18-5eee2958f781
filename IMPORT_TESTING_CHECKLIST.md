# 🧪 IMPORT SERVICE TESTING CHECKLIST

## **OVERVIEW**
This checklist verifies that all import combinations work correctly for both Generic and Hierarchical imports with all modes (CREATE, UPDATE, UPSERT) and error handling (isStopOnError true/false).

## **1. GENERIC IMPORTS TESTING**

### **Collections to Test:**
- `tickets` / `provider-credential-tickets`
- `payer`
- Any other non-hierarchical collections

### **Test Matrix:**

| Mode | isStopOnError | Test Scenario | Expected Result |
|------|---------------|---------------|-----------------|
| CREATE | true | Import existing record | ❌ Stop immediately, status=FAILED |
| CREATE | false | Import existing record | ⚠️ Continue, collect error |
| UPDATE | true | No unique fields configured | ❌ Stop immediately, status=FAILED |
| UPDATE | true | Record not found | ❌ Stop immediately, status=FAILED |
| UPDATE | false | Record not found | ⚠️ Continue, collect error |
| UPSERT | true | Any error occurs | ❌ Stop immediately, status=FAILED |
| UPSERT | false | Any error occurs | ⚠️ Continue, collect error |

### **Required Fields Testing:**
```json
{
  "requiredFields": ["ticketId", "subject", "priority"],
  "uniqueFields": ["ticketId"]
}
```

| Test Case | Data | isStopOnError | Expected Result |
|-----------|------|---------------|-----------------|
| Missing required field | `{"status": 1}` (no ticketId) | true | ❌ Stop immediately |
| Missing required field | `{"status": 1}` (no ticketId) | false | ⚠️ Continue, collect error |
| Empty required field | `{"ticketId": "", "subject": "test"}` | true | ❌ Stop immediately |
| Valid required fields | `{"ticketId": "001", "subject": "test"}` | true/false | ✅ Process normally |

### **Unique Fields Update Testing:**
```json
{
  "uniqueFields": ["ticketId"],
  "type": "update"
}
```

| Test Case | Existing Data | New Data | Expected Result |
|-----------|---------------|----------|-----------------|
| Update by unique field | `{"ticketId": "001", "status": 0}` | `{"ticketId": "001", "status": 1, "priority": "High"}` | ✅ Update status and priority |
| Update non-existent | N/A | `{"ticketId": "999", "status": 1}` | ❌ Record not found error |

## **2. HIERARCHICAL IMPORTS TESTING**

### **Collections to Test:**
- `cpts` (CPTs)
- `exceptions` 
- `action_status_code_maps` (Action Status Code Maps)

### **Test Matrix:**

| Collection | Mode | isStopOnError | Test Scenario | Expected Result |
|------------|------|---------------|---------------|-----------------|
| CPTs | CREATE | true | Import existing CPT combination | ❌ Stop immediately |
| CPTs | UPDATE | true | CPT combination not found | ❌ Stop immediately |
| CPTs | UPSERT | true | Any validation error | ❌ Stop immediately |
| Exceptions | CREATE | true | Import existing exception+process | ❌ Stop immediately |
| Exceptions | UPDATE | true | Exception not found | ❌ Stop immediately |
| Exceptions | UPSERT | true | Process doesn't exist | ❌ Stop immediately |
| Action Status | CREATE | true | Import existing combination | ❌ Stop immediately |
| Action Status | UPDATE | true | Combination not found | ❌ Stop immediately |
| Action Status | UPSERT | true | Process doesn't exist | ❌ Stop immediately |

### **Required Fields Testing (Hierarchical):**

#### **CPTs Required Fields:**
```javascript
const requiredFields = ['speciality', 'iCD', 'diagnosisCode', 'cPTCode'];
```

#### **Exceptions Required Fields:**
```javascript
// Built-in validation: process and exception are required
```

#### **Action Status Code Maps Required Fields:**
```javascript
// Built-in validation: process, statusCode, and actionCode are required
```

## **3. TESTING APPROACH**

### **Step 1: Prepare Test Data**

#### **Generic Import Test (Tickets):**
```json
{
  "templateId": "template-id",
  "collectionName": "provider-credential-tickets",
  "type": "create", // or "update", "upsert"
  "isStopOnError": true, // or false
  "filePath": "test-tickets.xlsx"
}
```

**Excel Data:**
```
ticketId | subject | priority | status
001      | Test 1  | High     | 0
002      |         | Medium   | 1  (missing subject - required field)
003      | Test 3  | Low      | 2
```

#### **Hierarchical Import Test (Exceptions):**
```json
{
  "templateId": "template-id", 
  "collectionName": "exceptions",
  "type": "create", // or "update", "upsert"
  "isStopOnError": true, // or false
  "filePath": "test-exceptions.xlsx"
}
```

**Excel Data:**
```
process        | exception | documentRequired | followUpInDays
Medical Coding | Test Ex 1 | Yes             | 5
Invalid Process| Test Ex 2 | No              | 10  (invalid process)
Medical Coding | Test Ex 3 | Yes             | 15
```

### **Step 2: Execute Test Cases**

#### **Test Case 1: CREATE + isStopOnError=true**
1. Import data with one invalid row
2. **Expected**: Import stops at first error, status=FAILED
3. **Verify**: Only records before error are inserted

#### **Test Case 2: CREATE + isStopOnError=false**
1. Import same data
2. **Expected**: Import continues, collects all errors
3. **Verify**: Valid records inserted, errors in failedRows

#### **Test Case 3: UPDATE + Unique Fields**
1. Pre-insert some records
2. Import updates using unique fields
3. **Expected**: Existing records updated based on unique field match
4. **Verify**: Other fields updated, unique field used for matching

#### **Test Case 4: UPSERT + Mixed Data**
1. Import mix of new and existing records
2. **Expected**: New records inserted, existing updated
3. **Verify**: Correct insert/update counts

### **Step 3: Validation Points**

#### **Database Verification:**
```javascript
// Check record counts
const insertedCount = await collection.countDocuments({createdAt: {$gte: testStartTime}});
const updatedCount = await collection.countDocuments({updatedAt: {$gte: testStartTime, $ne: createdAt}});

// Check specific records
const record = await collection.findOne({ticketId: "001"});
assert(record.status === expectedStatus);

// Check processId duplication (for exceptions)
const exception = await collection.findOne({exception: "Test Ex 1"});
assert(Object.keys(exception).filter(k => k.includes('processId')).length === 1);
```

#### **Task Status Verification:**
```javascript
const task = await taskModel.findOne({taskId});
assert(task.status === expectedStatus); // 'COMPLETED' or 'FAILED'
assert(task.insertedCount === expectedInsertedCount);
assert(task.updatedCount === expectedUpdatedCount);
assert(task.failedCount === expectedFailedCount);
```

## **4. AUTOMATED TEST SCRIPT**

### **Test Execution Order:**
1. **Setup**: Create test templates and configurations
2. **Generic Tests**: Run all generic import combinations
3. **Hierarchical Tests**: Run all hierarchical import combinations  
4. **Cleanup**: Remove test data
5. **Report**: Generate test results summary

### **Success Criteria:**
- ✅ All 18 test combinations pass (3 modes × 2 error settings × 3 import types)
- ✅ Required fields validation works correctly
- ✅ Unique fields-based updates work correctly
- ✅ Stop-on-error functionality works as expected
- ✅ No duplicate processId fields in hierarchical imports
- ✅ Proper error messages and status updates

## **5. QUICK VERIFICATION COMMANDS**

```bash
# Check import task status
db.import_tasks.find({taskId: "test-task-id"}).pretty()

# Check inserted records
db.provider_credential_tickets.find({createdAt: {$gte: ISODate("2025-08-03T10:00:00Z")}}).count()

# Check for duplicate processId in exceptions
db.exceptions.find({}, {processId: 1}).forEach(doc => {
  const processIdFields = Object.keys(doc).filter(k => k.includes('processId'));
  if (processIdFields.length > 1) print("Duplicate processId found:", doc._id);
})

# Check failed rows
db.import_tasks.find({taskId: "test-task-id"}, {failedRows: 1}).pretty()
```

This comprehensive testing approach ensures all import combinations work correctly! 🎯
