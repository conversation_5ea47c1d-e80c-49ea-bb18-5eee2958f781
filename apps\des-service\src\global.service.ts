import { Injectable, HttpException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Global, GlobalDocument } from './entities/global.entity';
import { CreateGlobalInput, UpdateGlobalInput, FindAllGlobalsInput } from './dto/global.dto';
// import { BaseResponse } from './dto/base.response.dto';
import {
    ErrorType,
    Error,
    ResponseCode,
    HttpStatus,
    Success,
} from '@app/error';
import * as jwt from 'jsonwebtoken';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class GlobalService {
    constructor(
        @InjectModel(Global.name)
        private readonly globalModel: Model<GlobalDocument>,
        @InjectModel('CptCode') private readonly cptCodeModel: Model<any>,
        @InjectModel('Diagnosis') private readonly diagnosisModel: Model<any>,
        @InjectModel('Specialty') private readonly specialtyModel: Model<any>,
        @InjectModel('Icd') private readonly icdModel: Model<any>,
        private readonly mongoConnectionService: MongoConnectionService,
        // @InjectModel('Process') private readonly processModel: Model<any>,
    ) { }

    private extractUserIdFromToken(token: string): string {
        try {
            const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
            return typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : '';
        } catch (error) {
            return '';
        }
    }

    async create(input: CreateGlobalInput, token?: string): Promise<Success> {
        try {
            const userId = token ? this.extractUserIdFromToken(token) : '';
            const newGlobal = new this.globalModel({
                ...input,
                isActive: true,
                createdBy: userId,
            });
            await newGlobal.save();

            return new Success(
                HttpStatus.CREATED,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { global: newGlobal },
            );
        } catch (error) {
            throw new HttpException(
                `Failed to create global: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async findAll(input: FindAllGlobalsInput): Promise<Success> {
        try {
            const {
                page = 1,
                limit = 10,
                search,
                filters,
                sortBy,
                sortOrder,
                selectedFields
            } = input;

            const query: any = {};

            // === Get dynamic fields ===
            const sampleDoc = await this.globalModel.findOne().lean();
            const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

            // === Projection & Searchable Fields ===
            let projection: Record<string, number> = {
                _id: 1,
                name: 1,
                isTable: 1,
                modalName: 1,
                options: 1,
                isActive: 1,
                createdBy: 1,
                updatedBy: 1,
                createdAt: 1,
                updatedAt: 1
            };

            const excludedFromSearch = ['__v', '_id', 'createdAt', 'updatedAt'];

            const searchableFields: string[] =
                selectedFields && selectedFields.length > 0
                    ? selectedFields.filter(f => !excludedFromSearch.includes(f))
                    : allFields.filter(f => !excludedFromSearch.includes(f));

            // === Search Handling ===
            if (search?.trim()) {
                const term = search.trim();
                const regex = { $regex: term.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };

                query.$or = searchableFields.map(field => ({ [field]: regex }));

                if (/^[0-9a-fA-F]{24}$/.test(term)) {
                    query.$or.push({ _id: term });
                }

                if (term.match(/^\d{4}-\d{2}-\d{2}/)) {
                    const date = new Date(term);
                    if (!isNaN(date.getTime())) {
                        const start = new Date(date.setHours(0, 0, 0, 0));
                        const end = new Date(date.setHours(23, 59, 59, 999));
                        query.$or.push({ createdAt: { $gte: start, $lte: end } });
                        query.$or.push({ updatedAt: { $gte: start, $lte: end } });
                    }
                }
            }

            // === Filters Handling ===
            if (filters) {
                Object.entries(filters).forEach(([key, value]) => {
                    if (value !== undefined && value !== '') {
                        query[key] = typeof value === 'string'
                            ? { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' }
                            : value;
                    }
                });
            }

            // === Sorting ===
            const sortQuery: Record<string, 1 | -1> = {};
            if (sortBy) {
                sortQuery[sortBy] = sortOrder === 'asc' ? 1 : -1;
            } else {
                sortQuery.createdAt = -1;
            }

            // === Pagination ===
            const safePage = Math.max(1, page);
            const safeLimit = Math.min(Math.max(1, limit), 100);
            const skip = (safePage - 1) * safeLimit;

            const totalItems = await this.globalModel.countDocuments(query);
            const globals = await this.globalModel
                .find(query)
                .select(projection)
                .sort(sortQuery)
                .skip(skip)
                .limit(safeLimit)
                .lean();

            // Process globals to ensure all required fields
            const processedGlobals = globals.map(global => ({
                id: global._id,
                name: global.name || '',
                isTable: global.isTable || false,
                modalName: global.modalName,
                options: global.options || [],
                isActive: global.isActive || false,
                createdBy: global.createdBy || '',
                updatedBy: global.updatedBy || '',
                createdAt: global.createdAt || new Date(),
                updatedAt: global.updatedAt || new Date(),
                data: global.isTable && global.modalName ? [] : undefined
            }));

            const totalPages = Math.ceil(totalItems / safeLimit);

            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                {
                    globals: processedGlobals,
                    pagination: {
                        page: safePage,
                        limit: safeLimit,
                        total: totalItems,
                        totalItems: totalItems,
                        totalPages,
                        hasNext: safePage < totalPages,
                        hasPrev: safePage > 1,
                    },
                },
            ).toJSON() as unknown as Success;
        } catch (error) {
            if (error instanceof Error) {
                throw error;
            }
            throw new Error(
                HttpStatus.INTERNAL_SERVER_ERROR,
                ResponseCode.INTERNAL_SERVER_ERROR,
                ErrorType.FORM,
                `Failed to retrieve globals: ${error.message}`,
            );
        }
    }

    async findById(id: string, selectedFields?: string[]): Promise<Success> {
        try {
            let projection: any = {};
            if (selectedFields?.length) {
                selectedFields.forEach(field => {
                    projection[field] = 1;
                });
            }

            const global = await this.globalModel.findById(id).select(projection).exec();
            if (!global) {
                throw new HttpException(
                    `Global with ID ${id} not found`,
                    HttpStatus.NOT_FOUND,
                );
            }

            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { global },
            );
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                `Error finding global: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async update(input: UpdateGlobalInput, token?: string): Promise<Success> {
        try {
            const userId = token ? this.extractUserIdFromToken(token) : '';
            const updatedGlobal = await this.globalModel
                .findByIdAndUpdate(
                    input.id,
                    {
                        $set: {
                            ...input,
                            updatedBy: userId,
                            updatedAt: new Date()
                        }
                    },
                    { new: true },
                )
                .exec();

            if (!updatedGlobal) {
                throw new HttpException(
                    `Global with ID ${input.id} not found`,
                    HttpStatus.NOT_FOUND,
                );
            }

            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { global: updatedGlobal },
            );
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                `Failed to update global: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    async delete(id: string): Promise<Success> {
        try {
            const deletedGlobal = await this.globalModel
                .findByIdAndDelete(id)
                .exec();

            if (!deletedGlobal) {
                throw new HttpException(
                    `Global with ID ${id} not found`,
                    HttpStatus.NOT_FOUND,
                );
            }

            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { global: deletedGlobal },
            );
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                `Failed to delete global: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
    async getGlobalByName(name: string, stateId?: string, orgId?: string): Promise<Success> {

        const normalized = name.replace(/ /g, '_').toLowerCase();

        // Fetch State Options early for reuse
        const stateglobals = await this.globalModel.findOne({
            name: { $regex: '^State$', $options: 'i' }
        }).exec();

        const stateOptions = Array.isArray(stateglobals?.options) ? stateglobals.options : [];

        // If state-related field
        if (normalized.includes('state')) {
            const response = stateOptions.map(item => ({
                id: item.id,
                value: item.value
            }));

            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { data: response }
            );
        }       
        if (normalized.includes('city') && stateId) {
            try {
                const cityGlobal = await this.globalModel.findOne({
                    name: { $regex: '^City$', $options: 'i' }
                }).exec();

                const cityOptions = Array.isArray(cityGlobal?.options) ? cityGlobal.options : [];

                // Resolve stateId from value (state name) to id if needed
                const stateMatch = stateOptions.find(
                    state =>
                        state.id?.toString() === stateId?.toString() ||
                        state.value?.trim().toLowerCase() === stateId?.trim().toLowerCase()
                );
                const resolvedStateId = stateMatch?.id?.toString() ?? stateId;

                const matchedState = cityOptions.find(
                    opt =>
                        typeof opt.state_id === 'string' &&
                        opt.state_id.trim().toLowerCase() === resolvedStateId.toLowerCase()
                );

                const cities: any[] = matchedState?.cities || cityOptions.flatMap(opt => opt.cities || []);

                const response = cities
                    .map(city => ({
                        id: city.id || city._id || city.city_id || null,
                        value: city.name || city.city_name || city.value || null
                    }))
                    .filter(item => !!item.id && !!item.value);

                return new Success(
                    HttpStatus.OK,
                    ResponseCode.SUCCESS,
                    ErrorType.FORM,
                    { data: response }
                );
            } catch (error) {
                throw new HttpException(`Error getting city data: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        // Fallback logic for other globals
        try {
            const global = await this.globalModel.findOne({
                name: { $regex: `^${name}$`, $options: 'i' }
            }).exec();

            if (!global) {
                throw new HttpException(`Global with name ${name} not found`, HttpStatus.NOT_FOUND);
            }

            let response: any[] = [];

            if (!global.isTable) {
                response = global.options || [];

                // Reuse same city logic if `stateId` passed for dynamic global
                if (stateId) {
                    const stateMatch = stateOptions.find(
                        state =>
                            state.id?.toString() === stateId?.toString() ||
                            state.value?.trim().toLowerCase() === stateId?.trim().toLowerCase()
                    );
                    const resolvedStateId = stateMatch?.id?.toString() ?? stateId;

                    const matchedState = response.find(
                        opt =>
                            typeof opt.state_id === 'string' &&
                            opt.state_id.trim().toLowerCase() === resolvedStateId.toLowerCase()
                    );

                    const cities = matchedState?.cities || response.flatMap(opt => opt.cities || []);
                    response = cities
                        .map(city => ({
                            id: city.id || city._id || city.city_id || null,
                            value: city.name || city.city_name || city.value || null
                        }))
                        .filter(item => !!item.id && !!item.value);
                }
            } else if (global.modalName) {
                const tableData = await this.getTableData(global.modalName, orgId);
                response = tableData.map(item => ({
                    id: item._id,
                    value: item.code || item.name || item._id
                }));
            }
            return new Success(
                HttpStatus.OK,
                ResponseCode.SUCCESS,
                ErrorType.FORM,
                { data: response }
            );
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new HttpException(
                `Error getting global: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
    private async getTableData(modalName: string, orgId?: string): Promise<any> {
        try {
            let data: any[] = [];
            const query = { isActive: true };
            const projection = { _id: 1, code: 1, name: 1 };

            switch (modalName.toLowerCase()) {
                case 'cptcode':
                    data = await this.cptCodeModel.find(query, projection).lean();
                    break;
                case 'diagnosis':
                    data = await this.diagnosisModel.find(query, projection).lean();
                    break;
                case 'specialty':
                    data = await this.specialtyModel.find(query, projection).lean();
                    break;
                case 'icd':
                    data = await this.icdModel.find(query, projection).lean();
                    break;
                case 'process': {
                    const processCollection = await this.mongoConnectionService.getCollectionByOrgId('processes', orgId);
                    data = await processCollection.find(query, { projection }).toArray();
                    break;
                }
                default:
                    throw new HttpException(
                        `Unknown table name: ${modalName}`,
                        HttpStatus.BAD_REQUEST,
                    );
            }

            return data;
        } catch (error) {
            throw new HttpException(
                `Error fetching table data: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    getAvailableSortFields(): string[] {
        return [
            '_id',
            'name',
            'isTable',
            'modalName',
            'isActive',
            'createdAt',
            'updatedAt',
            'createdBy',
            'updatedBy',
        ];
    }
} 