import { InputType, Field, ID, Int, ArgsType, ObjectType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsBoolean, IsObject, IsMongoId, IsInt, IsEnum } from 'class-validator';
import { Graph<PERSON>JSON } from 'graphql-type-json';
import { Specialty } from '../entities/specialty.entity';
import { ClientType } from '../entities/template.entity';
import { CptCode } from '../entities/cpt-code.entity';
import { SuccessResponse } from './base.response.dto';

@InputType()
export class GetSpecialtyInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

@InputType()
export class SpecialtyFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;
}

@ObjectType()
export class SpecialtyData {
  @Field(() => Specialty)
  specialty: Specialty;

  @Field(() => [CptCode], { nullable: true })
  cptCodes?: CptCode[];
}

@ObjectType()
export class SpecialtyResponse {
  @Field(() => Boolean)
  success: boolean;

  @Field(() => String, { nullable: true })
  message?: string;

  @Field(() => SpecialtyData, { nullable: true })
  data?: SpecialtyData;
}

@InputType()
export class CreateSpecialtyInput {
  @Field(() => ClientType)
  @IsEnum(ClientType)
  type: ClientType;

  @Field()
  @IsString()
  name: string;

  @Field(() => GraphQLJSON)
  @IsObject()
  values: Record<string, any>;

  @Field(() => ID)
  @IsMongoId()
  templateId: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdateSpecialtyInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field(() => ClientType, { nullable: true })
  @IsOptional()
  @IsEnum(ClientType)
  type?: ClientType;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@ObjectType()
export class SpecialtyPaginationInfo {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class PaginatedSpecialtyResponse {
 @Field(() => [GraphQLJSON])
  items: any[];

  @Field(() => SpecialtyPaginationInfo)
  pagination: SpecialtyPaginationInfo;
}

@ArgsType()
export class PaginateSpecialtyArgs {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}


export { SuccessResponse }; 