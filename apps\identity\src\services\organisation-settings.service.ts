import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OrganisationSettings, OrganisationSettingsSchema } from '../entities/organisation-settings.entity';
import { CreateOrganisationSettingsInput, UpdateOrganisationSettingsInput } from '../dto/organisation-settings.dto';

@Injectable()
export class OrganisationSettingsService {
  constructor(
    @InjectModel(OrganisationSettings.name)
    private readonly organisationSettingsModel: Model<OrganisationSettings>,
  ) {}

  async create(input: CreateOrganisationSettingsInput): Promise<OrganisationSettings> {
    return this.organisationSettingsModel.create(input);
  }

  async findAll(): Promise<OrganisationSettings[]> {
    return this.organisationSettingsModel.find().exec();
  }

  async findOne(id: string): Promise<OrganisationSettings> {
    const found = await this.organisationSettingsModel.findById(id).exec();
    if (!found) throw new NotFoundException('OrganisationSettings not found');
    return found;
  }

  async findByOrganisationId(organisationId: string): Promise<OrganisationSettings | null> {
    return this.organisationSettingsModel.findOne({ organisationId }).exec();
  }

  async updateByOrganisationId(organisationId: string, input: UpdateOrganisationSettingsInput): Promise<OrganisationSettings> {
    const updated = await this.organisationSettingsModel.findOneAndUpdate(
      { organisationId },
      input,
      { new: true }
    ).exec();
    if (!updated) throw new NotFoundException('OrganisationSettings not found');
    return updated;
  }

  async remove(id: string): Promise<boolean> {
    const res = await this.organisationSettingsModel.findByIdAndDelete(id).exec();
    return !!res;
  }
} 