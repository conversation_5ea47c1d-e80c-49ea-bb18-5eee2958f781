import { Test, TestingModule } from '@nestjs/testing';
import { DesServiceResolver } from './des-service.resolver';
import { DesServiceService } from './des-service.service';
import { Types } from 'mongoose';

describe('DesServiceResolver - Basic Tests', () => {
  let resolver: DesServiceResolver;

  const mockObjectId = new Types.ObjectId();
  const mockSuccessResponse = { code: 200, message: 'Success', data: {} };

  const mockService = {
    findAllForms: jest.fn().mockResolvedValue(mockSuccessResponse),
    findFormById: jest.fn().mockResolvedValue(mockSuccessResponse),
    createForm: jest.fn().mockResolvedValue(mockSuccessResponse),
    updateForm: jest.fn().mockResolvedValue(mockSuccessResponse),
    deleteForm: jest.fn().mockResolvedValue(mockSuccessResponse),
    getForm: jest.fn().mockResolvedValue(mockSuccessResponse),
    findAllTemplates: jest.fn().mockResolvedValue(mockSuccessResponse),
    findTemplateById: jest.fn().mockResolvedValue(mockSuccessResponse),
    createTemplate: jest.fn().mockResolvedValue(mockSuccessResponse),
    updateTemplate: jest.fn().mockResolvedValue(mockSuccessResponse),
    deleteTemplate: jest.fn().mockResolvedValue(mockSuccessResponse),
    getTemplateCount: jest.fn().mockResolvedValue(mockSuccessResponse),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DesServiceResolver,
        {
          provide: DesServiceService,
          useValue: mockService,
        },
      ],
    }).compile();

    resolver = module.get<DesServiceResolver>(DesServiceResolver);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('Form Operations', () => {
    it('should call getForms', async () => {
      const args = {
        page: 1,
        limit: 10,
        search: 'test',
      };

      const result = await resolver.getForms(args);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.findAllForms).toHaveBeenCalledWith(args);
    });

    it('should call getForm', async () => {
      const getFormInput = {
        formId: mockObjectId.toString(),
      };

      const result = await resolver.getForm(getFormInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.getForm).toHaveBeenCalledWith(getFormInput);
    });

    it('should call createForm', async () => {
      const createFormInput = {
        name: 'Test Form',
        description: 'Test Description',
        templateId: 'test-template-001',
        fields: { type: 'text', label: 'Name' },
        createdBy: 'test-user',
      };

      const result = await resolver.createForm(createFormInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.createForm).toHaveBeenCalledWith(createFormInput);
    });

    it('should call updateForm', async () => {
      const updateFormInput = {
        id: mockObjectId.toString(),
        name: 'Updated Form',
        value: { type: 'text', label: 'Updated Name' },
      };

      const result = await resolver.updateForm(updateFormInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.updateForm).toHaveBeenCalledWith(updateFormInput);
    });

    it('should call deleteForm', async () => {
      const formId = mockObjectId.toString();

      const result = await resolver.deleteForm(formId);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.deleteForm).toHaveBeenCalledWith(formId);
    });

    it('should call getForm with input object', async () => {
      const getFormInput = { formId: mockObjectId.toString() };

      const result = await resolver.getForm(getFormInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.getForm).toHaveBeenCalledWith(getFormInput);
    });
  });

  describe('Template Operations', () => {
    it('should call getTemplates', async () => {
      const args = {
        page: 1,
        limit: 10,
        search: 'test',
        filters: { status: 'active' },
        sort: { field: 'name', ascending: true },
      };

      const result = await resolver.getTemplates(args);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.findAllTemplates).toHaveBeenCalledWith(args);
    });

    it('should call getTemplate', async () => {
      const templateId = mockObjectId.toString();

      const result = await resolver.getTemplate(templateId);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.findTemplateById).toHaveBeenCalledWith(templateId);
    });

    it('should call createTemplate', async () => {
      const createTemplateInput = {
        name: 'Test Template',
        description: 'Test Template Description',
        templateId: 'test-template-001',
        value: [{ type: 'text', label: 'Name' }],
        status: 'active',
        createdBy: 'test-user',
      };

      const result = await resolver.createTemplate(createTemplateInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.createTemplate).toHaveBeenCalledWith(createTemplateInput);
    });

    it('should call updateTemplate', async () => {
      const updateTemplateInput = {
        id: mockObjectId.toString(),
        name: 'Updated Template',
        description: 'Updated Description',
      };

      const result = await resolver.updateTemplate(updateTemplateInput);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.updateTemplate).toHaveBeenCalledWith(updateTemplateInput);
    });

    it('should call deleteTemplate', async () => {
      const templateId = mockObjectId.toString();

      const result = await resolver.deleteTemplate(templateId);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.deleteTemplate).toHaveBeenCalledWith(templateId);
    });

    it('should call getTemplateCount without filters', async () => {
      const result = await resolver.getTemplateCount();

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.getTemplateCount).toHaveBeenCalledWith(undefined);
    });

    it('should call getTemplateCount with filters', async () => {
      const filters = '{"status":"active","name":"test"}';
      const parsedFilters = { status: 'active', name: 'test' };

      const result = await resolver.getTemplateCount(filters);

      expect(result).toBe(mockSuccessResponse);
      expect(mockService.getTemplateCount).toHaveBeenCalledWith(parsedFilters);
    });
  });

  describe('Service Integration', () => {
    it('should have all required service methods', () => {
      expect(mockService.findAllForms).toBeDefined();
      expect(mockService.findFormById).toBeDefined();
      expect(mockService.createForm).toBeDefined();
      expect(mockService.updateForm).toBeDefined();
      expect(mockService.deleteForm).toBeDefined();
      expect(mockService.getForm).toBeDefined();
      expect(mockService.findAllTemplates).toBeDefined();
      expect(mockService.findTemplateById).toBeDefined();
      expect(mockService.createTemplate).toBeDefined();
      expect(mockService.updateTemplate).toBeDefined();
      expect(mockService.deleteTemplate).toBeDefined();
      expect(mockService.getTemplateCount).toBeDefined();
    });
  });
});
