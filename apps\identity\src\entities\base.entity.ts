import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export abstract class BaseEntity extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field()
    @Prop({ required: true, default: true })
    isActive: boolean;

    @Field()
    @Prop({ required: true, default: Date.now })
    createdAt: Date;

    @Field()
    @Prop({ required: true, default: Date.now })
    updatedAt: Date;

    @Field({ nullable: true })
    @Prop()
    createdBy?: string;

    @Field({ nullable: true })
    @Prop()
    updatedBy?: string;
}
