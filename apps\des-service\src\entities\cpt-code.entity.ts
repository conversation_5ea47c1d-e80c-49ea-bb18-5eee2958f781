import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Specialty } from './specialty.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'cpt_codes',
  strict: false,  
})
export class CptCode extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String)
  @Prop()
  code: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Prop({ type: Object, default: {} })
  values: Record<string, any>;

  @Field(() => ID)
  @Prop({ type: Types.ObjectId, ref: 'Specialty', required: true })
  specialtyId: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ index: true })
  templateId?: string;
  
  @Field(() => Boolean)
  @Prop({ default: true })
  isActive: boolean;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  updatedBy?: string;
}

export const CptCodeSchema = SchemaFactory.createForClass(CptCode);

// Add text indexes for full-text search
CptCodeSchema.index({
  code: 'text',
  'values.description': 'text'
}, {
  weights: {
    code: 10,
    'values.description': 5
  },
  name: "CptCodeSearchIndex"
});

// Add indexes for common queries
CptCodeSchema.index({ specialtyId: 1 });
CptCodeSchema.index({ createdAt: -1 });
CptCodeSchema.index({ code: 1 }, { unique: true }); 