// import { connect, model } from 'mongoose';
// import { ProcessSchema } from './src/entities/process.entity';
// import { OrganisationProcessMappingSchema } from './src/entities/organisation-process-mapping.entity';

// // Replace with your MongoDB connection string
// const MONGO_URI = 'mongodb+srv://karthikyoki999:<EMAIL>/asp';

// // Models
// const OrganisationProcessMapping = model(
//   'OrganisationProcessMapping',
//   OrganisationProcessMappingSchema,
//   'organisation_process_mappings'
// );
// const Process = model('Process', ProcessSchema, 'processes');

// // Your target org/suborg IDs
// const organisationId = '685e4718d9d39c15e14ca019';
// const subOrganisationId = '685e482f41c78b62d1d4d2f6';

// async function main() {
//   await connect(MONGO_URI);

//   const processes = await Process.find().lean();

//   for (const process of processes) {
//     await OrganisationProcessMapping.updateOne(
//       {
//         organisationId,
//         subOrganisationId,
//         processId: process._id,
//       },
//       {
//         organisationId,
//         subOrganisationId,
//         processId: process._id.toString(),
//         processName: process.name,
//         isActive: false,
//       },
//       { upsert: true }
//     );
//   }

//   console.log('Seeding complete!');
//   process.exit(0);
// }

// main().catch(err => {
//   console.error(err);
//   process.exit(1);
// });