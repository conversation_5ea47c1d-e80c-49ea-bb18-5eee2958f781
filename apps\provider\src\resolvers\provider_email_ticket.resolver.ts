import {
  Resolver,
  Query,
  Mutation,
  Args,
  ID,
  Context
} from '@nestjs/graphql';
import { ProviderEmailTicketService } from '../services/provider_email_ticket.service';
import { EmailTicket } from '../entities/provider_email_ticket';
import {
  CreateProviderEmailTicketInput,
  UpdateProviderEmailTicketInput,
  PaginateProviderEmailTicketArgs,
  ProviderEmailTicketResponse,
  ReplyToEmailTicketInput,
} from '../dto/provider_email_ticket.dto';
import {
  BaseResponse,
} from '../dto/base.response.dto';
import { RequirePermission } from '@app/permissions';
import { Error, ErrorType, ResponseCode, HttpStatus } from '@app/error';

@Resolver(() => EmailTicket)
export class ProviderEmailTicketResolver {
  constructor(
    private readonly providerEmailTicketService: ProviderEmailTicketService,
  ) { }

  @Query(() => ProviderEmailTicketResponse, {
    name: 'providerEmailTickets',
  })
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'View' },
    { module: 'ticketing-system', subModule: 'All Tickets', permission: 'View' }
  )
  async getAllProviderEmailTickets(@Context() context: any,
    @Args('input', { nullable: true }) input?: PaginateProviderEmailTicketArgs
  ) {

    const orgId = context?.orgId;
    const userId = context?.userId;
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters || {};
    const roleName = context?.roleName;

    return this.providerEmailTicketService.findAll(
      search,
      selectedFields,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      userId,
      orgId,
      roleName,
    );
  }

  @Query(() => EmailTicket, {
    name: 'providerEmailTicket',
    nullable: true,
  })
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'View' },
    { module: 'ticketing-system', subModule: 'All Tickets', permission: 'View' }
  )
  async getProviderEmailTicket(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Validate ID before processing
    if (!id || id === 'null' || id === 'undefined') {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Valid ID is required'
      );
    }

    const orgId = context?.orgId;
    return this.providerEmailTicketService.findById(id, orgId);
  }

  @Mutation(() => BaseResponse)
  // @RequirePermission(   
  //   { module: 'ticketing-system', subModule: 'All Tickets', permission: 'Add' }
  // )
  async createProviderEmailTicket(
    @Args('input') input: CreateProviderEmailTicketInput,
    @Context() context: any
  ) {    
    const orgId = context?.orgId || context?.req?.headers?.orgid;
    const userId = context?.userId || context?.req?.headers?.userid;
    const subOrgId = context?.subOrgId || context?.req?.headers?.suborgid;
    const processId = context?.processId || context?.req?.headers?.processid;
    return this.providerEmailTicketService.create(input, orgId, userId, subOrgId, processId);
  }

  @Mutation(() => BaseResponse)
  async replyToEmailThread(
    @Args('input') input: ReplyToEmailTicketInput,
    @Context() context: any
  ) {
    // Validate conversationId before processing
    if (!input.conversationId || input.conversationId === 'null' || input.conversationId === 'undefined') {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Valid conversation ID is required'
      );
    }

    const orgId = context?.orgId;
    const userId = context?.userId;
    const subOrgId = context?.subOrgId;
    const processId = context?.processId;
    return this.providerEmailTicketService.replyToEmailThread(input.conversationId, input, orgId, userId, subOrgId, processId);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'Edit' },
    { module: 'ticketing-system', subModule: 'All Tickets', permission: 'Edit' }
  )
  async updateProviderEmailTicket(
    @Args('input') input: UpdateProviderEmailTicketInput,
    @Context() context: any
  ) {
    // Validate ID before processing
    if (!input.id || input.id === 'null' || input.id === 'undefined') {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Valid ID is required'
      );
    }
    const orgId = context?.orgId;
    const userId = context?.userId;
    const subOrgId = context?.subOrgId;
    const processId = context?.processId;
    return this.providerEmailTicketService.update(input, orgId, userId, subOrgId, processId);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission(
    { module: 'Tickets', subModule: 'All Ticket', permission: 'Delete' },
    { module: 'ticketing-system', subModule: 'All Tickets', permission: 'Delete' }
  )
  async deleteProviderEmailTicket(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    // Validate ID before processing
    if (!id || id === 'null' || id === 'undefined') {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Valid ID is required'
      );
    }

    const orgId = context?.orgId;
    return this.providerEmailTicketService.delete(id, orgId);
  }

}
