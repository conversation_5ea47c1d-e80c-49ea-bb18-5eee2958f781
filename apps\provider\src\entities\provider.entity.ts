import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';

export enum ProviderEntryType {
  EMAIL = 1,
  TICKET = 2,
  IMPORT = 3,

}

export enum ProviderStatus {
  PROGRESS = 2,
  EXCEPTION = 3,
  ALLOCATED = 1,
  REALLOCATE = 4,
  COMPLETED = 5,
  NEW = 0,
}

registerEnumType(ProviderEntryType, { name: 'ProviderEntryType' });
registerEnumType(ProviderStatus, { name: 'ProviderStatus' });

@ObjectType()
@Schema({ timestamps: true })
export class ProviderTicket extends Document {
  @Field(() => ID, { nullable: true })
  declare _id: Types.ObjectId;

  @Field({ nullable: true })
  @Prop()
  ticketId?: string;

  @Field({ nullable: true })
  @Prop()
  templateId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  values?: Record<string, any>;

  @Field(() => ProviderStatus, { nullable: true })
  @Prop({ type: String, enum: ProviderStatus })
  status?: ProviderStatus;

  @Field(() => ProviderEntryType, { nullable: true })
  @Prop({ type: String, enum: ProviderEntryType })
  type?: ProviderEntryType;

  @Field({ nullable: true })
  @Prop()
  allocated_type?: string;

  @Field({ nullable: true })
  @Prop()
  follow_up_date?: string;

  @Field({ nullable: true })
  @Prop()
  received_date?: string;

  @Field({ nullable: true })
  @Prop()
  assign?: boolean;

  @Field({ nullable: true })
  @Prop()
  worked_date?: string;

  @Field({ nullable: true })
  @Prop()
  audit_by?: string;

  @Field({ nullable: true })
  @Prop()
  createdby?: string;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;

  @Field({ nullable: true })
  @Prop()
  source_ticket_id?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  assigned_to?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  assigned_name?: string;


}

export const ProviderSchema = SchemaFactory.createForClass(ProviderTicket);
export { ProviderTicket as Provider };

// Counter Schema
export type CounterDocument = Counter & Document;

@Schema()
export class Counter {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ default: 0 })
  seq?: number;
}

export const CounterSchema = SchemaFactory.createForClass(Counter);
