import { Module } from '@nestjs/common';
import { AuditHelperService } from './src';
import { EmailModule } from '@app/email';
import { DatabaseModule } from '@app/db';

@Module({
  imports: [EmailModule, DatabaseModule],
  providers: [
    AuditHelperService,
    {
      provide: 'AUDIT_MODEL',
      useFactory: () => null, // Will be overridden in the consuming module
    },
  ],
  exports: [
    AuditHelperService,
  ],
})
export class AuditModule {}

// Export everything for easy imports
export * from './src/audit-helper.service';