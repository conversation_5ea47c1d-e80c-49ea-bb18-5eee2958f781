import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { ProcessService } from './services/process.service';
import { ProcessResolver } from './resolvers/process.resolver';
import { Process, ProcessSchema } from './entities/process.entity';
import { UserProcessAssignment, UserProcessAssignmentSchema } from './entities/user-process-assignment.entity';
import { Role, RoleSchema } from './entities/role.entity';
// Permissions guard is provided by the main Identity module

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Process.name, schema: ProcessSchema }]),
    MongooseModule.forFeature([
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
    ]),
  ],
  providers: [
    ProcessService,
    ProcessResolver,
  ],
  exports: [ProcessService],
})
export class ProcessModule {}