import { Controller, Post, Body, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AuditLog } from '../entities/audit-log.entity';

@Controller('audit')
export class AuditController {
  private readonly logger = new Logger(AuditController.name);

  constructor(
    @InjectModel(AuditLog.name) private auditModel: Model<AuditLog>,
  ) {}

  @Post()
  async createAuditLog(@Body() auditLogData: any): Promise<{ success: boolean; id?: string }> {
    try {
      this.logger.log(`📊 RECEIVED AUDIT DATA: ${JSON.stringify(auditLogData, null, 2)}`);

      const auditLog = new this.auditModel(auditLogData);
      const saved = await auditLog.save();
      
      this.logger.log(`✅ AUDIT LOG SAVED: ${saved._id?.toString()}`);
      
      return {
        success: true,
        id: saved._id?.toString()
      };
    } catch (error) {
      this.logger.error('❌ FAILED TO SAVE AUDIT LOG:', error);
      return {
        success: false
      };
    }
  }
}
