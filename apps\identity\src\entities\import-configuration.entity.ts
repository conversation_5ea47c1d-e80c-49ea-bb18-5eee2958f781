import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import GraphQLJSON from 'graphql-type-json';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'import_configurations',
})
export class ImportConfiguration extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => ID)
  @Prop({ type: Types.ObjectId, required: true, ref: 'User' })
  userId: Types.ObjectId;

  @Field()
  @Prop()
  templateId: string;

  @Field()
  @Prop({ required: true })
  collectionName: string;

  @Field(() => GraphQLJSON)
  @Prop({ type: Object, required: true })
  mappingJson: Record<string, string>; // { collectionField: xlsField }

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  templateFields?: Record<string, any>; // Template form values structure

  @Field(() => [String])
  @Prop({ type: [String], required: true })
  requiredFields: string[]; // Fields that must be present in the import file

  @Field(() => [String], { nullable: true })
  @Prop({ type: [String], default: [] })
  uniqueFields?: string[]; // Fields used for upsert operations

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Field({ nullable: true })
  @Prop()
  orgId?: string;

  @Field({ nullable: true })
  createdAt?: Date;

  @Field({ nullable: true })
  updatedAt?: Date;
}

export const ImportConfigurationSchema = SchemaFactory.createForClass(ImportConfiguration);

// Create indexes for better performance
ImportConfigurationSchema.index({ userId: 1, templateId: 1 }, { unique: true });
