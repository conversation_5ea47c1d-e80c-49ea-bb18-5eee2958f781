import { Controller, Post, Get, Delete, HttpCode, HttpStatus, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CacheCleanupService } from './cache-cleanup.service';
import { RedisCacheService } from './redis-cache.service';

@ApiTags('Cache Management')
@Controller('cache')
@ApiBearerAuth()
export class CacheManagementController {
  constructor(
    private readonly cleanupService: CacheCleanupService,
    private readonly cacheService: RedisCacheService
  ) {}

  @Get('health')
  @ApiOperation({ summary: 'Get cache health status' })
  @ApiResponse({ status: 200, description: 'Cache health information' })
  async getCacheHealth() {
    return await this.cleanupService.getCacheHealth();
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get cache statistics' })
  @ApiResponse({ status: 200, description: 'Cache statistics' })
  async getCacheStats() {
    return await this.cacheService.getCacheStats();
  }

  @Get('ttl-config')
  @ApiOperation({ summary: 'Get TTL configuration' })
  @ApiResponse({ status: 200, description: 'TTL configuration details' })
  async getTTLConfig() {
    const config = this.cacheService.getTTLConfig();
    return {
      ...config,
      defaultHours: config.default / 3600,
      shortHours: config.short / 3600,
      headerHours: config.header / 3600,
      description: {
        default: '24 hours - Standard cache TTL for user permissions and data',
        short: '1 hour - Short-lived cache for temporary data',
        header: '24 hours - Header selection cache (organizations, processes, etc.)'
      }
    };
  }

  @Post('cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Manually trigger cache cleanup' })
  @ApiResponse({ status: 200, description: 'Cache cleanup completed' })
  async manualCleanup() {
    return await this.cleanupService.manualCleanup();
  }

  @Post('extend-ttl')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Extend TTL for frequently accessed cache entries' })
  @ApiResponse({ status: 200, description: 'TTL extension completed' })
  async extendFrequentlyAccessedCache() {
    const extended = await this.cleanupService.extendFrequentlyAccessedCache();
    return {
      message: `Extended TTL for ${extended} cache entries`,
      extended
    };
  }

  @Get('expiring')
  @ApiOperation({ summary: 'Get cache entries expiring soon' })
  @ApiResponse({ status: 200, description: 'List of expiring cache entries' })
  async getExpiringEntries() {
    const expiring24h = await this.cacheService.getExpiringEntries(24);
    const expiring6h = await this.cacheService.getExpiringEntries(6);
    const expiring1h = await this.cacheService.getExpiringEntries(1);
    
    return {
      expiring_in_24h: expiring24h.length,
      expiring_in_6h: expiring6h.length,
      expiring_in_1h: expiring1h.length,
      keys_expiring_in_1h: expiring1h.slice(0, 10), // Show first 10 for debugging
      total_checked: expiring24h.length
    };
  }

  @Delete('emergency-cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Emergency cache cleanup - removes ALL cache entries',
    description: '⚠️ WARNING: This will remove all cache entries. Use only in emergency situations.'
  })
  @ApiResponse({ status: 200, description: 'Emergency cleanup completed' })
  async emergencyCleanup() {
    const removed = await this.cleanupService.emergencyCleanup();
    return {
      message: `Emergency cleanup completed: ${removed} entries removed`,
      removed,
      warning: 'All cache entries have been cleared'
    };
  }

  @Post('invalidate-user/:userId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Invalidate all cache entries for a specific user' })
  @ApiResponse({ status: 200, description: 'User cache invalidated' })
  async invalidateUserCache(userId: string) {
    try {
      await this.cacheService.invalidateUserPermissions(userId);
      await this.cacheService.invalidateUserHeaderCache(userId);
      
      return {
        message: `Cache invalidated for user: ${userId}`,
        userId,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        message: `Failed to invalidate cache for user: ${userId}`,
        error: error.message,
        userId,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Post('set-24h-cache')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Set a cache entry with 24-hour expiration (for testing)' })
  @ApiResponse({ status: 200, description: 'Cache entry set with 24-hour TTL' })
  async set24HourCache() {
    const testKey = `test:24h:${Date.now()}`;
    const testData = {
      message: 'This is a test cache entry with 24-hour TTL',
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
    
    await this.cacheService.setCacheWith24HourExpiry(testKey, testData);
    
    return {
      message: 'Test cache entry created with 24-hour TTL',
      key: testKey,
      data: testData,
      ttl_seconds: 86400,
      ttl_hours: 24
    };
  }
}
