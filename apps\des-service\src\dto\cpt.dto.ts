import { InputType, Field, ID, Int, ArgsType, ObjectType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsBoolean, IsObject, IsMongoId, IsInt, IsEnum } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { Cpt } from '../entities/cpt.entity';
import { ClientType } from '../entities/template.entity';
import { SuccessResponse, PaginationMeta } from './base.response.dto';

@InputType()
export class GetCptInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

@InputType()
export class CptFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  specialtyId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  diagnosisId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  cptCodeId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  icdId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => [FieldFilter], { nullable: true })
  @IsOptional()
  fieldFilters?: FieldFilter[];
}

@InputType()
export class FieldFilter {
  @Field()
  @IsString()
  path: string;

  @Field(() => GraphQLJSON)
  value: any;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  @IsBoolean()
  filter?: boolean;
}

@InputType()
export class CreateCptInput {
  @Field(() => ClientType, { nullable: true })
  @IsOptional()
  @IsEnum(ClientType)
  type?: ClientType;

  @Field(() => GraphQLJSON)
  @IsObject()
  values: Record<string, any>;

  @Field(() => ID)
  @IsMongoId()
  specialtyId: string;

  @Field(() => ID)
  @IsMongoId()
  diagnosisId: string;

  @Field(() => ID)
  @IsMongoId()
  templateId: string;

  @Field(() => ID)
  @IsMongoId()
  cptCodeId: string;

  @Field(() => ID)
  @IsMongoId()
  icdId: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdateCptInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  specialtyId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  diagnosisId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  cptCodeId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  icdId?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class AddDiagnosisToCptInput {
  @Field(() => ID)
  @IsMongoId()
  cptId: string;

  @Field(() => ID)
  @IsMongoId()
  diagnosisId: string;
}

@InputType()
export class RemoveDiagnosisFromCptInput {
  @Field(() => ID)
  @IsMongoId()
  cptId: string;

  @Field(() => ID)
  @IsMongoId()
  diagnosisId: string;
}

@ObjectType()
export class PaginatedCptResponse {
  @Field(() => [GraphQLJSON])
  items: any[];

  @Field(() => PaginationMeta)
  pagination: PaginationMeta;
}

@ArgsType()
export class PaginateCptArgs {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

export { SuccessResponse };