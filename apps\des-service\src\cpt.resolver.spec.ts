import { Test, TestingModule } from '@nestjs/testing';
import { MasterCptResolver } from './cpt.resolver';
import { MasterCptService } from './cpt.service';
import { CreateCptInput, UpdateCptInput, PaginateCptArgs, CptPaginationInfo } from './dto/cpt.dto';
import { HttpStatus, ErrorType, Success, ResponseCode, Error } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { ClientType } from './entities/template.entity';
import { Cpt } from './entities/cpt.entity';
import { PaginatedCptResponse } from './dto/cpt.dto';

jest.mock('jsonwebtoken');

describe('MasterCptResolver', () => {
  let resolver: MasterCptResolver;
  let mockCptService: any;

  beforeEach(async () => {
    mockCptService = {
      findById: jest.fn(),
      findAll: jest.fn(),
      count: jest.fn(),
      getAvailableSortFields: jest.fn(),
      getSearchableFields: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MasterCptResolver,
        {
          provide: MasterCptService,
          useValue: mockCptService,
        },
      ],
    }).compile();

    resolver = module.get<MasterCptResolver>(MasterCptResolver);
  });

  describe('getMasterCpt', () => {
    it('should return a CPT by ID', async () => {
      const mockCpt = {
        _id: '1',
        id: '1',
        type: ClientType.MAIN_CLIENT,
        values: { key: 'value' },
        specialtyId: 'specialty1',
        diagnosisId: 'diagnosis1',
        cptCodeId: 'cptCode1',
        icdId: 'icd1',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        toJSON: () => ({
          id: '1',
          type: ClientType.MAIN_CLIENT,
          values: { key: 'value' },
          specialtyId: 'specialty1',
          diagnosisId: 'diagnosis1',
          cptCodeId: 'cptCode1',
          icdId: 'icd1'
        })
      } as unknown as Cpt;

      mockCptService.findById.mockResolvedValue(mockCpt);

      const result = await resolver.getMasterCpt('1');

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('CPT fetched successfully.');
      expect(result.data).toEqual(mockCpt.toJSON());
    });

    // it('should handle not found error', async () => {
    //   const error = new Error(
    //     HttpStatus.NOT_FOUND,
    //     ResponseCode.NOT_FOUND,
    //     ErrorType.CPT,
    //     'CPT not found'
    //   );
    //   mockCptService.findById.mockRejectedValue(error);

    //   const result = await resolver.getMasterCpt('999');

    //   expect(result.code).toBe(HttpStatus.NOT_FOUND);
    //   expect(result.message).toBe('An unexpected error occurred.');
    //   expect(result.type).toContain('_NOT_FOUND');
    //   expect(result.data).toBeNull();
    // });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.findById.mockRejectedValue(error);

      const result = await resolver.getMasterCpt('1');

      expect(result.code).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.message).toBe('An unexpected error occurred.');
      expect(result.type).toContain('_FETCH_ERROR');
      expect(result.data).toBeNull();
    });

    it('should handle CPT without toJSON method', async () => {
      const mockCpt = {
        _id: '1',
        id: '1',
        type: ClientType.MAIN_CLIENT,
        values: { key: 'value' },
        specialtyId: 'specialty1',
        diagnosisId: 'diagnosis1',
        cptCodeId: 'cptCode1',
        icdId: 'icd1',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      } as unknown as Cpt;

      mockCptService.findById.mockResolvedValue(mockCpt);

      const result = await resolver.getMasterCpt('1');

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.data).toEqual(mockCpt);
    });
  });

  describe('cpts', () => {
    it('should return paginated CPTs', async () => {
      const mockPaginatedData: PaginatedCptResponse = {
        items: [
          {
            _id: '1',
            id: '1',
            type: ClientType.MAIN_CLIENT,
            values: { key: 'value1' },
            specialtyId: 'specialty1',
            diagnosisId: 'diagnosis1',
            cptCodeId: 'cptCode1',
            icdId: 'icd1',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            toJSON: () => ({
              id: '1',
              type: ClientType.MAIN_CLIENT,
              values: { key: 'value1' },
              specialtyId: 'specialty1',
              diagnosisId: 'diagnosis1',
              cptCodeId: 'cptCode1',
              icdId: 'icd1'
            })
          } as unknown as Cpt,
          {
            _id: '2',
            id: '2',
            type: ClientType.SUB_CLIENT,
            values: { key: 'value2' },
            specialtyId: 'specialty2',
            diagnosisId: 'diagnosis2',
            cptCodeId: 'cptCode2',
            icdId: 'icd2',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            toJSON: () => ({
              id: '2',
              type: ClientType.SUB_CLIENT,
              values: { key: 'value2' },
              specialtyId: 'specialty2',
              diagnosisId: 'diagnosis2',
              cptCodeId: 'cptCode2',
              icdId: 'icd2'
            })
          } as unknown as Cpt
        ],
        pagination: {
          totalItems: 2,
          totalPages: 1,
          page: 1,
          limit: 10,
          hasNext: false,
          hasPrev: false
        } as CptPaginationInfo
      };

      mockCptService.findAll.mockResolvedValue(mockPaginatedData);

      const args: PaginateCptArgs = { page: 1, limit: 10 };
      const result = await resolver.cpts(args);

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('CPTs fetched successfully.');
      expect(result.data?.items).toHaveLength(2);
      expect(result.data?.pagination).toEqual(mockPaginatedData.pagination);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.findAll.mockRejectedValue(error);

      const args: PaginateCptArgs = { page: 1, limit: 10 };
      const result = await resolver.cpts(args);

      expect(result.code).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.message).toBe('An unexpected error occurred.');
      expect(result.type).toContain('_FETCH_ERROR');
      expect(result.data).toBeNull();
    });

    it('should handle items without toJSON method', async () => {
      const mockPaginatedData: PaginatedCptResponse = {
        items: [{
          _id: '1',
          id: '1',
          type: ClientType.MAIN_CLIENT,
          values: { key: 'value' },
          specialtyId: 'specialty1',
          diagnosisId: 'diagnosis1',
          cptCodeId: 'cptCode1',
          icdId: 'icd1',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        } as unknown as Cpt],
        pagination: {
          totalItems: 1,
          totalPages: 1,
          page: 1,
          limit: 10,
          hasNext: false,
          hasPrev: false
        } as CptPaginationInfo
      };

      mockCptService.findAll.mockResolvedValue(mockPaginatedData);

      const args: PaginateCptArgs = { page: 1, limit: 10 };
      const result = await resolver.cpts(args);

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.data?.items).toEqual([{
        _id: '1',
        id: '1',
        type: ClientType.MAIN_CLIENT,
        values: { key: 'value' },
        specialtyId: 'specialty1',
        diagnosisId: 'diagnosis1',
        cptCodeId: 'cptCode1',
        icdId: 'icd1',
        isActive: true,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date)
      }]);
    });
  });

  describe('countMasterCpts', () => {
    it('should return total count', async () => {
      mockCptService.count.mockResolvedValue(10);

      const result = await resolver.countMasterCpts();

      expect(result).toBe(10);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.count.mockRejectedValue(error);

      await expect(resolver.countMasterCpts()).rejects.toThrow('An unexpected error occurred.');
    });
  });

  describe('getMasterCptSortFields', () => {
    it('should return available sort fields', async () => {
      const mockFields = ['id', 'type', 'createdAt'];
      mockCptService.getAvailableSortFields.mockReturnValue(mockFields);

      const result = await resolver.getMasterCptSortFields();

      expect(result).toEqual(mockFields);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.getAvailableSortFields.mockRejectedValue(error as any);

      await expect(resolver.getMasterCptSortFields()).rejects.toThrow('An unexpected error occurred.');
    });
  });

  describe('getMasterCptSearchFields', () => {
    it('should return searchable fields', async () => {
      const mockFields = ['id', 'type', 'values'];
      mockCptService.getSearchableFields.mockReturnValue(mockFields);

      const result = await resolver.getMasterCptSearchFields();

      expect(result).toEqual(mockFields);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.getSearchableFields.mockRejectedValue(error as any);

      await expect(resolver.getMasterCptSearchFields()).rejects.toThrow('An unexpected error occurred.');
    });
  });

  describe('createCpt', () => {
    const mockContext = {
      req: {
        headers: {
          authorization: 'Bearer valid-token'
        }
      }
    };

    const mockCreateInput: CreateCptInput = {
      type: ClientType.MAIN_CLIENT,
      values: { key: 'value' },
      specialtyId: 'specialty1',
      diagnosisId: 'diagnosis1',
      cptCodeId: 'cptCode1',
      icdId: 'icd1',
      templateId: 'template1',
      isActive: true
    };

    beforeEach(() => {
      (jwt.verify as jest.Mock).mockReturnValue({ userId: 'user123' });
    });

    it('should create CPT with user ID from token', async () => {
      const mockSuccess = new Success(
        HttpStatus.CREATED,
        ResponseCode.CREATED,
        ErrorType.CPT,
        { message: 'CPT created successfully' }
      );

      mockCptService.create.mockResolvedValue(mockSuccess);

      const result = await resolver.createCpt(mockCreateInput, mockContext);

      expect(result).toBe(mockSuccess);
      expect(mockCptService.create).toHaveBeenCalledWith({
        ...mockCreateInput,
        createdBy: 'user123'
      });
    });

    it('should create CPT without user ID when no token', async () => {
      const contextWithoutToken = { req: { headers: {} } };
      const mockSuccess = new Success(
        HttpStatus.CREATED,
        ResponseCode.CREATED,
        ErrorType.CPT,
        { message: 'CPT created successfully' }
      );

      mockCptService.create.mockResolvedValue(mockSuccess);

      const result = await resolver.createCpt(mockCreateInput, contextWithoutToken);

      expect(result).toBe(mockSuccess);
      expect(mockCptService.create).toHaveBeenCalledWith(mockCreateInput);
    });

    it('should handle invalid token', async () => {
      (jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error(
          HttpStatus.UNAUTHORIZED,
          ResponseCode.TOKEN_INVALID,
          ErrorType.TOKEN,
          'Invalid token'
        );
      });

      const mockSuccess = new Success(
        HttpStatus.CREATED,
        ResponseCode.CREATED,
        ErrorType.CPT,
        { message: 'CPT created successfully' }
      );

      mockCptService.create.mockResolvedValue(mockSuccess);

      const result = await resolver.createCpt(mockCreateInput, mockContext);

      expect(result).toBe(mockSuccess);
      expect(mockCptService.create).toHaveBeenCalledWith(mockCreateInput);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.create.mockRejectedValue(error);

      await expect(resolver.createCpt(mockCreateInput, mockContext)).rejects.toThrow('An unexpected error occurred.');
    });
  });

  describe('updateCpt', () => {
    const mockUpdateInput: UpdateCptInput = {
      id: '1',
      type: ClientType.SUB_CLIENT,
      values: { key: 'updated' }
    };

    it('should update CPT successfully', async () => {
      const mockSuccess = new Success(
        HttpStatus.OK,
        ResponseCode.CREATED,
        ErrorType.CPT,
        { message: 'CPT updated successfully' }
      );

      mockCptService.update.mockResolvedValue(mockSuccess);

      const result = await resolver.updateCpt(mockUpdateInput);

      expect(result).toBe(mockSuccess);
      expect(mockCptService.update).toHaveBeenCalledWith(mockUpdateInput);
    });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.update.mockRejectedValue(error);

      await expect(resolver.updateCpt(mockUpdateInput)).rejects.toThrow('An unexpected error occurred.');
    });
  });

  describe('deleteCpt', () => {
    it('should delete CPT successfully', async () => {
      const mockSuccess = new Success(
        HttpStatus.OK,
        ResponseCode.CREATED,
        ErrorType.CPT,
        { message: 'CPT deleted successfully' }
      );

      mockCptService.delete.mockResolvedValue(mockSuccess);

      const result = await resolver.deleteCpt('1');

      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('CPT deleted successfully.');
      expect(result.type).toBe(mockSuccess.type);
      expect(result.data).toEqual(mockSuccess.data);
    });

    // it('should handle not found error', async () => {
    //   const error = new Error(
    //     HttpStatus.NOT_FOUND,
    //     ResponseCode.NOT_FOUND,
    //     ErrorType.CPT,
    //     'CPT not found'
    //   );
    //   mockCptService.delete.mockRejectedValue(error);

    //   const result = await resolver.deleteCpt('999');

    //   expect(result.code).toBe(HttpStatus.NOT_FOUND);
    //   expect(result.message).toBe('An unexpected error occurred.');
    //   expect(result.type).toContain('_DELETE_NOT_FOUND');
    //   expect(result.data).toBeNull();
    // });

    it('should handle service error', async () => {
      const error = new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Service error'
      );
      mockCptService.delete.mockRejectedValue(error);

      const result = await resolver.deleteCpt('1');

      expect(result.code).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.message).toBe('An unexpected error occurred.');
      expect(result.type).toContain('_DELETE_ERROR');
      expect(result.data).toBeNull();
    });
  });
}); 