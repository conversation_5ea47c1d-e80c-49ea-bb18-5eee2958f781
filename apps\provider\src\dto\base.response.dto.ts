import { Field, ObjectType, Directive, Int } from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';

@ObjectType()
@Directive('@key(fields: "message")')
export class BaseResponse {
  @Field()
  @Directive('@shareable')
  message: string;

  @Field()
  @Directive('@shareable')
  code: number;

  @Field()
  @Directive('@shareable')
  type: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Directive('@shareable')
  data?: Record<string, unknown>;
}

@ObjectType()
export class PaginatedResponse<T> {
  @Field(() => [GraphQLJSONObject])
  items: T[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  pages: number;
}
