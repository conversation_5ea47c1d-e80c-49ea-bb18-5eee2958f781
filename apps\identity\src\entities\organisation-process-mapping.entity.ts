import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true, collection: 'organisation_process_mappings' })
export class OrganisationProcessMapping extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String)
  @Prop({ type: String, required: true, ref: 'Organisation' })
  organisationId: string;

  @Field(() => String)
  @Prop({ type: String, required: true, ref: 'Organisation' })
  subOrganisationId: string;

  @Field(() => String)
  @Prop({ type: String, required: true, ref: 'Process' })
  processId: string;

  @Field()
  @Prop({ required: true })
  processName: string;

  @Field()
  @Prop({ default: true })
  isActive: boolean;
}

// Additional entities

@ObjectType()
@Schema({ timestamps: true, collection: 'organisations' })
export class Organisation extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  name: string;
}

@ObjectType()
@Schema({ timestamps: true, collection: 'sub_organisations' })
export class SubOrganisation extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  name: string;
}

@ObjectType()
@Schema({ timestamps: true, collection: 'processes' })
export class Process extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  name: string;
}

@ObjectType()
@Schema({ timestamps: true, collection: 'users' })
export class User extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  username: string;
}

export const OrganisationProcessMappingSchema = SchemaFactory.createForClass(OrganisationProcessMapping); 