import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { OrganisationUserService } from '../services/organisation-user.service';
import { CreateOrganisationUserInput, UpdateOrganisationUserInput, PaginateOrganisationUserArgs, OrganisationUserResponse, PaginationOrganisationUserResponse } from '../dto/organisation-user.dto';
import { RequirePermission, RequireConditionalPermission, createConditionalPermission, ConditionalPermissionsGuard } from '@app/permissions';

@Resolver(() => OrganisationUserResponse)
export class OrganisationUserResolver {
  constructor(private readonly organisationUserService: OrganisationUserService) {}

  @Mutation(() => OrganisationUserResponse)
  @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_ORGANISATION' || input.type === 'mainOrganisation',
      'Organizations',
      'Main Organization Settings',
      'Add Users'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_ORGANISATION' || input.type === 'subOrganisation',
      'Organizations',
      'Sub Organization Settings',
      'Add Users'
    )
  )
  async createOrganisationUser(
    @Args('input') input: CreateOrganisationUserInput,
    @Context() context: any
  ): Promise<OrganisationUserResponse> {
    const orgId = context?.orgId;
    const user = await this.organisationUserService.create(input, orgId);
    return this.mapUser(user);
  }

  @Query(() => PaginationOrganisationUserResponse, { name: 'organisationUsers' })
  @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (args) => args.type === 'MAIN_ORGANISATION' || args.type === 'mainOrganisation' || args.organisationId?.includes('main'),
      'Organizations',
      'Main Organization Settings',
      'View'
    ),
    createConditionalPermission(
      (args) => args.type === 'SUB_ORGANISATION' || args.type === 'subOrganisation' || (!args.type && !args.organisationId?.includes('main')),
      'Organizations',
      'Sub Organization Settings',
      'View'
    )
  )
  async organisationUsers(@Args() args: PaginateOrganisationUserArgs): Promise<PaginationOrganisationUserResponse> {
    const result = await this.organisationUserService.findAll(args);
    result.items = result.items.map((u: any) => this.mapUser(u));
    return result;
  }

  @Query(() => OrganisationUserResponse, { name: 'organisationUser', nullable: true })
  // @RequirePermission(
  //   { module: 'Organizations', subModule: 'Main Organization Settings', permission: 'View Users' },
  //   { module: 'Organizations', subModule: 'Sub Organization Settings', permission: 'View Users' }
  // )
  async organisationUser(@Args('id') id: string): Promise<OrganisationUserResponse | null> {
    const user = await this.organisationUserService.findOne(id);
    return user ? this.mapUser(user) : null;
  }

  @Mutation(() => OrganisationUserResponse, { nullable: true })
  @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_ORGANISATION' || input.type === 'mainOrganisation',
      'Organizations',
      'Main Organization Settings',
      'Update Users'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_ORGANISATION' || input.type === 'subOrganisation',
      'Organizations',
      'Sub Organization Settings',
      'Update Users'
    )
  )
  async updateOrganisationUser(
    @Args('input') input: UpdateOrganisationUserInput,
    @Context() context: any
  ): Promise<OrganisationUserResponse | null> {
    const orgId = context?.orgId;
    const user = await this.organisationUserService.update(input, orgId);
    return user ? this.mapUser(user) : null;
  }

  @Mutation(() => OrganisationUserResponse, { nullable: true })
   @UseGuards(ConditionalPermissionsGuard)
  @RequireConditionalPermission(
    createConditionalPermission(
      (input) => input.type === 'MAIN_ORGANISATION' || input.type === 'mainOrganisation',
      'Organizations',
      'Main Organization Settings',
      'Remove Users'
    ),
    createConditionalPermission(
      (input) => input.type === 'SUB_ORGANISATION' || input.type === 'subOrganisation',
      'Organizations',
      'Sub Organization Settings',
      'Remove Users'
    )
  )
  async removeOrganisationUser(
    @Args('id') id: string,
    @Args('type') type: string
  ): Promise<OrganisationUserResponse | null> {
    const user = await this.organisationUserService.remove(id);
    return user ? this.mapUser(user) : null;
  }

  private mapUser(user: any): OrganisationUserResponse {
    if (!user) return user;
    const obj = user.toObject ? user.toObject() : { ...user };
    obj._id = obj._id ? obj._id.toString() : obj._id;
    if ('roleId' in obj && typeof obj.roleId === 'string' && 'roleName' in obj) {
      // Already mapped by service
      // do nothing, use as is
    } else {
      const populatedRole = obj.roleId && typeof obj.roleId === 'object' && obj.roleId.name ? obj.roleId : null;
      obj.roleId = populatedRole ? populatedRole._id.toString() : (typeof obj.roleId === 'string' ? obj.roleId : '');
      obj.roleName = populatedRole ? populatedRole.name : '';
    }
    if (obj.createdAt) obj.createdAt = new Date(obj.createdAt).toISOString();
    if (obj.updatedAt) obj.updatedAt = new Date(obj.updatedAt).toISOString();
    delete obj.id;
    return obj;
  }
} 