import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { BaseEntity } from './base.entity';

export enum ClientType {
  MAIN_CLIENT = 'MAIN_CLIENT',
  SUB_CLIENT = 'SUB_CLIENT',
  PAYER = 'PAYER',
  ACTION_CODE = 'ACTION_CODE',
}

// Register the enum with GraphQL
registerEnumType(ClientType, {
  name: 'ClientType',
  description: 'The type of client template',
});

@ObjectType()
@Schema({ timestamps: true })
export class Template extends BaseEntity {
  @Field()
  @Prop({ required: true, })
  key: string;

  @Field()
  @Prop({ required: true, })
  name: string;

  @Field(() => Int)
  @Prop({ default: 0 })
  version: number;


  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object, default: { in_grid: [], default: [] } })
  view_summary: {
    in_grid: string[];
    default: string[];
  };

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object, default: {} })
  fields: Record<string, any>;

  @Field({ nullable: true })
  @Prop()
  description?: string;

  @Field({ nullable: true })
  @Prop()
  type?: string;

  @Field({ nullable: true })
  @Prop()
  organisationId?: string;

  @Field({ nullable: true })
  @Prop()
  subOrganisationId?: string;

  // @Field({ nullable: true })
  // @Prop({ default: false })
  // useTemplate?: boolean;

  @Field({ nullable: true })
  @Prop()
  docType?: string;
}

export const TemplateSchema = SchemaFactory.createForClass(Template);


