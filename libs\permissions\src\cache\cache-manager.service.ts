import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisCacheService } from './redis-cache.service';
import { PermissionCacheData } from './redis-cache.service';
import { REDIS_CACHE_SETTINGS } from './redis.config';

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
  averageResponseTime: number;
  lastUpdated: Date;
}

export interface UserCacheEntry {
  userId: string;
  orgId: string;
  subOrgId: string;
  processId: string;
  lastAccessed: Date;
  accessCount: number;
}

@Injectable()
export class CacheManagerService implements OnModuleInit {
  private readonly logger = new Logger(CacheManagerService.name);
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    averageResponseTime: 0,
    lastUpdated: new Date(),
  };
  
  private recentAccess = new Map<string, UserCacheEntry>();
  private warmupInProgress = false;

  constructor(
    private readonly redisCache: RedisCacheService,
    private readonly configService: ConfigService
  ) {}

  async onModuleInit() {
    // Start cache monitoring
    this.startCacheMonitoring();
    
    // Schedule cache warmup if enabled
    if (this.configService.get<boolean>('CACHE_WARMUP_ENABLED', true)) {
      setTimeout(() => this.warmupFrequentlyAccessedUsers(), 5000);
    }
  }

  async getUserPermissionWithStats(
    userId: string,
    orgId: string,
    subOrgId: string,
    processId: string
  ): Promise<PermissionCacheData | null> {
    const startTime = Date.now();
    const cacheKey = `${userId}:${orgId}:${subOrgId}:${processId}`;
    
    try {
      const result = await this.redisCache.getUserPermission(userId, orgId, subOrgId, processId);
      const responseTime = Date.now() - startTime;
      
      // Update statistics
      this.updateStats(result !== null, responseTime);
      
      // Track user access patterns
      this.trackUserAccess(userId, orgId, subOrgId, processId);
      
      if (result) {
        this.logger.debug(`Cache hit for ${cacheKey} in ${responseTime}ms`);
      } else {
        this.logger.debug(`Cache miss for ${cacheKey} in ${responseTime}ms`);
      }
      
      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateStats(false, responseTime);
      this.logger.error(`Cache error for ${cacheKey}: ${error.message}`);
      return null;
    }
  }

  async cacheUserPermissionWithStats(
    userId: string,
    orgId: string,
    subOrgId: string,
    processId: string,
    data: PermissionCacheData,
    ttl?: number
  ): Promise<void> {
    try {
      await this.redisCache.cacheUserPermission(userId, orgId, subOrgId, processId, data, ttl);
      this.logger.debug(`Cached permission for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to cache permission for user ${userId}: ${error.message}`);
    }
  }

  async invalidateUserCache(userId: string): Promise<void> {
    try {
      await this.redisCache.invalidateUserPermissions(userId);
      this.recentAccess.delete(userId);
      this.logger.log(`Invalidated cache for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to invalidate cache for user ${userId}: ${error.message}`);
    }
  }

  async warmupFrequentlyAccessedUsers(): Promise<void> {
    if (this.warmupInProgress) {
      this.logger.debug('Cache warmup already in progress');
      return;
    }

    this.warmupInProgress = true;
    this.logger.log('Starting cache warmup for frequently accessed users');

    try {
      // Get most frequently accessed users from the last hour
      const frequentUsers = Array.from(this.recentAccess.values())
        .filter(entry => Date.now() - entry.lastAccessed.getTime() < 3600000) // 1 hour
        .sort((a, b) => b.accessCount - a.accessCount)
        .slice(0, REDIS_CACHE_SETTINGS.PERFORMANCE.BATCH_SIZE);

      this.logger.log(`Warming up cache for ${frequentUsers.length} frequently accessed users`);

      // Note: In a real implementation, you would fetch fresh data from the database
      // and cache it. This is just a placeholder for the warmup logic.
      for (const user of frequentUsers) {
        try {
          // Check if cache exists and is still valid
          const cached = await this.redisCache.getUserPermission(
            user.userId,
            user.orgId,
            user.subOrgId,
            user.processId
          );
          
          if (!cached) {
            this.logger.debug(`Cache miss during warmup for user ${user.userId} - would refresh from DB`);
            // Here you would typically fetch from database and cache
          }
        } catch (error) {
          this.logger.warn(`Failed to warmup cache for user ${user.userId}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Cache warmup failed: ${error.message}`);
    } finally {
      this.warmupInProgress = false;
    }
  }

  getCacheStats(): CacheStats {
    return { ...this.stats };
  }

  async getCacheHealth(): Promise<{ healthy: boolean; details: any }> {
    try {
      const isHealthy = await this.redisCache.isHealthy();
      const stats = await this.redisCache.getCacheStats();
      
      return {
        healthy: isHealthy,
        details: {
          redis: isHealthy ? 'Connected' : 'Disconnected',
          stats: this.stats,
          redisStats: stats,
          recentAccessCount: this.recentAccess.size,
        },
      };
    } catch (error) {
      return {
        healthy: false,
        details: {
          error: error.message,
          stats: this.stats,
        },
      };
    }
  }

  private updateStats(isHit: boolean, responseTime: number): void {
    this.stats.totalRequests++;
    
    if (isHit) {
      this.stats.hits++;
    } else {
      this.stats.misses++;
    }
    
    this.stats.hitRate = (this.stats.hits / this.stats.totalRequests) * 100;
    
    // Update average response time (simple moving average)
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / 
      this.stats.totalRequests;
    
    this.stats.lastUpdated = new Date();
  }

  private trackUserAccess(userId: string, orgId: string, subOrgId: string, processId: string): void {
    const key = `${userId}:${orgId}:${subOrgId}:${processId}`;
    const existing = this.recentAccess.get(key);
    
    if (existing) {
      existing.lastAccessed = new Date();
      existing.accessCount++;
    } else {
      this.recentAccess.set(key, {
        userId,
        orgId,
        subOrgId,
        processId,
        lastAccessed: new Date(),
        accessCount: 1,
      });
    }
    
    // Clean up old entries (older than 24 hours)
    const cutoff = Date.now() - 86400000; // 24 hours
    for (const [key, entry] of this.recentAccess.entries()) {
      if (entry.lastAccessed.getTime() < cutoff) {
        this.recentAccess.delete(key);
      }
    }
  }

  private startCacheMonitoring(): void {
    setInterval(() => {
      const stats = this.getCacheStats();
      
      if (stats.hitRate < REDIS_CACHE_SETTINGS.MONITORING.ALERT_THRESHOLDS.HIT_RATE_MIN) {
        this.logger.warn(`Low cache hit rate: ${stats.hitRate.toFixed(2)}%`);
      }
      
      if (stats.averageResponseTime > REDIS_CACHE_SETTINGS.MONITORING.ALERT_THRESHOLDS.RESPONSE_TIME_MAX) {
        this.logger.warn(`High average response time: ${stats.averageResponseTime.toFixed(2)}ms`);
      }
      
      this.logger.debug(`Cache stats - Hit rate: ${stats.hitRate.toFixed(2)}%, Avg response: ${stats.averageResponseTime.toFixed(2)}ms`);
    }, REDIS_CACHE_SETTINGS.MONITORING.HEALTH_CHECK_INTERVAL);
  }
}
