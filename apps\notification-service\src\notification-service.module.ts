import { Modu<PERSON> } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloFederationDriver, ApolloFederationDriverConfig } from '@nestjs/apollo';
import { getModelToken, MongooseModule } from '@nestjs/mongoose';
import { NotificationServiceController } from './notification-service.controller';
import { KafkaConsumerService } from './notification-service.service';
import { NotificationHistoryResolver } from './resolvers/notification-history.resolver';
import { NotificationHistoryService } from './services/notification-history.service';
import { NotificationHistory, NotificationHistorySchema } from './entities/notification-history.entity';
import { NotificationModule } from '@app/notification';
import { AuditService } from './services/audit.service';
import { AuditResolver } from './resolvers/audit.resolver';
import { AuditLog, AuditLogSchema } from './entities/audit-log.entity';
import { AuditController } from './controllers/audit.controller';

import {
  PermissionsGuard,
  UserProcessAssignment,
  UserProcessAssignmentSchema,
  Role,
  RoleSchema,
  PermissionsModule,
  OrganisationRole,
  OrganisationRoleSchema,
  RedisCacheService
} from '@app/permissions';
import { Reflector, APP_GUARD } from '@nestjs/core';
import { DatabaseModule, MongoConnectionService } from '@app/db';
import { EmailModule } from '@app/email';

const permissionsGuardFactory = {
  provide: PermissionsGuard,
  useFactory: (
    reflector: Reflector,
    UserProcessAssignmentModel: any,
    RoleModel: any,
    OrganisationRoleModel: any,
    cacheService: RedisCacheService,
    mongoConnectionService: MongoConnectionService
  ) => {


    return new PermissionsGuard(
      reflector,
      async (userId, orgId, subOrgId, processId) => {
        console.log(`🔍 [getAssignment-notification] Query params:`, { userId, orgId, subOrgId, processId });

        const result = await UserProcessAssignmentModel.findOne({
          userId,
          orgId,
          subOrganisationId: subOrgId,
          processId,
        }).populate('roleId', 'name permissions key category');

        console.log(`🔍 [getAssignment-notification] Query result:`, result ? {
          _id: result._id,
          userId: result.userId,
          roleId: result.roleId,
          orgId: result.orgId,
          subOrganisationId: result.subOrganisationId,
          processId: result.processId
        } : 'No assignment found');

        return result;
      },
      {
        getSystemRole: async (roleId) => {

          const role = await RoleModel.findById(roleId);

          return role;
        },
        getOrganisationRole: async (roleId) => {

          const orgRole = await OrganisationRoleModel.findById(roleId);

          return orgRole;
        },
      },
      process.env.JWT_SECRET ?? 'fallback-secret',
      cacheService,
      mongoConnectionService
    );
  },
  inject: [
    Reflector,
    getModelToken(UserProcessAssignment.name),
    getModelToken(Role.name),
    getModelToken(OrganisationRole.name),
    RedisCacheService,
    MongoConnectionService,
  ],
};

@Module({
  imports: [
    DatabaseModule,
    EmailModule,
    NotificationModule,
    PermissionsModule,
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/asp', {
      connectionFactory: (connection) => {
        connection.on('connected', () => {
          console.log('[Mongoose] ✅ Connected to MongoDB');
        });
        connection.on('error', (error) => {
          console.error('[Mongoose] ❌ Connection error:', error);
        });
        connection.on('disconnected', () => {
          console.log('[Mongoose] ⚠️ Disconnected from MongoDB');
        });
        return connection;
      },
    }),
    MongooseModule.forFeature([
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },

    ]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req, res }) => {
        // Create base context that will be enhanced by guards
        const context = {
          req,
          res,
          query: req?.body?.query,
          orgId: req?.headers['orgid'],
          userId: req?.headers['userid'],
          subOrgId: req?.headers['suborgid'],
          processId: req?.headers['processid'],
          roleName: req?.headers['rolename'],
        };

        return context;
      }
    }),
  ],
  controllers: [NotificationServiceController],
  providers: [
    permissionsGuardFactory,
    KafkaConsumerService,
    NotificationHistoryService,
    NotificationHistoryResolver,
    {
      provide: APP_GUARD,
      useExisting: PermissionsGuard,
    },
  ],
})
export class ProviderModule {
  constructor() {
    console.log('[ProviderModule] 🚀 Module loaded');
  }
}

@Module({
  imports: [
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/asp', {
      connectionFactory: (connection) => {
        connection.on('connected', () => {
          console.log('[Notification Service] ✅ Connected to MongoDB');
        });
        connection.on('error', (error) => {
          console.error('[Notification Service] ❌ Connection error:', error);
        });
        connection.on('disconnected', () => {
          console.log('[Notification Service] ⚠️ Disconnected from MongoDB');
        });
        return connection;
      },
    }),
    MongooseModule.forFeature([
      { name: NotificationHistory.name, schema: NotificationHistorySchema },
      { name: AuditLog.name, schema: AuditLogSchema }
    ]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req, res }) => {
        const context = {
          req,
          res,
          query: req?.body?.query,
          orgId: req?.headers['orgid'],
          userId: req?.headers['userid'],
          subOrgId: req?.headers['suborgid'],
          processId: req?.headers['processid'],
          roleName: req?.headers['rolename'],
        };
        return context;
      }
    }),
    NotificationModule,
  ],
  controllers: [NotificationServiceController, AuditController],
  providers: [
    KafkaConsumerService,
    NotificationHistoryService,
    NotificationHistoryResolver,
    AuditService,
    AuditResolver,
  ],
})
export class NotificationServiceModule { }
