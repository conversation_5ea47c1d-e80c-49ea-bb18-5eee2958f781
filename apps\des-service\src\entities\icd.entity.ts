import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSONObject } from 'graphql-type-json';
import { BaseEntity } from './base.entity';
import { ClientType } from './template.entity';
import { Diagnosis } from './diagnosis.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'icds',
  strict: false,

})
export class Icd extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String)
  @Prop({ required: true })
  code: string;

  // @Field(() => [Diagnosis], { nullable: true })
  // @Prop({ type: [{ type: Types.ObjectId, ref: 'Diagnosis' }], default: [] })
  // diagnoses: Types.ObjectId[] | Diagnosis[];

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Prop({ type: Object, default: {} })
  values: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @Prop({ index: true })
  templateId?: string;
  
  @Field(() => Boolean)
  @Prop({ default: true })
  isActive: boolean;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  updatedBy?: string;
}

export const IcdSchema = SchemaFactory.createForClass(Icd);

// Add text indexes for full-text search
IcdSchema.index({
  code: 'text',
  'values.description': 'text'
}, {
  weights: {
    code: 10,
    'values.description': 5
  },
  name: "IcdSearchIndex"
});

// Add indexes for common queries
IcdSchema.index({ createdAt: -1 });
IcdSchema.index({ updatedAt: -1 });
IcdSchema.index({ code: 1 }, { unique: true }); 