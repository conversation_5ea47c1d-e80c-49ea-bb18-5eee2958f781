import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  NotificationPayload,
  NotificationTemplate,
  TaskNotificationData,
  WebSocketNotification
} from './interfaces/notification.interface';
import {
  NotificationType,
  NotificationChannel,
  NotificationPriority,
  NotificationStatus
} from './enums/notification.enum';
import { FirebasePushService } from './firebase-push.service';
import { BaseDatabaseService } from '@app/common';
import { MongoConnectionService } from '@app/db';
import { NotificationEntity } from './entities/notification.entity';

@Injectable()
export class NotificationService extends BaseDatabaseService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly templates: Map<NotificationType, NotificationTemplate> = new Map();


  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly firebasePushService: FirebasePushService,
    mongoConnectionService: MongoConnectionService
  ) {
    super(mongoConnectionService);
    this.initializeTemplates();
  }

  private initializeTemplates() {
    // Import notification templates
    this.templates.set(NotificationType.IMPORT_COMPLETED, {
      type: NotificationType.IMPORT_COMPLETED,
      title: 'Import Completed Successfully',
      message: 'Your import task for {collectionName} has been completed successfully. {recordCount} records processed.',
      channels: [NotificationChannel.WEBSOCKET],
      priority: NotificationPriority.MEDIUM
    });

    this.templates.set(NotificationType.IMPORT_FAILED, {
      type: NotificationType.IMPORT_FAILED,
      title: 'Import Task Failed',
      message: 'Your import task for {collectionName} has failed. Error: {errorMessage}',
      channels: [NotificationChannel.WEBSOCKET],
      priority: NotificationPriority.HIGH
    });

    // Export notification templates
    this.templates.set(NotificationType.EXPORT_COMPLETED, {
      type: NotificationType.EXPORT_COMPLETED,
      title: 'Export Completed Successfully',
      message: 'Your export task for {collectionName} has been completed. Download is ready.',
      channels: [NotificationChannel.WEBSOCKET],
      priority: NotificationPriority.MEDIUM
    });

    this.templates.set(NotificationType.EXPORT_FAILED, {
      type: NotificationType.EXPORT_FAILED,
      title: 'Export Task Failed',
      message: 'Your export task for {collectionName} has failed. Error: {errorMessage}',
      channels: [NotificationChannel.WEBSOCKET],
      priority: NotificationPriority.HIGH
    });

    this.templates.set(NotificationType.TASK_STARTED, {
      type: NotificationType.TASK_STARTED,
      title: 'Task Started',
      message: 'Your {taskType} task for {collectionName} has started processing.',
      channels: [NotificationChannel.WEBSOCKET],
      priority: NotificationPriority.LOW
    });
  }

  async sendTaskNotification(data: TaskNotificationData): Promise<void> {
    try {
      this.logger.log(`🔔 Preparing task notification for user ${data.userId}, task: ${data.taskId}, status: ${data.status}`);

      const notificationType = this.getNotificationTypeFromTask(data);
      const template = this.templates.get(notificationType);

      if (!template) {
        this.logger.warn(`No template found for notification type: ${notificationType}`);
        return;
      }

      this.logger.log(`📝 Using template: ${notificationType}, channels: ${template.channels.join(', ')}`);

      const payload: NotificationPayload = {
        type: notificationType,
        title: this.interpolateTemplate(template.title, data),
        message: this.interpolateTemplate(template.message, data),
        userId: data.userId,
        userEmail: data.userEmail,
        channels: template.channels,
        priority: template.priority,
        data: {
          taskId: data.taskId,
          taskType: data.taskType,
          status: data.status
        },
        metadata: {
          taskId: data.taskId,
          taskType: data.taskType,
          collectionName: data.collectionName,
          recordCount: data.recordCount,
          failedCount: data.failedCount,
          insertedCount: data.insertedCount,
          updatedCount: data.updatedCount,
          downloadUrl: data.downloadUrl,
          errorMessage: data.errorMessage
        },
        senderId: ''
      };

      this.logger.log(`📤 Sending notification: "${payload.title}" to user ${payload.userId}`);
      await this.sendNotification(payload);
      this.logger.log(`✅ Task notification sent successfully`);
    } catch (error) {
      this.logger.error(`❌ Failed to send task notification: ${error.message}`, error.stack);
    }
  }

  async sendNotification(payload: NotificationPayload): Promise<void> {
    try {
      const channels = payload.channels || [NotificationChannel.WEBSOCKET];
      const notificationId = this.generateId();

      // Send to each specified channel
      for (const channel of channels) {
        switch (channel) {
          case NotificationChannel.WEBSOCKET:
            await this.sendWebSocketNotification(payload, notificationId);
            break;
          case NotificationChannel.IN_APP:
            await this.saveInAppNotification(payload);
            break;
          default:
            this.logger.warn(`Unsupported notification channel: ${channel}`);
        }
      }
      this.logger.log(`Push notification sent successfully to user ${payload.userId} with ID ${notificationId}`);
    } catch (error) {
      this.logger.error(`Failed to send push notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async sendWebSocketNotification(payload: NotificationPayload, notificationId?: string): Promise<void> {
    const wsNotification: WebSocketNotification = {
      id: notificationId || this.generateId(),
      type: payload.type,
      title: payload.title,
      message: payload.message,
      priority: payload.priority || NotificationPriority.MEDIUM,
      data: payload.data,
      metadata: payload.metadata,
      timestamp: new Date()
    };

    this.logger.log(`🌐 Emitting WebSocket notification event for user ${payload.userId}`);

    // Emit event for WebSocket gateway to handle
    this.eventEmitter.emit('notification.websocket', {
      userId: payload.userId,
      notification: wsNotification
    });

    this.logger.log(`📡 WebSocket notification event emitted: ${wsNotification.id}`);
  }

  // Email notifications removed - using only push/app notifications

  private async saveInAppNotification(payload: NotificationPayload): Promise<void> {
    // Emit event for database service to save in-app notification
    this.eventEmitter.emit('notification.inapp', {
      userId: payload.userId,
      type: payload.type,
      title: payload.title,
      message: payload.message,
      priority: payload.priority,
      data: payload.data,
      metadata: payload.metadata
    });
  }

  private getNotificationTypeFromTask(data: TaskNotificationData): NotificationType {
    const { taskType, status } = data;

    if (taskType === 'import') {
      switch (status) {
        case 'COMPLETED':
          return NotificationType.IMPORT_COMPLETED;
        case 'FAILED':
          return NotificationType.IMPORT_FAILED;
        case 'STARTED':
        case 'IN_PROGRESS':
          return NotificationType.TASK_STARTED;
        default:
          return NotificationType.CUSTOM;
      }
    } else if (taskType === 'export') {
      switch (status) {
        case 'COMPLETED':
          return NotificationType.EXPORT_COMPLETED;
        case 'FAILED':
          return NotificationType.EXPORT_FAILED;
        case 'STARTED':
        case 'IN_PROGRESS':
          return NotificationType.TASK_STARTED;
        default:
          return NotificationType.CUSTOM;
      }
    }

    return NotificationType.CUSTOM;
  }

  private interpolateTemplate(template: string, data: any): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return data[key] !== undefined ? String(data[key]) : match;
    });
  }

  // Email HTML generation removed - using only push/app notifications

  private generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Send a Firebase push notification to a device token
   * @param deviceToken string - FCM device token
   * @param title string - Notification title
   * @param body string - Notification body
   * @param data Record<string, string> - Optional custom data
   */

  async notifyUserWithFirebasePush(
    deviceTokens: string[] | string,
    title: string,
    body: string,
    data?: Record<string, any>,
    metadata?: Record<string, any>
  ) {
    const tokensArray = Array.isArray(deviceTokens) ? deviceTokens : [deviceTokens];

    const validTokens = tokensArray.filter(token => typeof token === 'string' && token.trim() !== '');

    if (validTokens.length === 0) {
      this.logger.warn('No valid device tokens provided for push notification.');
      return;
    }

    const stringifiedData: Record<string, string> = {};
    // Stringify `data`
    if (data) {
      Object.entries(data).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          stringifiedData[key] = String(value);
        }
      });
    }

    // Stringify `metadata` with a `meta_` prefix to distinguish
    if (metadata) {
      Object.entries(metadata).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          stringifiedData[`meta_${key}`] = String(value);
        }
      });
    }

    const payload = {
      notification: {
        title,
        body,
        type: data?.type !== undefined ? String(data.type) : undefined,
      },
      data: stringifiedData,
    };

    console.log(`🔥 DEBUG: Firebase payload prepared:`, {
      title,
      body,
      dataKeys: Object.keys(stringifiedData),
      tokenCount: validTokens.length,
    });

    const promises = validTokens.map((token) =>
      this.firebasePushService.sendPushNotification(token, payload)
    );

    await Promise.all(promises);
  }



  /**
   * Store notification in MongoDB using NotificationEntity
   */
  async saveNotificationToDb(
    payload: NotificationPayload,
    notificationId: string,
    orgId?: string
  ): Promise<Object | null> {
    try {
      const now = new Date();
      const notification: NotificationEntity = {
        senderId: payload.senderId,
        notificationId,
        userId: payload.userId,
        userEmail: payload.userEmail,
        type: payload.type,
        title: payload.title,
        message: payload.message,
        channels: payload.channels || [NotificationChannel.IN_APP],
        priority: payload.priority || NotificationPriority.MEDIUM,
        status: NotificationStatus.SENT,
        data: payload.data,
        metadata: payload.metadata,
        sentAt: now,
        isRead: false,
        createdAt: now,
        updatedAt: now,
        orgId: payload.metadata?.orgId,
        subOrgId: payload.metadata?.subOrgId,
        processId: payload.metadata?.processId,
        isRouted: payload.isRouted || false,
      };

      const collection = await this.getMasterCollection<NotificationEntity>('notification_history');
      const { insertedId } = await collection.insertOne(notification);
      this.logger.log(`✅ Notification stored in MongoDB: ${insertedId}`);
      return insertedId;
    } catch (error) {
      this.logger.error(`❌ Failed to store notification in MongoDB: ${error.message}`, error.stack);
      return null;
    }
  }


}
