import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';

// Define UserType Enum
export enum UserType {
  MAIN_CLIENT = 'MAIN_CLIENT',
  SUB_CLIENT = 'SUB_CLIENT',
  PAYER = 'PAYER',
  ACTION_CODE = 'ACTION_CODE',
  AGENT = 'AGENT',
}

// Define Client Status Enum
export enum ClientStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
  TERMINATED = 'TERMINATED',
}

registerEnumType(UserType, {
  name: 'UserType',
});

registerEnumType(ClientStatus, {
  name: 'ClientStatus',
});

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'organisations',
  toJSON: {
    virtuals: true,
    transform: function (doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
@Schema({ timestamps: true, strict: false })
export class Organisation extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  // Basic Information
  @Field()
  @Prop({ required: true, index: true })
  name: string;

  @Field()
  @Prop({ required: true, unique: true, index: true })
  email: string;

  @Field({ nullable: true })
  @Prop({ index: true })
  templateId?: string;

  @Field(() => UserType)
  @Prop({ type: String, enum: UserType, required: true })
  type: UserType;

  @Field(() => GraphQLJSON)
  @Prop({
    type: Object,
    required: true,
  })
  values: Record<string, any>;

  @Field(() => ClientStatus)
  @Prop({ enum: ClientStatus, default: ClientStatus.PENDING })
  status: ClientStatus;

  // Relationships
  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'Client' })
  main_client?: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'Agent' })
  created_by?: Types.ObjectId;

  @Field({ nullable: true, defaultValue: false })
  @Prop()
  is2FAenabled?: boolean;

  @Field({ nullable: true, defaultValue: false })
  @Prop()
  bypass2FA?: boolean;

  @Field({ nullable: true })
  @Prop()
  secretKey?: string;

  @Field({ nullable: true })
  @Prop()
  qrcode?: string;

  @Field({ nullable: true })
  @Prop()
  attempt?: number;

  @Field({ nullable: true })
  @Prop()
  dailyOtpAttempts?: number;

  @Field({ nullable: true })
  @Prop()
  lastOtpAttemptDate?: Date;

  @Prop()
  token?: string;

  @Field({ nullable: true })
  @Prop({ index: true })
  orgId?: string;
}

export const OrganisationSchema = SchemaFactory.createForClass(Organisation);

