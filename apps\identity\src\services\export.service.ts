import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import { Model, Connection } from 'mongoose';
import { TaskQueue } from '../entities/task.entity';
import { UserService } from './user.service';
import { NotificationService, NotificationType, NotificationPriority, NotificationChannel, NotificationPayload } from '@app/notification';
import { AuditHelperService } from '@app/audit';
import { KafkaService } from '@app/email';
import { ObjectId } from 'mongodb';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { GcsService } from './gcs.service';
import mongoose from 'mongoose';
import { MongoConnectionService } from '@app/db';
import { Parser } from 'json2csv';
import * as ExcelJS from 'exceljs';
import { ModuleUrlService } from './module-url.service';

@Injectable()
export class ExportService {
  constructor(
    @InjectModel(TaskQueue.name) private taskModel: Model<TaskQueue>,
    @InjectQueue('task-queue{identity}') private taskQueue: Queue, // Unified queue
    @InjectConnection() private readonly connection: Connection,
    private readonly mongoConnectionService: MongoConnectionService,
    private readonly gcsService: GcsService,
    private readonly userService: UserService,
    private readonly notificationService: NotificationService,
    private readonly auditHelperService: AuditHelperService,
    private readonly kafkaService: KafkaService,
    private readonly moduleUrlService: ModuleUrlService,
  ) { }

  /**
   * Get expiration times from module URL configuration
   */
  private async getExpirationTimes(collectionName: string): Promise<{
    importUrl?: Date;
    backupUrl?: Date;
    exportUrl?: Date;
    auditUrl?: Date;
  }> {
    try {
      // Find module URL configuration for this collection
      const moduleUrlConfig = await this.moduleUrlService.findByCollectionName(collectionName);

      if (!moduleUrlConfig) {
        console.warn(`No module URL configuration found for collection: ${collectionName}`);
        return {};
      }

      const now = new Date();
      const expires: any = {};

      // Calculate expiration dates based on days from module configuration
      if (moduleUrlConfig.importUrlTime && moduleUrlConfig.importUrlTime > 0) {
        expires.importUrl = new Date(now.getTime() + (moduleUrlConfig.importUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.backupUrlTime && moduleUrlConfig.backupUrlTime > 0) {
        expires.backupUrl = new Date(now.getTime() + (moduleUrlConfig.backupUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.exportUrlTime && moduleUrlConfig.exportUrlTime > 0) {
        expires.exportUrl = new Date(now.getTime() + (moduleUrlConfig.exportUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.auditUrlTime && moduleUrlConfig.auditUrlTime > 0) {
        expires.auditUrl = new Date(now.getTime() + (moduleUrlConfig.auditUrlTime * 24 * 60 * 60 * 1000));
      }

      console.log(`📅 Expiration times calculated for ${collectionName}:`, expires);
      return expires;
    } catch (error) {
      console.error(`❌ Failed to get expiration times for ${collectionName}:`, error);
      return {};
    }
  }

  async createExportTask(body: any): Promise<string> {
    const taskId = `Exp-${Math.floor(100000 + Math.random() * 900000)}`;

    // Get expiration times from module URL configuration
    const expires = await this.getExpirationTimes(body.collection);

    // Create the export task
    const createdTask = await this.taskModel.create({
      taskId,
      type: 'export',
      name: `Export Task - ${body.collection}`,
      createdBy: body.createdBy,
      createdByEmail: body.createdByEmail,
      collectionName: body.collection,
      fields: body.fields,
      fileType: body.fileType || 'CSV',
      status: 'NOT_STARTED',
      orgId: body.orgId || null,
      selectedRow: body.selectedRow || null,
      filters: body.filters || null, // Add filters support
      sortBy: body.sortBy || null,
      sortOrder: body.sortOrder || null,
      expires, // Expiration times from module URL configuration
      statusLog: [{
        status: 'SCHEDULED',
        message: 'Task scheduled',
        timestamp: new Date(),
      }],
    });

    // Enqueue the export job in BullMQ first (most important)
    await this.taskQueue.add('export', { taskId });

    // Send notification and audit log asynchronously (non-blocking)
    // Don't await these to prevent hanging the API response
    this.sendExportNotification(taskId, 'CREATED').catch(error => {
      console.error(`Failed to send export notification for task ${taskId}:`, error);
    });

    this.logExportAuditEvent(taskId, 'CREATE', null, createdTask.toObject(), body.createdBy, body.createdByEmail).catch(error => {
      console.error(`Failed to log export audit event for task ${taskId}:`, error);
    });

    return taskId;
  }

  async getExportStatus(taskId: string) {
    const task = await this.taskModel.findOne({ taskId }).lean();
    if (!task) throw new NotFoundException('Task not found');
    if (task.createdBy) {
      try {
        const user = await this.userService.findOne(task.createdBy);
        if (user && user.email) {
          return {
            ...task,
            createdBy: user.email
          };
        }
      } catch (error) {
        // If user not found, keep the original createdBy value
        console.warn(`User not found for ID: ${task.createdBy}`);
      }
    }

    return task;

  }
  async getAllExports(params: {
    page?: number;
    limit?: number;
    search?: string;
    filters?: Record<string, any>;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    selectedFields?: Record<string, number>;
    userId: string | undefined
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      filters = {},
      sortBy,
      sortOrder = 'asc',
      selectedFields,
      userId
    } = params || {};
    const query: any = { createdBy: userId };

    // Get a sample document to extract dynamic fields
    const sampleDoc = await this.taskModel.findOne().lean();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

    // Field Selection Logic
    // let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    // if (selectedFields && Object.keys(selectedFields).length > 0) {
    //   projection = selectedFields;
    //   // Restrict search to only name, collectionName, and createdBy
    //   searchableFields = ['name', 'collectionName', 'createdBy'];
    // } else {
    //   const excluded = ['__v', 'file'];
    //   projection = allFields.reduce((acc, field) => {
    //     if (!excluded.includes(field)) acc[field] = 1;
    //     return acc;
    //   }, {} as Record<string, number>);
    //   // Restrict search to only name, collectionName, and createdBy
    //   searchableFields = ['name', 'collectionName', 'createdBy'];
    // }

    // Search Handling
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };
      query.$or = searchableFields.map(field => ({ [field]: regex }));
      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: searchTerm });
      }
    }

    // Filters Handling
    if (filters) {
      const parsed =
        typeof filters === 'string'
          ? JSON.parse(filters)?.filters || JSON.parse(filters)
          : filters.filters || filters;
      for (const [key, value] of Object.entries(parsed)) {
        if (value !== undefined && value !== '') {
          if (key === 'createdBy' && typeof value === 'string' && value.includes('@')) {
            query['createdByEmail'] = value;
          } else if (sampleDoc && sampleDoc[key] instanceof Date) {
            // Parse DD-MM-YYYY or DD/MM/YYYY
            let dateValue: Date | undefined = undefined;
            if (typeof value === 'string' && value.match(/^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}$/)) {
              const [d, m, y] = value.split(/[-\/]/);
              dateValue = new Date(Number(y), Number(m) - 1, Number(d));
            } else if (typeof value === 'string') {
              dateValue = new Date(value);
            } else if (value instanceof Date) {
              dateValue = value;
            } else {
              continue;
            }
            if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
              const nextDay = new Date(dateValue.getTime());
              nextDay.setDate(nextDay.getDate() + 1);
              query[key] = { $gte: dateValue, $lt: nextDay };
            } else {
              continue;
            }
          } else if (typeof value === 'string') {
            query[key] = {
              $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
              $options: 'i'
            };
          } else {
            query[key] = value;
          }
        }
      }
    }

    // Sorting Logic
    const sort: Record<string, 1 | -1> = {};
    if (sortBy && allFields.includes(sortBy)) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    // Pagination
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;
    console.log("query", query);

    const totalItems = await this.taskModel.countDocuments(query);
    const exports = await this.taskModel
      .find(query)
      // .select(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .lean();
    console.log("exportsh", exports);

    // Patch createdBy with user email
    const exportsWithUserEmail = await Promise.all(
      exports.map(async (exportItem) => {
        if (exportItem.createdBy) {
          try {
            const user = await this.userService.findOne(exportItem.createdBy);
            if (user && user.email) {
              return {
                ...exportItem,
                createdBy: user.email
              };
            }
          } catch (error) {
            // If user not found, keep the original createdBy value
            console.warn(`User not found for ID: ${exportItem.createdBy}`);
          }
        }
        return exportItem;
      })
    );
    console.log("exportsvg", exportsWithUserEmail);
    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      items: exportsWithUserEmail,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems,
        totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async deleteTaskById(taskId: string): Promise<boolean> {
    const result = await this.taskModel.deleteOne({ taskId });
    return result.deletedCount > 0;
  }

  // Called by BullMQ worker
  async processExportTask(taskId: string) {
    // Mark as IN_PROGRESS
    const exportTaskDoc = await this.taskModel.findOneAndUpdate(
      { taskId, type: 'export', status: 'NOT_STARTED' },
      {
        status: 'IN_PROGRESS',
        $push: {
          statusLog: {
            status: 'IN_PROGRESS',
            message: 'Task Started',
            timestamp: new Date(),
          },
        },
      },
      { new: true }
    );
    if (!exportTaskDoc) return;
    // Cast to TaskQueue type for type safety
    const exportTask = exportTaskDoc.toObject() as TaskQueue;

    // Send notification for process started
    await this.sendExportNotification(taskId, 'STARTED');

    // Log audit event for process started
    await this.logExportAuditEvent(taskId, 'UPDATE', { status: 'NOT_STARTED' }, { status: 'IN_PROGRESS' }, exportTask.createdBy || 'System', exportTask.createdByEmail || '');

    try {
      // 1. Get the correct collection using the connection service
      const collection = await this.mongoConnectionService.getCollectionByOrgId(
        exportTask.collectionName || '',
        exportTask.orgId || 'asp'
      );
      // 2. Build query
      let query: any = {};

      // Apply filters if provided
      if (exportTask.filters) {
        const parsed = typeof exportTask.filters === 'string'
          ? JSON.parse(exportTask.filters)?.filters || JSON.parse(exportTask.filters)
          : exportTask.filters.filters || exportTask.filters;
        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            if (typeof value === 'string') {
              // Try to parse as date first (support DD-MM-YYYY or DD/MM/YYYY)
              let dateValue: Date;
              if (value.match(/^\d{1,2}[-\/]\d{1,2}[-\/]\d{4}$/)) {
                const [d, m, y] = value.split(/[-\/]/);
                dateValue = new Date(Number(y), Number(m) - 1, Number(d));
              } else {
                dateValue = new Date(value);
              }
              if (!isNaN(dateValue.getTime())) {
                const nextDay = new Date(dateValue.getTime());
                nextDay.setDate(nextDay.getDate() + 1);
                query[key] = { $gte: dateValue, $lt: nextDay };
              } else {
                // If not a date, use regex search
                query[key] = {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$1'),
                  $options: 'i'
                };
              }
            } else {
              query[key] = value;
            }
          }
        }
      }
      // If specific rows are selected, combine with filters using $and
      if (exportTask.selectedRow && exportTask.selectedRow.length > 0) {
        const rowQuery = { _id: { $in: exportTask.selectedRow.map(id => new mongoose.Types.ObjectId(id)) } };
        if (Object.keys(query).length > 0) {
          query = { $and: [query, rowQuery] };
        } else {
          query = rowQuery;
        }
      }
      // 3. Fetch data
      const fields = exportTask.fields || [];
      // Determine sort order
      let sort: Record<string, 1 | -1>;
      if (exportTask.sortBy && exportTask.sortOrder) {
        sort = {
          [exportTask.sortBy]: exportTask.sortOrder === 'asc' ? 1 : -1
        };
      }
      else {
        sort = { createdAt: -1 }
      }
      let result = collection.find(query, { projection: fields.reduce((acc, f) => ({ ...acc, [f]: 1 }), {}) })
        .sort(sort); // use dynamic sort
      // For print type, limit to 50
      if (exportTask.fileType && exportTask.fileType.toLowerCase() === 'json') {
        result = result.limit(50);
      }
      const data = await result.toArray();

      // 4. Convert to Buffer (CSV, JSON, XLS)

      let fileBuffer: Buffer;
      let fileName: string;
      if (exportTask.fileType && exportTask.fileType.toLowerCase() === 'csv') {
        const parser = new Parser({ fields });
        const csv = parser.parse(data);
        fileBuffer = Buffer.from(csv, 'utf-8');
        fileName = `${exportTask.collectionName}_${taskId}.csv`;
      } else if (exportTask.fileType && (exportTask.fileType.toLowerCase() === 'xls' || exportTask.fileType.toLowerCase() === 'xlsx')) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Export');
        worksheet.columns = fields.map(f => ({ header: f, key: f }));
        data.forEach(row => worksheet.addRow(row));
        const excelBuffer = await workbook.xlsx.writeBuffer();
        fileBuffer = Buffer.from(excelBuffer);
        fileName = `${exportTask.collectionName}_${taskId}.xlsx`;
      } else {
        fileBuffer = Buffer.from(JSON.stringify(data), 'utf-8');
        fileName = `${exportTask.collectionName}_${taskId}.json`;
      }

      // 5. Always upload to Google Cloud Storage (GCS), do not store file in DB
      const gcsFilePath = `exports/${exportTask.collectionName}/${taskId}/${fileName}`;
      let contentType = 'application/octet-stream';
      if (fileName.endsWith('.csv')) contentType = 'text/csv';
      else if (fileName.endsWith('.json')) contentType = 'application/json';
      else if (fileName.endsWith('.xlsx')) contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      else if (fileName.endsWith('.xls')) contentType = 'application/vnd.ms-excel';

      const downloadUrl = await this.gcsService.uploadBuffer(fileBuffer, gcsFilePath, contentType);
      await this.taskModel.updateOne(
        { taskId },
        {
          status: 'COMPLETED',
          file: null,
          downloadUrl,
          $push: {
            statusLog: {
              status: 'COMPLETED',
              message: 'Export Task Complete',
              timestamp: new Date(),
            },
          },
        }
      );

      // Send notification for process completed
      await this.sendExportNotification(taskId, 'COMPLETED');

      // Log audit event for process completed
      await this.logExportAuditEvent(taskId, 'UPDATE', { status: 'IN_PROGRESS' }, { status: 'COMPLETED', downloadUrl }, exportTask.createdBy || "System", exportTask.createdByEmail || '');
    } catch (err) {
      console.log("Export Task Error:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      await this.taskModel.updateOne(
        { taskId },
        {
          status: 'FAILED',
          $push: {
            statusLog: {
              status: 'FAILED',
              message: errorMessage.split('\n')[0],
              timestamp: new Date(),
            },
          },
        }
      );
      // Send notification for process failed
      await this.sendExportNotification(taskId, 'FAILED');

      // Log audit event for process failed
      await this.logExportAuditEvent(taskId, 'UPDATE', { status: 'IN_PROGRESS' }, { status: 'FAILED', error: errorMessage }, exportTask.createdBy || "System", exportTask.createdByEmail || '');

      throw err;
    } finally {
      // No need to close dbConnection when using MongoConnectionService
    }
  }
  // Send notification when export task completes or fails
  async sendExportNotification(taskId: string, status: string) {
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Notification timeout')), 10000); // 10 second timeout
    });

    try {
      await Promise.race([
        this.sendExportNotificationInternal(taskId, status),
        timeoutPromise
      ]);
    } catch (error) {
      console.error(`❌ Failed to send export notification for task ${taskId}:`, error);
      // Don't throw - notification failure shouldn't crash the export process
    }
  }

  private async sendExportNotificationInternal(taskId: string, status: string) {
    const task = await this.taskModel.findOne({ taskId }).lean();
    if (!task) {
      console.warn(`Task not found for notification: ${taskId}`);
      return;
    }

    // Skip notification if no user ID
    if (!task.createdBy) {
      console.warn(`No user ID found for task notification: ${taskId}`);
      return;
    }

    // Get device tokens and user email
    const deviceTokens = await this.getDeviceTokenForUser(task.createdBy);
    const email = task.createdByEmail;

    // Prepare notification content based on status
    let title = '';
    let message = '';
    let type: NotificationType;
    let priority = NotificationPriority.MEDIUM;   

    switch (status) {
      case 'CREATED':
        title = 'Export Task Created';
        message = `Export task for ${task.collectionName} has been created and scheduled.`;
        priority = NotificationPriority.LOW;
        type = NotificationType.TASK_STARTED;
        break;
      case 'STARTED':
      case 'IN_PROGRESS':
        title = 'Export Processing Started';
        message = `Export processing for ${task.collectionName} has started.`;
        priority = NotificationPriority.MEDIUM;
        type = NotificationType.TASK_PROGRESS;
        break;
      case 'COMPLETED':
        title = 'Export Completed';
        message = `Your export task for ${task.collectionName} has been completed successfully.`;
        priority = NotificationPriority.HIGH;
        type = NotificationType.EXPORT_COMPLETED;
        break;
      case 'FAILED':
        title = 'Export Failed';
        message = `Your export task for ${task.collectionName} has failed. Please try again.`;
        priority = NotificationPriority.HIGH;
        type = NotificationType.EXPORT_FAILED;
        break;
      default:
        console.warn(`Unknown export status: ${status}`);
        return;
    }

    const payload: NotificationPayload = {
      userId: task.createdBy,
      senderId: task.createdBy,
      type: NotificationType.CUSTOM,
      title,
      message,
      priority,
      channels: [NotificationChannel.IN_APP],
      isRouted: true,
      data: {
        taskId: task.taskId,
        collectionName: task.collectionName,
        fileType: task.fileType,
        status,
        downloadUrl: task.downloadUrl,
        notificationId: new ObjectId().toString(),
        deviceTokens: Array.isArray(deviceTokens) ? deviceTokens : [deviceTokens],
      },
      metadata: {
        taskId: task.taskId,
        collectionName: task.collectionName,
        fileType: task.fileType,
        orgId: task.orgId,
        status,
        userEmail: email,
      },
    };

    // Check if Kafka notifications are enabled
    const useKafka = process.env.ENABLE_KAFKA_NOTIFICATIONS !== 'false';

    if (useKafka) {
      try {
        await this.kafkaService.publishToKafka(payload, 'notification');
        console.log(`✅ Kafka notification sent for export task ${taskId}: ${status}`);
        return; // Success, no need for fallback
      } catch (kafkaError) {
        console.error(`❌ Kafka notification failed for task ${taskId}:`, kafkaError);
        console.log(`🔄 Falling back to direct notification service for task ${taskId}`);
      }
    } else {
      console.log(`⚠️ Kafka notifications disabled, using direct notification service for task ${taskId}`);
    }

    // Fallback to direct notification service (either Kafka failed or disabled)
    // try {
    //   if (status === 'CREATED') {
    //     // Use direct notification service for CREATED status
    //     await this.notificationService.sendNotification({
    //       senderId: 'system',
    //       userId: task.createdBy,
    //       title,
    //       message,
    //       type: NotificationType.CUSTOM,
    //       priority,
    //       channels: [NotificationChannel.WEBSOCKET, NotificationChannel.IN_APP],
    //       metadata: {
    //         taskId: task.taskId,
    //         collectionName: task.collectionName,
    //         fileType: task.fileType,
    //         status,
    //       },
    //     });
    //   } else {
    //     // Use task notification service for other statuses
    //     const notificationData = {
    //       taskId: task.taskId,
    //       taskType: 'export' as const,
    //       status: status as 'COMPLETED' | 'FAILED' | 'STARTED' | 'IN_PROGRESS',
    //       collectionName: task.collectionName || 'unknown',
    //       recordCount: 0,
    //       downloadUrl: task.downloadUrl,
    //       errorMessage: status === 'FAILED' ? task.statusLog?.[task.statusLog.length - 1]?.message : undefined,
    //       userId: task.createdBy,
    //       userEmail: email
    //     };
    //     await this.notificationService.sendTaskNotification(notificationData);
    //   }

    //   console.log(`✅ Direct notification sent for export task ${taskId}: ${status}`);
    // } catch (fallbackError) {
    //   console.error(`❌ Direct notification also failed for task ${taskId}:`, fallbackError);
    //   // Don't throw here - notification failure shouldn't crash the export process
    // }

    console.log(`📢 Notification processing completed for export task ${taskId}: ${status}`);
  }

  async logExportAuditEvent(
    taskId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    beforeData: any,
    afterData: any,
    userId: string,
    userEmail: string
  ) {
    console.log(`🚀 Export audit logging called for task ${taskId}:`, {
      action,
      userId,
      userEmail: userEmail ? userEmail.substring(0, 10) + '...' : 'N/A'
    });

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Audit logging timeout')), 5000); // 5 second timeout
    });

    try {
      await Promise.race([
        this.logExportAuditEventInternal(taskId, action, beforeData, afterData, userId, userEmail),
        timeoutPromise
      ]);
      console.log(`🎉 Export audit logging completed for task ${taskId}: ${action}`);
    } catch (error) {
      console.error('Failed to log export audit event:', error);
      // Don't throw - audit failure shouldn't crash the export process
    }
  }

  private async logExportAuditEventInternal(
    taskId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    beforeData: any,
    afterData: any,
    userId: string,
    userEmail: string
  ) {
    try {
      console.log(`🔍 Starting export audit logging for task ${taskId}:`, {
        action,
        userId,
        userEmail,
        beforeData,
        afterData
      });

      const task = await this.taskModel.findOne({ taskId }).lean();
      if (!task) {
        console.warn(`⚠️ Task not found for audit logging: ${taskId}`);
        return;
      }

      const auditData = {
        eventType: action,
        recordType: 'EXPORT_TASK',
        beforeData,
        afterData,
        authoredInfo: {
          id: userId,
          name: userEmail || 'System User',
          email: userEmail || 'System User'
        }
      };

      console.log(`📝 Export audit data prepared:`, auditData);

      await this.auditHelperService.logAuditEvent(auditData);

      console.log(`✅ Export audit event logged successfully for task ${taskId}: ${action}`);
    } catch (error) {
      console.error(`❌ Failed to log export audit event for task ${taskId}:`, error);
      console.error(`❌ Error details:`, error.message, error.stack);
      throw error;
    }
  }

  private async getDeviceTokenForUser(userId: string): Promise<string | null> {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<any>((_, reject) => {
        setTimeout(() => reject(new Error('Device token retrieval timeout')), 3000); // 3 second timeout
      });

      const tokenPromise = (async () => {
        const collection = await this.mongoConnectionService.getCollectionByOrgId(
          'user_notification_tokens'
        );
        return await collection.findOne({ userId });
      })();

      const tokenRecord = await Promise.race([tokenPromise, timeoutPromise]);

      if (!tokenRecord || !tokenRecord.token) {
        console.warn(`⚠️ Device token not found for user: ${userId}`);
        return null;
      }

      // Handle both array and string formats
      if (Array.isArray(tokenRecord.token)) {
        // Token is an array - get the first valid token
        const validTokens = tokenRecord.token.filter(t => t && typeof t === 'string' && t.trim() !== '');
        if (validTokens.length > 0) {
          console.log(`✅ Found device token array for user ${userId}, using first valid token`);
          return validTokens[0];
        } else {
          console.warn(`⚠️ No valid tokens in array for user: ${userId}`);
          return null;
        }
      } else if (typeof tokenRecord.token === 'string' && tokenRecord.token.trim() !== '') {
        // Token is a string
        console.log(`✅ Found device token string for user ${userId}: ${tokenRecord.token}`);
        return tokenRecord.token;
      }

      console.warn(`⚠️ Invalid token format for user: ${userId}`);
      return null;
    } catch (err) {
      console.error(`❌ Error retrieving device token for user ${userId}:`, err);
      return null; // Return null instead of throwing to prevent hanging
    }
  }
}