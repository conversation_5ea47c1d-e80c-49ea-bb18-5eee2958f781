import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { SpecialtyService } from './specialty.service';
import { Specialty } from './entities/specialty.entity';
import {
  CreateSpecialtyInput,
  UpdateSpecialtyInput,
  PaginateSpecialtyArgs,
  PaginatedSpecialtyResponse,
  GetSpecialtyInput,
  SuccessResponse,
} from './dto/specialty.dto';
import { HttpStatus, ErrorType, ResponseCode } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { RequirePermission } from '@app/permissions';

@Resolver(() => Specialty)
export class SpecialtyResolver {
  constructor(private readonly specialtyService: SpecialtyService) {}

  @Query(() => PaginatedSpecialtyResponse, { name: 'specialties' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getSpecialtiesWithPagination(
    @Args('input', { nullable: true }) input?: GetSpecialtyInput,
    @Context() context?: any
  ) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters;

    // Get the authorization token from the context if available
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    return this.specialtyService.findAll({
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      selectedFields,
    });
  }

  @Query(() => Specialty, { name: 'specialty' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getSpecialtyById(@Args('id') id: string) {
    return this.specialtyService.findById(id);
  }

  @Query(() => Number, { name: 'countSpecialties' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async countSpecialties() {
    return this.specialtyService.count();
  }

  @Query(() => [String], { name: 'specialtySortFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getSpecialtySortFields() {
    return this.specialtyService.getAvailableSortFields();
  }

  @Query(() => [String], { name: 'specialtySearchFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getSpecialtySearchFields() {
    return this.specialtyService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Add' })
  async createSpecialty(
    @Args('input') input: CreateSpecialtyInput,
    @Context() context: any
  ) {
    const authHeader = context.req?.headers?.authorization ?? '';
    let userId: string | undefined;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.replace('Bearer ', '');
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
        userId = typeof decoded === 'object' && decoded !== null && 'userId' in decoded ? (decoded as any).userId : undefined;
      } catch (jwtError) {
        console.error('JWT verification failed for Specialty creation:', jwtError);
      }
    }
    if (userId) {
      input.createdBy = userId;
    }

    const result = await this.specialtyService.create(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Edit' })
  async updateSpecialty(@Args('input') input: UpdateSpecialtyInput) {
    const result = await this.specialtyService.update(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Delete' })
  async deleteSpecialty(@Args('id') id: string) {
    const result = await this.specialtyService.delete(id);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }
} 