import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { OrganisationRoleService } from '../services/organisation-role.service';
import { OrganisationRoleResponse, CreateOrganisationRoleInput, UpdateOrganisationRoleInput, OrganisationRoleListArgs, OrganisationRolePagination } from '../dto/organisation-role.dto';
import { OrganisationRole } from '../entities/organisation-role.entity';
import { RequirePermission, RedisCacheService } from '@app/permissions';
import { Logger } from '@nestjs/common';

@Resolver(() => OrganisationRoleResponse)
export class OrganisationRoleResolver {
  private readonly logger = new Logger(OrganisationRoleResolver.name);

  constructor(
    private readonly organisationRoleService: OrganisationRoleService,
    private readonly cacheService: RedisCacheService
  ) {}

  @Mutation(() => OrganisationRoleResponse)
  @RequirePermission(
    { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'Add' }
  )
  async createOrganisationRole(
    @Args('input') input: CreateOrganisationRoleInput,
  ): Promise<OrganisationRole> {
    const result = await this.organisationRoleService.create(input);

    // Invalidate organisation role caches
    await this.invalidateOrganisationRoleCache(result._id?.toString());

    return result;
  }

  @Query(() => [OrganisationRoleResponse], { name: 'organisationRoles' })
  // @RequirePermission(
  //   { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'View' }
  // )
  async findAllOrganisationRoles(
    @Args('args', { type: () => OrganisationRoleListArgs, nullable: true }) args?: OrganisationRoleListArgs,
  ): Promise<OrganisationRole[]> {
    const result = await this.organisationRoleService.findAll(args || {});
    return result.items.map((item: any) => ({
      ...item,
      createdAt: item.createdAt ?? null,
      updatedAt: item.updatedAt ?? null,
    }));
  }

  @Query(() => OrganisationRoleResponse, { name: 'organisationRole' })
  // @RequirePermission(
  //   { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'View' }
  // )
  async findOneOrganisationRole(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<OrganisationRole | null> {
    return this.organisationRoleService.findOne(id);
    // const role = await this.organisationRoleService.findOne(id);
    // if (!role) return null;
    // return {
    //   _id: role._id?.toString?.() ?? role._id,
    //   name: role.name,
    //   permissions: role.permissions,
    //   isActive: typeof role.isActive === 'boolean' ? role.isActive : true,
    //   type: role.type,
    //   createdAt: role.createdAt ? role.createdAt : undefined,
    //   updatedAt: role.updatedAt ? role.updatedAt : undefined,
    // };
  }

  @Mutation(() => OrganisationRoleResponse)
  @RequirePermission(
    { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'Edit' }
  )
  async updateOrganisationRole(
    @Args('input') input: UpdateOrganisationRoleInput,
  ): Promise<OrganisationRole | null> {
    const result = await this.organisationRoleService.update(input);

    // Invalidate organisation role caches
    if (result) {
      await this.invalidateOrganisationRoleCache(input.id);
    }

    return result;
  }

  @Mutation(() => OrganisationRoleResponse)
  @RequirePermission(
    { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'Delete' }
  )
  async removeOrganisationRole(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<OrganisationRole | null> {
    const result = await this.organisationRoleService.remove(id);

    // Invalidate organisation role caches
    if (result) {
      await this.invalidateOrganisationRoleCache(id);
    }

    return result;
  }

  @Query(() => OrganisationRolePagination, { name: 'organisationRoles' })
  // @RequirePermission(
  //   { module: 'User Management', subModule: 'Org Roles and Managements', permission: 'View' }
  // )
  async organisationRoles(
    @Args('input', { type: () => OrganisationRoleListArgs, nullable: true }) input?: OrganisationRoleListArgs,
  ): Promise<OrganisationRolePagination> {
    const result = await this.organisationRoleService.findAll(input || {});
    return {
      items: result.items.map((item: any) => ({
        ...item,
        _id: item._id?.toString?.() ?? item._id,
        createdAt: item.createdAt ?? null,
        updatedAt: item.updatedAt ?? null,
      })),
      pagination: result.pagination,
    };
  }

  /**
   * Invalidate Redis cache entries related to organisation role
   */
  private async invalidateOrganisationRoleCache(roleId: string): Promise<void> {
    try {
      this.logger.log(`🗑️ Invalidating cache for organisation role: ${roleId}`);

      // 1. Invalidate role-specific cache
      await this.cacheService.invalidateRole(roleId);

      // 2. Invalidate related cache patterns
      const patterns = [
        `perm:role:*:${roleId}`,           // Role-specific permissions
        `perm:user:*`,                     // All user permissions (broad but safe)
        `header:*`,                        // Header cache (organizations, processes, etc.)
      ];

      for (const pattern of patterns) {
        const deletedCount = await this.cacheService.invalidateByPattern(pattern);
        this.logger.log(`🗑️ Invalidated ${deletedCount} cache entries for pattern: ${pattern}`);
      }

      this.logger.log(`✅ Successfully invalidated cache for organisation role: ${roleId}`);
    } catch (error) {
      this.logger.error(`❌ Failed to invalidate cache for organisation role ${roleId}:`, error.message);
      // Don't throw error - cache invalidation failure shouldn't break the mutation
    }
  }
}