<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Test</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            color: white;
        }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn:hover { opacity: 0.8; }
        
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .notifications {
            margin-top: 20px;
        }
        .notification {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .notification.HIGH { border-left-color: #dc3545; }
        .notification.MEDIUM { border-left-color: #ffc107; }
        .notification.LOW { border-left-color: #28a745; }
        
        .notification-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .notification-time {
            font-size: 0.8em;
            color: #666;
            margin-top: 10px;
        }

        /* Toast notifications - bottom right */
        .toast-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
        }
        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            margin-bottom: 10px;
            padding: 15px;
            border-left: 4px solid #007bff;
            animation: slideIn 0.3s ease-out;
        }
        .toast.HIGH { border-left-color: #dc3545; }
        .toast.MEDIUM { border-left-color: #ffc107; }
        .toast.LOW { border-left-color: #28a745; }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .toast-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .toast-message {
            color: #666;
            margin-bottom: 8px;
        }
        .toast-time {
            font-size: 0.7em;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Notification System Test</h1>
        
        <div id="status" class="status connecting">
            🔄 Connecting to notification service...
        </div>

        <div class="input-group">
            <label>User ID:</label>
            <input type="text" id="userId" value="686d42a42a81b54ae8a9242e" placeholder="Enter your user ID">
        </div>

        <div class="input-group">
            <label>JWT Token:</label>
            <input type="text" id="jwtToken" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************.fNlXFaaCQMo8eFutYndE7RE8v_rljtdE9MeMmlAjpOo" placeholder="Enter your JWT token">
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="connect()">🔌 Connect</button>
            <button class="btn btn-success" onclick="testImportSuccess()">✅ Test Import Success</button>
            <button class="btn btn-danger" onclick="testImportFailed()">❌ Test Import Failed</button>
            <button class="btn btn-success" onclick="testExportSuccess()">📤 Test Export Success</button>
            <button class="btn btn-danger" onclick="testExportFailed()">📤❌ Test Export Failed</button>
        </div>

        <div class="notifications">
            <h3>📋 Notification History</h3>
            <div id="notificationHistory">
                <p style="color: #999; text-align: center;">No notifications yet</p>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <script>
        let socket = null;
        let notifications = [];

        const statusEl = document.getElementById('status');
        const userIdEl = document.getElementById('userId');
        const jwtTokenEl = document.getElementById('jwtToken');
        const historyEl = document.getElementById('notificationHistory');
        const toastContainer = document.getElementById('toastContainer');

        function connect() {
            const userId = userIdEl.value.trim();
            const jwtToken = jwtTokenEl.value.trim();
            
            if (!userId) {
                alert('Please enter a User ID');
                return;
            }
            
            if (socket) {
                socket.disconnect();
            }
            
            updateStatus('connecting', '🔄 Connecting...');
            
            socket = io('http://localhost:4001/notifications', {
                auth: { token: jwtToken, userId: userId },
                transports: ['websocket', 'polling']
            });
            
            socket.on('connect', () => {
                console.log('✅ Connected');
            });
            
            socket.on('connected', (data) => {
                console.log('🎉 Authenticated:', data);
                updateStatus('connected', `✅ Connected as ${userId}`);
                
                socket.emit('subscribe', {
                    types: ['IMPORT_COMPLETED', 'IMPORT_FAILED', 'EXPORT_COMPLETED', 'EXPORT_FAILED']
                });
            });
            
            socket.on('subscribed', (data) => {
                console.log('📋 Subscribed to:', data.types);
                showToast('System', 'Connected and subscribed to notifications', 'LOW');
            });
            
            socket.on('notification', (notification) => {
                console.log('🔔 Notification:', notification);
                handleNotification(notification);
            });
            
            socket.on('connect_error', (error) => {
                console.error('❌ Connection error:', error);
                updateStatus('disconnected', `❌ Connection failed: ${error.message}`);
            });
            
            socket.on('disconnect', (reason) => {
                console.log('🔌 Disconnected:', reason);
                updateStatus('disconnected', `🔌 Disconnected: ${reason}`);
            });
        }

        function handleNotification(notification) {
            notifications.unshift(notification);
            updateHistory();
            showToast(notification.title, notification.message, notification.priority);
            
            // Browser notification
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification(notification.title, {
                    body: notification.message,
                    icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>'
                });
            }
        }

        function showToast(title, message, priority = 'MEDIUM') {
            const toast = document.createElement('div');
            toast.className = `toast ${priority}`;
            toast.innerHTML = `
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
                <div class="toast-time">${new Date().toLocaleTimeString()}</div>
            `;
            
            toastContainer.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }

        function updateStatus(type, message) {
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        function updateHistory() {
            if (notifications.length === 0) {
                historyEl.innerHTML = '<p style="color: #999; text-align: center;">No notifications yet</p>';
                return;
            }
            
            historyEl.innerHTML = notifications.slice(0, 10).map(notif => `
                <div class="notification ${notif.priority}">
                    <div class="notification-title">${notif.title}</div>
                    <div>${notif.message}</div>
                    <div class="notification-time">
                        ${new Date(notif.timestamp).toLocaleString()} | 
                        Type: ${notif.type} | 
                        Priority: ${notif.priority}
                    </div>
                </div>
            `).join('');
        }

        // Test functions using GraphQL
        async function testImportSuccess() {
            await sendGraphQLTest('testImportNotification', { userId: userIdEl.value, status: 'COMPLETED' });
        }

        async function testImportFailed() {
            await sendGraphQLTest('testImportNotification', { userId: userIdEl.value, status: 'FAILED' });
        }

        async function testExportSuccess() {
            await sendGraphQLTest('testExportNotification', { userId: userIdEl.value, status: 'COMPLETED' });
        }

        async function testExportFailed() {
            await sendGraphQLTest('testExportNotification', { userId: userIdEl.value, status: 'FAILED' });
        }

        async function sendGraphQLTest(mutation, variables) {
            if (!socket || !socket.connected) {
                alert('Please connect first');
                return;
            }

            try {
                const query = `
                    mutation {
                        ${mutation}(${Object.entries(variables).map(([key, value]) => `${key}: "${value}"`).join(', ')})
                    }
                `;

                const response = await fetch('http://localhost:4001/graphql', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query })
                });

                const result = await response.json();
                console.log('GraphQL result:', result);
            } catch (error) {
                console.error('GraphQL error:', error);
            }
        }

        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Auto-connect on load
        setTimeout(connect, 1000);
    </script>
</body>
</html>
