import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OrganisationUser } from '../entities/organisation-user.entity';
import { OrganisationRole } from '../entities/organisation-role.entity';
import { CreateOrganisationUserInput, UpdateOrganisationUserInput, PaginateOrganisationUserArgs } from '../dto/organisation-user.dto';
import { Error, ErrorType, HttpStatus, ResponseCode } from '@app/error';

enum OrganisationType {
  SUB_ORGANISATION = 'SUB_ORGANISATION',
  MAIN_ORGANISATION = 'MAIN_ORGANISATION'
}

@Injectable()
export class OrganisationUserService {
  constructor(
    @InjectModel(OrganisationUser.name) private organisationUserModel: Model<OrganisationUser>,
    @InjectModel(OrganisationRole.name) private organisationRoleModel: Model<OrganisationRole>,
  ) {}

  async create(input: CreateOrganisationUserInput, orgId?: string) {
    // Validate email format
    if (!input.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input.email)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_EMAIL,
        ErrorType.IDENTITY,
        'Invalid Email format',
      );
    }

    // Validate roleId is required
    if (!input.roleId) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.VALIDATION,
        'Role is required',
      );
    }

    // // Validate roleId format (ObjectId)
    // if (!/^[0-9a-fA-F]{24}$/.test(input.roleId)) {
    //   throw new Error(
    //     HttpStatus.BAD_REQUEST,
    //     ResponseCode.INVALID_PARAMETERS,
    //     ErrorType.VALIDATION,
    //     'Invalid role ID format',
    //   );
    // }

    // Check for existing email separately
    const existingEmail = await this.organisationUserModel.findOne({
      email: input.email
    });
    if (existingEmail) {
      throw new Error(
        HttpStatus.BAD_REQUEST, 
        ResponseCode.INVALID_PARAMETERS, 
        ErrorType.VALIDATION,
        'A user with this email already exists.'
      );
    }

    // Check for existing employee ID with organization-specific logic
    let query: any = { employeeId: input.employeeId };
    if (input.type == OrganisationType.SUB_ORGANISATION.toString() && !input.subOrganisationId) {
      query.subOrganisationId = input.subOrganisationId;
    } else {
      query.organisationId = input.organisationId;
    }

    const existingEmployeeId = await this.organisationUserModel.findOne(query);
    if (existingEmployeeId) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.VALIDATION,
        'A user with this employee ID already exists.'
      );
    }

    const finalOrgId = input.orgId || orgId || '';
    return this.organisationUserModel.create({
      ...input,
      orgId: finalOrgId,
      isActive: true
    });
  }

  async findAll(input: PaginateOrganisationUserArgs): Promise<any> {
    const {
      page = 1,
      limit = 10,
      search,
      filters,
      sortBy,
      sortOrder = 'asc',
      selectedFields,
      organisationId,
      type
    } = input;
    const query: any = {};
    let projection: Record<string, number> = {};
    let searchableFields: string[] = ['name', 'email', 'employeeId'];
    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
    }
    // Handle organisationId filtering based on type
    if (organisationId && type) {
      if (type.toString() == 'subOrganisation') {
        // For SUB_ORGANISATION type, compare with subOrganisationId field
        query.subOrganisationId = organisationId;
      } else {
        // For other types (MAIN_ORGANISATION, etc.), use organisationId field
        query.organisationId = organisationId;
      }
    }

    // Add type filtering
    if (type) {
      query.type = type;
    }
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };
      query.$or = searchableFields.map(field => ({ [field]: regex }));
    }
    if (filters) {
      for (const [key, value] of Object.entries(filters)) {
        if (value !== undefined && value !== '') {
          if ((key === 'createdAt' || key === 'updatedAt')) {
            const dateValue = typeof value === 'string' ? new Date(value) : value;
            if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
              const nextDay = new Date(dateValue.getTime());
              nextDay.setDate(nextDay.getDate() + 1);
              query[key] = { $gte: dateValue, $lt: nextDay };
            } else {
              continue;
            }
          } else if (typeof value === 'string') {
            query[key] = { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
          } else {
            query[key] = value;
          }
        }
      }
    }
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;
    const totalItems = await this.organisationUserModel.countDocuments(query);
console.log("query",query);

    // First, get users without population to handle invalid roleId values
    const users = await this.organisationUserModel
      .find(query)
      .select(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .lean();

    // Manually populate roleId for valid ObjectIds only
    const populatedUsers = await Promise.all(
      users.map(async (user: any) => {
        let roleInfo = { _id: '', name: '' };

        // Check if roleId is a valid ObjectId string
        if (user.roleId &&
            typeof user.roleId === 'string' &&
            user.roleId.length === 24 &&
            /^[0-9a-fA-F]{24}$/.test(user.roleId)) {
          try {
            const role = await this.organisationRoleModel.findById(user.roleId).select('name').lean();
            if (role) {
              roleInfo = {
                _id: role._id.toString(),
                name: role.name
              };
            }
          } catch (error) {
            // If role lookup fails, keep empty roleInfo
            console.warn(`Failed to populate role for user ${user._id}:`, error.message);
          }
        }

        return {
          ...user,
          roleId: roleInfo
        };
      })
    );
    const totalPages = Math.ceil(totalItems / safeLimit);
    const mappedUsers = populatedUsers.map((user: any) => ({
      ...user,
      _id: user._id.toString(),
      roleId: user.roleId._id || '',
      roleName: user.roleId.name || '',
    }));
    return {
      items: mappedUsers,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems,
        totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async findOne(id: string) {
    const user = await this.organisationUserModel.findById(id).populate('roleId', 'name').lean();
    if (!user) return null;
    let roleId = '';
    let roleName = '';
    if (user.roleId && typeof user.roleId === 'object' && user.roleId !== null) {
      if ('name' in user.roleId) {
        // Populated
        roleId = user.roleId._id?.toString?.() ?? user.roleId._id ?? '';
        roleName = typeof user.roleId.name === 'string' ? user.roleId.name : '';
      } else {
        // ObjectId or other object
        roleId = String(user.roleId);
      }
    } else if (typeof user.roleId === 'string') {
      roleId = user.roleId;
    }
    return {
      ...user,
      _id: user._id?.toString?.() ?? user._id,
      roleId,
      roleName,
    };
  }

  async update(input: UpdateOrganisationUserInput, orgId?: string) {
    // Validate roleId format if provided
    if (input.roleId && !/^[0-9a-fA-F]{24}$/.test(input.roleId)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.VALIDATION,
        'Invalid role ID format',
      );
    }

    // Check for unique email within the same organisation, excluding the current user
    if (input.email) {
      const existingEmail = await this.organisationUserModel.findOne({
        email: input.email,
        _id: { $ne: input.id }
      });
      if (existingEmail) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.VALIDATION,
          'A user with this email already exists.'
        );
      }
    }

    // Check for unique employee ID within the same organisation, excluding the current user
    if (input.employeeId) {
      const existingEmployeeId = await this.organisationUserModel.findOne({
        employeeId: input.employeeId,
        _id: { $ne: input.id }
      });
      if (existingEmployeeId) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.VALIDATION,
          'A user with this employee ID already exists.'
        );
      }
    }

    const finalOrgId = orgId || input.orgId || '';
    return this.organisationUserModel.findByIdAndUpdate(
      input.id,
      { ...input, updatedAt: new Date() },
      { new: true },
    ).lean();
  }

  async remove(id: string) {
    return this.organisationUserModel.findByIdAndDelete(id).lean();
  }
} 