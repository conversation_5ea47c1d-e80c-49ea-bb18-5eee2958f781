import { InputType, Field, ID, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsEnum } from 'class-validator';
import { ImportConfiguration } from '../entities/import-configuration.entity';

export enum ImportType {
  UPSERT = 'upsert',
  UPDATE = 'update',
  CREATE = 'create'
}

@InputType()
export class StartImportInput {
  @Field() filePath: string;
  @Field() collectionName: string;
  @Field() createdBy: string;
  @Field({ nullable: true }) orgId?: string;
  @Field({ nullable: true }) fileType?: string;

  @Field(() => String)
  @IsEnum(ImportType)
  type: ImportType; // upsert, update, create

  @Field(() => ID)
  templateId: string; // This is actually the import configuration ID - kept as templateId for backward compatibility

  @Field({ nullable: true, defaultValue: false })
  isStopOnError?: boolean; // Stop import on first error or continue
}

@InputType()
export class CreateImportConfigurationInput {


  @Field()
  collectionName: string;

  @Field()
  templateId: string;

  @Field(() => GraphQLJSON)
  mappingJson: Record<string, string>;

  @Field(() => [String])
  requiredFields: string[];

  @Field(() => [String], { nullable: true })
  uniqueFields?: string[];

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  orgId?: string;
}

@InputType()
export class UpdateImportConfigurationInput {
  @Field(() => ID)
  configurationId: string;

  @Field({ nullable: true })
  templateId?: string;

  @Field({ nullable: true })
  collectionName?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  mappingJson?: Record<string, string>;

  @Field(() => [String], { nullable: true })
  requiredFields?: string[];

  @Field(() => [String], { nullable: true })
  mappingFields?: string[];

  @Field(() => [String], { nullable: true })
  uniqueFields?: string[];

  @Field({ nullable: true })
  isActive?: boolean;
}

@ObjectType()
export class ImportConfigurationResponse {
  @Field()
  message: string;

  @Field()
  code: string;

  @Field(() => ImportConfiguration)
  data: ImportConfiguration;
}

@InputType()
export class GetImportConfigurationInput {
  @Field()
  templateId: string;

  @Field({ nullable: true })
  previousTemplateId?: string;
}

@ObjectType()
export class ImportConfigurationData {
  @Field(() => GraphQLJSON)
  mappingJson: Record<string, string>;

  @Field(() => [String])
  requiredFields: string[];

  @Field(() => [String], { nullable: true })
  uniqueFields?: string[];

  @Field()
  templateId: string;

  @Field()
  collectionName: string;

  @Field({ nullable: true })
  orgId?: string;
}

@ObjectType()
export class GetImportConfigurationResponse {
  @Field()
  message: string;

  @Field()
  code: string;

  @Field(() => ImportConfigurationData)
  data: ImportConfigurationData;
}