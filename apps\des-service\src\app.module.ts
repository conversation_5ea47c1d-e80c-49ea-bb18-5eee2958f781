import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLJSON, GraphQLJSONObject } from 'graphql-type-json';

import { Cpt, CptSchema } from './entities/cpt.entity';
import { CptCode } from './entities/cpt-code.entity';
import { Specialty, SpecialtySchema } from './entities/specialty.entity';
import { Icd, IcdSchema } from './entities/icd.entity';
import { Diagnosis, DiagnosisSchema } from './entities/diagnosis.entity';

import { MasterCptService } from './cpt.service';
import { CptResolver } from './cpt.resolver';
import { SpecialtyService } from './specialty.service';
import { SpecialtyResolver } from './specialty.resolver';
import { IcdService } from './icd.service';
import { IcdResolver } from './icd.resolver';
import { DiagnosisService } from './diagnosis.service';
import { DiagnosisResolver } from './diagnosis.resolver';
import { CptCodeModule } from './cpt-code.module';
import { CptCodeResolver } from './cpt-code.resolver';
import { CptCodeService } from './cpt-code.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: true,
      resolvers: {
        JSON: GraphQLJSON,
        JSONObject: GraphQLJSONObject,
      },
    }),
    CptCodeModule,
  ],
  providers: [
    MasterCptService,
    CptResolver,
    SpecialtyService,
    SpecialtyResolver,
    IcdService,
    IcdResolver,
    DiagnosisService,
    DiagnosisResolver,
    CptCodeService,
    CptCodeResolver,
  ],
})
export class AppModule {} 