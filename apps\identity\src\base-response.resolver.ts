import { Resolver, ResolveReference } from '@nestjs/graphql';
import { BaseResponse } from './dto/base.response.dto';

@Resolver(() => BaseResponse)
export class BaseResponseResolver {
  @ResolveReference()
  resolveReference(reference: { __typename: string; message: string }): BaseResponse {
    // This is a simple implementation that returns a BaseResponse with the referenced message
    return { 
      message: reference.message,
      code: 0,
      type: '',
      data: {}
    };
  }
}
