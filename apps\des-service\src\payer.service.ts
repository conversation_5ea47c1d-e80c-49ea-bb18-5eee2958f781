import { Injectable, HttpException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Payer } from './entities/payer.entity';
import { FindAllPayersArgs, PayersResponse, CreatePayerInput, UpdatePayerInput } from './dto/payer.dto';
import { BaseResponse } from './dto/base.response.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';

@Injectable()
export class PayerService {
  constructor(
    @InjectModel('Payer') private readonly payerModel: Model<Payer>,
  ) { }

  /**
   * Retrieves all payers with dynamic search, filter, and sort
   * @param args Query arguments including pagination, search, filters, and sort
   * @returns PayersResponse with payers and pagination info
   */
  async findAll(args: FindAllPayersArgs,userId:string ): Promise<PayersResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        filters,
        sortBy,
        sortOrder,
        selectedFields
      } = args;

      const query: any = {};//{user_id:userId};

      // === Get dynamic fields ===
      const sampleDoc = await this.payerModel.findOne().lean();
      const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

      // === Projection & Searchable Fields ===
      let projection: Record<string, number> = {};
      const excludedFromSearch = ['__v', '_id', 'createdAt', 'updatedAt'];

      const searchableFields: string[] =
        selectedFields && Object.keys(selectedFields).length > 0
          ? Object.keys(selectedFields).filter(f => selectedFields[f] === 1 && !excludedFromSearch.includes(f))
          : allFields.filter(f => !excludedFromSearch.includes(f));

      projection =
        selectedFields && Object.keys(selectedFields).length > 0
          ? selectedFields
          : allFields.reduce((acc, f) => {
            if (!excludedFromSearch.includes(f)) acc[f] = 1;
            return acc;
          }, {} as Record<string, number>);

      // === Search Handling ===
      if (search?.trim()) {
        const term = search.trim();
        const regex = { $regex: term.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };

        query.$or = searchableFields.map(field => ({ [field]: regex }));

        if (/^[0-9a-fA-F]{24}$/.test(term)) {
          query.$or.push({ _id: term });
        }

        if (term.match(/^\d{4}-\d{2}-\d{2}/)) {
          const date = new Date(term);
          if (!isNaN(date.getTime())) {
            const start = new Date(date.setHours(0, 0, 0, 0));
            const end = new Date(date.setHours(23, 59, 59, 999));
            query.$or.push({ createdAt: { $gte: start, $lte: end } });
            query.$or.push({ updatedAt: { $gte: start, $lte: end } });
          }
        }
      }

      // === Filters Handling ===
      if (filters) {
        let parsedFilters: Record<string, any>;
        try {
          parsedFilters = typeof filters === 'string' ? JSON.parse(filters) : {};
        } catch (e) {
          throw new HttpException('Invalid filters JSON', 400);
        }

        Object.entries(parsedFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            query[key] = typeof value === 'string'
              ? { $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' }
              : value;
          }
        });
      }

      // === Sorting ===
      const sortQuery: Record<string, 1 | -1> = {};
      if (sortBy) {
        sortQuery[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sortQuery.createdAt = -1;
      }

      // === Pagination ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.payerModel.countDocuments(query);
      const payers = await this.payerModel
        .find(query)
        .select(projection)
        .sort(sortQuery)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        payers,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems: totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1,
        },
      };

    } catch (error) {
      console.error('Error in findAll payers:', error);
      throw new HttpException(
        `Failed to retrieve payers: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  /**
   * Retrieves a payer by its ID
   * @param id The payer's ID
   * @returns BaseResponse with the payer
   */
  async findById(id: string): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PAYER,
          'Invalid ID format',
        );
      }

      const payer = await this.payerModel.findById(id).exec();
      if (!payer) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PAYER,
          `Payer with ID ${id} not found`,
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PAYER,
        { payer }
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PAYER,
        `Error finding payer: ${error.message}`,
      );
    }
  }

  /**
   * Creates a new payer
   * @param input Payer creation data
   * @returns BaseResponse with the created payer
   */
  async create(input: CreatePayerInput, userId: string): Promise<BaseResponse> {
    try {
      // Create a copy of input without flattenedValues for initial data
      const { flattenedValues, ...inputPlain } = input;

      // Process flattened values if provided
      const processedFlattenedValues: Record<string, any> = {};
      if (typeof input.flattenedValues === 'object' && input.flattenedValues) {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      // Combine all data
      const payerData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
        user_id: userId
      };

      const newPayer = new this.payerModel(payerData);
      await newPayer.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PAYER,
        { payer: newPayer },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PAYER,
        `Failed to create payer: ${error.message}`,
      );
    }
  }

  /**
   * Updates a payer
   * @param input Payer update data
   * @returns BaseResponse with the updated payer
   */
  async update(input: UpdatePayerInput): Promise<BaseResponse> {
    try {
      const { id, flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PAYER,
          'Invalid ID format',
        );
      }

      const payer = await this.payerModel.findById(id);
      if (!payer) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PAYER,
          'Payer not found',
        );
      }

      const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedPayer = await this.payerModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PAYER,
        { payer: updatedPayer },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PAYER,
        `Failed to update payer: ${error.message}`,
      );
    }
  }


  /**
   * Deletes a payer
   * @param id The payer's ID
   * @returns BaseResponse with success message
   */
  async delete(id: string): Promise<BaseResponse> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PAYER,
          'Invalid ID format',
        );
      }

      const deletedPayer = await this.payerModel.findByIdAndDelete(id).exec();

      if (!deletedPayer) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PAYER,
          'Payer not found',
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.PAYER,
        { message: 'Payer deleted successfully' },
      ).toJSON() as unknown as BaseResponse;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PAYER,
        `Failed to delete payer: ${error.message}`,
      );
    }
  }


}
