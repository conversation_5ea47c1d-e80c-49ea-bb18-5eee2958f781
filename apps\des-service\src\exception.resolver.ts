import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { ExceptionService } from './exception.service';
import { ExceptionResponse, CreateExceptionInput, UpdateExceptionInput, FindAllExceptionsInput, PaginatedExceptionsResponse, CountExceptionsInput } from './dto/exception.dto';
import { BaseResponse } from './dto/base.response.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import * as jwt from 'jsonwebtoken';
import { RequirePermission } from '@app/permissions';

@Resolver(() => ExceptionResponse)
@UseGuards(JwtAuthGuard)
export class ExceptionResolver {
  constructor(private readonly exceptionService: ExceptionService) {}

  private getUserIdFromContext(context: any): string {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    const userId = typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;

    if (!userId) {
      throw new Error('User ID not found in token');
    }
    return userId;
  }

  @Query(() => PaginatedExceptionsResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'View' })
  async findAllExceptions(
    @Args('input') input: FindAllExceptionsInput,
  ): Promise<PaginatedExceptionsResponse> {
    return this.exceptionService.findAll(input);
  }

  @Query(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'View' })
  async findExceptionById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<BaseResponse> {
    return this.exceptionService.findById(id);
  }

  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'Add' })
  async createException(
    @Args('input') input: CreateExceptionInput,
    @Context() context: any,
  ): Promise<BaseResponse> {
    const userId = this.getUserIdFromContext(context);
    return this.exceptionService.create({
      ...input,
      createdBy: userId,
      updatedBy: userId,
    });
  }

  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'Edit' })
  async updateException(
    @Args('input') input: UpdateExceptionInput,
    @Context() context: any,
  ): Promise<BaseResponse> {
    const userId = this.getUserIdFromContext(context);
    return this.exceptionService.update({
      ...input,
      updatedBy: userId,
    });
  }

  @Mutation(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'Delete' })
  async deleteException(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<BaseResponse> {
    return this.exceptionService.delete(id);
  }

  @Query(() => BaseResponse)
  @RequirePermission({ module: 'Masters', subModule: 'Exception', permission: 'View' })
  async countExceptions(
    @Args('input', { nullable: true }) input?: CountExceptionsInput,
  ): Promise<BaseResponse> {
    return this.exceptionService.count(input?.filters);
  }
} 