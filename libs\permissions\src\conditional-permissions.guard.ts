import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import 'reflect-metadata';
import { PermissionsGuard } from './permissions.guard';
import {
  ConditionalPermission,
  CONDITIONAL_PERMISSION_KEY
} from './conditional-permission.decorator';
import {
  RequiredPermission,
  REQUIRE_PERMISSION_KEY
} from './permissions.decorator';

/**
 * Conditional Permissions Guard
 * Checks permissions dynamically based on input conditions
 */
@Injectable()
export class ConditionalPermissionsGuard implements CanActivate {
  
  constructor(
    private reflector: Reflector,
    private permissionsGuard: PermissionsGuard
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // First check if there are conditional permissions
    const conditionalPermissions = this.reflector.getAllAndOverride<ConditionalPermission[]>(
      CONDITIONAL_PERMISSION_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (conditionalPermissions && conditionalPermissions.length > 0) {
      return this.handleConditionalPermissions(context, conditionalPermissions);
    }

    // Fall back to regular permission check
    return this.permissionsGuard.canActivate(context);
  }

  private async handleConditionalPermissions(
    context: ExecutionContext, 
    conditionalPermissions: ConditionalPermission[]
  ): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const args = ctx.getArgs();
    
    // Get the input from the first argument (usually 'input')
    const input = args.input || args;

    // Find the matching conditional permission
    const matchingPermission = conditionalPermissions.find(cp => cp.condition(input));

    if (!matchingPermission) {
      throw new ForbiddenException(`No matching permission condition found for type: ${input?.type}`);
    }

    // Set the required permission temporarily and use the regular guard
    const requiredPermission: RequiredPermission = matchingPermission.permission;
    
    // Store original metadata
    const originalPermissions = this.reflector.getAllAndOverride<RequiredPermission[]>(
      REQUIRE_PERMISSION_KEY,
      [context.getHandler(), context.getClass()]
    );

    // Set the conditional permission as the required permission
    Reflect.defineMetadata(
      REQUIRE_PERMISSION_KEY, 
      [requiredPermission], 
      context.getHandler()
    );

    try {
      // Use the regular permissions guard
      const result = await this.permissionsGuard.canActivate(context);
      return result;
    } finally {
      // Restore original metadata
      if (originalPermissions) {
        Reflect.defineMetadata(
          REQUIRE_PERMISSION_KEY, 
          originalPermissions, 
          context.getHandler()
        );
      } else {
        Reflect.deleteMetadata(REQUIRE_PERMISSION_KEY, context.getHandler());
      }
    }
  }
}
