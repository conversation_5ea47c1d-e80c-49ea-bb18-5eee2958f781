import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ImportConfiguration } from '../entities/import-configuration.entity';
import { CreateImportConfigurationInput, UpdateImportConfigurationInput, ImportConfigurationResponse } from '../dto/import.dto';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class ImportConfigurationService {
  constructor(
    @InjectModel(ImportConfiguration.name) 
    private importConfigModel: Model<ImportConfiguration>,
    private readonly mongoConnectionService: MongoConnectionService,
  ) {}

  async createConfiguration(
    userId: string,
    input: CreateImportConfigurationInput
  ): Promise<ImportConfigurationResponse> {
    // Upsert logic: Update if userId + templateId combination exists, otherwise create new
    try {
      // Try to find existing configuration with same userId and templateId
      const existingConfig = await this.importConfigModel.findOne({
        // userId: new Types.ObjectId(userId),
        templateId: input.templateId
      });

      if (existingConfig) {
        // Update existing configuration

        const updatedConfig = await this.importConfigModel.findByIdAndUpdate(
          existingConfig._id,
          {
            ...input,
            mappingJson: input.mappingJson ?? {},
            updatedAt: new Date()
          },
          {
            new: true, // Return updated document
            runValidators: true // Run schema validations
          }
        );

        if (!updatedConfig) {
          throw new Error('Failed to update configuration');
        }

        console.log(`Successfully updated import configuration: ${updatedConfig._id}`);
        return {
          message: 'Import configuration updated successfully',
          code: 'CONFIGURATION_UPDATED',
          data: updatedConfig
        };
      } else {
        // Create new configuration
        console.log(`Creating new import configuration for userId: ${userId}, templateId: ${input.templateId}`);

        const configuration = await this.importConfigModel.create({
          userId: new Types.ObjectId(userId),
          ...input,
          mappingJson: input.mappingJson ?? {},
        });

        console.log(`Successfully created import configuration: ${configuration._id}`);
        return {
          message: 'Import configuration created successfully',
          code: 'CONFIGURATION_CREATED',
          data: configuration
        };
      }
    } catch (error) {
      if (error.code === 11000) {
        throw new ConflictException('Template ID already exists for this user');
      }
      throw error;
    }
  }

  async getConfigurationById(
    configurationId: string,
    userId?: string
  ): Promise<ImportConfiguration> {
    const query: any = { _id: new Types.ObjectId(configurationId) };
    if (userId) {
      query.userId = new Types.ObjectId(userId);
    }

    const configuration = await this.importConfigModel.findOne(query);
    if (!configuration) {
      throw new NotFoundException('Import configuration not found');
    }
    return configuration;
  }

  async getConfigurationByTemplateId(
    templateId: string,
    userId?: string
  ): Promise<ImportConfiguration> {
    const query: any = { templateId };
    // if (userId) {
    //   query.userId = new Types.ObjectId(userId);
    // }

    let configuration = await this.importConfigModel.findOne(query);

    if (!configuration) {
      // If not found, fetch template and create config from isImport fields
      const collection = await this.mongoConnectionService.getCollectionByOrgId("templates");
      const template = await collection.findOne({ _id: new Types.ObjectId(templateId) });
      if (!template) {
        throw new NotFoundException('Template not found');
      }
      const isImport = template.view_summary?.isImport || [];
      // Create a default mappingJson where each isImport field maps to itself
      const mappingJson: Record<string, string> = {};
      isImport.forEach((field: string) => {
        mappingJson[field] = field;
      });
      console.log("mappingJson", mappingJson);
      
      if (!userId) {
        throw new NotFoundException('User ID is required to create a new import configuration');
      }

      // Save the new configuration to the DB
      configuration = await this.importConfigModel.create({
        userId: new Types.ObjectId(userId),
        templateId,
        collectionName: template.key || template.name || '',
        mappingJson:mappingJson||{},
        requiredFields: [],
        templateFields:template.fields || [],
        uniqueFields: [],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      console.log("Created new import configuration:", configuration._id);

    }
      if (!configuration.mappingJson) {
        configuration.mappingJson = {};
      }
      return configuration;
  }

  async getUserConfigurations(
    userId: string,
    params: {
      page?: number;
      limit?: number;
      search?: string;
      collectionName?: string;
      isActive?: boolean;
    } = {}
  ): Promise<{
    items: ImportConfiguration[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const {
      page = 1,
      limit = 10,
      search = '',
      collectionName,
      isActive = true
    } = params;

    const query: any = { 
      userId: new Types.ObjectId(userId),
      isActive 
    };

    if (collectionName) {
      query.collectionName = collectionName;
    }

    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };
      query.$or = [
        { templateId: regex },
        { collectionName: regex }
      ];
    }

    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;

    const totalItems = await this.importConfigModel.countDocuments(query);
    const configurations = await this.importConfigModel
      .find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(safeLimit)
      .lean();

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      items: configurations,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async updateConfiguration(
    userId: string,
    input: UpdateImportConfigurationInput
  ): Promise<ImportConfiguration> {
    const { configurationId, ...updateData } = input;
    
    const configuration = await this.importConfigModel.findOneAndUpdate(
      { 
        _id: new Types.ObjectId(configurationId),
        userId: new Types.ObjectId(userId)
      },
      { $set: updateData },
      { new: true }
    );

    if (!configuration) {
      throw new NotFoundException('Import configuration not found');
    }

    return configuration;
  }

  async deleteConfiguration(
    configurationId: string,
    userId: string
  ): Promise<boolean> {
    const result = await this.importConfigModel.deleteOne({
      _id: new Types.ObjectId(configurationId),
      userId: new Types.ObjectId(userId)
    });

    return result.deletedCount > 0;
  }

  async getConfigurationsByCollection(
    userId: string,
    collectionName: string
  ): Promise<ImportConfiguration[]> {
    return this.importConfigModel.find({
      userId: new Types.ObjectId(userId),
      collectionName,
      isActive: true
    }).sort({ updatedAt: -1 });
  }

  async validateConfiguration(
    configurationId: string,
    userId: string
  ): Promise<ImportConfiguration> {
    const configuration = await this.getConfigurationById(configurationId, userId);
    
    // Validate that required fields are present in mapping
    const mappingFields = Object.keys(configuration.mappingJson);
    const missingRequiredFields = configuration.requiredFields.filter(
      field => !mappingFields.includes(field)
    );

    if (missingRequiredFields.length > 0) {
      throw new ConflictException(
        `Required fields missing in mapping: ${missingRequiredFields.join(', ')}`
      );
    }

    return configuration;
  }
}
