import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@ObjectType('Process')
@Schema({
    collection: 'processes',
    timestamps: true,
    toJSON: {
        virtuals: true,
        transform: (doc, ret) => {
            delete ret._id;
            delete ret.__v;
        },
    },
})
export class Process extends Document {
    @Field(() => ID)
    declare id: string;

    @Field()
    @Prop({ required: true, unique: true })
    name: string;
}

export const ProcessSchema = SchemaFactory.createForClass(Process);

import { Types } from 'mongoose';

ProcessSchema.virtual('id').get(function () {
    return (this._id as Types.ObjectId).toHexString();
}); 