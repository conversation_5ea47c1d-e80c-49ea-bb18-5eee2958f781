import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { IdentityService } from './identity.service';
import { Agent } from './entities/agent.entities';
import {
  UpdateAgentInput,
  GetAgentInput,  
  AgentSSOLoginInput,
  GetAgentsInput,
  AgentsResponse
} from './dto/agent.dto';
import { BaseResponse } from './dto/base.response.dto';


@Resolver(() => Agent)
export class AgentResolver {
  constructor(private readonly identityService: IdentityService) { }

  /**
   * Fetches all agents from the system with dynamic search, filter, and sort functionality.
   * @param input - Contains search, filter, sort, and pagination parameters.
   * @returns A promise that resolves to an AgentsResponse with agents and pagination info.
   */
  @Query(() => AgentsResponse, { name: 'Agents' })
  async getAgents(@Args('input', { nullable: true }) input?: GetAgentsInput) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;

    // Parse filters from JSON string if provided
    let filters: Record<string, any> = {};
    if (input?.filters) {
      try {
        filters = JSON.parse(input.filters);
      } catch (error) {       
        filters = {};
      }
    }

    return this.identityService.findAllAgents(
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit
    );
  }

  /**
   * Fetches a specific agent by ID.
   * @param input - Contains the agent ID to fetch.
   * @returns A promise that resolves to an Agent entity.
   */
  @Query(() => Agent, { name: 'Agent', nullable: true })
  async getAgentById(@Args('input') input: GetAgentInput) {
    return this.identityService.findAgentById(input.id);
  }

  /**
   * Updates an existing agent in the system.
   * @param input - Data required to update the agent.
   * @returns A BaseResponse indicating success or failure of agent update.
   */
  @Mutation(() => BaseResponse)
  async updateAgent(@Args('input') input: UpdateAgentInput) {
    const result = await this.identityService.updateAgent(input);
    return result;
  }

  /**
   * Deletes an agent from the system.
   * @param input - Contains the agent ID to delete.
   * @returns A BaseResponse indicating success or failure of agent deletion.
   */
  @Mutation(() => BaseResponse)
  async deleteAgent(@Args('input') input: GetAgentInput) {
    const result = await this.identityService.deleteAgent(input.id);
    return result;
  }


  /**
   * Authenticates an agent using SSO token.
   * @param input - Contains the SSO token.
   * @returns A BaseResponse containing the authentication token.
   */
  @Mutation(() => BaseResponse)
  async agentSSOLogin(@Args('input') input: AgentSSOLoginInput) {
    const result = await this.identityService.agentSSOLogin(input.token);
    return result;
  }
}
