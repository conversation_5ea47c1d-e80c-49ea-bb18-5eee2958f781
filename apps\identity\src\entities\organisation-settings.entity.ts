import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum PreferredCommunication {
  TICKET = 'ticket',
  EMAIL = 'email',
}

registerEnumType(PreferredCommunication, {
  name: 'PreferredCommunication',
});

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'organisation_settings',
})
export class OrganisationSettings extends Document {
  @Field(() => ID)
  declare _id: string;

  @Field()
  @Prop({ required: true })
  organisationId: string;

  @Field(() => PreferredCommunication)
  @Prop({ required: true, enum: PreferredCommunication })
  preferredCommunication: PreferredCommunication;

  @Field()
  @Prop({ required: true })
  accessPortal: boolean;

  @Field()
  @Prop({ required: true })
  accessOrganisation: boolean;

  @Field()
  @Prop({ required: true })
  expiryTime: Date;

  @Field({ nullable: true })
  createdAt?: Date;

  @Field({ nullable: true })
  updatedAt?: Date;
}

export const OrganisationSettingsSchema = SchemaFactory.createForClass(OrganisationSettings);
OrganisationSettingsSchema.index({ organisationId: 1 }, { unique: true }); 