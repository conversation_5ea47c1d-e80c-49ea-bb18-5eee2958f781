import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { BaseEntity } from './base.entity';

@ObjectType()
@Schema({ timestamps: true })
export class Global extends BaseEntity {
    @Field()
    @Prop({ required: true, index: true })
    name: string;

    @Field(() => Boolean)
    @Prop({ required: true, default: false })
    isTable: boolean;

    @Field(() => String, { nullable: true })
    @Prop()
    modalName?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @Prop({ type: [Object] })
    options?: Record<string, any>[];
}

export type GlobalDocument = Global & Document;
export const GlobalSchema = SchemaFactory.createForClass(Global); 