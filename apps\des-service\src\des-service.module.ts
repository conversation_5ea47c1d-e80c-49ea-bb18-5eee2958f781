import { Module } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import {
  ApolloFederationDriver,
  ApolloFederationDriverConfig,
} from '@nestjs/apollo';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { DesServiceController } from './des-service.controller';
import { DesServiceService } from './des-service.service';
import { DesServiceResolver } from './des-service.resolver';
import { TemplateSchema } from './entities/template.entity';
import { DatabaseModule, MongoConnectionService } from '@app/db';
import { NotificationModule } from '@app/notification';
import { Cpt, CptSchema } from './entities/cpt.entity';
import { MasterCptService } from './cpt.service';
import { CptResolver } from './cpt.resolver';
import { PayerSchema } from './entities/payer.entity';
import { PayerService } from './payer.service';
import { PayerResolver } from './payer.resolver';
import { ActionStatusCodeService } from './action-status-code.service';
import { ActionStatusCodeResolver } from './action-status-code.resolver';
import { ActionCodeSchema, ActionStatusCodeMapSchema, StatusCodeSchema, ActionCode, StatusCode, ActionStatusCodeMap } from './entities/action-status-code.entity';
// import { ProcessSchema, Process } from '../../identity/src/entities/process.entity';
// import { ProcessResolver } from '../../identity/src/resolvers/process.resolver';
// import { ProcessService } from '../../identity/src/services/process.service';
import { CptCodeModule } from './cpt-code.module';
import { CptCode, CptCodeSchema } from './entities/cpt-code.entity';
import { Specialty, SpecialtySchema } from './entities/specialty.entity';
import { Icd, IcdSchema } from './entities/icd.entity';
import { Diagnosis, DiagnosisSchema } from './entities/diagnosis.entity';
import { SpecialtyResolver } from './specialty.resolver';
import { SpecialtyService } from './specialty.service';
import { IcdResolver } from './icd.resolver';
import { IcdService } from './icd.service';
import { DiagnosisResolver } from './diagnosis.resolver';
import { DiagnosisService } from './diagnosis.service';
import { Exception, ExceptionSchema } from './entities/exception.entity';
import { ExceptionService } from './exception.service';
import { ExceptionResolver } from './exception.resolver';
import { ProviderModule } from './provider.module';
import { Provider } from './entities/provider.entity';
import { FileUploadModule } from './file-upload.module';
import { GridTemplate, GridTemplateSchema } from './entities/grid_template.entity';
import { GridTemplateService } from './grid_template.service';
import { GridTemplateResolver } from './grid_template.resolver';
import { Global, GlobalSchema } from './entities/global.entity';
import { GlobalService } from './global.service';
import { GlobalResolver } from './global.resolver';
import { OrganisationRole, OrganisationRoleSchema, PermissionsGuard, PermissionsModule, Role, RoleSchema, UserProcessAssignment, UserProcessAssignmentSchema, RedisCacheService } from '@app/permissions';
import { EmailModule } from '@app/email';
import { TemplateSeedingService } from './template-seeding.service';
// import { UserModule } from '../../identity/src/user.module';

@Module({
  imports: [
    DatabaseModule,
    CptCodeModule,
    ProviderModule,
    FileUploadModule,
    NotificationModule,
    PermissionsModule,
    EmailModule,
    // UserModule,
    
    // Set up Mongoose connection
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/asp'),
    
    MongooseModule.forFeature([
      { name: 'Template', schema: TemplateSchema },
      { name: Cpt.name, schema: CptSchema },
      { name: 'Payer', schema: PayerSchema },
      // { name: 'Process', schema: ProcessSchema },
      { name: 'ActionCode', schema: ActionCodeSchema },
      { name: 'StatusCode', schema: StatusCodeSchema },
      { name: 'ActionStatusCodeMap', schema: ActionStatusCodeMapSchema },
      { name: CptCode.name, schema: CptCodeSchema },
      { name: Specialty.name, schema: SpecialtySchema },
      { name: Icd.name, schema: IcdSchema },
      { name: Diagnosis.name, schema: DiagnosisSchema },
      { name: 'Exception', schema: ExceptionSchema },
      { name: 'GridTemplate', schema: GridTemplateSchema },
      { name: Global.name, schema: GlobalSchema },
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },
    ]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2
      },
      buildSchemaOptions: {
        orphanedTypes: [Specialty, Cpt, CptCode, Icd, Diagnosis, Provider, GridTemplate, /*Process,*/ ActionCode, StatusCode, ActionStatusCodeMap, Global]
      },
      context: ({ req }) => {
        const context = {
          req,
          query: req?.body?.query,
          orgId: req?.headers['orgid'],
          userId: req?.headers['userid'],
          subOrgId: req?.headers['suborgid'],
          processId: req?.headers['processid'],
          roleName: req?.headers['rolename'],
        };

        return context;
      },
      playground: process.env.NODE_ENV !== 'production',
      introspection: process.env.NODE_ENV !== 'production',
    }),
  ],
  controllers: [DesServiceController],
  providers: [
    DesServiceService,
    DesServiceResolver,
    MasterCptService,
    CptResolver,
    PayerService,
    PayerResolver,
    ActionStatusCodeService,
    ActionStatusCodeResolver,
    SpecialtyService,
    SpecialtyResolver,
    IcdResolver,
    IcdService,
    DiagnosisResolver,
    DiagnosisService,
    ExceptionService,
    ExceptionResolver,
    GridTemplateService,
    GridTemplateResolver,
    // ProcessResolver,
    // ProcessService,
    GlobalService,
    GlobalResolver,
    TemplateSeedingService,
    {
      provide: PermissionsGuard,
      useFactory: (
        reflector: Reflector,
        UserProcessAssignmentModel: any,
        RoleModel: any,
        OrganisationRoleModel: any,
        cacheService: RedisCacheService,
        mongoConnectionService: MongoConnectionService
      ) => {
        return new PermissionsGuard(
          reflector,
          async (userId: string, orgId: string, subOrgId: string, processId: string) => {
            const result = await UserProcessAssignmentModel.findOne({
              userId,
              orgId,
              subOrganisationId: subOrgId,
              processId,
            });
            return result;
          },
          {
            getSystemRole: async (roleId: string) => {
              const role = await RoleModel.findById(roleId);
              return role;
            },
            getOrganisationRole: async (roleId: string) => {
              const role = await OrganisationRoleModel.findById(roleId);
              return role;
            },
          },
          process.env.JWT_SECRET || 'fallback-secret',
          cacheService,
          mongoConnectionService
        );
      },
      inject: [
        Reflector,
        getModelToken(UserProcessAssignment.name),
        getModelToken(Role.name),
        getModelToken(OrganisationRole.name),
        RedisCacheService,
        MongoConnectionService,
      ],
    },
    {
      provide: APP_GUARD,
      useExisting: PermissionsGuard,
    },
  ],
})
export class DesServiceModule { }
