import { ArgsType, Field, InputType, ObjectType, Int, ID } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsBoolean, IsMongoId } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { Payer } from '../entities/payer.entity';

// ===== CREATE, UPDATE, DELETE DTOs =====

@InputType()
export class CreatePayerInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  values?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdatePayerInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  values?: Record<string, any>;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  flattenedValues?: Record<string, any>;
}

// ===== PAGINATION AND RESPONSE DTOs =====

@ObjectType()
export class PayerPaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNext: boolean;

  @Field()
  hasPrev: boolean;
}

@ObjectType()
export class PayersResponse {
  @Field(() => [GraphQLJSON])
  payers: any[];

  @Field(() => PayerPaginationMeta)
  pagination: PayerPaginationMeta;
}


// ===== MAIN QUERY ARGS DTO =====

@ArgsType()
export class FindAllPayersArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;


  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  filters?: string; // JSON string of filters

  @Field({ nullable: true })
  @IsOptional()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;
}
