import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CptCodeService } from './cpt-code.service';
import { CptCode } from './entities/cpt-code.entity';
import {
  CreateCptCodeInput,
  UpdateCptCodeInput,
  PaginateCptCodeArgs,
  PaginatedCptCodeResponse,
  GetCptCodeInput,
  SuccessResponse,
} from './dto/cpt-code.dto';
import { BaseResponse } from './dto/base.response.dto';
import { HttpStatus, ErrorType, ResponseCode, Success, Error } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { RequirePermission } from '@app/permissions';

@Resolver(() => CptCode)
export class CptCodeResolver {
  constructor(private readonly cptCodeService: CptCodeService) {}

  @Query(() => PaginatedCptCodeResponse, { name: 'cptCodes' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptCodesWithPagination(
    @Args('input', { nullable: true }) input?: GetCptCodeInput,
    @Context() context?: any
  ) {
    const search = input?.search;
    const sortBy = input?.sortBy;
    const sortOrder = input?.sortOrder || 'asc';
    const page = input?.page || 1;
    const limit = input?.limit || 10;
    const selectedFields = input?.selectedFields;
    const filters = input?.filters;

   

    return this.cptCodeService.findAll({
      search,
      filters,
      sortBy,
      sortOrder,
      page,
      limit,
      selectedFields,
    });
  }

  @Query(() => CptCode, { name: 'cptCode' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptCodeById(@Args('id') id: string) {
    return this.cptCodeService.findById(id);
  }

  @Query(() => Number, { name: 'countCptCodes' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async countCptCodes() {
    return this.cptCodeService.count();
  }

  @Query(() => [String], { name: 'cptCodeSortFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptCodeSortFields() {
    return this.cptCodeService.getAvailableSortFields();
  }

  @Query(() => [String], { name: 'cptCodeSearchFields' })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptCodeSearchFields() {
    return this.cptCodeService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Add' })
  async createCptCode(@Args('input') input: CreateCptCodeInput) {
    const result = await this.cptCodeService.create(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Edit' })
  async updateCptCode(@Args('input') input: UpdateCptCodeInput) {
    const result = await this.cptCodeService.update(input);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Delete' })
  async deleteCptCode(@Args('id') id: string) {
    const result = await this.cptCodeService.delete(id);
    return {
      code: result.code,
      message: result.message,
      type: result.type,
      data: result.data
    };
  }
} 