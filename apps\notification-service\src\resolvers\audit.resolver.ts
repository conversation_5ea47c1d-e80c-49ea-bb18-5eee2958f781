import { Resolver, Query, Args } from '@nestjs/graphql';
import { AuditService } from '../services/audit.service';
import { 
  AuditLogConnection, 
  AuditLogQueryInput, 
  AuditStatsType
} from '../dto/audit-log.dto';

@Resolver()
export class AuditResolver {
  constructor(private readonly auditService: AuditService) {}

  @Query(() => AuditLogConnection, { 
    name: 'auditLogs',
    description: 'Get audit logs with pagination, search, filter, and sorting'
  })
  async getAuditLogs(
    @Args('input', { type: () => AuditLogQueryInput, nullable: true }) 
    input?: AuditLogQueryInput
  ): Promise<AuditLogConnection> {
    const queryInput = input || { page: 1, limit: 20 };
    return this.auditService.getAuditLogs(queryInput);
  }

  @Query(() => AuditStatsType, { 
    name: 'auditStats',
    description: 'Get audit log statistics'
  })
  async getAuditStats(): Promise<AuditStatsType> {
    return this.auditService.getAuditStats();
  }

  @Query(() => String, { 
    name: 'testAuditLog',
    description: 'Create a test audit log entry'
  })
  async testAuditLog(): Promise<string> {
    const success = await this.auditService.createTestAuditLog();
    
    if (success) {
      const check = await this.auditService.checkAuditLogs();
      return `✅ Test audit log created! Total logs: ${check.totalCount}`;
    } else {
      return '❌ Failed to create test audit log';
    }
  }

  @Query(() => String, { 
    name: 'auditHealthCheck',
    description: 'Check if audit logging system is working'
  })
  async auditHealthCheck(): Promise<string> {
    const result = await this.auditService.checkAuditLogs();
    
    return JSON.stringify({
      message: result.isWorking ? '✅ Audit logs are working!' : '❌ No audit logs found',
      totalCount: result.totalCount,
      lastLogTime: result.lastLogTime,
      recentLogs: result.recentLogs.length,
    }, null, 2);
  }
}
