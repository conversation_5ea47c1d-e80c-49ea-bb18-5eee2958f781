/**
 * Cache data structures and types for high-performance permission system
 */

export interface CacheMetadata {
  cachedAt: number;
  expiresAt: number;
  version?: string;
  source?: 'db' | 'cache' | 'computed';
}

export interface UserPermissionCacheKey {
  userId: string;
  orgId: string;
  subOrgId: string;
  processId: string;
}

export interface PermissionCheckCacheKey extends UserPermissionCacheKey {
  module: string;
  subModule: string;
  permission: string;
}

export interface CachedUserPermission extends CacheMetadata {
  userId: string;
  orgId: string;
  subOrgId: string;
  processId: string;
  roleId: string;
  roleName: string;
  roleKey: string;
  roleCategory?: string;
  permissions: PermissionModule[];
  assignmentId: string;
  reportToUserId?: string;
  isActive?: boolean;
}

export interface CachedRole extends CacheMetadata {
  roleId: string;
  name: string;
  key: string;
  permissions: PermissionModule[];
  isActive: boolean;
  category?: string;
  type: 'system' | 'organization';
}

export interface CachedPermissionCheck extends CacheMetadata {
  result: boolean;
  roleId: string;
  roleName: string;
}

export interface PermissionModule {
  moduleName: string;
  displayName: string;
  isEnabled: boolean;
  subModules: PermissionSubModule[];
}

export interface PermissionSubModule {
  displayName: string;
  isEnabled: boolean;
  permissions: Permission[];
}

export interface Permission {
  displayName: string;
  isEnabled: boolean;
}

export interface CacheKeyPatterns {
  // User-specific permission cache
  USER_PERMISSION: 'perm:user:{userId}:{orgId}:{subOrgId}:{processId}';
  USER_PERMISSIONS_SET: 'perm:user:{userId}:*';
  
  // Role cache
  SYSTEM_ROLE: 'perm:role:system:{roleId}';
  ORG_ROLE: 'perm:role:org:{roleId}';
  ROLE_PERMISSIONS: 'perm:role:permissions:{roleId}';
  
  // Permission check results cache (for frequently accessed permissions)
  PERMISSION_CHECK: 'perm:check:{userId}:{orgId}:{subOrgId}:{processId}:{module}:{subModule}:{permission}';
  
  // User role assignments
  USER_ROLES: 'perm:user_roles:{userId}';
  USER_ASSIGNMENTS: 'perm:assignments:{userId}';
  
  // Organization-specific caches
  ORG_USERS: 'perm:org:{orgId}:users';
  ORG_ROLES: 'perm:org:{orgId}:roles';
  PROCESS_USERS: 'perm:process:{processId}:users';
  
  // Hierarchy and reporting structure
  USER_HIERARCHY: 'perm:hierarchy:{userId}';
  MANAGER_SUBORDINATES: 'perm:manager:{userId}:subordinates';
  
  // Statistics and monitoring
  CACHE_STATS: 'perm:stats';
  CACHE_HEALTH: 'perm:health';
  INVALIDATION_LOG: 'perm:invalidation_log';
  PERFORMANCE_METRICS: 'perm:metrics';
}

export interface CacheConfiguration {
  // TTL settings (in seconds)
  ttl: {
    userPermission: number;      // 1 hour - user permissions change moderately
    role: number;                // 24 hours - roles change infrequently
    permissionCheck: number;     // 5 minutes - for high-frequency permission checks
    userRoles: number;           // 2 hours - user role assignments
    orgData: number;             // 6 hours - organization data
    hierarchy: number;           // 4 hours - reporting hierarchy
  };
  
  // Cache warming settings
  warming: {
    enabled: boolean;
    batchSize: number;
    concurrency: number;
    scheduleInterval: number;    // in milliseconds
  };
  
  // Performance settings
  performance: {
    maxBatchSize: number;
    pipelineThreshold: number;
    compressionEnabled: boolean;
    compressionThreshold: number; // bytes
  };
  
  // Monitoring settings
  monitoring: {
    metricsEnabled: boolean;
    statsRetentionDays: number;
    alertThresholds: {
      hitRateMin: number;        // minimum cache hit rate %
      responseTimeMax: number;   // maximum response time in ms
      errorRateMax: number;      // maximum error rate %
    };
  };
}

export interface CacheStats {
  // Hit/Miss statistics
  hits: number;
  misses: number;
  hitRate: number;
  
  // Operation counts
  gets: number;
  sets: number;
  deletes: number;
  invalidations: number;
  
  // Performance metrics
  avgResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  
  // Error tracking
  errors: number;
  errorRate: number;
  lastError?: string;
  lastErrorTime?: number;
  
  // Memory usage
  memoryUsed: number;
  keyCount: number;
  
  // Time-based metrics
  timestamp: number;
  uptime: number;
}

export interface CacheInvalidationEvent {
  type: 'user' | 'role' | 'permission' | 'organization' | 'process' | 'system';
  target: string;
  reason: string;
  affectedKeys: string[];
  timestamp: number;
  source: string;
  metadata?: Record<string, any>;
}

export interface CacheWarmupJob {
  id: string;
  type: 'user_permissions' | 'roles' | 'organizations' | 'full';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  startTime: number;
  endTime?: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface PermissionCacheService {
  // Core caching operations
  cacheUserPermission(key: UserPermissionCacheKey, data: CachedUserPermission, ttl?: number): Promise<void>;
  getUserPermission(key: UserPermissionCacheKey): Promise<CachedUserPermission | null>;
  
  cacheRole(roleId: string, data: CachedRole, ttl?: number): Promise<void>;
  getRole(roleId: string): Promise<CachedRole | null>;
  
  cachePermissionCheck(key: PermissionCheckCacheKey, result: CachedPermissionCheck, ttl?: number): Promise<void>;
  getPermissionCheck(key: PermissionCheckCacheKey): Promise<CachedPermissionCheck | null>;
  
  // Batch operations
  batchCacheUserPermissions(items: Array<{ key: UserPermissionCacheKey; data: CachedUserPermission; ttl?: number }>): Promise<void>;
  batchGetUserPermissions(keys: UserPermissionCacheKey[]): Promise<(CachedUserPermission | null)[]>;
  
  // Invalidation operations
  invalidateUserPermissions(userId: string): Promise<void>;
  invalidateRole(roleId: string): Promise<void>;
  invalidateOrganization(orgId: string): Promise<void>;
  invalidateProcess(processId: string): Promise<void>;
  invalidateByPattern(pattern: string): Promise<number>;
  
  // Cache warming
  warmUserPermissions(userIds: string[]): Promise<CacheWarmupJob>;
  warmRoles(roleIds: string[]): Promise<CacheWarmupJob>;
  warmOrganization(orgId: string): Promise<CacheWarmupJob>;
  
  // Monitoring and health
  getStats(): Promise<CacheStats>;
  getHealth(): Promise<boolean>;
  getInvalidationLog(limit?: number): Promise<CacheInvalidationEvent[]>;
  
  // Configuration
  updateConfiguration(config: Partial<CacheConfiguration>): Promise<void>;
  getConfiguration(): Promise<CacheConfiguration>;
}

export interface CacheMiddleware {
  // Pre-request middleware
  beforePermissionCheck?(context: any): Promise<void>;
  
  // Post-request middleware
  afterPermissionCheck?(context: any, result: boolean): Promise<void>;
  
  // Error handling
  onCacheError?(error: Error, context: any): Promise<void>;
  
  // Performance monitoring
  onPerformanceMetric?(metric: string, value: number, context: any): Promise<void>;
}

export const DEFAULT_CACHE_CONFIG: CacheConfiguration = {
  ttl: {
    userPermission: 3600,      // 1 hour
    role: 86400,               // 24 hours
    permissionCheck: 300,      // 5 minutes
    userRoles: 7200,           // 2 hours
    orgData: 21600,            // 6 hours
    hierarchy: 14400,          // 4 hours
  },
  warming: {
    enabled: true,
    batchSize: 100,
    concurrency: 5,
    scheduleInterval: 300000,  // 5 minutes
  },
  performance: {
    maxBatchSize: 1000,
    pipelineThreshold: 10,
    compressionEnabled: true,
    compressionThreshold: 1024, // 1KB
  },
  monitoring: {
    metricsEnabled: true,
    statsRetentionDays: 7,
    alertThresholds: {
      hitRateMin: 80,          // 80% minimum hit rate
      responseTimeMax: 100,    // 100ms maximum response time
      errorRateMax: 5,         // 5% maximum error rate
    },
  },
};

export const CACHE_EVENTS = {
  USER_PERMISSION_CACHED: 'user_permission_cached',
  USER_PERMISSION_INVALIDATED: 'user_permission_invalidated',
  ROLE_CACHED: 'role_cached',
  ROLE_INVALIDATED: 'role_invalidated',
  CACHE_WARMED: 'cache_warmed',
  CACHE_ERROR: 'cache_error',
  PERFORMANCE_ALERT: 'performance_alert',
} as const;

export type CacheEventType = typeof CACHE_EVENTS[keyof typeof CACHE_EVENTS];
