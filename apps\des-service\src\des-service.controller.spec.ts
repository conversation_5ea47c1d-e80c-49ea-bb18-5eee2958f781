import { Test, TestingModule } from '@nestjs/testing';
import { DesServiceController } from './des-service.controller';
import { DesServiceService } from './des-service.service';

describe('DesServiceController', () => {
  let controller: DesServiceController;

  const mockService = {
    findAllForms: jest.fn(),
    findFormById: jest.fn(),
    createForm: jest.fn(),
    updateForm: jest.fn(),
    deleteForm: jest.fn(),
    getForm: jest.fn(),
    findAllTemplates: jest.fn(),
    findTemplateById: jest.fn(),
    createTemplate: jest.fn(),
    updateTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
    getTemplateCount: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DesServiceController],
      providers: [
        {
          provide: DesServiceService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<DesServiceController>(DesServiceController);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllForms', () => {
    it('should get all forms', async () => {
      const mockResult = { forms: [], pagination: {} };
      mockService.findAllForms.mockResolvedValue(mockResult);

      const result = await controller.getAllForms();

      expect(result).toBe(mockResult);
      expect(mockService.findAllForms).toHaveBeenCalled();
    });
  });

  describe('getFormById', () => {
    it('should get form by ID', async () => {
      const formId = 'test-id';
      const mockResult = { form: {} };
      mockService.findFormById.mockResolvedValue(mockResult);

      const result = await controller.getFormById(formId);

      expect(result).toBe(mockResult);
      expect(mockService.findFormById).toHaveBeenCalledWith(formId);
    });
  });

  describe('createForm', () => {
    it('should create a form', async () => {
      const createFormInput = {
        name: 'Test Form',
        description: 'Test Description',
        templateId: 'test-template-001',
        fields: { type: 'text', label: 'Name' },
        createdBy: 'test-user',
      };
      const mockResult = { form: {} };
      mockService.createForm.mockResolvedValue(mockResult);

      const result = await controller.createForm(createFormInput);

      expect(result).toBe(mockResult);
      expect(mockService.createForm).toHaveBeenCalledWith(createFormInput);
    });
  });

  describe('updateForm', () => {
    it('should update a form', async () => {
      const formId = 'test-id';
      const updateFormInput = {
        name: 'Updated Form',
        value: { type: 'text', label: 'Updated Name' },
      };
      const mockResult = { form: {} };
      mockService.updateForm.mockResolvedValue(mockResult);

      const result = await controller.updateForm(formId, updateFormInput as any);

      expect(result).toBe(mockResult);
      expect(mockService.updateForm).toHaveBeenCalledWith({
        ...updateFormInput,
        id: formId,
      });
    });
  });

  describe('getForm', () => {
    it('should get form version', async () => {
      const formId = 'test-id';
      const mockResult = { form: {} };
      mockService.getForm.mockResolvedValue(mockResult);

      const result = await controller.getForm(formId);

      expect(result).toBe(mockResult);
      expect(mockService.getForm).toHaveBeenCalledWith({ formId });
    });
  });

  describe('deleteForm', () => {
    it('should delete a form', async () => {
      const formId = 'test-id';
      const mockResult = { message: 'Form deleted successfully' };
      mockService.deleteForm.mockResolvedValue(mockResult);

      const result = await controller.deleteForm(formId);

      expect(result).toBe(mockResult);
      expect(mockService.deleteForm).toHaveBeenCalledWith(formId);
    });
  });
});
