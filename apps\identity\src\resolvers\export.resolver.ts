import { Resolver, Mutation, Args, Query, ObjectType, Field, Int, Context } from '@nestjs/graphql';
import { ExportService } from '../services/export.service';
import { StartExportInput, ExportTaskResponse } from '../dto/export.dto';
import { TaskQueue, StatusLog } from '../entities/task.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { RequirePermission } from '@app/permissions';
import * as jwt from 'jsonwebtoken';

@ObjectType()
class ProcessExportResponse {
  @Field()
  message: string;
  @Field()
  status: string;
}

@ObjectType()
class ExportPaginationInfo {
  @Field(() => Int)
  page: number;
  @Field(() => Int)
  limit: number;
  @Field(() => Int)
  total: number;
  @Field(() => Int)
  totalItems: number;
  @Field(() => Int)
  totalPages: number;
  @Field()
  hasNext: boolean;
  @Field()
  hasPrev: boolean;
}

@ObjectType()
class ExportPagination {
  @Field(() => [TaskQueue])
  items: TaskQueue[];
  @Field(() => ExportPaginationInfo)
  pagination: ExportPaginationInfo;
}

@ObjectType()
class ExportDeleteResponse {
  @Field()
  success: boolean;
  @Field()
  message: string;
}

@Resolver(() => TaskQueue)
export class ExportResolver {
  constructor(private readonly exportService: ExportService) {}

  private getUserEmailFromContext(context: any): string | undefined {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    return typeof decoded === 'object' && 'email' in decoded ? (decoded as any).email : undefined;
  }

  @Mutation(() => ExportTaskResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'Export' }
  // )
  async startExport(@Args('input') input: StartExportInput, @Context() context: any): Promise<ExportTaskResponse> {
    console.log('🚀 StartExport mutation called with input:', input);

    try {
      const email = this.getUserEmailFromContext(context);
      console.log('📧 User email from context:', email);

      const taskId = await this.exportService.createExportTask({ ...input, createdByEmail: email });
      console.log('✅ Export task created with ID:', taskId);

      return { message: 'Export initiated successfully. Check the Download Queue for status.', taskId };
    } catch (error) {
      console.error('❌ Error in startExport mutation:', error);
      throw error;
    }
  }

  @Mutation(() => ProcessExportResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'Export' }
  // )
  async processExport(@Args('taskId') taskId: string): Promise<ProcessExportResponse> {
    await this.exportService.processExportTask(taskId);
    const task = await this.exportService.getExportStatus(taskId);
    return { message: 'Export processed', status: task?.status ?? 'unknown' };
  }

  @Mutation(() => ExportDeleteResponse)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'Delete' }
  // )
  async deleteTask(@Args('taskId') taskId: string): Promise<ExportDeleteResponse> {
    const success = await this.exportService.deleteTaskById(taskId);
    return {
      success,
      message: success ? 'Task deleted successfully.' : 'Task not found or could not be deleted.'
    };
  }

  @Query(() => TaskQueue, { nullable: true })
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'View' }
  // )
  async exportStatus(@Args('taskId') taskId: string): Promise<TaskQueue | null> {
    return this.exportService.getExportStatus(taskId);
  }

  @Query(() => [StatusLog], { nullable: true })
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'View' }
  // )
  async exportStatusLog(@Args('taskId') taskId: string): Promise<StatusLog[] | null> {
    const task = await this.exportService.getExportStatus(taskId);
    return task?.statusLog || null;
  }

  @Query(() => ExportPagination)
  // @RequirePermission(
  //   { module: 'Tickets', subModule: 'All Ticket', permission: 'View' }
  // )
  async allExports(
    @Args('page', { type: () => Int, nullable: true }) page?: number,
    @Args('limit', { type: () => Int, nullable: true }) limit?: number,
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('filters', { type: () => GraphQLJSON, nullable: true }) filters?: Record<string, any>,
    @Args('sortBy', { type: () => String, nullable: true }) sortBy?: string,
    @Args('sortOrder', { type: () => String, nullable: true }) sortOrder?: 'asc' | 'desc',
    @Args('selectedFields', { type: () => String, nullable: true }) selectedFields?: string,
    @Context() context?: any,
  ): Promise<ExportPagination> {
    let parsedFields: Record<string, number> | undefined = undefined;
    if (selectedFields) {
      try {
        parsedFields = JSON.parse(selectedFields);
      } catch {}
    }

    // Extract userId from context for filtering by createdBy
    let userId: string | undefined;
    try {
      if (context?.req?.headers?.authorization) {
        const token = context.req.headers.authorization.replace('Bearer ', '');
        const decoded = jwt.decode(token) as any;
        userId = decoded?.userId || decoded?.id || decoded?.sub;
      }
    } catch (error) {
      console.warn('Failed to extract userId from token:', error);
    }

   

    // filters is now expected as a JSON object, not a string
    const result = await this.exportService.getAllExports({
      page,
      limit,
      search,
      filters,
      sortBy,
      sortOrder,
      userId,
      selectedFields: parsedFields,
    });
    return {
      items: result.items,
      pagination: result.pagination,
    };
  }
} 