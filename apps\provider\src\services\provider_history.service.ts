import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ProviderHistory } from '../entities/provider_history';
import { CreateProviderHistoryInput, UpdateProviderHistoryInput } from '../dto/provider_history.dto';
import { ErrorType, Error, ResponseCode, HttpStatus, Success } from '@app/error';
import { MongoConnectionService } from '@app/db';

@Injectable()
export class ProviderHistoryService {
  constructor(
    private readonly mongoConnectionService: MongoConnectionService,
    // private providerTicketModel: Model<ProviderTicket>,
  ) { }


  /**
 * Get user collection based on orgId, fallback to master DB
 */
  private async getProviderHistoryCollection(orgId?: string) {
    return this.mongoConnectionService.getCollectionByOrgId('providerHistories', orgId);
  }


  async findAll(
    search?: string,
    selectedFields?: Record<string, any>, // optional field projection
    filters?: Record<string, any>,
    sortBy?: string,
    sortOrder: 'asc' | 'desc' = 'asc',
    page = 1,
    limit = 10,
    userId?: string,
    orgId?: string
  ): Promise<{
    providerHistory: any[];
    pagination: {
      page: number;
      limit: number;
      total?: number;
      totalItems: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const userCollection = await this.getProviderHistoryCollection(orgId);
    let query: any = {};


    // === Load sample document for field detection ===
    const sampleDoc = await userCollection.findOne();
    const allFields = sampleDoc ? Object.keys(sampleDoc) : [];

    const excludedFields = [
      '__v', '_id', 'values', 'flattenedValues', 'token', 'secretKey',
      'qrcode', 'attempt', 'dailyOtpAttempts', 'lastOtpAttemptDate',
      'createdAt', 'updatedAt'
    ];

    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
      searchableFields = Object.keys(selectedFields).filter(
        key => selectedFields[key] === 1 && !excludedFields.includes(key)
      );
    } else {
      projection = allFields.reduce((acc, key) => {
        if (!['__v', 'values', 'flattenedValues'].includes(key)) acc[key] = 1;
        return acc;
      }, {} as Record<string, number>);

      searchableFields = allFields.filter(field => !excludedFields.includes(field));
    }

    // === Search Handling ===
    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };

      // Search only in the determined searchable fields
      query.$or = searchableFields.map(field => ({ [field]: regex }));

      // Allow search by _id if valid ObjectId
      if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
        query.$or.push({ _id: searchTerm });
      }

      // Handle date search separately if the search term looks like a date
      if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
        try {
          const searchDate = new Date(searchTerm);
          if (!isNaN(searchDate.getTime())) {
            // Search for dates within the same day
            const startOfDay = new Date(searchDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(searchDate);
            endOfDay.setHours(23, 59, 59, 999);

            query.$or.push(
              { createdAt: { $gte: startOfDay, $lte: endOfDay } },
              { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
            );
          }
        } catch (error) {
          // Ignore invalid date formats
        }
      }
    }
    let parsedFilters: Record<string, any> = {};
    // === Filters Handling ===
    if (filters) {
      try {
        parsedFilters =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters?.filters || filters;

        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i',
                }
                : value;
          }
        }
      } catch (err) {
        console.warn('Invalid filters provided. Skipping filters.', err);
      }
    }


    if (userId && parsedFilters.main_client) {
      delete query.created_by;
    }

    // === Sorting Handling ===
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }
    // === Pagination ===
    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;
    const totalItems = await userCollection.countDocuments(query);
    const providerHistory = await userCollection
      .find(query)
      .project(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .toArray();

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      providerHistory,
  pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems, // always a number, never null
        totalItems: totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async findById(id: string, orgId?: string, userId?: string): Promise<ProviderHistory> {
    try {
      const userCollection = await this.getProviderHistoryCollection(orgId);
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.PROVIDER,
          'Invalid ID format',
        );
      }

      const providerHistoryDoc = await userCollection.findOne({ _id: new Types.ObjectId(id) });

      if (!providerHistoryDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.PROVIDER,
          'Provider history not found',
        );
      }

      // Convert the plain MongoDB document to ProviderHistory type
      const providerHistory = providerHistoryDoc as unknown as ProviderHistory;
      return providerHistory;

    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to find provider history: ${error.message}`,
      );
    }
  }

  async create(input: CreateProviderHistoryInput, orgId?: string) {
    try {
      const userCollection = await this.getProviderHistoryCollection(orgId);
      const result = await userCollection.insertOne({
        ...input,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.PROVIDER,
        { insertedId: result.insertedId }
      );
    } catch (error: any) {
      console.error('ProviderHistory creation failed:', error);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.PROVIDER,
        `Failed to create provider history: ${error.message || 'Unknown error'}`
      );
    }
  }

 async update(input: UpdateProviderHistoryInput, orgId?: string) {
    const { id, ...updateData } = input;

    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Invalid ID format'
      );
    }

    const userCollection = await this.getProviderHistoryCollection(orgId);
    const result = await userCollection.findOneAndUpdate(
      { _id: new Types.ObjectId(id) },
      { $set: { ...updateData, updatedAt: new Date() } },
      { returnDocument: 'after' }
    );

    if (!result?.value) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider history not found'
      );
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.PROVIDER,
      { providerHistory: result.value }
    );
  }

  async delete(id: string, orgId?: string) {
    if (!Types.ObjectId.isValid(id)) {
      throw new Error(
        HttpStatus.BAD_REQUEST,
        ResponseCode.INVALID_PARAMETERS,
        ErrorType.PROVIDER,
        'Invalid ID format'
      );
    }

    const userCollection = await this.getProviderHistoryCollection(orgId);
    const result = await userCollection.findOneAndDelete({ _id: new Types.ObjectId(id) });

    if (!result?.value) {
      throw new Error(
        HttpStatus.NOT_FOUND,
        ResponseCode.NOT_FOUND,
        ErrorType.PROVIDER,
        'Provider history not found'
      );
    }

    return new Success(
      HttpStatus.OK,
      ResponseCode.SUCCESS,
      ErrorType.PROVIDER,
      { message: 'Provider history deleted successfully' }
    );
  }
}