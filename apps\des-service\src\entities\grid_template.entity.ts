import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { BaseEntity } from './base.entity';
import { Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class GridTemplate extends BaseEntity {

    @Field()
    @Prop({ required: true, index: true })
    name: string;

    @Field(() => GraphQLJSON)
    @Prop({ type: [Object], required: true })
    grid_fields: Record<string, any>[];

    @Field(() => ID, { nullable: true })
    @Prop({ type: Types.ObjectId })
    user_id?: Types.ObjectId;

    @Field()
    @Prop({ required: true })
    type: string;

    @Prop({ required: true, index: true })
    grid_hash: string;
}

export const GridTemplateSchema = SchemaFactory.createForClass(GridTemplate);

// Create indexes for efficient querying
GridTemplateSchema.index({ name: 1, user_id: 1 }, { unique: true });
GridTemplateSchema.index({ user_id: 1 });
GridTemplateSchema.index({ isActive: 1 });
