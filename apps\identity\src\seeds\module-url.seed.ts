import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ModuleUrl } from '../entities/module-url.entity';

@Injectable()
export class ModuleUrlSeeder implements OnModuleInit {
  constructor(
    @InjectModel(ModuleUrl.name) private moduleUrlModel: Model<ModuleUrl>
  ) {}

  async onModuleInit() {
    await this.seedModuleUrls();
  }

  private async seedModuleUrls() {
    try {
      // Check if seed data already exists
      const existingCount = await this.moduleUrlModel.countDocuments();
      if (existingCount > 0) {
        console.log('🌱 Module URL seed data already exists, skipping seeding');
        return;
      }

      console.log('🌱 Starting Module URL seeding...');

      // Default URL time in days (30 days)
      const defaultUrlTime = 30;

      // Seed data for your collections
      const seedData = [
        // CPTS Collection
        {
          moduleName: 'Cpt dictionary',
          collectionName: 'cpts',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: true,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Tickets Collection (Provider Credential)
        {
          moduleName: 'Provider Credentials',
          collectionName: 'tickets',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: true,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Exceptions Collection
        {
          moduleName: 'Exceptions',
          collectionName: 'exceptions',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: true,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Action Status Code Maps Collection
        {
          moduleName: 'Action Status Code',
          collectionName: 'action_status_code_maps',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: true,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Organisations Collection
        {
          moduleName: 'Organisations',
          collectionName: 'organisations',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: false,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Payers Collection
        {
          moduleName: 'Payers',
          collectionName: 'payers',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: false,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Providers Collection
        {
          moduleName: 'Providers',
          collectionName: 'providers',
          importUrlTime: defaultUrlTime,
          backupUrlTime: defaultUrlTime,
          exportUrlTime: defaultUrlTime,
          auditUrlTime: defaultUrlTime,
          isImport: false,
          isExport: true,
          updatedBy: 'system',
          isActive: true
        },

        // Additional collections for other modules (optional)
        
        // Identity module collections
        // {
        //   moduleName: 'identity',
        //   collectionName: 'users',
        //   importUrlTime: getExpirationTime(30),
        //   backupUrlTime: getExpirationTime(30),
        //   exportUrlTime: getExpirationTime(30),
        //   auditUrlTime: getExpirationTime(30),
        //   updatedBy: 'system',
        //   isActive: true
        // },

        // {
        //   moduleName: 'identity',
        //   collectionName: 'organizations',
        //   importUrlTime: getExpirationTime(30),
        //   backupUrlTime: getExpirationTime(30),
        //   exportUrlTime: getExpirationTime(30),
        //   auditUrlTime: getExpirationTime(30),
        //   updatedBy: 'system',
        //   isActive: true
        // },

        // // DES module collections
        // {
        //   moduleName: 'des',
        //   collectionName: 'data_entries',
        //   importUrlTime: getExpirationTime(30),
        //   backupUrlTime: getExpirationTime(30),
        //   exportUrlTime: getExpirationTime(30),
        //   auditUrlTime: getExpirationTime(30),
        //   updatedBy: 'system',
        //   isActive: true
        // },

        // // Notification module collections
        // {
        //   moduleName: 'notification',
        //   collectionName: 'notifications',
        //   importUrlTime: getExpirationTime(30),
        //   backupUrlTime: getExpirationTime(30),
        //   exportUrlTime: getExpirationTime(30),
        //   auditUrlTime: getExpirationTime(30),
        //   updatedBy: 'system',
        //   isActive: true
        // }
      ];

      // Insert seed data
      const insertedUrls = await this.moduleUrlModel.insertMany(seedData);

      console.log('✅ Module URL seeding completed successfully!');
      console.log(`📊 Seeded ${insertedUrls.length} module URL records:`);
      
      // Log summary by module
      const summary = seedData.reduce((acc, item) => {
        acc[item.moduleName] = (acc[item.moduleName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      Object.entries(summary).forEach(([module, count]) => {
        console.log(`   📁 ${module}: ${count} collections`);
      });

      console.log('🕒 All URLs set to 30 days retention period');

    } catch (error) {
      console.error('❌ Module URL seeding failed:', error);
      throw error;
    }
  }

  // Method to manually reseed (useful for development)
  async reseedModuleUrls() {
    try {
      console.log('🔄 Clearing existing module URL data...');
      await this.moduleUrlModel.deleteMany({});
      
      console.log('🌱 Reseeding module URLs...');
      await this.seedModuleUrls();
      
      console.log('✅ Module URL reseeding completed!');
    } catch (error) {
      console.error('❌ Module URL reseeding failed:', error);
      throw error;
    }
  }


}
