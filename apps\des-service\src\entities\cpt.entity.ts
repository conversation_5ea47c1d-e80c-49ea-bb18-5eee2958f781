import { ObjectType, Field, ID, } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';
import { BaseEntity } from './base.entity';
import { ClientType } from './template.entity';
import { Specialty } from './specialty.entity';
import { Diagnosis } from './diagnosis.entity';
import { CptCode } from './cpt-code.entity';
import { Icd } from './icd.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'cpts',
  strict: false,
  
})
export class Cpt extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String, { nullable: true })
  @Prop({ index: true, sparse: true })
  code?: string;

  @Field(() => ClientType)
  @Prop({ type: String, enum: ClientType, required: true })
  type: ClientType;

  @Field(() => GraphQLJSON)
  @Prop({
    type: Object,
    required: true,
    get: function (data: any) {
      try {
        return typeof data === 'string' ? JSON.parse(data) : data;
      } catch (e) {
        return data;
      }
    },
    set: function (data: any) {
      return typeof data === 'string' ? data : JSON.stringify(data);
    }
  })
  values: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @Prop({ index: true })
  templateId?: string;

  @Field(() => Boolean)
  @Prop({ default: true })
  isActive: boolean;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  updatedBy?: string;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'Specialty', required: true })
  specialtyId: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'Diagnosis', required: true })
  diagnosisId: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'CptCode', required: true })
  cptCodeId: Types.ObjectId;

  @Field(() => ID, { nullable: true })
  @Prop({ type: Types.ObjectId, ref: 'Icd', required: true })
  icdId: Types.ObjectId;

  @Field(() => Specialty, { nullable: true })
  specialty?: Specialty;

  @Field(() => Diagnosis, { nullable: true })
  diagnosis?: Diagnosis;

  @Field(() => CptCode, { nullable: true })
  cptCode?: CptCode;

  @Field(() => Icd, { nullable: true })
  icd?: Icd;

  @Prop()
  clientName: string;

  @Prop()
  legalBusinessName: string;

  @Prop()
  clientEmail: string;

  @Prop()
  clientID: string;

  @Prop()
  telephone: string;

  @Prop()
  address1: string;

  @Prop()
  stateProvince: string;

  @Prop()
  zipPostalCode: string;

  @Prop()
  county: string;

  @Prop()
  country: string;
}

export const CptSchema = SchemaFactory.createForClass(Cpt);

// Add virtual fields for references
CptSchema.virtual('specialty', {
  ref: 'Specialty',
  localField: 'specialtyId',
  foreignField: '_id',
  justOne: true
});

CptSchema.virtual('diagnosis', {
  ref: 'Diagnosis',
  localField: 'diagnosisId',
  foreignField: '_id',
  justOne: true
});

CptSchema.virtual('cptCode', {
  ref: 'CptCode',
  localField: 'cptCodeId',
  foreignField: '_id',
  justOne: true
});

CptSchema.virtual('icd', {
  ref: 'Icd',
  localField: 'icdId',
  foreignField: '_id',
  justOne: true
});

// Add text indexes for full-text search
CptSchema.index({
  type: 'text',
  values: 'text'
}, {
  weights: {
    type: 10,
    values: 5
  },
  name: "CptSearchIndex"
});

// Add compound indexes for common search patterns
// CptSchema.index({ type: 1, isActive: 1 });
// CptSchema.index({ createdAt: -1 });
// CptSchema.index({ updatedAt: -1 });
CptSchema.index({ specialtyId: 1, diagnosisId: 1, cptCodeId: 1, icdId: 1 }, { unique: true });// Add indexes for foreign key references
// CptSchema.index({ specialtyId: 1 });
// CptSchema.index({ diagnosisId: 1 });
// CptSchema.index({ cptCodeId: 1 });
// CptSchema.index({ icdId: 1 });
