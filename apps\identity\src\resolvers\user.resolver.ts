import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UserService } from '../services/user.service';
import { CreateSystemUserInput, UpdateSystemUserInput, UserResponse, PaginateUserArgs, AssignUserRoleInput, UpdateRolePermissionsInput, ModulePermissionInput, AssignRoleHierarchyInput, RemoveRoleMappingInput, AssignOperationsRoleInput, AssignAuditRoleInput, AssignManagementsRoleInput, ProcessAssignmentsByCategoryResult, RemoveOperationsRoleInput, RemoveAuditRoleInput, RemoveManagementsRoleInput, GetUsersUnderManagerInput, UsersUnderManagerResponse } from '../dto/user.dto';
import { User } from './../entities/user.entity';
import * as jwt from 'jsonwebtoken';
import { GraphQLJSONObject, GraphQLJSON } from 'graphql-type-json';
import { ObjectType, Field, Int, ArgsType } from '@nestjs/graphql';
import { RequirePermission, RedisCacheService } from '@app/permissions'

import { UseGuards, ForbiddenException } from '@nestjs/common';
import { JwtAuthGuard } from '@app/permissions';
import { AuditHelperService } from '@app/audit/audit-helper.service';
import { MongoConnectionService } from '@app/db';


@ObjectType()
export class Permission {
  @Field()
  displayName: string;

  @Field()
  isEnabled: boolean;
}

@ObjectType()
export class SubModule {
  @Field()
  displayName: string;

  @Field()
  isEnabled: boolean;

  @Field(() => [Permission])
  permissions: Permission[];
}

@ObjectType()
export class ModulePermission {
  @Field()
  moduleName: string;

  @Field()
  displayName: string;

  @Field()
  isEnabled: boolean;

  @Field(() => [SubModule])
  subModules: SubModule[];
}

@ObjectType()
export class RoleWithPermissions {
  @Field(() => String)
  _id: string;

  @Field()
  name: string;

  @Field()
  createdAt: string;

  @Field()
  updatedAt: string;

  @Field(() => GraphQLJSON)
  permissions: any;

  @Field({ nullable: true })
  isActive?: boolean;

  @Field({ nullable: true })
  category?: string;
}

@ObjectType()
export class PaginationRolesResponse {
  @Field(() => [RoleWithPermissions])
  items: RoleWithPermissions[];

  @Field(() => GraphQLJSON)
  pagination: any;
}

@ArgsType()
export class RoleListArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number = 10;

  @Field({ nullable: true })
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  filters?: any;

  @Field({ nullable: true })
  sortBy?: string;

  @Field({ nullable: true, defaultValue: 'asc' })
  sortOrder?: 'asc' | 'desc';
}

@ObjectType()
export class OrgHierarchyResult {
  @Field(() => [GraphQLJSON], { nullable: true })
  organizations?: any[];

  @Field(() => [GraphQLJSON], { nullable: true })
  subOrganizations?: any[];

  @Field(() => [GraphQLJSON], { nullable: true })
  process?: any[];
}

// Keep the enum for internal use only
export enum SelectHeaderType {
  ORGANISATION = 'organisation',
  SUBORGANISATION = 'suborganisation',
  PROCESS = 'process',
}

@Resolver(() => UserResponse)
@UseGuards(JwtAuthGuard)
export class UserResolver {
  constructor(
    private readonly userService: UserService,
    private readonly auditHelperService: AuditHelperService,
    private readonly cacheService: RedisCacheService,
    private readonly mongoConnectionService: MongoConnectionService
  ) { }

  private getUserIdFromContext(context: any): string | undefined {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    return typeof decoded === 'object' && 'userId' in decoded ? (decoded as any).userId : undefined;
  }
  private getUserFromContext(context: any): Record<string, any> | undefined {
    const authHeader = context.req.headers.authorization ?? '';
    const token = authHeader.replace('Bearer ', '');
    const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret');
    return typeof decoded === 'object' && 'userId' in decoded ? (decoded as any) : undefined;
  }
  private mapUser(user: any): UserResponse {
    if (!user) return user;
    const obj = user.toObject ? user.toObject() : { ...user };
    obj.id = obj._id ? obj._id.toString() : obj.id;
    return obj;
  }

  private async getProcessCollection() {
    if (!this.mongoConnectionService) {
      console.error('❌ MongoConnectionService is not initialized');
      throw new Error('MongoConnectionService is not initialized');
    }

    try {
      return this.mongoConnectionService.getCollectionByOrgId('organisation_process_mappings');
    } catch (error) {
      console.error('❌ Failed to get process collection:', error.message);
      throw new Error(`Failed to get process collection: ${error.message}`);
    }
  }

  private async filterEnabledProcesses(processes: any[], subOrganisationId: string): Promise<any[]> {
    if (!processes || processes.length === 0) {
      return [];
    }

    try {
      // Check if MongoConnectionService is available
      if (!this.mongoConnectionService) {
        console.warn(`⚠️ [filterEnabledProcesses] MongoConnectionService not available, returning all processes`);
        return processes;
      }

      const processCollection = await this.getProcessCollection();
      const enabledProcesses: any[] = [];

      for (const process of processes) {
        const processId = process.id;

        // Check if process is enabled for this sub-organisation
        const checkProcessEnable = await processCollection.findOne({
          processId: processId,
          subOrganisationId: subOrganisationId,
          isActive: true
        });

        if (checkProcessEnable) {
          enabledProcesses.push(process);
        } else {
          console.log(`🚫 [SelectHeader] Process ${processId} is disabled for sub-organisation ${subOrganisationId}`);
        }
      }

      console.log(`✅ [SelectHeader] Filtered processes: ${enabledProcesses.length}/${processes.length} enabled for sub-organisation ${subOrganisationId}`);
      return enabledProcesses;
    } catch (error) {
      console.error(`❌ [SelectHeader] Error filtering enabled processes for sub-organisation ${subOrganisationId}:`, error.message);

      // If MongoConnectionService is not initialized, return all processes as fallback
      if (error.message.includes('MongoConnectionService is not initialized')) {
        console.warn(`⚠️ [filterEnabledProcesses] Falling back to returning all processes due to MongoConnectionService issue`);
        return processes;
      }

      // For other errors, still return all processes to avoid breaking functionality
      console.warn(`⚠️ [filterEnabledProcesses] Falling back to returning all processes due to error`);
      return processes;
    }
  }

  @Mutation(() => UserResponse)
  @RequirePermission(
    { module: 'User Management', subModule: 'System Users', permission: 'Add' }
  )
  async createSystemUser(
    @Args('input') input: CreateSystemUserInput,
    @Context() context: any,
  ): Promise<UserResponse> {
    const userId = this.getUserIdFromContext(context);
    const loggedUser = this.getUserFromContext(context);

    if (!userId) throw new Error('Unauthorized: userId not found in token');
    const user = await this.userService.create(input, userId);

    // Invalidate cache for the newly created user
    try {
      await this.cacheService.invalidateUserPermissions(user._id.toString());
      await this.cacheService.invalidateUserHeaderCache(user._id.toString());
      console.log(`🗑️ [createSystemUser] Cache invalidated for new user: ${user._id.toString()}`);
    } catch (err) {
      console.warn(`⚠️ [createSystemUser] Failed to invalidate cache for user ${user._id.toString()}:`, err.message);
    }

    // Log audit event
    await this.auditHelperService.logAuditEvent({
      eventType: 'CREATE',
      recordType: 'User',
      afterData: user,
      authoredInfo: {
        id: userId,
        name: loggedUser?.name || 'System User',
        email: loggedUser?.email || 'System User'
      }
    });

    return this.mapUser(user);
  }

  @Query(() => GraphQLJSONObject)
  @UseGuards(JwtAuthGuard)
  @RequirePermission(
    { module: 'User Management', subModule: 'System Users', permission: 'View' }
  )
  async systemUsers(@Args() args: PaginateUserArgs, @Context() context: any): Promise<any> {
    const userId = this.getUserIdFromContext(context);
    if (!userId) throw new Error('Unauthorized: userId not found in token');
    const result = await this.userService.findAll(args, userId);
    result.items = result.items.map((u: any) => this.mapUser(u));
    return result;
  }

  @Query(() => UserResponse, { nullable: true })
  @RequirePermission(
    { module: 'User Management', subModule: 'System Users', permission: 'View' }
  )
  async systemUser(@Args('id') id: string): Promise<UserResponse | null> {
    const user = await this.userService.findOne(id);
    return user ? this.mapUser(user) : null;
  }

  @Mutation(() => UserResponse, { nullable: true })
  @RequirePermission(
    { module: 'User Management', subModule: 'System Users', permission: 'Edit' }
  )
  async updateSystemUser(
    @Args('input') input: UpdateSystemUserInput,
    @Context() context: any,
  ): Promise<UserResponse | null> {
    const userId = this.getUserIdFromContext(context);
    const loggedUser = this.getUserFromContext(context);

    if (!userId) throw new Error('Unauthorized: userId not found in token');

    // Get the before data for audit logging
    const beforeData = await this.userService.findOne(input.id);

    const user = await this.userService.update(input, userId);

    // Invalidate cache for the updated user
    if (user) {
      try {
        await this.cacheService.invalidateUserPermissions(user._id.toString());
        await this.cacheService.invalidateUserHeaderCache(user._id.toString());
        console.log(`🗑️ [updateSystemUser] Cache invalidated for updated user: ${user._id.toString()}`);
      } catch (err) {
        console.warn(`⚠️ [updateSystemUser] Failed to invalidate cache for user ${user._id.toString()}:`, err.message);
      }
    }

    // Log audit event with before and after data
    await this.auditHelperService.logAuditEvent({
      eventType: 'UPDATE',
      recordType: 'User',
      beforeData: beforeData,
      afterData: user,
      authoredInfo: {
        id: userId,
        name: loggedUser?.name || 'System User',
        email: loggedUser?.email || 'System User'
      }
    });

    return user ? this.mapUser(user) : null;
  }

  @Mutation(() => UserResponse, { nullable: true })
  @RequirePermission(
    { module: 'User Management', subModule: 'System Users', permission: 'Delete' }
  )
  async removeSystemUser(@Args('id') id: string,
    @Context() context: any): Promise<UserResponse | null> {
    const userId = this.getUserIdFromContext(context);
    const loggedUser = this.getUserFromContext(context);
    if (!userId) throw new Error('Unauthorized: userId not found in token');

    // Get the before data for audit logging
    const beforeData = await this.userService.findOne(id);

    const user = await this.userService.remove(id, userId);

    // Invalidate cache for the deleted user
    if (user) {
      try {
        await this.cacheService.invalidateUserPermissions(user._id.toString());
        await this.cacheService.invalidateUserHeaderCache(user._id.toString());
        console.log(`🗑️ [removeSystemUser] Cache invalidated for deleted user: ${user._id.toString()}`);
      } catch (err) {
        console.warn(`⚠️ [removeSystemUser] Failed to invalidate cache for user ${user._id.toString()}:`, err.message);
      }
    }

    // Log audit event with before data (what was deleted)
    await this.auditHelperService.logAuditEvent({
      eventType: 'DELETE',
      recordType: 'User',
      beforeData: beforeData, // What was deleted
      afterData: null, // Nothing after deletion
      authoredInfo: {
        id: userId,
        name: loggedUser?.name || 'System User',
        email: loggedUser?.email || 'System User'
      }
    });

    return user ? this.mapUser(user) : null;
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'User Management', subModule: 'Roles and Managements', permission: 'Edit' }
  )
  async assignUserRole(
    @Args('input') input: AssignUserRoleInput,
  ): Promise<boolean> {
    // Invalidate cache for the user being assigned the role
    try {
      await this.cacheService.invalidateUserPermissions(input.userId);
      await this.cacheService.invalidateUserHeaderCache(input.userId);
      console.log(`🗑️ [assignUserRole] Cache invalidated for user: ${input.userId}`);
    } catch (err) {
      console.warn(`⚠️ [assignUserRole] Failed to invalidate cache for user ${input.userId}:`, err.message);
    }

    await this.userService.assignUserRole(input);
    return true;
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'User Management', subModule: 'Roles and Managements', permission: 'Edit' }
  )
  async updateRolePermissions(
    @Args('input') input: UpdateRolePermissionsInput,
  ): Promise<boolean> {
    // Update role permissions
    await this.userService.updateRolePermissions(input);

    // Clear permission cache to ensure all permission changes take effect immediately
    try {
      console.log(`🧹 [updateRolePermissions] Clearing permission cache due to role permission update for role: ${input.roleId}`);

      // Use script-based cache clearing since it works with Redis Cluster
      const result = await this.cacheService.clearPermissionCacheWithScript();

      if (result.success) {
        console.log(`✅ [updateRolePermissions] Cache cleared successfully: ${result.message} (${result.deletedKeys} keys deleted)`);
        console.log(`🧹 [updateRolePermissions] Cache clear details:`, result.details);
      } else {
        console.error(`❌ [updateRolePermissions] Cache clear failed: ${result.message}`);

        // Fallback to regular method if script fails
        console.log(`🔄 [updateRolePermissions] Trying fallback cache clear method...`);
        const fallbackResult = await this.cacheService.clearPermissionCache();
        console.log(`🔄 [updateRolePermissions] Fallback result: ${fallbackResult.message} (${fallbackResult.deletedKeys} keys deleted)`);
      }
    } catch (error) {
      console.error(`❌ [updateRolePermissions] Failed to clear cache after role update:`, error.message);
      // Continue execution even if cache clear fails
    }

    return true;
  }

  /**
   * Invalidate Redis cache entries related to role permissions
   */
  private async invalidateRolePermissionsCache(roleId: string): Promise<void> {
    try {
      console.log(`🗑️ [invalidateRolePermissionsCache] Invalidating cache for role: ${roleId}`);

      // 1. Invalidate role-specific cache
      await this.cacheService.invalidateRole(roleId);

      // 2. Invalidate all user permission caches that might be using this role
      // Since we don't know which users have this role, we need to invalidate broader patterns
      const patterns = [
        `perm:role:*:${roleId}`,           // Role-specific permissions
        `perm:user:*`,                     // All user permissions (broad but safe)
        `header:*`,                        // Header cache (organizations, processes, etc.)
        `roles:*`,                         // Roles query cache
      ];

      let totalDeleted = 0;
      for (const pattern of patterns) {
        console.log(`🔍 [invalidateRolePermissionsCache] Invalidating pattern: ${pattern}`);
        const deletedCount = await this.cacheService.invalidateByPattern(pattern);
        totalDeleted += deletedCount;
        console.log(`🗑️ [invalidateRolePermissionsCache] Invalidated ${deletedCount} cache entries for pattern: ${pattern}`);
      }

      console.log(`✅ [invalidateRolePermissionsCache] Successfully invalidated ${totalDeleted} total cache entries for role: ${roleId}`);
    } catch (error) {
      console.error(`❌ [invalidateRolePermissionsCache] Failed to invalidate cache for role ${roleId}:`, error.message);
      // Don't throw error - cache invalidation failure shouldn't break the mutation
    }
  }

  @Query(() => PaginationRolesResponse, { name: 'roles' })
  @RequirePermission(
    { module: 'User Management', subModule: 'Roles and Managements', permission: 'View' }
  )
  async roles(@Args() args: RoleListArgs): Promise<PaginationRolesResponse> {
    const result = await this.userService.listRoles(args);
    return {
      items: result.items.map((r: any) => ({
        _id: r._id?.toString() ?? r.id,
        name: r.name,
        permissions: r.permissions,
        createdAt: r.createdAt,
        updatedAt: r.updatedAt,
        isActive: typeof r.isActive !== 'undefined' ? r.isActive : undefined,
        category: typeof r.category === 'string' ? r.category : undefined,
      })),
      pagination: result.pagination,
    };
  }

  @Mutation(() => Boolean)
  async createRole(
    @Args('name') name: string,
    @Args('permissions', { type: () => [ModulePermissionInput] }) permissions: ModulePermissionInput[],
  ): Promise<boolean> {
    await this.userService.createRole(name, permissions);

    // Clear permission cache to ensure new role is immediately available
    try {
      console.log(`🧹 [createRole] Clearing permission cache due to new role creation: ${name}`);

      // Use script-based cache clearing since it works with Redis Cluster
      const result = await this.cacheService.clearPermissionCacheWithScript();

      if (result.success) {
        console.log(`✅ [createRole] Cache cleared successfully: ${result.message} (${result.deletedKeys} keys deleted)`);
        console.log(`🧹 [createRole] Cache clear details:`, result.details);
      } else {
        console.error(`❌ [createRole] Cache clear failed: ${result.message}`);

        // Fallback to regular method if script fails
        console.log(`🔄 [createRole] Trying fallback cache clear method...`);
        const fallbackResult = await this.cacheService.clearPermissionCache();
        console.log(`🔄 [createRole] Fallback result: ${fallbackResult.message} (${fallbackResult.deletedKeys} keys deleted)`);
      }
    } catch (error) {
      console.error(`❌ [createRole] Failed to clear cache after role creation:`, error.message);
      // Continue execution even if cache clear fails
    }

    return true;
  }


  @Query(() => RoleWithPermissions, { name: 'role' })
  async getRoleById(@Args('id') id: string): Promise<RoleWithPermissions | null> {
    const role = await this.userService.getRoleById(id);
    if (!role) return null;
    return {
      _id: role._id?.toString() ?? role.id,
      name: role.name,
      permissions: role.permissions,
      createdAt: (role as any).createdAt ?? '',
      updatedAt: (role as any).updatedAt ?? '',
      isActive: typeof role.isActive !== 'undefined' ? role.isActive : undefined,
      category: typeof role.category === 'string' ? role.category : undefined,
    };
  }

  @Query(() => [GraphQLJSON], { name: 'selectHeader' })
  async selectHeader(
    @Args('name') name: string, // Accept as string for GraphQL
    @Args('userId') userId: string,
    @Args('organisationId', { nullable: true }) organisationId?: string,
    @Args('subOrganisationId', { nullable: true }) subOrganisationId?: string,
  ): Promise<any[]> {
    const startTime = Date.now();
    console.log(`🔍 [SelectHeader] Fetching ${name} for user: ${userId}`);

    try {
      if (name === SelectHeaderType.ORGANISATION) {
        return await this.getOrganizationsWithCache(userId, startTime);
      } else if (name === SelectHeaderType.SUBORGANISATION && organisationId) {
        return await this.getSubOrganizationsWithCache(userId, organisationId, startTime);
      } else if (name === SelectHeaderType.PROCESS && organisationId && subOrganisationId) {
        return await this.getProcessesWithCache(userId, organisationId, subOrganisationId, startTime);
      }

      // Provide more specific error messages
      if (name === SelectHeaderType.SUBORGANISATION && !organisationId) {
        console.log(`⚠️ [SelectHeader] Missing organisationId for suborganisation request`);
      } else if (name === SelectHeaderType.PROCESS && (!organisationId || !subOrganisationId)) {
        console.log(`⚠️ [SelectHeader] Missing organisationId or subOrganisationId for process request`);
      } else {
        console.log(`⚠️ [SelectHeader] Invalid request type: ${name}`);
      }
      return [];
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ [SelectHeader] Error fetching ${name} for user ${userId} in ${duration}ms:`, error.message);
      return [];
    }
  }

  private async getOrganizationsWithCache(userId: string, startTime: number): Promise<any[]> {
    // Try cache first
    // const cached = await this.cacheService.getUserOrganizations(userId);
    // if (cached) {
    //   const duration = Date.now() - startTime;
    //   console.log(`✅ [SelectHeader] Organizations cache hit for user ${userId} in ${duration}ms`);
    //   return cached;
    // }

    // Cache miss - fetch from database
    console.log(`🔄 [SelectHeader] Organizations cache miss for user ${userId} - querying database`);
    const orgs = await this.userService.listOrganizations(userId);
    const orgArray = Array.isArray(orgs) ? orgs : orgs ? [orgs] : [];
    const result = orgArray.map((org: any) => ({
      id: org.id,
      value: org.name,
      organisationId: org.orgId?.toString() ?? org.orgId
    }));
    // Cache the result with 24-hour TTL
    await this.cacheService.cacheUserOrganizations(userId, result); // Uses default 24-hour TTL

    const duration = Date.now() - startTime;
    console.log(`💾 [SelectHeader] Organizations cached for user ${userId} in ${duration}ms (24h TTL)`);
    return result;
  }
  private async getSubOrganizationsWithCache(userId: string, organisationId: string, startTime: number): Promise<any[]> {
    // Try cache first
    // const cached = await this.cacheService.getUserSubOrganizations(userId, organisationId);
    // if (cached) {
    //   const duration = Date.now() - startTime;
    //   console.log(`✅ [SelectHeader] Sub-organizations cache hit for user ${userId}, org ${organisationId} in ${duration}ms`);
    //   return cached;
    // }

    // Cache miss - fetch from database
    // console.log(`🔄 [SelectHeader] Sub-organizations cache miss for user ${userId}, org ${organisationId} - querying database`);
    const subOrgs = await this.userService.listSubOrganizations(userId, organisationId);
    const subOrgArray = Array.isArray(subOrgs) ? subOrgs : subOrgs ? [subOrgs] : [];
    const result = subOrgArray.map((item: any) => ({
      id: item.subOrganisationId?._id?.toString() ?? item.subOrganisationId,
      value: item.subOrganisationId?.name ?? item.subOrganisationId
    }));

    // Cache the result with 24-hour TTL
    await this.cacheService.cacheUserSubOrganizations(userId, organisationId, result); // Uses default 24-hour TTL

    const duration = Date.now() - startTime;
    console.log(`💾 [SelectHeader] Sub-organizations cached for user ${userId}, org ${organisationId} in ${duration}ms (24h TTL)`);
    return result;
  }
  private async getProcessesWithCache(userId: string, organisationId: string, subOrganisationId: string, startTime: number): Promise<any[]> {
    // Try cache first
    // const cached = await this.cacheService.getUserProcesses(userId, organisationId, subOrganisationId);
    // if (cached) {
    //   // Even with cached data, we need to filter out disabled processes
    //   const filteredCached = await this.filterEnabledProcesses(cached, subOrganisationId);
    //   const duration = Date.now() - startTime;
    //   console.log(`✅ [SelectHeader] Processes cache hit for user ${userId}, org ${organisationId}, subOrg ${subOrganisationId} in ${duration}ms (${filteredCached.length}/${cached.length} enabled)`);
    //   return filteredCached;
    // }

    // Cache miss - fetch from database
    console.log(`🔄 [SelectHeader] Processes cache miss for user ${userId}, org ${organisationId}, subOrg ${subOrganisationId} - querying database`);
    const processes = await this.userService.listProcesses(userId, organisationId, subOrganisationId);
    const processArray = Array.isArray(processes) ? processes : processes ? [processes] : [];
    const result = processArray.map((item: any) => ({
      id: item.processId?._id?.toString() ?? item.processId,
      value: item.processId?.name ?? item.processId,
      role: item.roleId ?? 'viewer' // Assuming role is part of the item, adjust as necessary
    }));

    // Filter out disabled processes before caching
    const enabledProcesses = await this.filterEnabledProcesses(result, subOrganisationId);

    // Cache the filtered result with 24-hour TTL
    await this.cacheService.cacheUserProcesses(userId, organisationId, subOrganisationId, enabledProcesses);

    const duration = Date.now() - startTime;
    console.log(`💾 [SelectHeader] Processes cached for user ${userId}, org ${organisationId}, subOrg ${subOrganisationId} in ${duration}ms (${enabledProcesses.length}/${result.length} enabled, 24h TTL)`);
    return enabledProcesses;
  }


  @Query(() => ProcessAssignmentsByCategoryResult, { name: 'processAssignmentsByCategory' })
  async processAssignmentsByCategory(
    @Args('processId') processId: string,
    @Args('organisationId', { nullable: true }) organisationId?: string,
    @Args('subOrganisationId', { nullable: true }) subOrganisationId?: string,
  ): Promise<ProcessAssignmentsByCategoryResult | null> {
    return this.userService.listProcessAssignmentsByCategoryPopulate(processId, organisationId, subOrganisationId);
  }

  @Query(() => [UserResponse], { name: 'unassignedUsers' })
  async unassignedUsers(
    @Args('orgId') orgId: string,
    @Args('subOrgId') subOrgId: string,
    @Args('processId') processId: string,
  ): Promise<UserResponse[]> {
    const users = await this.userService.getUnassignedUsers(orgId, subOrgId, processId);
    return users.map((u: any) => this.mapUser(u));
  }

  @Mutation(() => Boolean, { name: 'assignGlobalRole' })
  @RequirePermission({
    module: 'User Management',
    subModule: 'Global Roles',
    permission: 'Assign',
  })
  async assignGlobalRole(
    @Args('userId') userId: string,
    @Args('roleKey') roleKey: 'admin' | 'sub-admin'
  ): Promise<boolean> {
    return this.userService.assignGlobalRole(userId, roleKey);
  }

  @Mutation(() => Boolean, { name: 'removeGlobalRole' })
  @RequirePermission({
    module: 'User Management',
    subModule: 'Global Roles',
    permission: 'Remove',
  })
  async removeGlobalRole(
    @Args('userId') userId: string,
    @Args('roleKey') roleKey: 'admin' | 'sub-admin'
  ): Promise<boolean> {
    return this.userService.removeGlobalRole(userId, roleKey);
  }

  @Query(() => GraphQLJSON, { name: 'checkGlobalRole' })
  @RequirePermission({
    module: 'User Management',
    subModule: 'Global Roles',
    permission: 'View',
  })
  async checkGlobalRole(@Args('userId') userId: string): Promise<{ isGlobal: boolean; roleKey?: string }> {
    return this.userService.isGlobalAdmin(userId);
  }

  @Mutation(() => Boolean)
  @RequirePermission({
    module: 'Organizations',
    subModule: 'Process settings',
    permission: 'Assign user',
  })
  async assignOperationsRole(
    @Args('input') input: AssignOperationsRoleInput,
    @Context() context: any,
  ): Promise<boolean> {
    const orgId = context?.orgId;

    // Invalidate cache for all users being assigned operations roles
    const userIds = [input.managerId, input.supervisorId, input.agentId].filter(Boolean);
    console.log(`🔄 [assignOperationsRole] Invalidating cache for ${userIds.length} users: ${userIds.join(', ')}`);

    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [assignOperationsRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [assignOperationsRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    const result = await this.userService.assignOperationsRole(input, orgId);
    return result;
  }


  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Assign user' }
  )
  async assignAuditRole(
    @Args('input') input: AssignAuditRoleInput,
    @Context() context: any
  ): Promise<boolean> {
    const orgId = context?.orgId;

    // Invalidate cache for all users being assigned audit roles
    const userIds = [input.qcManagerId, input.qcSupervisorId, input.qcAgentId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [assignAuditRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [assignAuditRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.assignAuditRole(input, orgId);
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Assign user' }
  )
  async assignManagementsRole(
    @Args('input') input: AssignManagementsRoleInput,
    @Context() context: any
  ): Promise<boolean> {
    const orgId = context?.orgId;

    // Invalidate cache for all users being assigned management roles
    const userIds = [input.adminId, input.subAdminId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [assignManagementsRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [assignManagementsRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.assignManagementsRole(input, orgId);
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Remove user' }
  )
  async removeRoleMapping(
    @Args('input') input: RemoveRoleMappingInput,
  ): Promise<boolean> {
    // Invalidate cache for all users being removed from role mappings
    const userIds = [input.managerId, input.supervisorId, input.agentId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [removeRoleMapping] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [removeRoleMapping] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.removeRoleMapping(input);
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Remove user' }
  )
  async removeOperationsRole(
    @Args('input') input: RemoveOperationsRoleInput,
  ): Promise<boolean> {
    // Invalidate cache for all users being removed from operations roles
    const userIds = [input.managerId, input.supervisorId, input.agentId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [removeOperationsRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [removeOperationsRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.removeOperationsRole(input);
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Remove user' }
  )
  async removeAuditRole(
    @Args('input') input: RemoveAuditRoleInput,
  ): Promise<boolean> {
    // Invalidate cache for all users being removed from audit roles
    const userIds = [input.qcManagerId, input.qcSupervisorId, input.qcAgentId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [removeAuditRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [removeAuditRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.removeAuditRole(input);
  }

  @Mutation(() => Boolean)
  @RequirePermission(
    { module: 'Organizations', subModule: 'Process settings', permission: 'Remove user' }
  )
  async removeManagementsRole(
    @Args('input') input: RemoveManagementsRoleInput,
  ): Promise<boolean> {
    // Invalidate cache for all users being removed from management roles
    const userIds = [input.adminId, input.subAdminId].filter(Boolean);
    for (const userId of userIds) {
      try {
        await this.cacheService.invalidateUserPermissions(userId);
        await this.cacheService.invalidateUserHeaderCache(userId);
        console.log(`🗑️ [removeManagementsRole] Cache invalidated for user: ${userId}`);
      } catch (err) {
        console.warn(`⚠️ [removeManagementsRole] Failed to invalidate cache for user ${userId}:`, err.message);
      }
    }

    return this.userService.removeManagementsRole(input);
  }

  @Query(() => [UserResponse], { name: 'unassignedUsersByRoleAndCategory' })
  async unassignedUsersByRoleAndCategory(
    @Args('orgId') orgId: string,
    @Args('subOrgId') subOrgId: string,
    @Args('processId') processId: string,
    @Args('category') category: string,
    @Args('role') role: string,
  ): Promise<UserResponse[]> {
    const users = await this.userService.getUnassignedUsersByRoleAndCategory(orgId, subOrgId, processId, category as any, role);
    return users.map(u => this.mapUser(u));
  }


  @Query(() => UsersUnderManagerResponse, { name: 'usersUnderManager' })
  async getUsersUnderManager(
    // @Args('input') input: GetUsersUnderManagerInput,
    @Context() context: any
  ): Promise<UsersUnderManagerResponse> {
    const userId = this.getUserIdFromContext(context);

    if (!userId) throw new Error('Unauthorized: userId not found in token');

    // Try multiple possible header formats
    const subOrgId = context?.suborgid ||
      context?.subOrgId ||
      context?.req?.headers?.suborgid ||
      context?.req?.headers?.suborg_id ||
      context?.req?.headers?.['sub-org-id'];

    const processId = context?.processid ||
      context?.processId ||
      context?.req?.headers?.processid ||
      context?.req?.headers?.process_id ||
      context?.req?.headers?.['process-id'];

    if (!subOrgId) {
      throw new Error('subOrgId not found in context.');
    }
    if (!processId) {
      throw new Error('processId not found in context.');
    }

    const result = await this.userService.getUsersUnderManager(
      userId,
      subOrgId,
      processId
    );

    return {
      role: result.role || undefined,
      users: (result.users || []).map((user: any) => ({
        _id: user._id?.toString?.() || user._id,
        name: user.name,
        email: user.email,
        employeeId: user.employeeId || '',
        roleName: user.roleName
      }))
    };
  }

  @Mutation(() => Boolean)
  async addNotificationToken(
    @Args('token', { type: () => [String] }) token: string[],
    @Context() context: any,
  ): Promise<boolean> {
    const userId = this.getUserIdFromContext(context);
    if (!userId) throw new Error('Unauthorized: userId not found in token');
    return this.userService.addNotificationToken(userId, token);
  }

  @Mutation(() => Boolean)
  async deleteNotificationToken(
    @Args('token', { type: () => [String] }) token: string[],
    @Context() context: any,
  ): Promise<boolean> {
    const userId = this.getUserIdFromContext(context);
    if (!userId) throw new Error('Unauthorized: userId not found in token');
    return this.userService.deleteNotificationToken(userId, token);
  }

  @Query(() => GraphQLJSON, { name: 'debugUserCache' })
  async debugUserCache(
    @Args('userId') userId: string,
  ): Promise<any> {
    try {
      // This is a debug method to help verify cache invalidation
      console.log(`🔍 [debugUserCache] Checking cache status for user: ${userId}`);

      // Get detailed cache key information
      const cacheKeys = await this.cacheService.debugUserCacheKeys(userId);

      // Try to get some sample cache data to see if it exists
      const orgs = await this.cacheService.getUserOrganizations(userId);
      const hasOrgCache = !!orgs;

      return {
        userId,
        timestamp: new Date().toISOString(),
        cacheKeys,
        cacheStatus: {
          hasOrganizationCache: hasOrgCache,
          organizationCount: orgs ? orgs.length : 0,
        },
        message: hasOrgCache
          ? `User ${userId} has cached data - selectHeader will return from cache`
          : `User ${userId} has no cached data - selectHeader will query database`
      };
    } catch (error) {
      return {
        userId,
        timestamp: new Date().toISOString(),
        error: error.message,
        message: `Error checking cache for user ${userId}`
      };
    }
  }

  @Mutation(() => Boolean, { name: 'forceInvalidateUserCache' })
  async forceInvalidateUserCache(
    @Args('userId') userId: string,
  ): Promise<boolean> {
    try {
      console.log(`🔄 [forceInvalidateUserCache] Manually invalidating cache for user: ${userId}`);

      // Get cache status before invalidation
      const beforeCache = await this.cacheService.debugUserCacheKeys(userId);
      console.log(`🔍 [forceInvalidateUserCache] Cache before invalidation:`, beforeCache);

      await this.cacheService.invalidateUserPermissions(userId);
      await this.cacheService.invalidateUserHeaderCache(userId);

      // Get cache status after invalidation
      const afterCache = await this.cacheService.debugUserCacheKeys(userId);
      console.log(`🔍 [forceInvalidateUserCache] Cache after invalidation:`, afterCache);

      console.log(`✅ [forceInvalidateUserCache] Cache invalidated for user: ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ [forceInvalidateUserCache] Failed to invalidate cache for user ${userId}:`, error.message);
      return false;
    }
  }

  @Query(() => GraphQLJSON, { name: 'debugProcessEnablement' })
  async debugProcessEnablement(
    @Args('processId') processId: string,
    @Args('subOrganisationId') subOrganisationId: string,
  ): Promise<any> {
    try {
      console.log(`🔍 [debugProcessEnablement] Checking process ${processId} enablement for sub-organisation ${subOrganisationId}`);

      const processCollection = await this.getProcessCollection();
      const processMapping = await processCollection.findOne({
        processId: processId,
        subOrganisationId: subOrganisationId,
        isActive: true
      });

      const isEnabled = !!processMapping;

      return {
        processId,
        subOrganisationId,
        isEnabled,
        mapping: processMapping,
        timestamp: new Date().toISOString(),
        message: isEnabled
          ? `Process ${processId} is enabled for sub-organisation ${subOrganisationId}`
          : `Process ${processId} is disabled or not mapped for sub-organisation ${subOrganisationId}`
      };
    } catch (error) {
      return {
        processId,
        subOrganisationId,
        isEnabled: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        message: `Error checking process enablement: ${error.message}`
      };
    }
  }

  @Mutation(() => Boolean, { name: 'testRoleCacheInvalidation' })
  async testRoleCacheInvalidation(
    @Args('roleId') roleId: string,
  ): Promise<boolean> {
    try {
      console.log(`🧪 [testRoleCacheInvalidation] Testing cache invalidation for role: ${roleId}`);

      // Manually trigger the same cache invalidation that happens during updateRolePermissions
      await this.invalidateRolePermissionsCache(roleId);

      console.log(`✅ [testRoleCacheInvalidation] Role cache invalidation test completed for role: ${roleId}`);
      return true;
    } catch (error) {
      console.error(`❌ [testRoleCacheInvalidation] Failed to test role cache invalidation for role ${roleId}:`, error.message);
      return false;
    }
  }

  @Mutation(() => Boolean, { name: 'invalidateUserPermissionCache' })
  async invalidateUserPermissionCache(
    @Args('userId') userId: string,
  ): Promise<boolean> {
    try {
      console.log(`🗑️ [invalidateUserPermissionCache] Invalidating all cache for user: ${userId}`);

      // Invalidate user permissions cache
      await this.cacheService.invalidateUserPermissions(userId);

      // Invalidate user header cache
      await this.cacheService.invalidateUserHeaderCache(userId);

      // Also invalidate any role-based cache patterns
      await this.cacheService.invalidateByPattern(`perm:user:${userId}:*`);
      await this.cacheService.invalidateByPattern(`header:*${userId}*`);

      console.log(`✅ [invalidateUserPermissionCache] Successfully invalidated cache for user: ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ [invalidateUserPermissionCache] Failed to invalidate cache for user ${userId}:`, error.message);
      return false;
    }
  }

  @Mutation(() => Boolean, { name: 'clearPermissionCache' })
  async clearPermissionCache(): Promise<boolean> {
    try {
      console.log(`🧹 [clearPermissionCache] Clearing permission cache...`);

      const result = await this.cacheService.clearPermissionCache();

      if (result.success) {
        console.log(`✅ [clearPermissionCache] ${result.message} (${result.deletedKeys} keys deleted)`);
        console.log(`🧹 [clearPermissionCache] Details:`, result.details);
      } else {
        console.error(`❌ [clearPermissionCache] ${result.message}`);
      }

      return result.success;
    } catch (error) {
      console.error(`❌ [clearPermissionCache] Failed to clear cache:`, error.message);
      return false;
    }
  }

  @Mutation(() => GraphQLJSON, { name: 'clearEntirePermissionCache' })
  async clearEntirePermissionCache(): Promise<any> {
    try {
      console.log(`🧹 [clearEntirePermissionCache] Clearing permission cache due to permission updates...`);

      const result = await this.cacheService.clearPermissionCache();

      if (result.success) {
        console.log(`✅ [clearEntirePermissionCache] ${result.message}`);
      } else {
        console.error(`❌ [clearEntirePermissionCache] ${result.message}`);
      }

      return {
        success: result.success,
        deletedKeys: result.deletedKeys,
        message: result.message,
        details: result.details,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ [clearEntirePermissionCache] Failed to clear cache:`, error.message);
      return {
        success: false,
        deletedKeys: 0,
        message: `Failed to clear cache: ${error.message}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  @Query(() => GraphQLJSON, { name: 'checkPermissionCacheStatus' })
  async checkPermissionCacheStatus(): Promise<any> {
    try {
      console.log(`🔍 [checkPermissionCacheStatus] Checking current permission cache status...`);

      const keyPrefix = 'asp:';
      const patterns = [
        `${keyPrefix}perm:*`,
        `${keyPrefix}header:*`,
        `${keyPrefix}roles:*`,
      ];

      const status: any = {
        timestamp: new Date().toISOString(),
        patterns: {}
      };

      let totalKeys = 0;

      for (const pattern of patterns) {
        const keys = await this.cacheService['redis']?.keys(pattern) || [];
        status.patterns[pattern] = {
          count: keys.length,
          sampleKeys: keys.slice(0, 5)
        };
        totalKeys += keys.length;
        console.log(`🔍 [checkPermissionCacheStatus] Pattern ${pattern}: ${keys.length} keys`);
      }

      status.totalPermissionKeys = totalKeys;
      status.message = totalKeys === 0 ? 'Permission cache is empty (cleared)' : `Found ${totalKeys} permission cache entries`;

      return status;
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        error: error.message,
        message: `Error checking cache status`
      };
    }
  }

  @Query(() => GraphQLJSON, { name: 'validateCacheIntegrity' })
  async validateCacheIntegrity(
    @Args('userId') userId: string,
  ): Promise<any> {
    try {
      console.log(`🔍 [validateCacheIntegrity] Validating cache integrity for user: ${userId}`);

      // Get detailed cache information
      const cacheKeys = await this.cacheService.debugUserCacheKeys(userId);

      // Check if user index is properly maintained
      const userIndexConsistent = cacheKeys.userIndex.keys.length === cacheKeys.patternSearch.keys.length;

      // Check for orphaned keys (keys that exist but aren't in user index)
      const orphanedKeys = cacheKeys.patternSearch.keys.filter((key: string) =>
        !cacheKeys.userIndex.keys.some((indexKey: string) => key.includes(indexKey.replace('asp:', '')))
      );

      // Check for missing keys (keys in index but don't exist)
      const missingKeys = cacheKeys.userIndex.keys.filter((indexKey: string) =>
        !cacheKeys.patternSearch.keys.some((key: string) => key.includes(indexKey.replace('asp:', '')))
      );

      const integrity: any = {
        userId,
        timestamp: new Date().toISOString(),
        cacheKeys,
        validation: {
          userIndexConsistent,
          orphanedKeysCount: orphanedKeys.length,
          missingKeysCount: missingKeys.length,
          orphanedKeys,
          missingKeys,
        },
        recommendations: [] as string[]
      };

      // Add recommendations based on findings
      if (!userIndexConsistent) {
        integrity.recommendations.push('User index is inconsistent with actual cache keys');
      }
      if (orphanedKeys.length > 0) {
        integrity.recommendations.push(`Found ${orphanedKeys.length} orphaned cache keys that should be cleaned up`);
      }
      if (missingKeys.length > 0) {
        integrity.recommendations.push(`Found ${missingKeys.length} missing cache keys that should be removed from index`);
      }
      if (integrity.recommendations.length === 0) {
        integrity.recommendations.push('Cache integrity looks good!');
      }

      return integrity;
    } catch (error) {
      return {
        userId,
        timestamp: new Date().toISOString(),
        error: error.message,
        message: `Error validating cache integrity for user ${userId}`
      };
    }
  }

  @Mutation(() => Boolean)
  async clearAllPermissionCache(): Promise<boolean> {
    try {
      console.log(`🧹 [clearAllPermissionCache] Starting to clear all permission cache using script method...`);

      // Use script-based cache clearing as primary method
      const result = await this.cacheService.clearPermissionCacheWithScript();

      if (result.success) {
        console.log(`✅ [clearAllPermissionCache] Script method SUCCESS: ${result.message}`);
        console.log(`🧹 [clearAllPermissionCache] Keys deleted: ${result.deletedKeys}`);
        console.log(`🧹 [clearAllPermissionCache] Details:`, JSON.stringify(result.details, null, 2));
        return true;
      } else {
        console.error(`❌ [clearAllPermissionCache] Script method failed: ${result.message}`);

        // Fallback to enhanced method
        console.log(`🔄 [clearAllPermissionCache] Trying fallback enhanced method...`);
        const fallbackResult = await this.cacheService.clearPermissionCache();

        console.log(`🔄 [clearAllPermissionCache] Fallback result: ${fallbackResult.success ? 'SUCCESS' : 'FAILED'}`);
        console.log(`🔄 [clearAllPermissionCache] Fallback message: ${fallbackResult.message}`);
        console.log(`🔄 [clearAllPermissionCache] Fallback keys deleted: ${fallbackResult.deletedKeys}`);

        return fallbackResult.success;
      }
    } catch (error) {
      console.error(`❌ [clearAllPermissionCache] Error:`, error.message);
      return false;
    }
  }

  @Mutation(() => Boolean)
  async clearPermissionCacheForced(): Promise<boolean> {
    try {
      console.log(`🧹 [clearPermissionCacheForced] Starting FORCED permission cache clear using pipeline...`);

      const result = await this.cacheService.clearPermissionCacheWithPipeline();

      console.log(`🧹 [clearPermissionCacheForced] Result: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      console.log(`🧹 [clearPermissionCacheForced] Message: ${result.message}`);
      console.log(`🧹 [clearPermissionCacheForced] Keys deleted: ${result.deletedKeys}`);
      console.log(`🧹 [clearPermissionCacheForced] Details:`, JSON.stringify(result.details, null, 2));

      return result.success;
    } catch (error) {
      console.error(`❌ [clearPermissionCacheForced] Error:`, error.message);
      return false;
    }
  }

  @Mutation(() => Boolean)
  async clearCacheWithScript(): Promise<boolean> {
    try {
      console.log(`🧹 [clearCacheWithScript] Starting SCRIPT-based permission cache clear...`);

      const result = await this.cacheService.clearPermissionCacheWithScript();

      console.log(`🧹 [clearCacheWithScript] Result: ${result.success ? 'SUCCESS' : 'FAILED'}`);
      console.log(`🧹 [clearCacheWithScript] Message: ${result.message}`);
      console.log(`🧹 [clearCacheWithScript] Keys deleted: ${result.deletedKeys}`);
      console.log(`🧹 [clearCacheWithScript] Details:`, JSON.stringify(result.details, null, 2));

      return result.success;
    } catch (error) {
      console.error(`❌ [clearCacheWithScript] Error:`, error.message);
      return false;
    }
  }
}