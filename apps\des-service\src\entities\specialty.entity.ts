import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSONObject } from 'graphql-type-json';
import { BaseEntity } from './base.entity';
import { ClientType } from './template.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'specialties',
  strict: false,

})
export class Specialty extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field(() => String, { nullable: true })
  @Prop({ sparse: true })
  code?: string;

  @Field(() => ClientType)
  @Prop({ type: String, enum: ClientType, required: true })
  type: ClientType;

  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @Prop({ type: Object, default: {} })
  values: Record<string, any>;

  @Field(() => ID, { nullable: true })
  @Prop({ index: true })
  templateId?: string;
  
  @Field(() => Boolean)
  @Prop({ default: true })
  isActive: boolean;

  @Field(() => Date)
  @Prop({ default: Date.now })
  createdAt: Date;

  @Field(() => Date)
  @Prop({ default: Date.now })
  updatedAt: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  createdBy?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  updatedBy?: string;
}

export const SpecialtySchema = SchemaFactory.createForClass(Specialty);

// Add text indexes for full-text search
SpecialtySchema.index({
  name: 'text',
  'values.description': 'text'
}, {
  weights: {
    name: 10,
    'values.description': 5
  },
  name: "SpecialtySearchIndex"
});

// Add indexes for common queries
SpecialtySchema.index({ createdAt: -1 });
SpecialtySchema.index({ name: 1 }, { unique: true });

// SpecialtySchema.index({ code: 1, type: 1 }, { unique: false, sparse: true }); 