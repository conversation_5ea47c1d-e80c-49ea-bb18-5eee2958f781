{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/gateway/tsconfig.app.json"}, "monorepo": true, "root": "apps/gateway", "projects": {"audit": {"type": "library", "root": "libs/audit", "entryFile": "index", "sourceRoot": "libs/audit/src", "compilerOptions": {"tsConfigPath": "libs/audit/tsconfig.lib.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "config": {"type": "library", "root": "libs/config", "entryFile": "index", "sourceRoot": "libs/config/src", "compilerOptions": {"tsConfigPath": "libs/config/tsconfig.lib.json"}}, "db": {"type": "library", "root": "libs/db", "entryFile": "index", "sourceRoot": "libs/db/src", "compilerOptions": {"tsConfigPath": "libs/db/tsconfig.lib.json"}}, "des-service": {"type": "application", "root": "apps/des-service", "entryFile": "main", "sourceRoot": "apps/des-service/src", "compilerOptions": {"tsConfigPath": "apps/des-service/tsconfig.app.json"}}, "notification-service": {"type": "application", "root": "apps/notification-service", "entryFile": "main", "sourceRoot": "apps/notification-service/src", "compilerOptions": {"tsConfigPath": "apps/notification-service/tsconfig.app.json"}}, "email": {"type": "library", "root": "libs/email", "entryFile": "index", "sourceRoot": "libs/email/src", "compilerOptions": {"tsConfigPath": "libs/email/tsconfig.lib.json"}}, "error": {"type": "library", "root": "libs/error", "entryFile": "index", "sourceRoot": "libs/error/src", "compilerOptions": {"tsConfigPath": "libs/error/tsconfig.lib.json"}}, "functions": {"type": "library", "root": "libs/functions", "entryFile": "index", "sourceRoot": "libs/functions/src", "compilerOptions": {"tsConfigPath": "libs/functions/tsconfig.lib.json"}}, "gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.app.json"}}, "identity": {"type": "application", "root": "apps/identity", "entryFile": "main", "sourceRoot": "apps/identity/src", "compilerOptions": {"tsConfigPath": "apps/identity/tsconfig.app.json", "assets": [{"include": "templates/**/*", "outDir": "dist/apps/identity"}]}}, "permissions": {"type": "library", "root": "libs/permissions", "entryFile": "index", "sourceRoot": "libs/permissions/src", "compilerOptions": {"tsConfigPath": "libs/permissions/src/tsconfig.lib.json"}}, "provider": {"type": "application", "root": "apps/provider", "entryFile": "main", "sourceRoot": "apps/provider/src", "compilerOptions": {"tsConfigPath": "apps/provider/tsconfig.app.json"}}}}