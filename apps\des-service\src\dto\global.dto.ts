import { Field, ID, InputType, ObjectType, Int } from '@nestjs/graphql';
import { IsBoolean, IsMongoId, IsOptional, IsString } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';

@InputType()
export class CreateGlobalInput {
    @Field()
    @IsString()
    name: string;

    @Field(() => Boolean)
    @IsBoolean()
    isTable: boolean;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    modalName?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    options?: Record<string, any>[];

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    createdBy?: string;
}

@InputType()
export class UpdateGlobalInput {
    @Field(() => ID)
    @IsMongoId()
    id: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isTable?: boolean;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    modalName?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    options?: Record<string, any>[];

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    updatedBy?: string;

    @Field(() => Boolean, { nullable: true })
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;
}

@InputType()
export class FindAllGlobalsInput {
    @Field(() => Number, { nullable: true })
    @IsOptional()
    page?: number;

    @Field(() => Number, { nullable: true })
    @IsOptional()
    limit?: number;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    search?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    filters?: Record<string, any>;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    sortBy?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    sortOrder?: string;

    @Field(() => [String], { nullable: true })
    @IsOptional()
    selectedFields?: string[];
}

@ObjectType()
export class GlobalListItem {
    @Field()
    name: string;

    @Field(() => Boolean)
    isTable: boolean;

    @Field(() => String, { nullable: true })
    modalName?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    options?: Record<string, any>[];

    @Field()
    isActive: boolean;

    @Field(() => String, { nullable: true })
    createdBy?: string;

    @Field(() => String, { nullable: true })
    updatedBy?: string;
}

@ObjectType()
export class GlobalDetail extends GlobalListItem {
    @Field(() => ID)
    id: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

    @Field(() => GraphQLJSON, { nullable: true })
    data?: Record<string, any>[];
}

@ObjectType()
export class GlobalPaginationMeta {
    @Field(() => Int)
    page: number;

    @Field(() => Int)
    limit: number;

    @Field(() => Int)
    total: number;

    @Field(() => Int)
    totalItems: number;

    @Field(() => Int)
    totalPages: number;

    @Field(() => Boolean)
    hasNext: boolean;

    @Field(() => Boolean)
    hasPrev: boolean;
}

@ObjectType()
export class GlobalsResponse {
    @Field(() => [GlobalDetail])
    globals: GlobalDetail[];

    @Field(() => GlobalPaginationMeta)
    pagination: GlobalPaginationMeta;
}

@ObjectType()
export class GlobalDataResponse {
    @Field(() => ID)
    id: string;

    @Field(() => String)
    value: string;
} 