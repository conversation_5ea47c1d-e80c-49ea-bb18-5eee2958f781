import * as nodemailer from 'nodemailer';
import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PubSub } from '@google-cloud/pubsub';
import { <PERSON><PERSON><PERSON>, Producer, Consumer, EachMessagePayload } from 'kafkajs';
import axios from 'axios';
import { NotificationService } from '@app/notification';

// ------------------ Email Service ------------------
export class EmailService {

  private transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.SMTP_USER, // your Gmail address
      pass: process.env.SMTP_PASS, // your app password
    },
  });


  async sendEmail(to: string, html?: string, subject?: string): Promise<{ messageId: string }> {
    try {
      const info = await this.transporter.sendMail({
        from: `"ASP RCM" <${process.env.OUTLOOK_EMAIL}>`,
        to,
        subject: subject || 'OTP Verification - RCM Genie',
        html,
      });
      console.log(`📧 Email sent to ${to}. Message ID: ${info.messageId}`);
      return { messageId: info.messageId };
    } catch (error: any) {
      console.error(`❌ Failed to send email to ${to}:`, error.message);
      throw error;
    }
  }

  async sendReplyEmail({
    from, to, subject, html, inReplyTo, references,
  }: {
    from: string;
    to: string;
    subject: string;
    html: string;
    inReplyTo: string;
    references: string[];
  }): Promise<{ messageId: string }> {
    try {
      const info = await this.transporter.sendMail({
        from: `"ASP RCM" <${from}>`,
        to,
        subject,
        html,
        inReplyTo,
        references,
      });
      console.log(`📨 Reply sent to ${to}. Message ID: ${info.messageId}`);
      return { messageId: info.messageId };
    } catch (error: any) {
      console.error(`❌ Failed to send reply to ${to}:`, error.message);
      throw error;
    }
  }
}

// ------------------ Kafka Service ------------------
// @Injectable()
// export class KafkaService implements OnModuleInit, OnModuleDestroy {
//   private kafka: Kafka;
//   private producer: Producer;
//   private consumer: Consumer;
//   private readonly topicName = process.env.KAFKA_EMAIL_TOPIC || 'email-replies';
//   private readonly groupId = process.env.KAFKA_CONSUMER_GROUP_ID || 'asp-rcm-email-group';
//   private emailService = new EmailService();

//   constructor() {
//     this.kafka = new Kafka({
//       clientId: process.env.KAFKA_CLIENT_ID || 'asp-rcm-email-service',
//       brokers: (process.env.KAFKA_BROKERS || '35.209.79.126:9092').split(','),
//       retry: {
//         initialRetryTime: 300,
//         retries: 5,
//       },
//     });

//     this.producer = this.kafka.producer();
//     this.consumer = this.kafka.consumer({ groupId: this.groupId });
//   }

//   async onModuleInit() {
//     try {
//       await this.producer.connect();
//       await this.consumer.connect();
//       await this.consumer.subscribe({ topic: this.topicName, fromBeginning: false });

//       console.log(`✅ Kafka connected. Subscribed to topic: ${this.topicName}`);

//       await this.consumer.run({
//         eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {
//           try {
//             const rawValue = message.value?.toString();
//             if (!rawValue) return console.warn('⚠️ Empty Kafka message received');
//             const parsed = JSON.parse(rawValue);
//             const { from, to, body, conversationId, messageId } = parsed;

//             if (!from || !to || !body) {
//               console.warn('⚠️ Missing email fields:', parsed);
//               return;
//             }
//             console.log('📥 Kafka message:', parsed);
//             // 🔥 Trigger the webhook
//             const webhookUrl = 'https://n8n.asprcmsolutions.com/webhook/replyemail';
//             const response = await axios.post(webhookUrl, parsed); // send entire parsed body
//             console.log(`✅ Webhook triggered successfully:`, response.status);

//           } catch (err) {
//             console.error('❌ Kafka message error:', err);
//           }
//         },
//       });
//     } catch (err) {
//       console.error('❌ Kafka init failed:', err.message);
//     }
//   }

//   async onModuleDestroy() {
//     try {
//       await this.producer.disconnect();
//       await this.consumer.disconnect();
//       console.log('🛑 Kafka disconnected');
//     } catch (err) {
//       console.error('❌ Error during Kafka disconnect:', err.message);
//     }
//   }

//   async publishToKafka(data: any): Promise<void> {
//     try {
//       const message = {
//         key: data.conversationId || data.orgId,
//         value: JSON.stringify(data),
//         timestamp: Date.now().toString(),
//       };

//       const result = await this.producer.send({
//         topic: this.topicName,
//         messages: [message],
//       });

//       console.log(`📨 Kafka publish success:`, {
//         partition: result[0].partition,
//         offset: result[0].offset,
//         conversationId: data.conversationId,
//       });
//     } catch (error) {
//       console.error('❌ Kafka publish error:', error);
//     }
//   }
// }

// // ------------------ PubSub Listener ------------------
// export class PubSubListenerService {
//   private pubSubClient = new PubSub({
//     projectId: process.env.GOOGLE_CLOUD_PROJECT || 'asp-rcm',
//     keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
//   });

//   private subscriptionName = 'projects/asp-rcm/subscriptions/asprcm-dev-n8n-subscription';

//   constructor(private readonly kafkaService: KafkaService) { }

//   async initListener() {
//     const subscription = this.pubSubClient.subscription(this.subscriptionName);

//     subscription.on('message', async (message) => {
//       try {
//         const data = JSON.parse(message.data.toString());
//         console.log('📩 PubSub Message:', data);
//         await this.kafkaService.publishToKafka(data);
//         message.ack();
//       } catch (err) {
//         console.error('❌ PubSub process failed:', err.message);
//         message.nack();
//       }
//     });

//     subscription.on('error', (error) => {
//       console.error('❌ PubSub Subscription error:', error);
//     });
//   }
// }

// // ------------------ Bootstrap ------------------
// async function bootstrap() {
//   const kafkaService = new KafkaService();
//   kafkaService.onModuleInit().catch((err) => {
//     console.error('⚠️ Kafka init skipped (non-blocking):', err.message);
//   });

//   const pubsubService = new PubSubListenerService(kafkaService);
//   await pubsubService.initListener();

//   console.log('🚀 Email service running. Waiting for PubSub or Kafka messages...');
// }

// bootstrap();


@Injectable()
export class KafkaService implements OnModuleInit, OnModuleDestroy {
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;

  private readonly topics = [
    process.env.KAFKA_EMAIL_TOPIC || 'email-replies',
    process.env.KAFKA_NOTIFICATION_TOPIC || 'notification',
    process.env.KAFKA_AUDIT_TOPIC || 'audit-history',
  ];

  constructor(private readonly notificationService: NotificationService) {
    this.kafka = new Kafka({
      clientId: process.env.KAFKA_CLIENT_ID || 'asp-rcm-service',
      brokers: (process.env.KAFKA_BROKERS || '**************:9092').split(','),
      retry: {
        initialRetryTime: 300,
        retries: 5,
      },
    });

    this.producer = this.kafka.producer();
    this.consumer = this.kafka.consumer({
      groupId: process.env.KAFKA_CONSUMER_GROUP_ID || 'asp-rcm-consumer-group',
    });
  }

  async onModuleInit() {
    await this.producer.connect();
    await this.consumer.connect();

    // ✅ Subscribe to all topics
    for (const topic of this.topics) {
      await this.consumer.subscribe({ topic, fromBeginning: false });
    }

    // ✅ Run the consumer
    await this.consumer.run({
      eachMessage: async ({ topic, partition: _partition, message }: EachMessagePayload) => {
        console.log("topic:", topic);
        
        try {
          const rawValue = message.value?.toString();
          if (!rawValue) return console.warn('⚠️ Empty Kafka message received');

          const parsed = JSON.parse(rawValue);
          console.log(`📥 Received message from topic [${topic}]:`, parsed);

          if (topic === 'email-replies') {
            const { from, to, body } = parsed;
            if (!from || !to || !body) {
              console.warn('⚠️ Missing email fields:', parsed);
              return;
            }

            const webhookUrl = process.env.EMAIL_WEBHOOK_URL || 'https://n8n.asprcmsolutions.com/webhook/replyemail';
console.log("webhookUrl:", webhookUrl);

            try {
              const response = await axios.post(webhookUrl, parsed, {
                timeout: 7000, // 5 second timeout
                headers: {
                  'Content-Type': 'application/json',
                  'User-Agent': 'ASP-RCM-Email-Service/1.0'
                }
              });

              console.log(`✅ Email webhook triggered successfully:`, {
                status: response.status,
                statusText: response.statusText,
                url: webhookUrl
              });
            } catch (webhookError) {
              const errorMessage = webhookError.code === 'ETIMEDOUT'
                ? `Email webhook timeout after 5 seconds`
                : webhookError.message;

              console.warn(`⚠️ Email webhook failed:`, {
                url: webhookUrl,
                error: errorMessage,
                code: webhookError.code,
                from: parsed.from,
                to: parsed.to
              });

              // Don't throw error - webhook failure shouldn't break email processing
            }
          
          // else if (topic === 'notification') {
          //   // Handle notification messages
          //   console.log(`📢 DEBUG: Processing notification from Kafka:`, {
          //     userId: parsed.userId,
          //     title: parsed.title,
          //     message: parsed.message,
          //     type: parsed.type,
          //     priority: parsed.priority,
          //     taskId: parsed.data?.taskId,
          //     status: parsed.metadata?.status,
          //     hasDeviceTokens: !!parsed.data?.deviceTokens,
          //     deviceTokenCount: parsed.data?.deviceTokens?.length || 0
          //   });

          //   // 1. Firebase push notifications are handled by the notification service
          //   if (parsed.data?.deviceTokens && Array.isArray(parsed.data.deviceTokens)) {
          //     console.log(`🔥 DEBUG: Found ${parsed.data.deviceTokens.length} device tokens in notification data`);
          //     console.log(`📱 DEBUG: Firebase push notifications should be handled by the notification service`);

          //     // await this.notificationService.notifyUserWithFirebasePush(
          //     //   parsed.data.deviceTokens,
          //     //   parsed.title,
          //     //   parsed.message,
          //     //   parsed.data
          //     // );
          //   } else {
          //     console.log(`⚠️ DEBUG: No device tokens found in notification data`);
          //   }

            // 2. Store notifications in database (if needed)
            // 3. Send WebSocket notifications to connected clients (if needed)
            // 4. Send email notifications (if needed)
            // 5. Trigger webhooks to external systems

            // Send to notification webhook (if configured and enabled)
            // const notificationWebhookUrl = process.env.NOTIFICATION_WEBHOOK_URL || 'https://n8n.asprcmsolutions.com/webhook/replyemail';
            // const webhookEnabled = process.env.ENABLE_NOTIFICATION_WEBHOOK !== 'false';

            // if (notificationWebhookUrl ) {
            //   try {
            //     console.log(`🔗 DEBUG: Sending notification to webhook: ${notificationWebhookUrl}`);

            //     const response = await axios.post(notificationWebhookUrl, parsed, {
            //       timeout: 5000, // 5 second timeout
            //       headers: {
            //         'Content-Type': 'application/json',
            //         'User-Agent': 'ASP-RCM-Notification-Service/1.0'
            //       }
            //     });

            //     console.log(`✅ DEBUG: Notification webhook triggered successfully:`, {
            //       status: response.status,
            //       statusText: response.statusText,
            //       url: notificationWebhookUrl,
            //       userId: parsed.userId,
            //       taskId: parsed.data?.taskId
            //     });
            //   } catch (webhookError) {
            //     const errorMessage = webhookError.code === 'ETIMEDOUT'
            //       ? `Webhook timeout after 5 seconds`
            //       : webhookError.message;

            //     console.warn(`⚠️ DEBUG: Notification webhook failed:`, {
            //       url: notificationWebhookUrl,
            //       error: errorMessage,
            //       code: webhookError.code,
            //       userId: parsed.userId,
            //       notificationTitle: parsed.title,
            //       taskId: parsed.data?.taskId
            //     });

            //     // Don't throw error - webhook failure shouldn't break notification processing
            //   }
            // } else {
            //   console.log(`ℹ️ Notification webhook skipped:`, {
            //     configured: !!notificationWebhookUrl,
            //     // enabled: webhookEnabled,
            //     reason: !notificationWebhookUrl ? 'No webhook URL configured' : 'Webhook disabled'
            //   });
            // }
          } 
          // else if (topic === 'audit-history') {
          //   // Handle audit messages
          //   console.log(`📋 Processing audit log:`, {
          //     eventType: parsed.eventType,
          //     recordType: parsed.recordType,
          //     userId: parsed.authoredInfo?.id
          //   });

          //   // You can add audit processing logic here
          // }
           else {
            console.warn(`⚠️ No handler for topic: ${topic}`);
          }

        } catch (err) {
          console.error('❌ Kafka message error:', err);
        }
      },
    });
  }

  async onModuleDestroy() {
    await this.producer.disconnect();
    await this.consumer.disconnect();
    console.log('🛑 Kafka disconnected');
  }

  async publishToKafka(data: any, topic: string): Promise<void> {
    try {
      const message = {
        key: data.conversationId || data.orgId || Date.now().toString(),
        value: JSON.stringify(data),
        timestamp: Date.now().toString(),
      };
      const result = await this.producer.send({
        topic,
        messages: [message],
      });
      console.log(`📨 Kafka publish success to "${topic}":`, result);
    } catch (error) {
      console.error(`❌ Kafka publish error to "${topic}":`, error);
    }
  }
}

