import { ServiceEndpointDefinition } from '@apollo/gateway';

export const subgraphs: Array<ServiceEndpointDefinition> = [
  {
    name: 'identity',
    url: `http://identity-service:4001/graphql`,
  },
  {
    name: 'des-service',
    url: `http://des-service-service:4002/graphql`,
  },
  {
    name: 'provider',
    url: `http://provider-service:4003/graphql`,
  },
  {
    name: 'notification-service',
    url: `http://notification-service-service:4004/graphql`,
  }
];