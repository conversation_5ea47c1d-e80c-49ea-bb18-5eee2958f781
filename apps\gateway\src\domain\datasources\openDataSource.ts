import { RemoteGraphQLDataSource } from '@apollo/gateway';
import { GatewayGraphQLRequest } from '@apollo/server-gateway-interface';
import { ApolloAuthContext } from '../types';
import { HEADER_KEYS } from '../config/vars';
import * as jwt from 'jsonwebtoken';

export default class OpenDataSource extends RemoteGraphQLDataSource<ApolloAuthContext> {
  async willSendRequest({
    request,
    context,
  }: {
    request: GatewayGraphQLRequest;
    context: ApolloAuthContext;
  }) {
    const headers =
      (context.req?.headers as Record<string, string | undefined>) ?? {};   
    for (const [key, value] of Object.entries(headers)) {
      if (HEADER_KEYS.OPEN_DATA_SOURCE.includes(key) && value !== undefined) {
        request.http?.headers.set(key, value);
      }
    }    
    const authHeader = headers['authorization'] || headers['Authorization'];
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];

      try {
       
        const decoded = jwt.verify(token, process.env.JWT_SECRET ?? 'fallback-secret') as { userId?: string };

        if (decoded?.userId) {
          request.http?.headers.set('userId', decoded.userId);
        }
      } catch (error) {
        console.warn('JWT verification failed in OpenDataSource:', error.message);
      }
    }
  }
}
