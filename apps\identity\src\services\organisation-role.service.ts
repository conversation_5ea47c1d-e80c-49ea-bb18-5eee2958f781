import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OrganisationRole } from '../entities/organisation-role.entity';
import { CreateOrganisationRoleInput, UpdateOrganisationRoleInput, OrganisationRoleListArgs } from '../dto/organisation-role.dto';
import { organizationRoleTemplate } from '../../../../roles-permission';
import { Error, ErrorType, HttpStatus, ResponseCode } from '@app/error';

// const ALLOWED_ROLES = ['main-client', 'sub-client', 'provider'];

@Injectable()
export class OrganisationRoleService {
  constructor(
    @InjectModel(OrganisationRole.name)
    private readonly organisationRoleModel: Model<OrganisationRole>,
  ) {}

  async create(input: CreateOrganisationRoleInput): Promise<OrganisationRole> {
    console.log('CreateOrganisationRoleInput:', input);
    const exists = await this.organisationRoleModel.findOne({
      name: input.name,
      type: input.type,
    });
    if (exists) {
      //  Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION, 'Invalid statusCodeId');
      throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.VALIDATION,'Role name already exists for this organisation type.');
    }
    return this.organisationRoleModel.create({
      name: input.name,
      permissions: JSON.parse(JSON.stringify(organizationRoleTemplate)),
      isActive: typeof input.isActive === 'boolean' ? input.isActive : true,
      type: input.type,
    });
  }

  async findAll(args: OrganisationRoleListArgs): Promise<{ items: OrganisationRole[]; pagination: any }> {
    const {
      page = 1,
      limit = 10,
      search,
      filters,
      sortBy,
      sortOrder = 'asc',
    } = args;

    const query: any = {};

    // Global search across 'name' and 'permissions' only
    if (search) {
      const regex = { $regex: search, $options: 'i' };
      query.$or = [
        { name: regex },
        { permissions: regex },
      ];
    }

    // Field-specific filters
    if (filters) {
       const parsed =
        typeof filters === 'string'
          ? JSON.parse(filters)?.filters || JSON.parse(filters)
          : filters.filters || filters;
      for (const [key, value] of Object.entries(parsed)) {
        if (value !== undefined && value !== '') {
          // Handle date fields
          if (key === 'createdAt' || key === 'updatedAt') {
            const dateValue = typeof value === 'string' ? new Date(value) : value;
            if (dateValue instanceof Date && !isNaN(dateValue.getTime())) {
              const nextDay = new Date(dateValue.getTime());
              nextDay.setDate(nextDay.getDate() + 1);
              query[key] = { $gte: dateValue, $lt: nextDay };
            } else {
              continue;
            }
          } else if (typeof value === 'string' && key !== 'type') {
            query[key] = { $regex: value, $options: 'i' };
          } else if (key === 'type') {
            query[key] =
              value === 'SUB_ORGANISATION' ? 'subOrganisation' :
              value === 'MAIN_ORGANISATION' ? 'mainOrganisation' :
              value;
          } else {
            query[key] = value;
          }
        }
      }
    }

    // If both search and name filter, ensure $and includes $or and $in
    if (query.$or && filters && filters.name) {
      query.$and = query.$and || [];
      query.$and.push({ $or: query.$or });
      delete query.$or;
    }

    const skip = (page - 1) * limit;
    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    const [items, total] = await Promise.all([
      this.organisationRoleModel.find(query).sort(sort).skip(skip).limit(limit).lean(),
      this.organisationRoleModel.countDocuments(query),
    ]);
    // Map _id to id for each item
    const mappedItems = items.map((item: any) => ({
      ...item,
      _id: item._id?.toString?.() ?? item._id,
      isActive: typeof item.isActive !== 'undefined' ? item.isActive : null,
      updatedAt: item.updatedAt || null,
    }));
    return {
      items: mappedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  async findOne(id: string): Promise<OrganisationRole | null> {
    return this.organisationRoleModel.findById(id).exec();
  }

  async update(input: UpdateOrganisationRoleInput): Promise<OrganisationRole | null> {
    
    const updateData: any = {};
    if (input.name) {
      const exists = await this.organisationRoleModel.findOne({
        name: input.name,
        type: input.type || "mainOrganisation",
        _id: { $ne: input.id }, // Exclude current record from duplicate check
      });
      if (exists) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.VALIDATION,
          'Role name already exists for this organisation type.'
        );
      }
      // if (!ALLOWED_ROLES.includes(input.name)) {
      //   throw new Error('Invalid role name. Allowed: main-client, sub-client, provider');
      // }
      updateData.name = input.name;
    }
    if (input.permissions) updateData.permissions = input.permissions;
    if (typeof input.isActive === 'boolean') updateData.isActive = input.isActive;
    return this.organisationRoleModel.findByIdAndUpdate(input.id, updateData, { new: true }).exec();
  }

  async remove(id: string): Promise<OrganisationRole | null> {
    return this.organisationRoleModel.findByIdAndDelete(id).exec();
  }
} 