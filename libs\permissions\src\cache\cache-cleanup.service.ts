import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RedisCacheService } from './redis-cache.service';

@Injectable()
export class CacheCleanupService implements OnModuleInit {
  private readonly logger = new Logger(CacheCleanupService.name);

  constructor(private readonly cacheService: RedisCacheService) {}

  async onModuleInit() {
    this.logger.log('🧹 Cache Cleanup Service initialized');
    this.logger.log('📅 Scheduled cleanup will run daily at 2:00 AM');
    
    // Log current TTL configuration
    const ttlConfig = this.cacheService.getTTLConfig();
    this.logger.log(`⏰ Cache TTL Configuration: Default=${ttlConfig.default}s (${ttlConfig.default/3600}h), Header=${ttlConfig.header}s (${ttlConfig.header/3600}h)`);
  }

  /**
   * Scheduled cleanup task that runs daily at 2:00 AM
   * This ensures expired cache entries are cleaned up automatically
   */
  @Cron('0 2 * * *', {
    name: 'daily-cache-cleanup',
    timeZone: 'UTC',
  })
  async scheduledCleanup() {
    this.logger.log('🧹 Starting scheduled cache cleanup...');
    
    try {
      const result = await this.cacheService.cleanupExpiredEntries();
      
      if (result.cleaned > 0) {
        this.logger.log(`✅ Scheduled cleanup completed: ${result.cleaned} expired entries removed from ${result.checked} total entries`);
      } else {
        this.logger.log(`✅ Scheduled cleanup completed: No expired entries found (${result.checked} entries checked)`);
      }

      // Also log cache statistics
      await this.logCacheStatistics();
      
    } catch (error) {
      this.logger.error('❌ Scheduled cache cleanup failed:', error.message);
    }
  }

  /**
   * Manual cleanup method that can be called via API or admin interface
   */
  async manualCleanup(): Promise<{ cleaned: number; checked: number; stats: any }> {
    this.logger.log('🧹 Starting manual cache cleanup...');
    
    try {
      const result = await this.cacheService.cleanupExpiredEntries();
      const stats = await this.cacheService.getCacheStats();
      
      this.logger.log(`✅ Manual cleanup completed: ${result.cleaned} expired entries removed from ${result.checked} total entries`);
      
      return {
        ...result,
        stats
      };
    } catch (error) {
      this.logger.error('❌ Manual cache cleanup failed:', error.message);
      throw error;
    }
  }

  /**
   * Get cache health information
   */
  async getCacheHealth(): Promise<{
    status: string;
    totalKeys: number;
    expiringIn24h: number;
    ttlConfig: any;
    lastCleanup?: Date;
  }> {
    try {
      const stats = await this.cacheService.getCacheStats();
      const expiringKeys = await this.cacheService.getExpiringEntries(24);
      const ttlConfig = this.cacheService.getTTLConfig();
      
      return {
        status: stats.status || 'Unknown',
        totalKeys: parseInt(stats.keyCount || '0'),
        expiringIn24h: expiringKeys.length,
        ttlConfig,
        lastCleanup: new Date() // This would be stored in a database in a real implementation
      };
    } catch (error) {
      this.logger.error('Failed to get cache health:', error.message);
      return {
        status: 'Error',
        totalKeys: 0,
        expiringIn24h: 0,
        ttlConfig: this.cacheService.getTTLConfig()
      };
    }
  }

  /**
   * Log cache statistics for monitoring
   */
  private async logCacheStatistics() {
    try {
      const stats = await this.cacheService.getCacheStats();
      const expiringKeys = await this.cacheService.getExpiringEntries(24);
      
      this.logger.log(`📊 Cache Statistics: ${stats.keyCount} total keys, ${expiringKeys.length} expiring in 24h`);
      
      if (expiringKeys.length > 0) {
        this.logger.log(`⏰ Keys expiring soon: ${expiringKeys.slice(0, 5).join(', ')}${expiringKeys.length > 5 ? '...' : ''}`);
      }
    } catch (error) {
      this.logger.warn('Failed to log cache statistics:', error.message);
    }
  }

  /**
   * Emergency cleanup - removes all cache entries (use with caution)
   */
  async emergencyCleanup(): Promise<number> {
    this.logger.warn('🚨 Starting emergency cache cleanup - removing ALL cache entries');
    
    try {
      const permissionKeys = await this.cacheService.invalidateByPattern('perm:*');
      const headerKeys = await this.cacheService.invalidateByPattern('header:*');
      
      const totalRemoved = permissionKeys + headerKeys;
      this.logger.warn(`🚨 Emergency cleanup completed: ${totalRemoved} entries removed`);
      
      return totalRemoved;
    } catch (error) {
      this.logger.error('❌ Emergency cleanup failed:', error.message);
      throw error;
    }
  }

  /**
   * Extend TTL for frequently accessed cache entries
   */
  async extendFrequentlyAccessedCache(): Promise<number> {
    this.logger.log('⏰ Extending TTL for frequently accessed cache entries...');
    
    try {
      // This is a placeholder - in a real implementation, you would track access patterns
      // and extend TTL for frequently accessed keys
      const expiringKeys = await this.cacheService.getExpiringEntries(6); // Keys expiring in 6 hours
      let extended = 0;
      
      for (const key of expiringKeys.slice(0, 10)) { // Limit to first 10 for safety
        const success = await this.cacheService.extendCacheTTL(key);
        if (success) extended++;
      }
      
      this.logger.log(`⏰ Extended TTL for ${extended} frequently accessed cache entries`);
      return extended;
    } catch (error) {
      this.logger.error('Failed to extend cache TTL:', error.message);
      return 0;
    }
  }
}
