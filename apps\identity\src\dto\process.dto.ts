import { InputType, Field, Int, PartialType, ObjectType, ID, ArgsType } from '@nestjs/graphql';
import { Process } from '../entities/process.entity';
import { IsInt, IsOptional, IsString, Max, Min, ValidateNested } from 'class-validator';
import { ClientSortInput } from './client.dto';
import { Type } from 'class-transformer';
import GraphQLJSON from 'graphql-type-json';

@InputType()
export class CreateProcessInput {
    @Field(() => String)
    name: string;
}

@InputType()
export class UpdateProcessInput extends PartialType(CreateProcessInput) {
    @Field(() => ID)
    id: string;
}

@ObjectType()
export class PaginatedProcessesResponse {
    @Field(() => [Process])
    items: Process[];

    @Field(() => Int)
    total: number;
}

@ArgsType()
export class FindAllArgs {
    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsOptional()
    @IsInt()
    @Min(1)
    page?: number = 1;

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsOptional()
    @IsInt()
    @Min(1)
    @Max(100)
    limit?: number = 10;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    search?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    filters?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    sortBy?: string;

    @Field(() => String, { nullable: true, description: "Should be 'asc' or 'desc'" })
    @IsOptional()
    @IsString()
    sortOrder?: 'asc' | 'desc';

    @Field(() => ClientSortInput, { nullable: true })
    @IsOptional()
    @ValidateNested()
    @Type(() => ClientSortInput)
    sort?: ClientSortInput;
    @Field(() => GraphQLJSON, { nullable: true })
    selectedFields?: Record<string, any>;
}