import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { GlobalService } from './global.service';
import { Global, GlobalDocument } from './entities/global.entity';
import {
    CreateGlobalInput,
    UpdateGlobalInput,
    FindAllGlobalsInput,
    GlobalDetail,
    GlobalsResponse,
    GlobalDataResponse,
} from './dto/global.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { Success } from '@app/error';

@Resolver(() => Global)
export class GlobalResolver {
    constructor(private readonly globalService: GlobalService) { }

    @Mutation(() => GlobalDetail)
    // @UseGuards(JwtAuthGuard)
    async createGlobal(
        @Args('input') input: CreateGlobalInput,
        @Context() context: any,
    ): Promise<GlobalDetail> {
        const token = context.req.headers.authorization?.replace('Bearer ', '') ?? '';
        const result = await this.globalService.create(input, token);
        if (!result.data?.global) {
            throw new Error('Failed to create global');
        }
        return result.data.global as unknown as GlobalDetail;
    }

    @Query(() => GlobalsResponse)
    // @UseGuards(JwtAuthGuard)
    async findAllGlobals(
        @Args('input', { nullable: true }) input?: FindAllGlobalsInput,
    ): Promise<GlobalsResponse> {
        const defaultInput: FindAllGlobalsInput = {
            page: 1,
            limit: 10,
            search: '',
            filters: {},
            sortBy: 'createdAt',
            sortOrder: 'desc',
        };

        const result = await this.globalService.findAll(input || defaultInput);
        if (!result.data) {
            throw new Error('Failed to fetch globals');
        }
        return result.data as unknown as GlobalsResponse;
    }

    @Query(() => GlobalDetail)
    // @UseGuards(JwtAuthGuard)
    async findGlobalById(
        @Args('id', { type: () => ID }) id: string,
        @Args('selectedFields', { type: () => [String], nullable: true })
        selectedFields?: string[],
    ): Promise<GlobalDetail> {
        // Add null/undefined check for ID
        if (!id) {
            throw new Error('Global ID is required and cannot be null or empty.');
        }

        const result = await this.globalService.findById(id, selectedFields);
        if (!result.data?.global) {
            throw new Error(`Global with ID ${id} not found`);
        }
        return result.data.global as unknown as GlobalDetail;
    }

    @Mutation(() => GlobalDetail)
    // @UseGuards(JwtAuthGuard)
    async updateGlobal(
        @Args('input') input: UpdateGlobalInput,
        @Context() context: any,
    ): Promise<GlobalDetail> {
        const token = context.req.headers.authorization?.replace('Bearer ', '') ?? '';
        const result = await this.globalService.update(input, token);
        if (!result.data?.global) {
            throw new Error(`Global with ID ${input.id} not found`);
        }
        return result.data.global as unknown as GlobalDetail;
    }

    @Mutation(() => GlobalDetail)
    // @UseGuards(JwtAuthGuard)
    async deleteGlobal(
        @Args('id', { type: () => ID }) id: string,
    ): Promise<GlobalDetail> {
        const result = await this.globalService.delete(id);
        if (!result.data?.global) {
            throw new Error(`Global with ID ${id} not found`);
        }
        return result.data.global as unknown as GlobalDetail;
    }

    @Query(() => [GlobalDataResponse])
    // @UseGuards(JwtAuthGuard)
    async getGlobalByName(
        @Args('name', { type: () => String }) name: string,
        @Args('stateId', { type: () => String, nullable: true }) stateId?: string
    ): Promise<GlobalDataResponse[]> {
        const result = await this.globalService.getGlobalByName(name, stateId);

        if (!result?.data?.data || !Array.isArray(result.data.data)) {
            throw new Error(`Global with name "${name}" not found or invalid data format`);
        }

        return result.data.data;
    }
} 