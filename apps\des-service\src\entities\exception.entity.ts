import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true ,strict: false,})
export class Exception extends Document {
  @Prop({ type: String, required: true })
  createdBy: string;

  @Prop({ type: String, required: true })
  updatedBy: string;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ type: Types.ObjectId, ref: 'Template', required: true })
  templateId: Types.ObjectId;

  @Prop({ type: Object, required: true })
  values: Record<string, any>;

  // @Prop({ type: Object, required: true })
  // flattenedValues: Record<string, any>;

  @Prop({ type: Types.ObjectId, ref: 'Process', required: true })
  processId: Types.ObjectId;

  @Prop({ type: String, required: true })
  exceptions: string;
}

export const ExceptionSchema = SchemaFactory.createForClass(Exception); 