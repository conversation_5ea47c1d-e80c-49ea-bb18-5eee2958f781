import { InputType, Field, ID, ObjectType, Int, ArgsType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsMongoId, IsNumber } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { IsInt, Min, Max } from 'class-validator';

@InputType()
export class CreateProviderHistoryInput {
  @Field(() => ID)
  @IsMongoId()
  providerId: string;

  @Field()
  @IsString()
  comments: string;

  @Field()
  @IsString()
  follow_date: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  exception_type?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  touch_count?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updated_by?: string;

  @Field()
  status: boolean;

  @Field(() => GraphQLJSON, { nullable: true }) 
  @IsOptional()
  values?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  start_time?: string;

  @Field({ nullable: true })
  @IsOptional()
  end_time?: string;

}

@InputType()
export class UpdateProviderHistoryInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  comments?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  follow_date?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  exception_type?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  touch_count?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updated_by?: string;

  @Field({ nullable: true })
  @IsOptional()
  status?: boolean;

}

@InputType()
export class PaginateProviderHistoryArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  selectedFields?: Record<string, any>;
}



@ObjectType()
@Directive('@shareable')
export class Pagination {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}



@ObjectType()
export class ProviderHistoryResponse {
  @Field(() => [GraphQLJSON])
  providerHistory: any[];

  @Field(() => Pagination)
  pagination: Pagination;
}
