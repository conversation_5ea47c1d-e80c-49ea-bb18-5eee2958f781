import { ObjectType, Field, Int, InputType, registerEnumType } from '@nestjs/graphql';
import { GraphQLJSON } from 'graphql-type-json';
import { IsOptional, IsString } from 'class-validator';

// Enums
export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  ASSIGN = 'ASSIGN',
  UNASSIGN = 'UNASSIGN',
  ROLE_ASSIGNMENT = 'ROLE_ASSIGNMENT',
  BULK_OPERATION = 'BULK_OPERATION',
  TEST = 'TEST',
}

export enum AuditSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum AuditCategory {
  USER = 'USER',
  SYSTEM = 'SYSTEM',
  SECURITY = 'SECURITY',
  BUSINESS = 'BUSINESS',
  AUDIT = 'AUDIT',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

// Register enums with GraphQL
registerEnumType(AuditAction, { name: 'AuditAction' });
registerEnumType(AuditSeverity, { name: 'AuditSeverity' });
registerEnumType(AuditCategory, { name: 'AuditCategory' });
registerEnumType(SortOrder, { name: 'SortOrder' });

// GraphQL Types
@ObjectType()
export class AuditLogType {
  @Field()
  id: string;

  @Field()
  auditId: string;

  @Field()
  userId: string;

  @Field()
  orgId: string;

  @Field({ nullable: true })
  userEmail?: string;

  @Field(() => AuditAction)
  action: AuditAction;

  @Field()
  entityType: string;

  @Field({ nullable: true })
  entityId?: string;

  @Field(() => [String])
  message: string[];

  @Field({ nullable: true })
  beforeData?: string;

  @Field({ nullable: true })
  afterData?: string;

  @Field(() => AuditSeverity)
  severity: AuditSeverity;

  @Field(() => AuditCategory)
  category: AuditCategory;

  @Field()
  timestamp: Date;

  @Field({ nullable: true })
  ipAddress?: string;

  @Field({ nullable: true })
  userAgent?: string;

  @Field({ nullable: true })
  sessionId?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class AuditLogConnection {
  @Field(() => [AuditLogType])
  data: AuditLogType[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;

  @Field()
  hasNextPage: boolean;

  @Field()
  hasPreviousPage: boolean;
}

// Input Types
@InputType()
export class AuditLogFilterInput {
  @Field({ nullable: true })
  userId?: string;

  @Field({ nullable: true })
  orgId?: string;

  @Field({ nullable: true })
  userEmail?: string;

  @Field(() => [AuditAction], { nullable: true })
  actions?: AuditAction[];

  @Field({ nullable: true })
  entityType?: string;

  @Field({ nullable: true })
  entityId?: string;

  @Field(() => [AuditSeverity], { nullable: true })
  severities?: AuditSeverity[];

  @Field(() => [AuditCategory], { nullable: true })
  categories?: AuditCategory[];

  @Field({ nullable: true })
  startDate?: Date;

  @Field({ nullable: true })
  endDate?: Date;

  @Field({ nullable: true })
  ipAddress?: string;
}

@InputType()
export class AuditLogSortInput {
  @Field({ defaultValue: 'timestamp' })
  field: string;

  @Field(() => SortOrder, { defaultValue: SortOrder.DESC })
  order: SortOrder;
}

@InputType()
export class AuditLogQueryInput {
  @Field(() => Int, { defaultValue: 1 })
  page: number;

  @Field(() => Int, { defaultValue: 20 })
  limit: number;

  @Field({ nullable: true })
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

@ObjectType()
export class AuditStatsType {
  @Field(() => Int)
  totalLogs: number;

  @Field(() => [ActionStatType])
  actionStats: ActionStatType[];

  @Field(() => [UserStatType])
  userStats: UserStatType[];

  @Field(() => [SeverityStatType])
  severityStats: SeverityStatType[];

  @Field(() => [CategoryStatType])
  categoryStats: CategoryStatType[];
}

@ObjectType()
export class ActionStatType {
  @Field(() => AuditAction)
  action: AuditAction;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class UserStatType {
  @Field()
  userId: string;

  @Field({ nullable: true })
  userEmail?: string;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class SeverityStatType {
  @Field(() => AuditSeverity)
  severity: AuditSeverity;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class CategoryStatType {
  @Field(() => AuditCategory)
  category: AuditCategory;

  @Field(() => Int)
  count: number;
}
