// email-service/kafka/kafka-consumer.service.ts
import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Kafka, EachMessagePayload } from 'kafkajs';
import { NotificationService } from '@app/notification'; // shared module
import { NotificationPayload } from '@app/notification'; // shared interface

@Injectable()
export class KafkaConsumerService implements OnModuleInit {
  private readonly kafka = new Kafka({
    clientId: 'email-service',
    brokers: [process.env.KAFKA_BROKER || '35.209.174.164:9092'],
  });

  private readonly consumer = this.kafka.consumer({
    groupId: 'email-reply-consumer-group',
  });

  private readonly logger = new Logger(KafkaConsumerService.name);

  constructor(
    private readonly notificationHistoryService: NotificationService,
  ) {}

  async onModuleInit() {
    await this.consumer.connect();
    await this.consumer.subscribe({ topic: 'notification', fromBeginning: false });

    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }: EachMessagePayload) => {       
        const raw = message.value?.toString();
        if (!raw) return;

        try {
          const payload: NotificationPayload = JSON.parse(raw);

          const notificationId = payload?.data?.notificationId || 'unknown-id';
          const deviceTokens = payload?.data?.deviceTokens || [];

          this.logger.log(`📩 Kafka [${topic}]: Received message for user ${payload.userId}`);

          // ✅ 1. Save to DB
          await this.notificationHistoryService.saveNotificationToDb(
            payload,
            notificationId,
            payload.metadata?.orgId,
          );

          // ✅ 2. Send Firebase push (if device tokens exist)
          if (deviceTokens.length > 0) {
            await Promise.all(
              deviceTokens.map(token =>
                this.notificationHistoryService.notifyUserWithFirebasePush(
                  token,
                  payload.title,
                  payload.message,
                  payload.data,
                  payload.metadata
                )
              )
            );
          } else {
            this.logger.warn(`⚠️ No device tokens found for user ${payload.userId}`);
          }

        } catch (error) {
          this.logger.error(`❌ Failed to process message: ${error.message}`, error.stack);
        }
      },
    });
  }
}
