import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Response } from 'express';
// Bull Board imports
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization,orgId,subOrgId,processId,userId,roleName',
  });

  // --- Bull Board integration (before any /admin routes) ---
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/graphql/bull/');

  const taskQueue = new Queue('task-queue{identity}', {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: +(process.env.REDIS_PORT || 6379),
    },
  });
  const exportQueue = new Queue('export-queue{identity}', {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: +(process.env.REDIS_PORT || 6379),
    },
  });
  const importQueue = new Queue('import-queue{identity}', {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: +(process.env.REDIS_PORT || 6379),
    },
  });

  createBullBoard({
    queues: [
      new BullMQAdapter(taskQueue),
      new BullMQAdapter(exportQueue),
      new BullMQAdapter(importQueue),
    ],
    serverAdapter,
  });
  app.use('/graphql/bull/', serverAdapter.getRouter());
  // --- End Bull Board integration ---

  // Add simple health check endpoint
  app.use('/health', (_, res: Response) => {
    res.status(200).json({ status: 'ok', uptime: process.uptime() });
  });

  await app.listen(process.env.PORT ?? 4000);
}




bootstrap();
