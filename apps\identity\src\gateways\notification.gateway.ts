import {
  WebSocketGateway,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { WebSocketNotification } from '@app/notification';
import * as jwt from 'jsonwebtoken';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  namespace: '/notifications',
})
export class NotificationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationGateway.name);
  private connectedUsers = new Map<string, AuthenticatedSocket[]>();

  constructor() {
    this.logger.log('🚀 NotificationGateway initialized');
  }

  afterInit(server: Server) {
    this.logger.log('🌐 WebSocket server initialized on /notifications namespace');
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract token from handshake auth or query
      const token = client.handshake.auth?.token || client.handshake.query?.token;

      if (!token) {
        this.logger.warn('Client connected without authentication token');
        client.disconnect();
        return;
      }


      let decoded: any;

      try {
        // Try to verify JWT token
        decoded = jwt.verify(token as string, process.env.JWT_SECRET ?? 'fallback-secret');
      } catch (jwtError) {
        // For development purposes, allow test tokens
        if (process.env.NODE_ENV === 'development' && (token.toString().startsWith('eyJ') || token === 'test-token')) {
          this.logger.warn('Using test token for development');
          decoded = {
            userId: client.handshake.auth?.userId || 'test-user-123',
            email: '<EMAIL>'
          };
        } else {
          this.logger.error('JWT verification failed:', jwtError.message);
          client.disconnect();
          return;
        }
      }

      if (typeof decoded === 'object' && 'userId' in decoded) {
        client.userId = (decoded as any).userId;
        client.userEmail = (decoded as any).email;

        // Store connection by userId
        if (!this.connectedUsers.has(client.userId||"")) {
          this.connectedUsers.set(client.userId||"", []);
        }
        this.connectedUsers.get(client.userId || "")!.push(client);

        this.logger.log(`User ${client.userId} connected to notifications`);

        // Join user to their personal room
        client.join(`user:${client.userId}`);

        // Send connection confirmation
        client.emit('connected', {
          message: 'Connected to notification service',
          userId: client.userId,
          timestamp: new Date(),
        });
      } else {
        this.logger.warn('Invalid token payload');
        client.disconnect();
      }
    } catch (error) {
      this.logger.error('Authentication failed:', error.message);
      client.disconnect();
    }
  }
  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      const userConnections = this.connectedUsers.get(client.userId);
      if (userConnections) {
        const index = userConnections.indexOf(client);
        if (index > -1) {
          userConnections.splice(index, 1);
        }
        
        if (userConnections.length === 0) {
          this.connectedUsers.delete(client.userId);
        }
      }
      
      this.logger.log(`User ${client.userId} disconnected from notifications`);
    }
  }

  @SubscribeMessage('subscribe')
  handleSubscribe(
    @MessageBody() data: { types: string[] },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) return;
    
    // Join specific notification type rooms
    data.types.forEach(type => {
      client.join(`${type}:${client.userId}`);
    });
    
    client.emit('subscribed', {
      types: data.types,
      message: 'Subscribed to notification types',
    });
  }

  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(
    @MessageBody() data: { types: string[] },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) return;
    
    // Leave specific notification type rooms
    data.types.forEach(type => {
      client.leave(`${type}:${client.userId}`);
    });
    
    client.emit('unsubscribed', {
      types: data.types,
      message: 'Unsubscribed from notification types',
    });
  }

  @OnEvent('notification.websocket')
  async handleWebSocketNotification(data: { userId: string; notification: WebSocketNotification }) {
    const { userId, notification } = data;
    
    try {
      // Send to user's personal room
      this.server.to(`user:${userId}`).emit('notification', notification);
      
      // Also send to specific notification type room
      this.server.to(`${notification.type}:${userId}`).emit('notification', notification);
      
      this.logger.log(`WebSocket notification sent to user ${userId}: ${notification.type}`);
    } catch (error) {
      this.logger.error(`Failed to send WebSocket notification: ${error.message}`);
    }
  }

  // Method to send notification to specific user
  async sendToUser(userId: string, notification: WebSocketNotification) {
    this.server.to(`user:${userId}`).emit('notification', notification);
  }

  // Method to broadcast to all connected users
  async broadcast(notification: WebSocketNotification) {
    this.server.emit('notification', notification);
  }

  // Get connected users count
  getConnectedUsersCount(): number {
    return this.connectedUsers.size;
  }

  // Get specific user's connection count
  getUserConnectionCount(userId: string): number {
    return this.connectedUsers.get(userId)?.length || 0;
  }

  // Check if user is online
  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId);
  }
}
