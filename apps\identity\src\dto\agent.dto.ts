import { InputType, Field, ID, ObjectType } from '@nestjs/graphql';
import { Agent } from '../entities/agent.entities';

@InputType()
export class CreateAgentInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  role: string;

}

@InputType()
export class UpdateAgentInput {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  email?: string;

  @Field({ nullable: true })
  role?: string;
}

@InputType()
export class GetAgentInput {
  @Field(() => ID)
  id: string;
}

@InputType()
export class GetAgentByEmailInput {
  @Field()
  email: string;
}

@InputType()
export class AgentLoginInput {
  @Field()
  email: string;

  @Field()
  password: string;
}

@InputType()
export class AgentSSOLoginInput {
  @Field()
  token: string;
}


@InputType()
export class GetAgentsInput {
  @Field({ nullable: true })
  search?: string;

  @Field(() => String, { nullable: true })
  filters?: string; // JSON string of filters

  @Field({ nullable: true })
  sortBy?: string;

  @Field({ nullable: true })
  sortOrder?: 'asc' | 'desc';

  @Field({ nullable: true })
  page?: number;

  @Field({ nullable: true })
  limit?: number;
}

@ObjectType()
export class AgentsResponse {
  @Field(() => [Agent])
  agents: Agent[];

  @Field()
  total: number;

  @Field()
  page: number;

  @Field()
  limit: number;
}

@ObjectType()
export class ValidateTokenResponse {
  @Field()
  email: string;

  @Field()
  exists: boolean;

  @Field()
  name: string;

  @Field()
  role: string;
}

