name: Client Build

on:
  push:
    branches:
      - develop
    paths:
      - 'apps/gateway/**'
      - 'apps/identity/**'
      - 'apps/des-service/**'
      - 'apps/provider/**'
      - 'apps/notification-service/**'
permissions:
  contents: write
  id-token: write
  # pull_request:
  #   branches:
  #     - develop

jobs:
  build:
    name: Sonarqube test
    runs-on: ubuntu-latest
    steps:
      # Step 1: Checkout the code
      - name: checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          tags: true

      # Step 2: Set Build Number
      - name: Set Build Number
        id: build_number
        run: echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV

      # # Step 3: Set up the required language runtime
      # - name: Set up Node.js
      #   uses: actions/setup-node@v3
      #   with:
      #     node-version: 18

      # # Step 4: Install dependencies
      # - name: Install Dependencies
      #   run: npm install --legacy-peer-deps

      # # Step 5: Run unit tests and generate coverage report
      # - name: Run Unit Tests
      #   run: npm test -- --coverage

      # # Step 6: Upload coverage report as an artifact
      # - name: Upload Coverage Report
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: coverage-report
      #     path: coverage/

      # Step 7: Run SonarQube analysis
      - uses: sonarsource/sonarqube-scan-action@v4
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
          SONAR_SCANNER_OPTS: |
            -Dsonar.projectKey=${{ secrets.SONAR_PROJECT_KEY }}
            -Dsonar.projectVersion=${{ env.BUILD_NUMBER }}
            -Dsonar.login=${{ secrets.SONAR_TOKEN }}
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
      # Step 8: Authenticate to GCP
      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          token_format: "access_token"
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT_EMAIL }}
      # Step 9: Configure Docker
      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker us-central1-docker.pkg.dev
      # Step 10: Install Docker Compose
      - name: Install Docker Compose
        run: |
          sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
          sudo chmod +x /usr/local/bin/docker-compose
          docker-compose --version
      # Step 11: Fetch All Tags (Ensures Latest Tag Detection)
      - name: Fetch All Tags
        run: git fetch --tags
      # Step 12: Detect Changes, Version, Build & Push to Artifact Registry
      - name: Detect Changes, Version, Build & Push to Artifact Registry
        run: |
          BASE_REF=${{ github.event.before }}
          HEAD_REF=${{ github.event.after }}
          CHANGED_FILES=$(git diff --name-only $BASE_REF $HEAD_REF)

          echo "Changed files:"
          echo "$CHANGED_FILES"

          SERVICES=("identity" "des-service" "provider" "notification-service" "gateway")

          for SERVICE in "${SERVICES[@]}"; do
            if echo "$CHANGED_FILES" | grep -q "^apps/$SERVICE/"; then
              echo "Building $SERVICE..."

              BRANCH="develop"
              latest_tag=$(git tag --list "${BRANCH}-${SERVICE}-v*" --sort=-v:refname | head -n 1)
              if [[ -z "$latest_tag" ]]; then
                latest_tag="${BRANCH}-${SERVICE}-v0.0.0"
              fi

              version=${latest_tag#${BRANCH}-${SERVICE}-v}
              IFS='.' read -r major minor patch <<< "$version"

              commits=$(git log --oneline "$latest_tag"..HEAD -- apps/$SERVICE || echo "")

              if echo "$commits" | grep -Eiq "^breaking change"; then
                major=$((major + 1)); minor=0; patch=0
              elif echo "$commits" | grep -Eiq "^feat:"; then
                minor=$((minor + 1)); patch=0
              elif echo "$commits" | grep -Eiq "^fix:"; then
                patch=$((patch + 1))
              else
                patch=$((patch + 1))
              fi

              version_only="v$major.$minor.$patch"
              full_tag="${BRANCH}-${SERVICE}-${version_only}"
              echo "New tag: $full_tag"

              echo "Building Docker image for $SERVICE..."
              docker-compose -f docker-compose.yml build ${SERVICE}-service

              echo "Docker images after build:"
              docker images | grep $SERVICE

              IMAGE_PATH="us-central1-docker.pkg.dev/${{ secrets.GCP_PROJECT_ID }}/asprcm-dev-backend-${SERVICE}-repo/${SERVICE}"
              echo "Pushing to $IMAGE_PATH"

              docker tag backend-${SERVICE}-service:latest $IMAGE_PATH:$version_only
              docker tag backend-${SERVICE}-service:latest $IMAGE_PATH:latest

              docker push $IMAGE_PATH:$version_only
              docker push $IMAGE_PATH:latest

              echo "Pushing Git tag $full_tag"
              git config user.email "<EMAIL>"
              git config user.name "GitHub Actions"
              git tag $full_tag
              git push origin $full_tag
            
              else
                echo "No changes in $SERVICE. Skipping."
              fi
            done
      # Step 13: Update image version in Kubemanifest repo
      - name: Update backend image versions in Kubemanifest repo
        run: |
          set -e  # Exit on error

          git config --global user.email "${{ secrets.HUB_USER_EMAIL }}"
          git config --global user.name "${{ secrets.HUB_USERNAME }}"

          BASE_REF=${{ github.event.before }}
          HEAD_REF=${{ github.event.after }}
          CHANGED_FILES=$(git diff --name-only $BASE_REF $HEAD_REF)

          SERVICES=("identity" "des-service" "provider" "notification-service" "gateway")
          declare -A SERVICE_TAGS
          ANY_CHANGES=false

          echo "Fetching latest tags..."
          git fetch --tags

          for SERVICE in "${SERVICES[@]}"; do
            if echo "$CHANGED_FILES" | grep -q "^apps/$SERVICE/"; then
              echo "Detected change in $SERVICE. Looking up latest tag..."

              BRANCH="develop"
              latest_tag=$(git tag --list "${BRANCH}-${SERVICE}-v*" --sort=-v:refname | head -n 1)

              if [[ -z "$latest_tag" ]]; then
                echo "No tag found for $SERVICE, skipping..."
                continue
              fi

              version=${latest_tag#${BRANCH}-${SERVICE}-v}
              SERVICE_TAGS[$SERVICE]=$version
              echo "$SERVICE version: $version"
            fi
          done

          echo "Cloning Kubemanifest repository..."
          git clone --branch develop https://x-access-token:${{ secrets.HUB_TOKEN }}@github.com/ASPRCM-SOLUTIONS-PVT-LTD/Kubemanifest.git

          cd Kubemanifest/overlays/dev/patch

          for SERVICE in "${!SERVICE_TAGS[@]}"; do
            version="${SERVICE_TAGS[$SERVICE]}"
            IMAGE_PATH="us-central1-docker.pkg.dev/asp-rcm/asprcm-dev-backend-${SERVICE}-repo/${SERVICE}"
            PATCH_FILE="patch-${SERVICE}.yml"

            echo "Updating $PATCH_FILE with version $version..."

            if [ ! -f "$PATCH_FILE" ]; then
              echo "Warning: $PATCH_FILE not found. Skipping $SERVICE."
              continue
            fi

            sed -i "s|image: .*${SERVICE}:.*|image: ${IMAGE_PATH}:v${version}|g" "$PATCH_FILE"

            if ! git diff --quiet "$PATCH_FILE"; then
              git add "$PATCH_FILE"
              ANY_CHANGES=true
              echo "$SERVICE patch updated to version $version"
            else
              echo "$SERVICE already up to date."
            fi
          done

          if [ "$ANY_CHANGES" = true ]; then
            git commit -m "ci(backend): bump image versions for updated services"
            git push origin develop
          else
            echo "No patch file changes to commit."
          fi
      # Step 14: Authenticate to GKE
      - name: Get GKE Credentials
        uses: google-github-actions/get-gke-credentials@v2
        with:
          cluster_name: ${{ secrets.GKE_CLUSTER_NAME }}
          location: ${{ secrets.GKE_CLUSTER_REGION }}
          project_id: ${{ secrets.GCP_PROJECT_ID }}
      # Step 15: Apply updated manifest to GKE
      - name: Apply updated Kubernetes manifests
        run: |
          cd Kubemanifest/overlays/dev
          kubectl apply -k .
      # Step 16: Delay for 60 seconds
      - name: Wait before restarting Gateway Deployment
        run: sleep 60
      # Step 17: Restart the gateway deployment
      - name: Restart Gateway Deployment
        run: |
          kubectl rollout restart deployment gateway -n asprcm