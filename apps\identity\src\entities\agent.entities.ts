import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@ObjectType()
@Schema()
export class Agent extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;

    @Field()
    @Prop({ required: true, unique: true })
    email: string;

    @Field()
    @Prop({ required: true })
    name: string;

    @Field()
    @Prop({ required: true })
    role: string;

    @Field(() => [ID], { nullable: true })
    @Prop({ type: [Types.ObjectId], ref: 'Client' })
    clients: Types.ObjectId[];

    @Field({ nullable: true })
    @Prop()
    token?: string;
}

export const AgentSchema = SchemaFactory.createForClass(Agent);
