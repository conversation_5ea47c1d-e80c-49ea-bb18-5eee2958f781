import { InputType, Field, ID, ObjectType, Int } from '@nestjs/graphql';
import { IsMongoId, IsOptional, IsString, IsBoolean, IsArray, IsI<PERSON>, Min, Max } from 'class-validator';
import { Graph<PERSON>JSON, GraphQLJSONObject } from 'graphql-type-json';
import { OrganisationType } from '../entities/organisation-role.entity';

@InputType()
export class CreateOrganisationRoleInput {
  @Field()
  @IsString()
  name: string;
  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsOptional()
  permissions?: any[];
  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
  @Field(() => OrganisationType)
  type: OrganisationType;
}

@InputType()
export class UpdateOrganisationRoleInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsOptional()
  permissions?: any[];

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => OrganisationType, { nullable: true })
  @IsOptional()
  type?: OrganisationType;
}

@ObjectType()
export class OrganisationRoleResponse {
  @Field(() => ID)
  _id: string;

  @Field()
  name: string;

  @Field(() => GraphQLJSONObject)
  permissions: any[];

  @Field()
  isActive: boolean;

  @Field(() => OrganisationType)
  type: OrganisationType;

  @Field({ nullable: true })
  updatedAt?: Date;

  @Field({ nullable: true })
  createdAt?: Date;
}

@InputType()
export class OrganisationRoleListArgs {
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

@ObjectType()
export class OrganisationRolePagination {
  @Field(() => [OrganisationRoleResponse])
  items: OrganisationRoleResponse[];

  @Field(() => GraphQLJSON)
  pagination: any;
} 