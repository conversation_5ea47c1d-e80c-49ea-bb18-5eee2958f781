import { InputType, Field, ArgsType, Int, ObjectType } from '@nestjs/graphql';
import { IsString, IsOptional, IsBoolean, IsMongoId, IsNumber } from 'class-validator';
import { ModuleUrl, PaginationMetas } from '../entities/module-url.entity';
import { GraphQLJSON } from 'graphql-type-json';

@InputType()
export class CreateModuleUrlInput {
  @Field()
  @IsString()
  moduleName: string;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  importUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  backupUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  exportUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  auditUrlTime?: number;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isImport?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isExport?: boolean;

  @Field()
  @IsString()
  updatedBy: string;

  @Field()
  @IsString()
  collectionName: string;
}

@InputType()
export class UpdateModuleUrlInput {
  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  importUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  backupUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  exportUrlTime?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  auditUrlTime?: number;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isImport?: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isExport?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  collectionName?: string;
}

@ObjectType()
export class ModuleUrlPaginatedResponse {
  @Field(() => [ModuleUrl])
  data: ModuleUrl[];

  @Field(() => PaginationMetas)
  pagination: PaginationMetas;
}

@ArgsType()
export class GetModuleUrlsArgs {
  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>; // JSON object for dynamic filtering

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsNumber()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true, defaultValue: 'desc' })
  @IsOptional()
  @IsString()
  sortOrder?: string;
}

@ArgsType()
export class GetModuleUrlByIdArgs {
  @Field()
  @IsMongoId()
  id: string;
}

@ArgsType()
export class UpdateModuleUrlArgs {
  @Field()
  @IsMongoId()
  id: string;

  @Field(() => UpdateModuleUrlInput)
  input: UpdateModuleUrlInput;
}

@ArgsType()
export class DeleteModuleUrlArgs {
  @Field()
  @IsMongoId()
  id: string;
}
