import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import { Model, Connection, Types } from 'mongoose';
import { TaskQueue, FailedRowInfo } from '../entities/task.entity';
import { ImportConfiguration } from '../entities/import-configuration.entity';
import { ImportConfigurationService } from './import-configuration.service';
import { UserService } from './user.service';
import { NotificationService, NotificationType, NotificationPriority, NotificationChannel, NotificationPayload } from '@app/notification';
import { AuditHelperService } from '@app/audit';
import { KafkaService } from '@app/email';
import { ImportType } from '../dto/import.dto';
import { v4 as uuidv4 } from 'uuid';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { GcsService } from './gcs.service';
import mongoose from 'mongoose';
import { MongoConnectionService } from '@app/db';
import * as ExcelJS from 'exceljs';
import { ObjectId } from 'mongodb';
import { ModuleUrlService } from './module-url.service';

@Injectable()
export class ImportService {
  constructor(
    @InjectModel(TaskQueue.name) private taskModel: Model<TaskQueue>,
    @InjectQueue('task-queue{identity}') private taskQueue: Queue,
    @InjectConnection() private readonly connection: Connection,
    private readonly mongoConnectionService: MongoConnectionService,
    private readonly gcsService: GcsService,
    private readonly importConfigurationService: ImportConfigurationService,
    private readonly userService: UserService,
    private readonly notificationService: NotificationService,
    private readonly auditHelperService: AuditHelperService,
    private readonly kafkaService: KafkaService,
    private readonly moduleUrlService: ModuleUrlService,
  ) { }

  /**
   * Get expiration times from module URL configuration
   */
  private async getExpirationTimes(collectionName: string): Promise<{
    importUrl?: Date;
    backupUrl?: Date;
    exportUrl?: Date;
    auditUrl?: Date;
  }> {
    try {
      // Find module URL configuration for this collection
      const moduleUrlConfig = await this.moduleUrlService.findByCollectionName(collectionName);

      if (!moduleUrlConfig) {
        console.warn(`No module URL configuration found for collection: ${collectionName}`);
        return {};
      }

      const now = new Date();
      const expires: any = {};

      // Calculate expiration dates based on days from module configuration
      if (moduleUrlConfig.importUrlTime && moduleUrlConfig.importUrlTime > 0) {
        expires.importUrl = new Date(now.getTime() + (moduleUrlConfig.importUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.backupUrlTime && moduleUrlConfig.backupUrlTime > 0) {
        expires.backupUrl = new Date(now.getTime() + (moduleUrlConfig.backupUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.exportUrlTime && moduleUrlConfig.exportUrlTime > 0) {
        expires.exportUrl = new Date(now.getTime() + (moduleUrlConfig.exportUrlTime * 24 * 60 * 60 * 1000));
      }

      if (moduleUrlConfig.auditUrlTime && moduleUrlConfig.auditUrlTime > 0) {
        expires.auditUrl = new Date(now.getTime() + (moduleUrlConfig.auditUrlTime * 24 * 60 * 60 * 1000));
      }

      console.log(`📅 Expiration times calculated for ${collectionName}:`, expires);
      return expires;
    } catch (error) {
      console.error(`❌ Failed to get expiration times for ${collectionName}:`, error);
      return {};
    }
  }

  async createImportTask(body: {
    filePath: string;
    collectionName: string;
    createdBy: string;
    orgId?: string;
    type: ImportType;
    fileType?: string;
    configurationId: string; // Now required - this is the import configuration ID
    isStopOnError?: boolean; // Stop on first error flag
  }): Promise<string> {
    const taskId = `Imp-${Math.floor(100000 + Math.random() * 900000)}`;
    // const taskUuid = uuidv4();

    let mapping: Record<string, string> = {};
    let uniqueFields: string[] = [];
    let configurationId: Types.ObjectId | undefined;

    // Load configuration from configurationId (now required)
    try {
      const configuration = await this.importConfigurationService.getConfigurationByTemplateId(
        body.configurationId,
        body.createdBy // Pass userId for security check
      );

      // Always use configuration mapping and unique fields
      mapping = configuration.mappingJson;
      uniqueFields = configuration.uniqueFields || [];
      configurationId = configuration._id;

      // Validate that collection names match
      // if (configuration.collectionName !== body.collectionName) {
      //   throw new BadRequestException(
      //     `Template collection (${configuration.collectionName}) does not match request collection (${body.collectionName})`
      //   );
      // }
    } catch (error) {
      console.log('Failed to load import configuration:', error);

      if (error instanceof NotFoundException) {
        throw new BadRequestException('Invalid import configuration ID provided');
      }
      throw error;
    }

    // Validate that we have mapping data from template
    if (!mapping || Object.keys(mapping).length === 0) {
      console.error('No mapping found in import configuration. Mapping:', mapping);
      throw new BadRequestException('Import configuration must have valid mapping configuration. Please check that your import configuration has a mappingJson field with proper field mappings.');
    }

    // Validate import type
    if (!Object.values(ImportType).includes(body.type)) {
      throw new BadRequestException(`Invalid import type. Must be one of: ${Object.values(ImportType).join(', ')}`);
    }

    // Prevent imports into processes collection - processes must be static
    if (body.collectionName === 'processes') {
      throw new BadRequestException('Direct import into processes collection is not allowed. Processes must be created manually and exist as static data in the system.');
    }

    // Fetch user email
    let createdByEmail: string | undefined = undefined;
    if (body.createdBy) {
      try {
        const user = await this.userService.findOne(body.createdBy);
        if (user && user.email) {
          createdByEmail = user.email;
        }
      } catch { }
    }

    // Get expiration times from module URL configuration
    const expires = await this.getExpirationTimes(body.collectionName);
    console.log("expires:", body);

    await this.taskModel.create({
      taskId,
      type: 'import',
      name: `Import Task - ${body.collectionName}`,
      // mapping, // Commented out in entity - will get from template
      downloadUrl: body.filePath,
      collectionName: body.collectionName,
      status: 'NOT_STARTED',
      createdBy: body.createdBy,
      createdByEmail,
      fileType: body.fileType || body.filePath.split('.').pop()?.toLowerCase(),
      orgId: body.orgId || null,
      // uniqueFields, // Commented out in entity - will get from template
      importMode: body.type, // 'upsert', 'update', or 'create'
      configurationId, // Reference to ImportConfiguration
      isStopOnError: body.isStopOnError || false, // Stop on first error flag
      expires, // Expiration times from module URL configuration
      insertedCount: 0,
      updatedCount: 0,
      statusLog: [{
        status: 'SCHEDULED',
        message: 'Task scheduled',
        timestamp: new Date(),
      }],
    });

    // Log audit event for task creation
    await this.logImportAuditEvent(
      taskId,
      'CREATE',
      null,
      {
        status: 'CREATED',
        collectionName: body.collectionName,
        importMode: body.type,
        templateId: configurationId?.toString(),
        totalRecords: 0
      },
      body.createdBy || 'system',
      createdByEmail || ''
    );

    await this.taskQueue.add('import', { taskId });
    return taskId;
  }

  async processImportTask(taskId: string) {
    console.log('🚀 DEBUG: processImportTask STARTED for taskId:', taskId);

    const importTaskDoc = await this.taskModel.findOneAndUpdate(
      { taskId, type: 'import', status: 'NOT_STARTED' },
      {
        status: 'IN_PROGRESS',
        $push: {
          statusLog: {
            status: 'IN_PROGRESS',
            message: 'Task Started',
            timestamp: new Date(),
          },
        },
      },
      { new: true }
    );
    if (!importTaskDoc) {
      console.log('❌ DEBUG: No import task found for taskId:', taskId);
      return;
    }
    const importTask = importTaskDoc.toObject() as TaskQueue;
    console.log('📋 DEBUG: Import task loaded:', JSON.stringify(importTask, null, 2));

    // Log audit event for task start
    await this.logImportAuditEvent(
      taskId,
      'UPDATE',
      { status: 'NOT_STARTED' },
      { status: 'IN_PROGRESS' },
      importTask.createdBy || 'system',
      importTask.createdByEmail || ''
    );

    // Get mapping, uniqueFields, requiredFields, and configuration fields from import configuration
    let mapping: Record<string, string> = {};
    let uniqueFields: string[] = [];
    let requiredFields: string[] = [];
    let configurationFields: any[] = [];

    console.log('🔧 DEBUG: Loading configuration for configurationId:', importTask.configurationId);

    if (importTask.configurationId) {
      try {
        const configuration = await this.importConfigurationService.getConfigurationById(
          importTask.configurationId.toString()
        );
        console.log('📝 DEBUG: Loaded import configuration:', JSON.stringify(configuration, null, 2));
        mapping = configuration.mappingJson;
        uniqueFields = configuration.uniqueFields || [];
        requiredFields = configuration.requiredFields || [];

        // Always get the template fields from the template table
        console.log('🔍 DEBUG: About to fetch template with ID:', configuration.templateId);
        console.log('🔍 DEBUG: Using orgId for template collection:', importTask.orgId || "asp");

        const templateCollection = await this.mongoConnectionService.getCollectionByOrgId("templates", importTask.orgId || "asp");
        console.log('🔍 DEBUG: Template collection obtained');

        const template = await templateCollection.findOne({ _id: new Types.ObjectId(configuration.templateId) });
        console.log('🔍 DEBUG: Template query result:', template ? 'FOUND' : 'NOT FOUND');
        console.log('🔍 DEBUG: Template data:', JSON.stringify(template, null, 2));

        // Parse the fields if it's a JSON string
        let templateFields = template?.fields;
        console.log('🔍 DEBUG: Raw template.fields type:', typeof templateFields);
        console.log('🔍 DEBUG: Raw template.fields value:', templateFields);

        if (typeof templateFields === 'string') {
          try {
            templateFields = JSON.parse(templateFields);
            console.log('🔍 DEBUG: Successfully parsed JSON string to object');
          } catch (parseError) {
            console.error('🔍 DEBUG: Failed to parse template.fields JSON:', parseError);
            templateFields = [];
          }
        }

        configurationFields = Array.isArray(templateFields) ? templateFields : [];
        console.log('🔍 DEBUG: Extracted configurationFields length:', configurationFields.length);
        console.log('🔍 DEBUG: configurationFields type:', typeof configurationFields);
        console.log('🔍 DEBUG: configurationFields isArray:', Array.isArray(configurationFields));

        console.log('🗺️ DEBUG: Final mapping to be used:', JSON.stringify(mapping, null, 2));
        console.log('🔑 DEBUG: Unique fields:', uniqueFields);
        console.log('⚠️ DEBUG: Required fields:', requiredFields);
        console.log('📋 DEBUG: Configuration fields from template:', JSON.stringify(configurationFields, null, 2));
        console.log('🆔 DEBUG: Configuration ID used:', importTask.configurationId.toString());
        console.log('📄 DEBUG: Template ID used:', configuration.templateId);
        console.log('📄 DEBUG: Template found:', template ? 'YES' : 'NO');
      } catch (error) {
        console.error('Failed to load import configuration:', error);
        await this.taskModel.updateOne(
          { taskId },
          {
            status: 'FAILED',
            $push: {
              statusLog: {
                status: 'FAILED',
                message: 'Failed to load import configuration',
                timestamp: new Date(),
              },
            },
          }
        );
        return;
      }
    } else {
      // Fallback: this shouldn't happen with new implementation
      await this.taskModel.updateOne(
        { taskId },
        {
          status: 'FAILED',
          $push: {
            statusLog: {
              status: 'FAILED',
              message: 'No import configuration found',
              timestamp: new Date(),
            },
          },
        }
      );
      return;
    }

    let insertedCount = 0, updatedCount = 0, failedCount = 0;
    const failedRows: FailedRowInfo[] = [];
    const insertedIds: string[] = [];
    const updatedIds: string[] = [];
    const backupRows: any[] = []; // Store rows that will be updated for backup

    try {
      // 1. Get the correct collection using the connection service
      const collection = await this.mongoConnectionService.getCollectionByOrgId(
        importTask.collectionName || '',
        importTask.orgId || 'asp'
      );
      console.log("importTask.collectionName:", importTask.collectionName);
      console.log("importTask.downloadUrl:", importTask.downloadUrl);
      console.log("importTask.orgId:", importTask.orgId);

      // 2. Validate file path and extension
      const filePath = importTask.downloadUrl || '';
      if (!filePath) {
        await this.taskModel.updateOne(
          { taskId },
          {
            status: 'FAILED',
            $push: {
              statusLog: {
                status: 'FAILED',
                message: 'No file path provided for import.',
                timestamp: new Date(),
              },
            },
          }
        );
        await this.sendImportNotification(taskId, 'FAILED');
        return;
      }

      // Check if file has proper extension (Excel or CSV)
      const fileExtension = filePath.split('.').pop()?.toLowerCase();
      if (!fileExtension || !['xlsx', 'xls', 'csv'].includes(fileExtension)) {
        await this.taskModel.updateOne(
          { taskId },
          {
            status: 'FAILED',
            $push: {
              statusLog: {
                status: 'FAILED',
                message: `Invalid file format. Expected .xlsx, .xls, or .csv file, but got: ${filePath}`,
                timestamp: new Date(),
              },
            },
          }
        );
        await this.sendImportNotification(taskId, 'FAILED');
        return;
      }

      // 3. Download Excel file from GCS
      let buffer: Buffer;
      try {
        buffer = await this.gcsService.downloadBuffer(filePath);
      } catch (downloadError) {
        await this.taskModel.updateOne(
          { taskId },
          {
            status: 'FAILED',
            $push: {
              statusLog: {
                status: 'FAILED',
                message: `Failed to download file from GCS: ${downloadError instanceof Error ? downloadError.message : String(downloadError)}`,
                timestamp: new Date(),
              },
            },
          }
        );
        await this.sendImportNotification(taskId, 'FAILED');
        return;
      }

      if (!buffer || buffer.length === 0) {
        await this.taskModel.updateOne(
          { taskId },
          {
            status: 'FAILED',
            $push: {
              statusLog: {
                status: 'FAILED',
                message: 'The uploaded Excel file is empty or could not be read.',
                timestamp: new Date(),
              },
            },
          }
        );
        await this.sendImportNotification(taskId, 'FAILED');
        return;
      }
      console.log("buffer");

      // 4. Parse file based on extension
      const rows: Array<Record<string, any>> = [];
      let headerRow: any[] = [];

      console.log('📄 DEBUG: File extension detected:', fileExtension);
      console.log('🗂️ DEBUG: Starting file parsing...');

      if (fileExtension === 'csv') {
        console.log('📊 DEBUG: Parsing CSV file...');
        // Parse CSV file
        try {
          const csvContent = buffer.toString('utf-8');
          const lines = csvContent.split('\n').filter(line => line.trim());

          if (lines.length === 0) {
            await this.taskModel.updateOne(
              { taskId },
              {
                status: 'FAILED',
                $push: {
                  statusLog: {
                    status: 'FAILED',
                    message: 'The uploaded CSV file is empty.',
                    timestamp: new Date(),
                  },
                },
              }
            );
            await this.sendImportNotification(taskId, 'FAILED');
            return;
          }

          // Parse header row
          headerRow = this.parseCSVLine(lines[0]);
          console.log('CSV Headers found:', headerRow);
          console.log('Mapping configuration:', mapping);

          // Parse data rows
          for (let i = 1; i < lines.length; i++) {
            const csvRow = this.parseCSVLine(lines[i]);
            console.log(`CSV Row ${i}:`, csvRow);
            const rowObj = {};

            // Apply explicit mapping first
            Object.entries(mapping || {}).forEach(([collectionField, csvField]) => {
              const colIndex = typeof csvField === 'string' ? headerRow.indexOf(csvField) : -1;
              console.log(`Mapping ${collectionField} -> ${csvField}, colIndex: ${colIndex}`);
              if (colIndex !== -1 && colIndex < csvRow.length) {
                rowObj[collectionField] = csvRow[colIndex];
                console.log(`Set ${collectionField} = ${csvRow[colIndex]}`);
              } else {
                console.log(`Column ${csvField} not found in headers or no data at index ${colIndex}`);
              }
            });

            // Add unmapped columns directly (fallback for missing mappings)
            const mappedCsvFields = Object.values(mapping || {});

            headerRow.forEach((header, index) => {
              if (header && typeof header === 'string' && !mappedCsvFields.includes(header)) {
                const cellValue = csvRow[index];
                if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                  rowObj[header] = cellValue;
                  console.log(`Added unmapped CSV field: ${header} = ${cellValue}`);
                }
              }
            });

            console.log('Final row object:', rowObj);
            // Attach rowNumber for error tracking
            (rowObj as any).__rowNumber = i + 1; // +1 because we're 0-indexed but want 1-based row numbers
            rows.push(rowObj);
          }
        } catch (parseError) {
          await this.taskModel.updateOne(
            { taskId },
            {
              status: 'FAILED',
              $push: {
                statusLog: {
                  status: 'FAILED',
                  message: `Failed to parse CSV file. Error: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
                  timestamp: new Date(),
                },
              },
            }
          );
          await this.sendImportNotification(taskId, 'FAILED');
          return;
        }
      } else {
        // Parse Excel file
        const workbook = new ExcelJS.Workbook();
        try {
          await workbook.xlsx.load(buffer);
        } catch (parseError) {
          await this.taskModel.updateOne(
            { taskId },
            {
              status: 'FAILED',
              $push: {
                statusLog: {
                  status: 'FAILED',
                  message: `Failed to parse Excel file. Please ensure the file is a valid Excel (.xlsx or .xls) format. Error: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
                  timestamp: new Date(),
                },
              },
            }
          );
          await this.sendImportNotification(taskId, 'FAILED');
          return;
        }

        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
          await this.taskModel.updateOne(
            { taskId },
            {
              status: 'FAILED',
              $push: {
                statusLog: {
                  status: 'FAILED',
                  message: 'No worksheet found in the uploaded Excel file.',
                  timestamp: new Date(),
                },
              },
            }
          );
          await this.sendImportNotification(taskId, 'FAILED');
          return;
        }

        const excelHeaderRow = worksheet.getRow(1).values;
        headerRow = Array.isArray(excelHeaderRow) ? excelHeaderRow : [];
        console.log('Excel Headers found:', headerRow);
        console.log('Mapping configuration:', mapping);

        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
          if (rowNumber === 1) return; // skip header
          const rowObj = {};
          console.log(`Excel Row ${rowNumber} values:`, row.values);

          // Apply explicit mapping first
          Object.entries(mapping || {}).forEach(([collectionField, xlsField]) => {
            const headerValues = Array.isArray(headerRow) ? headerRow : [];
            const colIndex = typeof xlsField === 'string' ? headerValues.indexOf(xlsField) : -1;
            console.log(`Mapping ${collectionField} -> ${xlsField}, colIndex: ${colIndex}`);
            if (colIndex !== -1) {
              const cellValue = row.getCell(colIndex).value;
              rowObj[collectionField] = cellValue;
              console.log(`Set ${collectionField} = ${cellValue}`);
            } else {
              console.log(`Column ${xlsField} not found in headers`);
            }
          });

          // Add unmapped columns directly (fallback for missing mappings)
          const headerValues = Array.isArray(headerRow) ? headerRow : [];
          const mappedXlsFields = Object.values(mapping || {});

          headerValues.forEach((header, index) => {
            if (header && typeof header === 'string' && !mappedXlsFields.includes(header)) {
              const cellValue = row.getCell(index).value;
              if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                rowObj[header] = cellValue;
                console.log(`Added unmapped field: ${header} = ${cellValue}`);
              }
            }
          });

          console.log('Final Excel row object:', rowObj);
          // Attach rowNumber for error tracking
          (rowObj as any).__rowNumber = rowNumber;
          rows.push(rowObj);
        });
      }

      // --- CPTS HIERARCHICAL IMPORT LOGIC ---
      if (importTask.collectionName === 'cpts') {



        // Simplified affected tracker for clean audit logging
        const affected: Record<string, {
          createdCount: number,
          updatedCount: number,
          unchangedCount: number,
          changes: Record<string, { before: any, after: any, action: string, timestamp: Date, rowNumber: number }>
        }> = {};

        // Track actual Excel row counts
        let excelRowsProcessed = 0;
        let excelRowsCreated = 0;
        let excelRowsUpdated = 0;
        let excelRowsUnchanged = 0;

        function trackAffected(collection: string, id: any, action: 'created' | 'updated' | 'unchanged' = 'created', beforeData?: any, afterData?: any, auditInfo?: { action: string, timestamp: Date, rowNumber: number }) {
          if (!id) return;

          if (!affected[collection]) {
            affected[collection] = {
              createdCount: 0,
              updatedCount: 0,
              unchangedCount: 0,
              changes: {}
            };
          }

          if (action === 'created') {
            affected[collection].createdCount++;
            affected[collection].changes[id.toString()] = {
              before: null,
              after: afterData,
              action: auditInfo?.action || 'CREATED',
              timestamp: auditInfo?.timestamp || new Date(),
              rowNumber: auditInfo?.rowNumber || 0
            };
          }

          if (action === 'updated') {
            affected[collection].updatedCount++;
            affected[collection].changes[id.toString()] = {
              before: beforeData,
              after: afterData,
              action: auditInfo?.action || 'UPDATED',
              timestamp: auditInfo?.timestamp || new Date(),
              rowNumber: auditInfo?.rowNumber || 0
            };
          }

          if (action === 'unchanged') {
            affected[collection].unchangedCount++;
          }
        }

        // Get all collections
        const specialtiesCollection = await this.mongoConnectionService.getCollectionByOrgId('specialties', importTask.orgId || 'asp');
        const icdsCollection = await this.mongoConnectionService.getCollectionByOrgId('icds', importTask.orgId || 'asp');
        const diagnosesCollection = await this.mongoConnectionService.getCollectionByOrgId('diagnoses', importTask.orgId || 'asp');
        const cptCodesCollection = await this.mongoConnectionService.getCollectionByOrgId('cpt_codes', importTask.orgId || 'asp');
        const cptsCollection = await this.mongoConnectionService.getCollectionByOrgId('cpts', importTask.orgId || 'asp');

        // Create complete backup of entire collections BEFORE making any changes
        const backupUrls: Record<string, { url: string; format: string; recordCount: number; timestamp: Date; status: string }> = {};

        // Determine collections to backup based on import type
        let collectionsToBackup: any[] = [];

        if (importTask.collectionName === 'cpts') {
          // CPT import - backup 5 collections
          collectionsToBackup = [
            { key: 'specialty', collection: specialtiesCollection, name: 'specialties', isHierarchical: false },
            { key: 'icd', collection: icdsCollection, name: 'icds', isHierarchical: false },
            { key: 'diagnosis', collection: diagnosesCollection, name: 'diagnoses', isHierarchical: true, parentKey: 'icd' },
            { key: 'cpt_code', collection: cptCodesCollection, name: 'cpt_codes', isHierarchical: true, parentKey: 'specialtyId' },
            { key: 'cpts', collection: cptsCollection, name: 'cpts', isHierarchical: true, parentKeys: ['specialtyId', 'diagnosisId', 'cptCodeId', 'icdId'] }
          ];
        } else if (importTask.collectionName === 'action_status_code_maps' || importTask.collectionName === 'action-status-code-maps') {
          // Action Status Code Maps import - backup 3 collections
          const actionCodesCollection = await this.mongoConnectionService.getCollectionByOrgId('action_codes', importTask.orgId || 'asp');
          const statusCodesCollection = await this.mongoConnectionService.getCollectionByOrgId('status_codes', importTask.orgId || 'asp');
          const actionStatusMapsCollection = await this.mongoConnectionService.getCollectionByOrgId('action_status_code_maps', importTask.orgId || 'asp');

          collectionsToBackup = [
            { key: 'action_codes', collection: actionCodesCollection, name: 'action_codes', isHierarchical: false },
            { key: 'status_codes', collection: statusCodesCollection, name: 'status_codes', isHierarchical: false },
            { key: 'action_status_code_maps', collection: actionStatusMapsCollection, name: 'action_status_code_maps', isHierarchical: true, parentKeys: ['actionCodeId', 'statusCodeId'] }
          ];
        } else {
          // Single collection import (tickets, exceptions, payer, etc.)
          const singleCollection = await this.mongoConnectionService.getCollectionByOrgId(importTask.collectionName, importTask.orgId || 'asp');
          collectionsToBackup = [
            { key: importTask.collectionName, collection: singleCollection, name: importTask.collectionName, isHierarchical: false }
          ];
        }

        console.log(`🔄 Creating complete JSON backup of all collections before import...`);

        for (const { key, collection, name, isHierarchical, parentKey, parentKeys } of collectionsToBackup) {
          try {
            // Get entire collection data
            const allDocuments = await collection.find({}).toArray();

            if (allDocuments.length > 0) {
              // Clean documents by removing version keys and converting ObjectIds to strings
              const cleanedDocuments = allDocuments.map(doc => {
                const cleanDoc: any = { ...doc };
                delete cleanDoc.__v; // Remove version key

                // Convert ObjectId to string for JSON serialization
                if (cleanDoc._id) {
                  cleanDoc._id = cleanDoc._id.toString();
                }

                // Convert other ObjectId fields to strings
                Object.keys(cleanDoc).forEach(field => {
                  if (cleanDoc[field] && typeof cleanDoc[field] === 'object' && cleanDoc[field].constructor.name === 'ObjectId') {
                    cleanDoc[field] = cleanDoc[field].toString();
                  }
                });

                return cleanDoc;
              });

              // Create backup metadata
              const backupMetadata = {
                taskId,
                collectionName: name,
                backupTimestamp: new Date(),
                recordCount: allDocuments.length,
                isHierarchical,
                parentKey: parentKey || null,
                parentKeys: parentKeys || null,
                backupType: 'complete_collection'
              };

              // Create complete backup object
              const backupData = {
                metadata: backupMetadata,
                data: cleanedDocuments
              };

              // Store as JSON format
              const jsonBuffer = Buffer.from(JSON.stringify(backupData, null, 2), 'utf-8');

              // Upload complete collection backup as JSON
              const backupPath = `backup/${taskId}/complete_${key}_backup.json`;
              const url = await this.gcsService.uploadBuffer(jsonBuffer, backupPath, 'application/json');

              backupUrls[key] = {
                url,
                format: 'json',
                recordCount: allDocuments.length,
                timestamp: new Date(),
                status: 'success'
              };

              console.log(`✅ Complete ${name} collection backed up as JSON: ${allDocuments.length} records`);
            } else {
              backupUrls[key] = {
                url: '',
                format: 'json',
                recordCount: 0,
                timestamp: new Date(),
                status: 'no_data'
              };
              console.log(`ℹ️ No data found in ${name} collection`);
            }
          } catch (error) {
            console.error(`❌ Failed to backup ${name} collection:`, error);
            backupUrls[key] = {
              url: '',
              format: 'json',
              recordCount: 0,
              timestamp: new Date(),
              status: `error: ${error.message}`
            };
          }
        }

        console.log(`✅ All collection JSON backups completed`);

        for (const [rowIndex, row] of rows.entries()) {
          try {
            console.log(`Processing row ${rowIndex + 1}:`, row);
            excelRowsProcessed++;



            const requiredFields = ['speciality', 'iCD', 'diagnosisCode', 'cPTCode'];
            const missingFields = requiredFields.filter(field => {
              const value = row[field];
              return !value || value.toString().trim() === '';
            });

            if (missingFields.length > 0) {
              failedRows.push({
                row: JSON.stringify(row),
                error: `Missing required fields: ${missingFields.join(', ')}`,
                rowNumber: rowIndex + 2
              });
              failedCount++;

              if (importTask.isStopOnError) {
                console.log(`🛑 [STOP ON ERROR] CPT required field validation failed for row ${rowIndex + 2}`);
                const parts: string[] = [];
                if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                const finalMessage = parts.join(', ');

                await this.taskModel.updateOne(
                  { taskId },
                  {
                    status: 'FAILED',
                    insertedCount,
                    updatedCount,
                    failedCount,
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: Missing required fields: ${missingFields.join(', ')}`,
                      timestamp: new Date(),
                      details: finalMessage
                    }
                  }
                );

                throw new Error(`Import stopped on first error: Missing required fields: ${missingFields.join(', ')}`);
              }
              continue;
            }


            // 1. Specialty
            let specialty = await specialtiesCollection.findOne({ name: row.speciality });

            // Get specialty-specific template fields
            const specialtyTemplateFields = await this.getTemplateFieldsByKey('specialty-code', importTask.orgId || 'asp');

            // Structure specialty values dynamically based on specialty-specific template
            const specialtyRowData = {
              name: row.speciality,
              description: row.specialty_description || "",
              ...row // Include all row data for template processing
            };
            const structuredSpecialtyValues = this.convertToValuesStructure(
              specialtyRowData,
              specialtyTemplateFields.length > 0 ? specialtyTemplateFields : configurationFields
            );

            const specialtyData = {
              name: row.speciality,
              values: JSON.stringify(structuredSpecialtyValues),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            if (!specialty) {







              const specialtyInsert = await specialtiesCollection.insertOne(specialtyData);
              specialty = { _id: specialtyInsert.insertedId, ...specialtyData };
              trackAffected('specialties', specialty._id, 'created', null, specialtyData, {
                action: 'CREATED',
                timestamp: new Date(),
                rowNumber: rowIndex + 2
              });
              excelRowsCreated++;
            } else {
              const beforeData = { ...specialty };
              if (JSON.stringify(specialty.values) !== JSON.stringify(specialtyData.values)) {
                const updatedData = {
                  ...specialtyData,
                  updatedAt: new Date()
                };
                await specialtiesCollection.updateOne(
                  { _id: specialty._id },
                  { $set: updatedData }
                );
                trackAffected('specialties', specialty._id, 'updated', beforeData, updatedData, {
                  action: 'UPDATED',
                  timestamp: new Date(),
                  rowNumber: rowIndex + 2
                });
                excelRowsUpdated++;
              } else {
                trackAffected('specialties', specialty._id, 'unchanged');
                excelRowsUnchanged++;
              }
            }



            // 2. ICD - Unique by code
            let icd = await icdsCollection.findOne({ code: row.iCD });

            // Get ICD-specific template fields
            const icdTemplateFields = await this.getTemplateFieldsByKey('icd-code', importTask.orgId || 'asp');

            // Structure ICD values dynamically based on ICD-specific template
            const icdRowData = {
              code: row.iCD,
              description: row.icd_description || row.iCD,
              ...row // Include all row data for template processing
            };
            const structuredIcdValues = this.convertToValuesStructure(
              icdRowData,
              icdTemplateFields.length > 0 ? icdTemplateFields : configurationFields
            );

            const icdData = {
              code: row.iCD,
              values: JSON.stringify(structuredIcdValues),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            if (!icd) {
              const icdInsert = await icdsCollection.insertOne(icdData);
              icd = { _id: icdInsert.insertedId, ...icdData };
              trackAffected('icds', icd._id, 'created', null, icdData, {
                action: 'CREATED',
                timestamp: new Date(),
                rowNumber: rowIndex + 2
              });
            } else {
              const beforeData = { ...icd };
              const needsUpdate = JSON.stringify(icd.values) !== JSON.stringify(icdData.values) ||
                icd.isActive !== icdData.isActive;

              if (needsUpdate) {
                const updatedData = {
                  ...icdData,
                  updatedAt: new Date()
                };
                await icdsCollection.updateOne(
                  { _id: icd._id },
                  { $set: updatedData }
                );
                trackAffected('icds', icd._id, 'updated', beforeData, updatedData, {
                  action: 'UPDATED',
                  timestamp: new Date(),
                  rowNumber: rowIndex + 2
                });
              } else {
                trackAffected('icds', icd._id, 'unchanged');
              }
            }



            // 3. Diagnosis - Unique by name + icd combination
            let diagnosis = await diagnosesCollection.findOne({ name: row.diagnosisCode, icd: icd?._id });

            // Get diagnosis-specific template fields
            const diagnosisTemplateFields = await this.getTemplateFieldsByKey('diagnosis-code', importTask.orgId || 'asp');

            // Structure diagnosis values dynamically based on diagnosis-specific template
            const diagnosisRowData = {
              name: row.diagnosisCode,
              description: row.diagnosis_description || "",
              icd: icd?._id.toString(),
              ...row // Include all row data for template processing
            };
            const structuredDiagnosisValues = this.convertToValuesStructure(
              diagnosisRowData,
              diagnosisTemplateFields.length > 0 ? diagnosisTemplateFields : configurationFields
            );

            const diagnosisData = {
              name: row.diagnosisCode,
              icd: icd?._id.toString(),
              values: JSON.stringify(structuredDiagnosisValues),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            if (!diagnosis) {

              const existingDiagnosis = await diagnosesCollection.findOne({ name: row.diagnosisCode });
              if (existingDiagnosis && existingDiagnosis.icd.toString() !== icd?._id.toString()) {
                failedRows.push({
                  row: JSON.stringify(row),
                  error: `Diagnosis "${row.diagnosisCode}" already exists with a different ICD code`,
                  rowNumber: rowIndex + 2
                });
                failedCount++;

                if (importTask.isStopOnError) {
                  console.log(`🛑 [STOP ON ERROR] CPT diagnosis validation failed for row ${rowIndex + 2}`);
                  const parts: string[] = [];
                  if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                  if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                  parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                  const finalMessage = parts.join(', ');

                  await this.taskModel.updateOne(
                    { taskId },
                    {
                      status: 'FAILED',
                      insertedCount,
                      updatedCount,
                      failedCount,
                      statusLog: {
                        status: 'FAILED',
                        message: `Import stopped on first error: Diagnosis "${row.diagnosisCode}" already exists with a different ICD code`,
                        timestamp: new Date(),
                        details: finalMessage
                      }
                    }
                  );

                  throw new Error(`Import stopped on first error: Diagnosis "${row.diagnosisCode}" already exists with a different ICD code`);
                }
                continue;
              }

              const diagnosisInsert = await diagnosesCollection.insertOne(diagnosisData);
              diagnosis = { _id: diagnosisInsert.insertedId, ...diagnosisData };
              trackAffected('diagnoses', diagnosis._id, 'created', null, diagnosisData, {
                action: 'CREATED',
                timestamp: new Date(),
                rowNumber: rowIndex + 2
              });
            } else {
              const beforeData = { ...diagnosis };
              if (JSON.stringify(diagnosis.values) !== JSON.stringify(diagnosisData.values)) {
                const updatedData = {
                  ...diagnosisData,
                  updatedAt: new Date()
                };
                await diagnosesCollection.updateOne(
                  { _id: diagnosis._id },
                  { $set: updatedData }
                );
                trackAffected('diagnoses', diagnosis._id, 'updated', beforeData, updatedData, {
                  action: 'UPDATED',
                  timestamp: new Date(),
                  rowNumber: rowIndex + 2
                });
              } else {
                trackAffected('diagnoses', diagnosis._id, 'unchanged');
              }
            }



            // 4. CPT Code - Unique by code + specialtyId combination
            let cptCode = await cptCodesCollection.findOne({ code: row.cPTCode, specialtyId: specialty?._id });

            // Get CPT code-specific template fields
            const cptCodeTemplateFields = await this.getTemplateFieldsByKey('cpt-code', importTask.orgId || 'asp');

            // Structure CPT code values dynamically based on CPT code-specific template
            const cptCodeRowData = {
              code: row.cPTCode,
              description: row.cpt_description || "",
              specialtyId: specialty?._id.toString(),
              ...row // Include all row data for template processing
            };
            const structuredCptCodeValues = this.convertToValuesStructure(
              cptCodeRowData,
              cptCodeTemplateFields.length > 0 ? cptCodeTemplateFields : configurationFields
            );

            const cptCodeData = {
              code: row.cPTCode,
              specialtyId: specialty?._id.toString(),
              values: JSON.stringify(structuredCptCodeValues),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            if (!cptCode) {

              const existingCptCode = await cptCodesCollection.findOne({ code: row.cPTCode });
              if (existingCptCode && existingCptCode.specialtyId.toString() !== specialty?._id.toString()) {
                failedRows.push({
                  row: JSON.stringify(row),
                  error: `CPT Code "${row.cPTCode}" already exists with a different Specialty`,
                  rowNumber: rowIndex + 2
                });
                failedCount++;
                continue;
              }

              const cptCodeInsert = await cptCodesCollection.insertOne(cptCodeData);
              cptCode = { _id: cptCodeInsert.insertedId, ...cptCodeData };
              trackAffected('cpt_codes', cptCode._id, 'created', null, cptCodeData, {
                action: 'CREATED',
                timestamp: new Date(),
                rowNumber: rowIndex + 2
              });
            } else {
              const beforeData = { ...cptCode };
              if (JSON.stringify(cptCode.values) !== JSON.stringify(cptCodeData.values)) {
                const updatedData = {
                  ...cptCodeData,
                  updatedAt: new Date()
                };
                await cptCodesCollection.updateOne(
                  { _id: cptCode._id },
                  { $set: updatedData }
                );
                trackAffected('cpt_codes', cptCode._id, 'updated', beforeData, updatedData, {
                  action: 'UPDATED',
                  timestamp: new Date(),
                  rowNumber: rowIndex + 2
                });
              } else {
                trackAffected('cpt_codes', cptCode._id, 'unchanged');
              }
            }



            if (!specialty?._id || !icd?._id || !diagnosis?._id || !cptCode?._id) {
              failedRows.push({
                row: JSON.stringify(row),
                error: 'One or more related IDs could not be resolved for this row',
                rowNumber: rowIndex + 2
              });
              failedCount++;
              continue;
            }



            // 5. Main cpts table
            // Structure values dynamically based on template configuration
            const structuredRowData = this.convertToValuesStructure(row, configurationFields);
            const structuredValues = JSON.stringify(structuredRowData);

            const cptData = {
              specialtyId: specialty._id.toString(),
              diagnosisId: diagnosis._id.toString(),
              cptCodeId: cptCode._id.toString(),
              icdId: icd._id.toString(),
              type: row.type || 'MAIN_CLIENT',
              iCD: row.iCD,
              diagnosisCode: row.diagnosisCode,
              speciality: row.speciality,
              cPTCode: row.cPTCode,
              values: structuredValues,
              templateId: importTask.configurationId?.toString(),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };

            const cptFilter = {
              specialtyId: specialty._id.toString(),
              diagnosisId: diagnosis._id.toString(),
              cptCodeId: cptCode._id.toString(),
              icdId: icd._id.toString()
            };

            const exists = await cptsCollection.findOne(cptFilter);

            // Handle different import modes
            switch (importTask.importMode) {
              case ImportType.CREATE:
                // CREATE mode - fail if exists
                if (exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'CPT already exists for this combination',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] CPT CREATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: CPT already exists for this combination`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: CPT already exists for this combination`);
                  }
                  continue;
                }

                // Insert new CPT
                const cptInsertResult = await cptsCollection.insertOne(cptData);
                if (cptInsertResult.insertedId) {
                  insertedCount++;
                  insertedIds.push(cptInsertResult.insertedId.toString());
                  trackAffected('cpts', cptInsertResult.insertedId, 'created', null, cptData, {
                    action: 'CREATED',
                    timestamp: new Date(),
                    rowNumber: rowIndex + 2
                  });
                }
                break;

              case ImportType.UPDATE:
                // UPDATE mode - fail if doesn't exist
                if (!exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'CPT not found for update',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] CPT UPDATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: CPT not found for update`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: CPT not found for update`);
                  }
                  continue;
                }

                // Update existing CPT
                const beforeData = { ...exists };
                const updatedData: any = { ...cptData, updatedBy: importTask.createdBy };
                delete updatedData.createdAt; // Don't update createdAt

                const updateResult = await cptsCollection.updateOne(
                  cptFilter,
                  { $set: updatedData }
                );
                if (updateResult.modifiedCount > 0) {
                  updatedCount++;
                  updatedIds.push(exists._id.toString());
                  trackAffected('cpts', exists._id, 'updated', beforeData, updatedData, {
                    action: 'UPDATED',
                    timestamp: new Date(),
                    rowNumber: rowIndex + 2
                  });
                }
                break;

              case ImportType.UPSERT:
              default:
                // UPSERT mode - insert if new, update if exists (original behavior)
                if (exists) {
                  const beforeData = { ...exists };
                  if (JSON.stringify(exists.values) !== JSON.stringify(cptData.values)) {
                    const updatedData: any = { ...cptData, updatedBy: importTask.createdBy };
                    delete updatedData.createdAt; // Don't update createdAt

                    await cptsCollection.updateOne(
                      { _id: exists._id },
                      { $set: updatedData }
                    );
                    trackAffected('cpts', exists._id, 'updated', beforeData, updatedData, {
                      action: 'UPDATED',
                      timestamp: new Date(),
                      rowNumber: rowIndex + 2
                    });
                    updatedCount++;
                    updatedIds.push(exists._id.toString());
                  } else {
                    trackAffected('cpts', exists._id, 'unchanged');
                  }
                } else {
                  const cptInsertResult = await cptsCollection.insertOne(cptData);
                  trackAffected('cpts', cptInsertResult.insertedId, 'created', null, cptData, {
                    action: 'CREATED',
                    timestamp: new Date(),
                    rowNumber: rowIndex + 2
                  });
                  insertedCount++;
                  insertedIds.push(cptInsertResult.insertedId.toString());
                }
                break;
            }
          } catch (err: any) {
            failedRows.push({
              row: JSON.stringify(row),
              error: err.message,
              rowNumber: rowIndex + 2
            });
            failedCount++;
          }
        }


        // Convert affected data to clean result format
        const affectedResult = Object.fromEntries(
          Object.entries(affected).map(([k, v]) => [k, {
            createdCount: v.createdCount,
            updatedCount: v.updatedCount,
            unchangedCount: v.unchangedCount,
            changes: v.changes
          }])
        );



        // Store comprehensive audit data as JSON in audit buckets
        let auditLogUrl = '';
        if (excelRowsProcessed > 0) {
          // Get user email for audit log
          let createdByEmail = '';
          try {
            const userCollection = await this.mongoConnectionService.getCollectionByOrgId('users', importTask.orgId || 'asp');
            const user = await userCollection.findOne({ _id: new Types.ObjectId(importTask.createdBy) });
            createdByEmail = user?.email || '';
          } catch (error) {
            console.warn('Could not fetch user email for audit log:', error);
          }

          // Create comprehensive audit data
          const auditData = {
            taskId,
            timestamp: new Date(),
            orgId: importTask.orgId,
            createdBy: importTask.createdBy,
            createdByEmail,
            collectionName: importTask.collectionName,
            summary: {
              totalCreated: excelRowsCreated,
              totalUpdated: excelRowsUpdated,
              totalUnchanged: excelRowsUnchanged,
              totalFailed: failedCount,
              totalProcessed: excelRowsProcessed
            },
            affectedCollections: affectedResult,
            backupUrls,
            failedRows: failedRows.length > 0 ? failedRows : null
          }

          // Upload audit JSON to audit bucket
          const auditJsonBuffer = Buffer.from(JSON.stringify(auditData, null, 2), 'utf-8');
          const auditPath = `import/audit/${taskId}/audit_log.json`;
          auditLogUrl = await this.gcsService.uploadBuffer(auditJsonBuffer, auditPath, 'application/json');
          console.log(`✅ Comprehensive audit log stored at: ${auditLogUrl}`);
        }

        // Generate comprehensive import summary using Excel row counts
        const summaryMessage = [
          `CPT Dictionary import completed:`,
          `${excelRowsCreated} records created`,
          `${excelRowsUpdated} records updated`,
          `${excelRowsUnchanged} records unchanged`,
          failedCount > 0 ? `${failedCount} records failed` : ''
        ].filter(Boolean).join(', ');

        // Add backup URLs to affected result
        const finalAffectedResult = {
          ...affectedResult,
          backupUrls
        };

        // Determine final status first
        let finalStatus = 'COMPLETED';
        if (failedCount > 0) {
          finalStatus = (insertedCount > 0 || updatedCount > 0) ? 'PARTIAL_COMPLETED' : 'FAILED';
        }

        await this.taskModel.updateOne(
          { taskId },
          {
            status: finalStatus,
            insertedCount: excelRowsCreated,
            updatedCount: excelRowsUpdated,
            unchangedCount: excelRowsUnchanged,
            failedCount,
            failedRows,
            auditLogUrl: auditLogUrl || null,
            backupUrls: backupUrls, // Structured backup URLs for each collection
            $push: {
              statusLog: {
                status: finalStatus,
                message: summaryMessage,
                timestamp: new Date(),
              },
            },
          }
        );

        // Log audit event for CPTS import completion
        await this.logImportAuditEvent(
          taskId,
          'UPDATE',
          { status: 'IN_PROGRESS' },
          {
            status: finalStatus,
            excelRowsProcessed,
            excelRowsCreated,
            excelRowsUpdated,
            excelRowsUnchanged,
            failedCount,
            affectedCollections: Object.keys(affectedResult)
          },
          importTask.createdBy || 'system',
          importTask.createdByEmail || ''
        );

        await this.sendImportNotification(taskId, finalStatus);
        return;
      }

      // --- ACTION STATUS CODE MAPS HIERARCHICAL IMPORT LOGIC ---
      if (importTask.collectionName === 'action_status_code_maps' || importTask.collectionName === 'action-status-code-maps') {
        // Dynamic affected tracker
        const affected: Record<string, Set<string>> = {};
        function trackAffected(collection: string, id: any) {
          if (!id) return;
          if (!affected[collection]) affected[collection] = new Set();
          affected[collection].add(id.toString());
        }

        // Track total records from file
        let totalRecords = rows.length;
        console.log(`📊 Total records to process: ${totalRecords}`);

        // Get all collections
        const processesCollection = await this.mongoConnectionService.getCollectionByOrgId('processes', importTask.orgId || 'asp');
        const statusCodesCollection = await this.mongoConnectionService.getCollectionByOrgId('status_codes', importTask.orgId || 'asp');
        const actionCodesCollection = await this.mongoConnectionService.getCollectionByOrgId('action_codes', importTask.orgId || 'asp');
        const actionStatusCodeMapsCollection = await this.mongoConnectionService.getCollectionByOrgId('action_status_code_maps', importTask.orgId || 'asp');

        console.log('📋 Action Status Code Maps - Collections initialized successfully');
        console.log('📋 OrgId:', importTask.orgId || 'asp');

        for (const [rowIndex, row] of rows.entries()) {
          try {
            console.log(`Processing action-status-code-maps row ${rowIndex + 1}:`, row);

            // Validate required fields are present in the row
            if (!row.process || !row.statusCode || !row.actionCode) {
              failedRows.push({
                row: JSON.stringify(row),
                error: `Missing required fields: process, statusCode, and actionCode are required`,
                rowNumber: rowIndex + 2
              });
              failedCount++;

              if (importTask.isStopOnError) {
                console.log(`🛑 [STOP ON ERROR] Action Status Code Maps required field validation failed for row ${rowIndex + 2}`);
                const parts: string[] = [];
                if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                const finalMessage = parts.join(', ');

                await this.taskModel.updateOne(
                  { taskId },
                  {
                    status: 'FAILED',
                    insertedCount,
                    updatedCount,
                    failedCount,
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: Missing required fields: process, statusCode, and actionCode are required`,
                      timestamp: new Date(),
                      details: finalMessage
                    }
                  }
                );

                throw new Error(`Import stopped on first error: Missing required fields: process, statusCode, and actionCode are required`);
              }
              continue;
            }

            // 1. Process validation - Must exist in collection (don't create new ones)
            console.log(`🔍 Looking for existing process: "${row.process}"`);
            let process = await processesCollection.findOne({ name: row.process });
            if (!process) {
              console.log(`❌ Process "${row.process}" not found in collection`);
              failedRows.push({
                row: JSON.stringify(row),
                error: `Invalid process: "${row.process}" does not exist in the system. Please ensure the process exists before importing action status code maps.`,
                rowNumber: rowIndex + 2
              });
              failedCount++;

              if (importTask.isStopOnError) {
                console.log(`🛑 [STOP ON ERROR] Process validation failed for row ${rowIndex + 2}`);
                const parts: string[] = [];
                if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                const finalMessage = parts.join(', ');

                await this.taskModel.updateOne(
                  { taskId },
                  {
                    status: 'FAILED',
                    insertedCount,
                    updatedCount,
                    failedCount,
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: Invalid process: "${row.process}" does not exist in the system`,
                      timestamp: new Date(),
                      details: finalMessage
                    }
                  }
                );

                throw new Error(`Import stopped on first error: Invalid process: "${row.process}" does not exist in the system`);
              }
              continue;
            }
            console.log(`✅ Process found with ID: ${process._id}`);
            trackAffected('processes', process._id);

            // 2. Status Code (unique by code + processId) - Use direct insertOne
            let statusCode = await statusCodesCollection.findOne({ code: row.statusCode, processId: process._id.toString() });
            if (!statusCode) {
              // Check if status code exists with different process
              const existingStatusCode = await statusCodesCollection.findOne({ code: row.statusCode });
              if (existingStatusCode && existingStatusCode.processId.toString() !== process._id.toString()) {
                failedRows.push({
                  row: JSON.stringify(row),
                  error: `Status Code "${row.statusCode}" already exists with a different Process`,
                  rowNumber: rowIndex + 2
                });
                failedCount++;
                continue;
              }

              // Get status code-specific template fields
              const statusCodeTemplateFields = await this.getTemplateFieldsByKey('status-code', importTask.orgId || 'asp');

              // Structure status code values dynamically based on status code-specific template
              const statusCodeRowData = {
                code: row.statusCode,
                description: row.statusCode_description || row.statusCode,
                processId: process._id.toString(),
                ...row // Include all row data for template processing
              };
              const structuredStatusCodeValues = this.convertToValuesStructure(
                statusCodeRowData,
                statusCodeTemplateFields.length > 0 ? statusCodeTemplateFields : configurationFields
              );

              const statusCodeInsert = await statusCodesCollection.insertOne({
                code: row.statusCode,
                processId: process._id.toString(),
                values: JSON.stringify(structuredStatusCodeValues),
                isActive: true,
                createdBy: importTask.createdBy,
                createdAt: new Date(),
                updatedAt: new Date()
              });
              statusCode = { _id: statusCodeInsert.insertedId, code: row.statusCode, processId: process._id.toString() };
            }
            trackAffected('status_codes', statusCode?._id);

            // 3. Action Code (unique by code + statusCodeId) - Use direct insertOne
            let actionCode = await actionCodesCollection.findOne({ code: row.actionCode, statusCodeId: statusCode?._id });
            if (!actionCode) {
              // Check if action code exists with different status code
              const existingActionCode = await actionCodesCollection.findOne({ code: row.actionCode });
              if (existingActionCode && existingActionCode.statusCodeId.toString() !== statusCode?._id.toString()) {
                failedRows.push({
                  row: JSON.stringify(row),
                  error: `Action Code "${row.actionCode}" already exists with a different Status Code`,
                  rowNumber: rowIndex + 2
                });
                failedCount++;
                continue;
              }

              // Get action code-specific template fields
              const actionCodeTemplateFields = await this.getTemplateFieldsByKey('action-code', importTask.orgId || 'asp');

              // Structure action code values dynamically based on action code-specific template
              const actionCodeRowData = {
                code: row.actionCode,
                description: row.actionCode_description || row.actionCode,
                statusCodeId: statusCode?._id.toString(),
                ...row // Include all row data for template processing
              };
              const structuredActionCodeValues = this.convertToValuesStructure(
                actionCodeRowData,
                actionCodeTemplateFields.length > 0 ? actionCodeTemplateFields : configurationFields
              );

              const actionCodeInsert = await actionCodesCollection.insertOne({
                code: row.actionCode,
                statusCodeId: statusCode?._id.toString(),
                values: JSON.stringify(structuredActionCodeValues),
                isActive: true,
                createdBy: importTask.createdBy,
                createdAt: new Date(),
                updatedAt: new Date()
              });
              actionCode = { _id: actionCodeInsert.insertedId, code: row.actionCode, statusCodeId: statusCode?._id };
            }
            trackAffected('action_codes', actionCode?._id);

            // Ensure all IDs are present
            if (!process?._id || !statusCode?._id || !actionCode?._id) {
              failedRows.push({
                row: JSON.stringify(row),
                error: 'One or more related IDs could not be resolved for this row',
                rowNumber: rowIndex + 2
              });
              failedCount++;
              continue;
            }

            // 4. Main table: action_status_code_maps - handle different import modes
            const actionStatusFilter = {
              processId: process._id.toString(),
              statusCodeId: statusCode._id.toString(),
              actionCodeId: actionCode._id.toString()
            };

            const exists = await actionStatusCodeMapsCollection.findOne(actionStatusFilter);

            // Structure values dynamically based on template configuration
            const structuredRowDataAction = this.convertToValuesStructure(row, configurationFields);
            const structuredValues = JSON.stringify(structuredRowDataAction);

            // Prepare action status code map data
            const actionStatusData: any = {
              processId: process._id.toString(),
              statusCodeId: statusCode._id.toString(),
              actionCodeId: actionCode._id.toString(),
              process: row.process,
              actionCode: row.actionCode,
              statusCode: row.statusCode,
              values: structuredValues,
              templateId: importTask.configurationId?.toString(),
              isActive: true,
              createdBy: importTask.createdBy,
              createdAt: new Date(),
              updatedAt: new Date()
            };

            // Handle different import modes
            switch (importTask.importMode) {
              case ImportType.CREATE:
                // CREATE mode - fail if exists
                if (exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'Action Status Code Map already exists for this combination',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] Action Status Code Map CREATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: Action Status Code Map already exists for this combination`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: Action Status Code Map already exists for this combination`);
                  }
                  continue;
                }

                // Insert new action status code map
                const insertResult = await actionStatusCodeMapsCollection.insertOne(actionStatusData);
                if (insertResult.insertedId) {
                  insertedCount++;
                  insertedIds.push(insertResult.insertedId.toString());
                  trackAffected('action_status_code_maps', insertResult.insertedId);
                }
                break;

              case ImportType.UPDATE:
                // UPDATE mode - fail if doesn't exist
                if (!exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'Action Status Code Map not found for update',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] Action Status Code Map UPDATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: Action Status Code Map not found for update`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: Action Status Code Map not found for update`);
                  }
                  continue;
                }

                // Update existing action status code map
                const updateData = { ...actionStatusData, updatedBy: importTask.createdBy };
                delete updateData.createdAt; // Don't update createdAt

                const updateResult = await actionStatusCodeMapsCollection.updateOne(
                  actionStatusFilter,
                  { $set: updateData }
                );
                if (updateResult.modifiedCount > 0) {
                  updatedCount++;
                  updatedIds.push(exists._id.toString());
                  trackAffected('action_status_code_maps', exists._id);
                }
                break;

              case ImportType.UPSERT:
              default:
                // UPSERT mode - insert if new, update if exists
                if (exists) {
                  // Update existing
                  const updateData = { ...actionStatusData, updatedBy: importTask.createdBy };
                  delete updateData.createdAt; // Don't update createdAt

                  const updateResult = await actionStatusCodeMapsCollection.updateOne(
                    actionStatusFilter,
                    { $set: updateData }
                  );
                  if (updateResult.modifiedCount > 0) {
                    updatedCount++;
                    updatedIds.push(exists._id.toString());
                    trackAffected('action_status_code_maps', exists._id);
                  }
                } else {
                  // Insert new
                  const insertResult = await actionStatusCodeMapsCollection.insertOne(actionStatusData);
                  if (insertResult.insertedId) {
                    insertedCount++;
                    insertedIds.push(insertResult.insertedId.toString());
                    trackAffected('action_status_code_maps', insertResult.insertedId);
                  }
                }
                break;
            }
          } catch (err: any) {
            failedRows.push({
              row: JSON.stringify(row),
              error: err.message,
              rowNumber: rowIndex + 2
            });
            failedCount++;
          }
        }
        // Convert affected sets to arrays for output/logging
        const affectedResult = Object.fromEntries(
          Object.entries(affected).map(([k, v]) => [k, Array.from(v)])
        );
        // Generate and upload backup CSVs for each collection
        const backupUrls: Record<string, { url: string; format: string; recordCount: number; timestamp: Date; status: string }> = {};
        const backupCollections = [
          { key: 'processes', name: 'process', collection: processesCollection },
          { key: 'status_codes', name: 'status_code', collection: statusCodesCollection },
          { key: 'action_codes', name: 'action_code', collection: actionCodesCollection },
        ];
        for (const { key, name, collection } of backupCollections) {
          const ids = affectedResult[key];
          if (ids && ids.length > 0) {
            try {
              const docs = await collection.find({ _id: { $in: ids.map((id: string) => new Types.ObjectId(id)) } }).toArray();
              if (docs.length > 0) {
                // Convert docs to CSV
                const csvHeaders = Object.keys(docs[0]).join(',');
                const csvRows = docs.map(doc =>
                  Object.values(doc).map(value =>
                    typeof value === 'string' && value.includes(',') ? `"${value}"` : value
                  ).join(',')
                );
                const csvContent = [csvHeaders, ...csvRows].join('\n');
                const csvBuffer = Buffer.from(csvContent, 'utf-8');
                // Upload to GCS (masters/jobid/collectionname.csv)
                const backupPath = `masters/${taskId}/${name}.csv`;
                const url = await this.gcsService.uploadBuffer(csvBuffer, backupPath, 'text/csv');

                backupUrls[key] = {
                  url,
                  format: 'csv',
                  recordCount: docs.length,
                  timestamp: new Date(),
                  status: 'success'
                };
              } else {
                backupUrls[key] = {
                  url: '',
                  format: 'csv',
                  recordCount: 0,
                  timestamp: new Date(),
                  status: 'no_data'
                };
              }
            } catch (error) {
              console.error(`❌ Failed to backup ${name} collection:`, error);
              backupUrls[key] = {
                url: '',
                format: 'csv',
                recordCount: 0,
                timestamp: new Date(),
                status: `error: ${error.message}`
              };
            }
          } else {
            backupUrls[key] = {
              url: '',
              format: 'csv',
              recordCount: 0,
              timestamp: new Date(),
              status: 'no_affected_records'
            };
          }
        }
        // Determine final status first
        let finalStatus = 'COMPLETED';
        if (failedCount > 0) {
          finalStatus = insertedCount > 0 ? 'PARTIAL_COMPLETED' : 'FAILED';
        }

        // Generate audit log URL for action-status-code-maps import
        let auditLogUrl = '';
        if (totalRecords > 0) {
          const auditData = {
            taskId,
            timestamp: new Date(),
            orgId: importTask.orgId,
            createdBy: importTask.createdBy,
            createdByEmail: importTask.createdByEmail,
            collectionName: importTask.collectionName,
            summary: {
              totalCreated: insertedCount,
              totalUpdated: 0,
              totalUnchanged: 0,
              totalFailed: failedCount,
              totalProcessed: totalRecords
            },
            affectedCollections: affectedResult,
            backupUrls,
            failedRows: failedRows.length > 0 ? failedRows : null
          }

          // Upload audit JSON to audit bucket
          const auditJsonBuffer = Buffer.from(JSON.stringify(auditData, null, 2), 'utf-8');
          const auditPath = `import/audit/${taskId}/audit_log.json`;
          auditLogUrl = await this.gcsService.uploadBuffer(auditJsonBuffer, auditPath, 'application/json');
          console.log(`✅ Action Status Code Maps audit log stored at: ${auditLogUrl}`);
        }

        await this.taskModel.updateOne(
          { taskId },
          {
            status: finalStatus,
            insertedCount,
            failedCount,
            failedRows,
            insertedIds,
            totalRecords,
            affected: affectedResult,
            auditLogUrl: auditLogUrl || null,
            backupUrls: backupUrls, // Structured backup URLs for each collection
            $push: {
              statusLog: {
                status: finalStatus,
                message: finalStatus === 'PARTIAL_COMPLETED' ?
                  `Action Status Code Map import partially completed. Processed ${totalRecords} records: ${insertedCount} inserted, ${failedCount} failed` :
                  finalStatus === 'FAILED' ?
                    `Action Status Code Map import failed. Processed ${totalRecords} records: ${failedCount} failed` :
                    `Action Status Code Map import completed. Processed ${totalRecords} records: ${insertedCount} inserted successfully`,
                timestamp: new Date(),
              },
            },
          }
        );

        // Log audit event for Action Status Code Maps import completion
        await this.logImportAuditEvent(
          taskId,
          'UPDATE',
          { status: 'IN_PROGRESS' },
          {
            status: finalStatus,
            totalRecords,
            insertedCount,
            failedCount,
            affectedCollections: Object.keys(affectedResult)
          },
          importTask.createdBy || 'system',
          importTask.createdByEmail || ''
        );

        await this.sendImportNotification(taskId, finalStatus);
        return;
      }

      // --- EXCEPTIONS HIERARCHICAL IMPORT LOGIC ---
      if (importTask.collectionName === 'exceptions') {
        // Dynamic affected tracker
        const affected: Record<string, Set<string>> = {};
        function trackAffected(collection: string, id: any) {
          if (!id) return;
          if (!affected[collection]) affected[collection] = new Set();
          affected[collection].add(id.toString());
        }

        // Track total records from file
        let totalRecords = rows.length;
        console.log(`📊 Total records to process: ${totalRecords}`);

        // Get all collections
        const processesCollection = await this.mongoConnectionService.getCollectionByOrgId('processes', importTask.orgId || 'asp');
        const exceptionsCollection = await this.mongoConnectionService.getCollectionByOrgId('exceptions', importTask.orgId || 'asp');

        console.log('📋 Exceptions - Collections initialized successfully');
        console.log('📋 OrgId:', importTask.orgId || 'asp');

        for (const [rowIndex, row] of rows.entries()) {
          try {
            console.log(`Processing exceptions row ${rowIndex + 1}:`, row);

            // Validate required fields are present in the row
            // Check for process and exception (the main required fields)
            if (!row.process || !row.exception) {
              failedRows.push({
                row: JSON.stringify(row),
                error: `Missing required fields: process and exception are required`,
                rowNumber: rowIndex + 2
              });
              failedCount++;

              if (importTask.isStopOnError) {
                console.log(`🛑 [STOP ON ERROR] Exceptions required field validation failed for row ${rowIndex + 2}`);
                const parts: string[] = [];
                if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                const finalMessage = parts.join(', ');

                await this.taskModel.updateOne(
                  { taskId },
                  {
                    status: 'FAILED',
                    insertedCount,
                    updatedCount,
                    failedCount,
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: Missing required fields: process and exception are required`,
                      timestamp: new Date(),
                      details: finalMessage
                    }
                  }
                );

                throw new Error(`Import stopped on first error: Missing required fields: process and exception are required`);
              }
              continue;
            }

            // 1. Process validation - Must exist in collection (don't create new ones)
            console.log(`🔍 Looking for existing process: "${row.process}"`);
            const process = await processesCollection.findOne({ name: row.process });
            if (!process) {
              console.log(`❌ Process "${row.process}" not found in collection`);
              failedRows.push({
                row: JSON.stringify(row),
                error: `Invalid process: "${row.process}" does not exist in the system. Please ensure the process exists before importing exceptions.`,
                rowNumber: rowIndex + 2
              });
              failedCount++;

              if (importTask.isStopOnError) {
                console.log(`🛑 [STOP ON ERROR] Exceptions process validation failed for row ${rowIndex + 2}`);
                const parts: string[] = [];
                if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                const finalMessage = parts.join(', ');

                await this.taskModel.updateOne(
                  { taskId },
                  {
                    status: 'FAILED',
                    insertedCount,
                    updatedCount,
                    failedCount,
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: Invalid process: "${row.process}" does not exist in the system`,
                      timestamp: new Date(),
                      details: finalMessage
                    }
                  }
                );

                throw new Error(`Import stopped on first error: Invalid process: "${row.process}" does not exist in the system`);
              }
              continue;
            }
            console.log(`✅ Process found with ID: ${process._id}`);
            trackAffected('processes', process._id);

            // 2. Main exceptions table: handle different import modes
            const exceptionFilter = {
              processId: process._id.toString(),
              exception: row.exception
            };

            const exists = await exceptionsCollection.findOne(exceptionFilter);

            // Structure values dynamically based on template configuration
            const structuredRowDataException = this.convertToValuesStructure(row, configurationFields);
            const structuredValues = JSON.stringify(structuredRowDataException);

            // Prepare exception data (avoid duplicate processId)
            const exceptionData: any = {
              processId: process._id.toString(), // Use the resolved process ID
              exception: row.exception,
              documentRequired: row.documentRequired || null,
              followUpInDays: row.followUpInDays ? parseInt(row.followUpInDays) : null,
              status: row.status || 'Active',
              process: row.process, // Keep process name for reference
              values: structuredValues,
              templateId: importTask.configurationId?.toString(),
              isActive: true,
              createdBy: importTask.createdBy,
              createdAt: new Date(),
              updatedAt: new Date()
            };

            // Handle different import modes
            switch (importTask.importMode) {
              case ImportType.CREATE:
                // CREATE mode - fail if exists
                if (exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'Exception already exists for this process and exception combination',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] Exception CREATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: Exception already exists for this process and exception combination`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: Exception already exists for this process and exception combination`);
                  }
                  continue;
                }

                // Insert new exception
                const exceptionInsertResult = await exceptionsCollection.insertOne(exceptionData);
                if (exceptionInsertResult.insertedId) {
                  insertedCount++;
                  insertedIds.push(exceptionInsertResult.insertedId.toString());
                  trackAffected('exceptions', exceptionInsertResult.insertedId);
                }
                break;

              case ImportType.UPDATE:
                // UPDATE mode - fail if doesn't exist
                if (!exists) {
                  failedRows.push({
                    row: JSON.stringify(row),
                    error: 'Exception not found for update',
                    rowNumber: rowIndex + 2
                  });
                  failedCount++;

                  if (importTask.isStopOnError) {
                    console.log(`🛑 [STOP ON ERROR] Exception UPDATE mode failed for row ${rowIndex + 2}`);
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');

                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        statusLog: {
                          status: 'FAILED',
                          message: `Import stopped on first error: Exception not found for update`,
                          timestamp: new Date(),
                          details: finalMessage
                        }
                      }
                    );

                    throw new Error(`Import stopped on first error: Exception not found for update`);
                  }
                  continue;
                }

                // Update existing exception
                const updateData = { ...exceptionData, updatedBy: importTask.createdBy };
                delete updateData.createdAt; // Don't update createdAt

                const exceptionUpdateResult = await exceptionsCollection.updateOne(
                  exceptionFilter,
                  { $set: updateData }
                );
                if (exceptionUpdateResult.modifiedCount > 0) {
                  updatedCount++;
                  updatedIds.push(exists._id.toString());
                  trackAffected('exceptions', exists._id);
                }
                break;

              case ImportType.UPSERT:
              default:
                // UPSERT mode - insert if new, update if exists
                if (exists) {
                  // Update existing
                  const updateData = { ...exceptionData, updatedBy: importTask.createdBy };
                  delete updateData.createdAt; // Don't update createdAt

                  const exceptionUpdateResult = await exceptionsCollection.updateOne(
                    exceptionFilter,
                    { $set: updateData }
                  );
                  if (exceptionUpdateResult.modifiedCount > 0) {
                    updatedCount++;
                    updatedIds.push(exists._id.toString());
                    trackAffected('exceptions', exists._id);
                  }
                } else {
                  // Insert new
                  const exceptionInsertResult = await exceptionsCollection.insertOne(exceptionData);
                  if (exceptionInsertResult.insertedId) {
                    insertedCount++;
                    insertedIds.push(exceptionInsertResult.insertedId.toString());
                    trackAffected('exceptions', exceptionInsertResult.insertedId);
                  }
                }
                break;
            }
          } catch (err: any) {
            failedRows.push({
              row: JSON.stringify(row),
              error: err.message,
              rowNumber: rowIndex + 2
            });
            failedCount++;
          }
        }

        // Convert affected sets to arrays for output/logging
        const affectedResult = Object.fromEntries(
          Object.entries(affected).map(([k, v]) => [k, Array.from(v)])
        );

        // Generate and upload backup CSVs for each collection
        const backupUrls: Record<string, { url: string; format: string; recordCount: number; timestamp: Date; status: string }> = {};
        const backupCollections = [
          { key: 'processes', name: 'process', collection: processesCollection },
        ];
        for (const { key, name, collection } of backupCollections) {
          const ids = affectedResult[key];
          if (ids && ids.length > 0) {
            try {
              const docs = await collection.find({ _id: { $in: ids.map((id: string) => new Types.ObjectId(id)) } }).toArray();
              if (docs.length > 0) {
                // Convert docs to CSV
                const csvHeaders = Object.keys(docs[0]).join(',');
                const csvRows = docs.map(doc =>
                  Object.values(doc).map(value =>
                    typeof value === 'string' && value.includes(',') ? `"${value}"` : value
                  ).join(',')
                );
                const csvContent = [csvHeaders, ...csvRows].join('\n');
                const csvBuffer = Buffer.from(csvContent, 'utf-8');
                // Upload to GCS (masters/jobid/collectionname.csv)
                const backupPath = `masters/${taskId}/${name}.csv`;
                const url = await this.gcsService.uploadBuffer(csvBuffer, backupPath, 'text/csv');

                backupUrls[key] = {
                  url,
                  format: 'csv',
                  recordCount: docs.length,
                  timestamp: new Date(),
                  status: 'success'
                };
              } else {
                backupUrls[key] = {
                  url: '',
                  format: 'csv',
                  recordCount: 0,
                  timestamp: new Date(),
                  status: 'no_data'
                };
              }
            } catch (error) {
              console.error(`❌ Failed to backup ${name} collection:`, error);
              backupUrls[key] = {
                url: '',
                format: 'csv',
                recordCount: 0,
                timestamp: new Date(),
                status: `error: ${error.message}`
              };
            }
          } else {
            backupUrls[key] = {
              url: '',
              format: 'csv',
              recordCount: 0,
              timestamp: new Date(),
              status: 'no_affected_records'
            };
          }
        }

        // Determine final status first
        let finalStatus = 'COMPLETED';
        if (failedCount > 0) {
          finalStatus = insertedCount > 0 ? 'PARTIAL_COMPLETED' : 'FAILED';
        }

        // Generate audit log URL for exceptions import
        let auditLogUrl = '';
        if (totalRecords > 0) {
          const auditData = {
            taskId,
            timestamp: new Date(),
            orgId: importTask.orgId,
            createdBy: importTask.createdBy,
            createdByEmail: importTask.createdByEmail,
            collectionName: importTask.collectionName,
            summary: {
              totalCreated: insertedCount,
              totalUpdated: 0,
              totalUnchanged: 0,
              totalFailed: failedCount,
              totalProcessed: totalRecords
            },
            affectedCollections: affectedResult,
            backupUrls,
            failedRows: failedRows.length > 0 ? failedRows : null
          }

          // Upload audit JSON to audit bucket
          const auditJsonBuffer = Buffer.from(JSON.stringify(auditData, null, 2), 'utf-8');
          const auditPath = `import/audit/${taskId}/audit_log.json`;
          auditLogUrl = await this.gcsService.uploadBuffer(auditJsonBuffer, auditPath, 'application/json');
          console.log(`✅ Exceptions audit log stored at: ${auditLogUrl}`);
        }

        await this.taskModel.updateOne(
          { taskId },
          {
            status: finalStatus,
            insertedCount,
            failedCount,
            failedRows,
            insertedIds,
            totalRecords,
            affected: affectedResult,
            auditLogUrl: auditLogUrl || null,
            backupUrls: backupUrls, // Structured backup URLs for each collection
            $push: {
              statusLog: {
                status: finalStatus,
                message: finalStatus === 'PARTIAL_COMPLETED' ?
                  `Exceptions import partially completed. Processed ${totalRecords} records: ${insertedCount} inserted, ${failedCount} failed` :
                  finalStatus === 'FAILED' ?
                    `Exceptions import failed. Processed ${totalRecords} records: ${failedCount} failed` :
                    `Exceptions import completed. Processed ${totalRecords} records: ${insertedCount} inserted successfully`,
                timestamp: new Date(),
              },
            },
          }
        );

        // Log audit event for Exceptions import completion
        await this.logImportAuditEvent(
          taskId,
          'UPDATE',
          { status: 'IN_PROGRESS' },
          {
            status: finalStatus,
            totalRecords,
            insertedCount,
            failedCount,
            affectedCollections: Object.keys(affectedResult)
          },
          importTask.createdBy || 'system',
          importTask.createdByEmail || ''
        );

        await this.sendImportNotification(taskId, finalStatus);
        return;
      }

      // 4. Import logic per row based on import mode
      // Track total records from file for normal import
      let totalRecords = rows.length;
      console.log(`📊 DEBUG: Total records to process: ${totalRecords}`);
      console.log(`📋 DEBUG: Configuration fields being passed to convertToValuesStructure:`, JSON.stringify(configurationFields, null, 2));

      for (const row of rows) {
        const rowNumber = (row as any).__rowNumber || null;
        console.log(`\n🔄 DEBUG: Processing row ${rowNumber}:`);
        console.log(`📝 DEBUG: Original row data:`, JSON.stringify(row, null, 2));

        // Validate required fields FIRST - before any processing
        if (requiredFields && requiredFields.length > 0) {
          console.log(`⚠️ DEBUG: Checking required fields:`, requiredFields);
          const missingFields = requiredFields.filter(field => {
            const value = row[field];
            const isEmpty = !value || (typeof value === 'string' && value.trim() === '') || value === null || value === undefined;
            if (isEmpty) {
              console.log(`❌ DEBUG: Required field "${field}" is missing or empty. Value:`, value);
            }
            return isEmpty;
          });

          if (missingFields.length > 0) {
            const errorMessage = `Missing required fields: ${missingFields.join(', ')}`;
            console.log(`❌ DEBUG: Required field validation failed:`, errorMessage);

            failedCount++;
            failedRows.push({
              row: JSON.stringify(row),
              error: errorMessage,
              rowNumber
            });

            if (importTask.isStopOnError) {
              console.log(`🛑 [STOP ON ERROR] Required field validation failed for row ${rowNumber}`);
              const parts: string[] = [];
              if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
              if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
              parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
              const finalMessage = parts.join(', ');

              await this.taskModel.updateOne(
                { taskId },
                {
                  status: 'FAILED',
                  insertedCount,
                  updatedCount,
                  failedCount,
                  failedRows,
                  insertedIds,
                  updatedIds,
                  $push: {
                    statusLog: {
                      status: 'FAILED',
                      message: `Import stopped on first error: ${errorMessage}`,
                      timestamp: new Date(),
                    },
                  },
                }
              );
              await this.sendImportNotification(taskId, 'FAILED');
              throw new Error(`Import stopped on first error: ${errorMessage}`);
            }
            continue; // Skip this row and continue with next
          }
          console.log(`✅ DEBUG: All required fields are present for row ${rowNumber}`);
        }

        // Convert row data to values structure format based on template
        console.log(`🔧 DEBUG: Calling convertToValuesStructure with:`);
        console.log(`   - Row:`, JSON.stringify(row, null, 2));
        console.log(`   - ConfigurationFields length:`, configurationFields?.length || 0);

        const valuesStructuredRowGeneric = this.convertToValuesStructure(row, configurationFields);

        console.log(`✅ DEBUG: convertToValuesStructure returned:`, JSON.stringify(valuesStructuredRowGeneric, null, 2));

        try {
          // Create the document structure - different approach based on collection type
          let documentData: any = {};

          // Handle different collection types with their specific field structures
          if (importTask.collectionName === 'tickets' || importTask.collectionName === 'provider-credential-tickets') {
            // Tickets: Store individual fields + values JSON
            documentData = {
              ...valuesStructuredRowGeneric, // Spread all fields as individual columns
              values: JSON.stringify(valuesStructuredRowGeneric), // Also keep as JSON for compatibility
              type: 3,
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };
            console.log(`🎫 DEBUG: Created ticket document with individual fields + values JSON`);
          } else {
            // Other collections: Use generic structure
            documentData = {
              values: JSON.stringify(valuesStructuredRowGeneric),
              isActive: true,
              createdBy: importTask.createdBy,
              received_date: new Date(),
              createdAt: new Date(),
              updatedAt: new Date()
            };
            console.log(`📄 DEBUG: Created generic document with values JSON only`);
          }

          console.log(`💾 DEBUG: Final data to be inserted/updated:`, JSON.stringify(documentData, null, 2));
          console.log(`🔄 DEBUG: Import mode:`, importTask.importMode);
          console.log(`🗂️ DEBUG: Collection name:`, importTask.collectionName);

          switch (importTask.importMode) {
            case ImportType.CREATE:
              // Create only - insert new documents
              console.log(`➕ DEBUG: Attempting to INSERT document...`);
              try {
                const insertResult = await collection.insertOne(documentData);
                console.log(`✅ DEBUG: Insert successful, insertedId:`, insertResult.insertedId);
                if (insertResult.insertedId) {
                  insertedCount++;
                  insertedIds.push(insertResult.insertedId.toString());
                }
              } catch (insertError) {
                failedCount++;
                const errorMessage = `Insert failed: ${insertError instanceof Error ? insertError.message : String(insertError)}`;
                failedRows.push({
                  row: JSON.stringify(documentData),
                  error: errorMessage,
                  rowNumber
                });
                if (importTask.isStopOnError) {
                  // Set status and log immediately, then throw
                  const parts: string[] = [];
                  if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                  if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                  parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                  const finalMessage = parts.join(', ');
                  await this.taskModel.updateOne(
                    { taskId },
                    {
                      status: 'FAILED',
                      insertedCount,
                      updatedCount,
                      failedCount,
                      failedRows,
                      insertedIds,
                      updatedIds,
                      $push: {
                        statusLog: {
                          status: 'FAILED',
                          message: finalMessage,
                          timestamp: new Date(),
                        },
                      },
                    }
                  );
                  // await this.sendImportNotification(taskId, 'FAILED');
                  throw new Error(`Import stopped on first error: ${errorMessage}`);
                }
                continue;
              }
              break;

            case ImportType.UPDATE:
              // Update only - update existing documents
              const filter: Record<string, any> = {};

              // Check if we have unique fields for filtering
              if (!uniqueFields || uniqueFields.length === 0) {
                failedCount++;
                failedRows.push({
                  row: JSON.stringify(documentData),
                  error: 'No unique fields configured for update operation',
                  rowNumber
                });
                if (importTask.isStopOnError) {
                  const parts: string[] = [];
                  if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                  if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                  parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                  const finalMessage = parts.join(', ');
                  await this.taskModel.updateOne(
                    { taskId },
                    {
                      status: 'FAILED',
                      insertedCount,
                      updatedCount,
                      failedCount,
                      failedRows,
                      insertedIds,
                      updatedIds,
                      $push: {
                        statusLog: {
                          status: 'FAILED',
                          message: finalMessage,
                          timestamp: new Date(),
                        },
                      },
                    }
                  );
                  // await this.sendImportNotification(taskId, 'FAILED');
                  throw new Error('Import stopped on first error: No unique fields configured for update operation');
                }
                continue;
              }

              // Build filter using unique fields - check both row data and structured data
              for (const field of uniqueFields) {
                let fieldValue = undefined;

                // First try to get from original row data
                if (row[field] !== undefined && row[field] !== null && row[field] !== '') {
                  fieldValue = row[field];
                }
                // Then try to get from structured data (for tickets with individual fields)
                else if (valuesStructuredRowGeneric[field] !== undefined && valuesStructuredRowGeneric[field] !== null && valuesStructuredRowGeneric[field] !== '') {
                  fieldValue = valuesStructuredRowGeneric[field];
                }

                if (fieldValue !== undefined) {
                  filter[field] = fieldValue;
                  console.log(`🔑 DEBUG: Added unique field to UPDATE filter: ${field} = ${fieldValue}`);
                }
              }

              if (Object.keys(filter).length === 0) {
                failedCount++;
                const errorMessage = 'No valid unique field values found for update operation';
                failedRows.push({
                  row: JSON.stringify(documentData),
                  error: errorMessage,
                  rowNumber
                });
                if (importTask.isStopOnError) {
                  const parts: string[] = [];
                  if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                  if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                  parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                  const finalMessage = parts.join(', ');
                  await this.taskModel.updateOne(
                    { taskId },
                    {
                      status: 'FAILED',
                      insertedCount,
                      updatedCount,
                      failedCount,
                      failedRows,
                      insertedIds,
                      updatedIds,
                      $push: {
                        statusLog: {
                          status: 'FAILED',
                          message: finalMessage,
                          timestamp: new Date(),
                        },
                      },
                    }
                  );
                  // await this.sendImportNotification(taskId, 'FAILED');
                  throw new Error(`Import stopped on first error: ${errorMessage}`);
                }
                continue;
              }

              try {
                console.log(`🔄 DEBUG: Performing UPDATE with filter:`, JSON.stringify(filter, null, 2));
                console.log(`🔄 DEBUG: Update data contains ${Object.keys(documentData).length} fields:`, Object.keys(documentData));

                // Get original document for backup before updating
                const originalDoc = await collection.findOne(filter);
                if (originalDoc) {
                  backupRows.push(originalDoc);
                  console.log(`📋 DEBUG: Found existing document for update, ID: ${originalDoc._id}`);
                } else {
                  console.log(`❌ DEBUG: No existing document found with filter`);
                }

                const updateResult = await collection.updateOne(
                  filter,
                  { $set: { ...documentData, updatedAt: new Date() } },
                  { upsert: false }
                );

                console.log(`✅ DEBUG: Update result - matched: ${updateResult.matchedCount}, modified: ${updateResult.modifiedCount}`);

                if (updateResult.modifiedCount > 0 || updateResult.matchedCount > 0) {
                  updatedCount++;
                  const updatedDoc = await collection.findOne(filter, { projection: { _id: 1 } });
                  if (updatedDoc && updatedDoc._id) updatedIds.push(updatedDoc._id.toString());
                } else {
                  failedCount++;
                  const errorMessage = 'No matching document found for update';
                  failedRows.push({
                    row: JSON.stringify(documentData),
                    error: errorMessage,
                    rowNumber
                  });
                  if (importTask.isStopOnError) {
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');
                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        failedRows,
                        insertedIds,
                        updatedIds,
                        $push: {
                          statusLog: {
                            status: 'FAILED',
                            message: finalMessage,
                            timestamp: new Date(),
                          },
                        },
                      }
                    );
                    // await this.sendImportNotification(taskId, 'FAILED');
                    throw new Error(`Import stopped on first error: ${errorMessage}`);
                  }
                  continue;
                }
              } catch (updateError) {
                failedCount++;
                const errorMessage = `Update failed: ${updateError instanceof Error ? updateError.message : String(updateError)}`;
                failedRows.push({
                  row: JSON.stringify(documentData),
                  error: errorMessage,
                  rowNumber
                });
                if (importTask.isStopOnError) {
                  const parts: string[] = [];
                  if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                  if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                  parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                  const finalMessage = parts.join(', ');
                  await this.taskModel.updateOne(
                    { taskId },
                    {
                      status: 'FAILED',
                      insertedCount,
                      updatedCount,
                      failedCount,
                      failedRows,
                      insertedIds,
                      updatedIds,
                      $push: {
                        statusLog: {
                          status: 'FAILED',
                          message: finalMessage,
                          timestamp: new Date(),
                        },
                      },
                    }
                  );
                  await this.sendImportNotification(taskId, 'FAILED');
                  throw new Error(`Import stopped on first error: ${errorMessage}`);
                }
                continue;
              }
              break;

            case ImportType.UPSERT:
            default:
              // Upsert - insert or update
              if (!uniqueFields || uniqueFields.length === 0) {
                // No unique fields - treat as CREATE operation
                try {
                  const insertResult = await collection.insertOne(documentData);
                  if (insertResult.insertedId) {
                    insertedCount++;
                    insertedIds.push(insertResult.insertedId.toString());
                  }
                } catch (insertError) {
                  failedCount++;
                  failedRows.push({
                    row: JSON.stringify(documentData),
                    error: `Insert failed (no unique fields): ${insertError instanceof Error ? insertError.message : String(insertError)}`,
                    rowNumber
                  });
                  if (importTask.isStopOnError) {
                    const parts: string[] = [];
                    if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                    if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                    parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                    const finalMessage = parts.join(', ');
                    await this.taskModel.updateOne(
                      { taskId },
                      {
                        status: 'FAILED',
                        insertedCount,
                        updatedCount,
                        failedCount,
                        failedRows,
                        insertedIds,
                        updatedIds,
                        $push: {
                          statusLog: {
                            status: 'FAILED',
                            message: finalMessage,
                            timestamp: new Date(),
                          },
                        },
                      }
                    );
                    // await this.sendImportNotification(taskId, 'FAILED');
                    throw new Error(`Import stopped on first error: ${insertError instanceof Error ? insertError.message : String(insertError)}`);
                  }
                  continue;
                }
              } else {
                // Has unique fields - perform upsert
                const upsertFilter: Record<string, any> = {};
                for (const field of uniqueFields) {
                  let fieldValue = undefined;

                  // First try to get from original row data
                  if (row[field] !== undefined && row[field] !== null && row[field] !== '') {
                    fieldValue = row[field];
                  }
                  // Then try to get from structured data (for tickets with individual fields)
                  else if (valuesStructuredRowGeneric[field] !== undefined && valuesStructuredRowGeneric[field] !== null && valuesStructuredRowGeneric[field] !== '') {
                    fieldValue = valuesStructuredRowGeneric[field];
                  }

                  if (fieldValue !== undefined) {
                    upsertFilter[field] = fieldValue;
                    console.log(`🔑 DEBUG: Added unique field to UPSERT filter: ${field} = ${fieldValue}`);
                  }
                }

                if (Object.keys(upsertFilter).length === 0) {
                  // No valid unique field values - treat as CREATE
                  try {
                    const insertResult = await collection.insertOne(documentData);
                    if (insertResult.insertedId) {
                      insertedCount++;
                      insertedIds.push(insertResult.insertedId.toString());
                    }
                  } catch (insertError) {
                    failedCount++;
                    failedRows.push({
                      row: JSON.stringify(documentData),
                      error: `Insert failed (no valid unique values): ${insertError instanceof Error ? insertError.message : String(insertError)}`,
                      rowNumber
                    });
                    if (importTask.isStopOnError) {
                      const parts: string[] = [];
                      if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                      if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                      parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                      const finalMessage = parts.join(', ');
                      await this.taskModel.updateOne(
                        { taskId },
                        {
                          status: 'FAILED',
                          insertedCount,
                          updatedCount,
                          failedCount,
                          failedRows,
                          insertedIds,
                          updatedIds,
                          $push: {
                            statusLog: {
                              status: 'FAILED',
                              message: finalMessage,
                              timestamp: new Date(),
                            },
                          },
                        }
                      );
                      // await this.sendImportNotification(taskId, 'FAILED');
                      throw new Error(`Import stopped on first error: ${insertError instanceof Error ? insertError.message : String(insertError)}`);
                    }
                    continue;
                  }
                } else {
                  // Perform upsert with unique fields
                  try {
                    // Get original document for backup before upserting (in case it's an update)
                    const originalDoc = await collection.findOne(upsertFilter);

                    console.log(`🔄 DEBUG: Performing upsert with filter:`, JSON.stringify(upsertFilter, null, 2));
                    console.log(`🔄 DEBUG: Upsert data contains ${Object.keys(documentData).length} fields:`, Object.keys(documentData));

                    const upsertResult = await collection.updateOne(
                      upsertFilter,
                      { $set: { ...documentData, updatedAt: new Date() } },
                      { upsert: true }
                    );

                    console.log(`✅ DEBUG: Upsert result - matched: ${upsertResult.matchedCount}, modified: ${upsertResult.modifiedCount}, upserted: ${upsertResult.upsertedCount}`);

                    if (upsertResult.upsertedCount && upsertResult.upsertedId) {
                      insertedCount++;
                      insertedIds.push(upsertResult.upsertedId._id.toString());
                    } else if (upsertResult.modifiedCount > 0 || upsertResult.matchedCount > 0) {
                      updatedCount++;
                      const updatedDoc = await collection.findOne(upsertFilter, { projection: { _id: 1 } });
                      if (updatedDoc && updatedDoc._id) updatedIds.push(updatedDoc._id.toString());

                      // Add original document to backup if it existed (this was an update, not insert)
                      if (originalDoc) {
                        backupRows.push(originalDoc);
                        console.log(`Added document to backup in upsert mode for ID: ${originalDoc._id}`);
                      }
                    }
                  } catch (upsertError) {
                    failedCount++;
                    failedRows.push({
                      row: JSON.stringify(documentData),
                      error: `Upsert failed: ${upsertError instanceof Error ? upsertError.message : String(upsertError)}`,
                      rowNumber
                    });
                    if (importTask.isStopOnError) {
                      const parts: string[] = [];
                      if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
                      if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
                      parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
                      const finalMessage = parts.join(', ');
                      await this.taskModel.updateOne(
                        { taskId },
                        {
                          status: 'FAILED',
                          insertedCount,
                          updatedCount,
                          failedCount,
                          failedRows,
                          insertedIds,
                          updatedIds,
                          $push: {
                            statusLog: {
                              status: 'FAILED',
                              message: finalMessage,
                              timestamp: new Date(),
                            },
                          },
                        }
                      );
                      // await this.sendImportNotification(taskId, 'FAILED');
                      throw new Error(`Import stopped on first error: ${upsertError instanceof Error ? upsertError.message : String(upsertError)}`);
                    }
                    continue;
                  }
                }
              }
              break;
          }
        } catch (err) {
          failedCount++;
          failedRows.push({
            row: JSON.stringify(valuesStructuredRowGeneric),
            error: `Unexpected error: ${err instanceof Error ? err.message : String(err)}`,
            rowNumber
          });
          if (importTask.isStopOnError) {
            const parts: string[] = [];
            if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
            if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
            parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
            const finalMessage = parts.join(', ');
            await this.taskModel.updateOne(
              { taskId },
              {
                status: 'FAILED',
                insertedCount,
                updatedCount,
                failedCount,
                failedRows,
                insertedIds,
                updatedIds,
                $push: {
                  statusLog: {
                    status: 'FAILED',
                    message: finalMessage,
                    timestamp: new Date(),
                  },
                },
              }
            );
            // await this.sendImportNotification(taskId, 'FAILED');
            throw new Error(`Import stopped on first error: ${err instanceof Error ? err.message : String(err)}`);
          }
        }
      }

      // Create backup file if there are updated rows
      const backupUrls: Record<string, { url: string; format: string; recordCount: number; timestamp: Date; status: string }> = {};
      const collectionName = importTask.collectionName || 'unknown';

      if (backupRows.length > 0) {
        try {
          console.log(`Creating backup for ${backupRows.length} updated rows`);

          // Convert backup data to CSV
          const csvHeaders = Object.keys(backupRows[0]).join(',');
          const csvRows = backupRows.map(row =>
            Object.values(row).map(value =>
              typeof value === 'string' && value.includes(',') ? `"${value}"` : value
            ).join(',')
          );
          const csvContent = [csvHeaders, ...csvRows].join('\n');
          const csvBuffer = Buffer.from(csvContent, 'utf-8');

          // Upload backup to GCS
          const backupFileName = `complete_${collectionName}_backup.csv`;
          const backupPath = `backup/${taskId}/${backupFileName}`;
          const backupUrl = await this.gcsService.uploadBuffer(csvBuffer, backupPath, 'text/csv');

          backupUrls[collectionName] = {
            url: backupUrl,
            format: 'csv',
            recordCount: backupRows.length,
            timestamp: new Date(),
            status: 'success'
          };

          console.log(`Backup created successfully: ${backupUrl}`);
        } catch (backupError) {
          console.error('Failed to create backup:', backupError);
          backupUrls[collectionName] = {
            url: '',
            format: 'csv',
            recordCount: 0,
            timestamp: new Date(),
            status: `error: ${backupError.message}`
          };
        }
      } else {
        backupUrls[collectionName] = {
          url: '',
          format: 'csv',
          recordCount: 0,
          timestamp: new Date(),
          status: 'no_backup_needed'
        };
      }

      // Determine final status and message
      let finalStatus: string;
      let finalMessage: string;
      if (failedCount > 0) {
        // Use PARTIAL_COMPLETED if some records succeeded
        finalStatus = (insertedCount > 0 || updatedCount > 0) ? 'PARTIAL_COMPLETED' : 'FAILED';
        const parts: string[] = [];
        if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
        if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
        parts.push(`${failedCount} record${failedCount > 1 ? 's' : ''} failed`);
        finalMessage = `Processed ${totalRecords} records: ${parts.join(', ')}`;
      } else {
        finalStatus = 'COMPLETED';
        const parts: string[] = [];
        if (insertedCount > 0) parts.push(`${insertedCount} record${insertedCount > 1 ? 's' : ''} inserted`);
        if (updatedCount > 0) parts.push(`${updatedCount} record${updatedCount > 1 ? 's' : ''} updated`);
        finalMessage = `Processed ${totalRecords} records: ${parts.join(', ')}`;
      }

      // Generate audit log URL for normal import
      let auditLogUrl = '';
      if (totalRecords > 0) {
        const auditData = {
          taskId,
          timestamp: new Date(),
          orgId: importTask.orgId,
          createdBy: importTask.createdBy,
          createdByEmail: importTask.createdByEmail,
          collectionName: importTask.collectionName,
          summary: {
            totalCreated: insertedCount,
            totalUpdated: updatedCount,
            totalUnchanged: 0,
            totalFailed: failedCount,
            totalProcessed: totalRecords
          },
          affectedCollections: (() => {
            const collections: any = {};
            collections[importTask.collectionName || 'unknown'] = {
              createdCount: insertedCount,
              updatedCount: updatedCount,
              unchangedCount: 0,
              changes: {}
            };
            return collections;
          })(),
          backupUrls: backupUrls,
          failedRows: failedRows.length > 0 ? failedRows : null
        }

        // Upload audit JSON to audit bucket
        const auditJsonBuffer = Buffer.from(JSON.stringify(auditData, null, 2), 'utf-8');
        const auditPath = `import/audit/${taskId}/audit_log.json`;
        auditLogUrl = await this.gcsService.uploadBuffer(auditJsonBuffer, auditPath, 'application/json');
        console.log(`✅ Normal import audit log stored at: ${auditLogUrl}`);
      }

      await this.taskModel.updateOne(
        { taskId },
        {
          status: finalStatus,
          insertedCount,
          updatedCount,
          failedCount,
          failedRows,
          insertedIds,
          updatedIds,
          totalRecords,
          backupUrls: backupUrls, // Store structured backup URLs
          auditLogUrl: auditLogUrl || null, // Store audit log URL
          $push: {
            statusLog: {
              status: finalStatus,
              message: finalMessage,
              timestamp: new Date(),
            },
          },
        }
      );

      // Log audit event for normal import completion
      await this.logImportAuditEvent(
        taskId,
        'UPDATE',
        { status: 'IN_PROGRESS' },
        {
          status: finalStatus,
          totalRecords,
          insertedCount,
          updatedCount,
          failedCount,
          collectionName: importTask.collectionName,
          importMode: importTask.importMode
        },
        importTask.createdBy || 'system',
        importTask.createdByEmail || ''
      );

      await this.sendImportNotification(taskId, finalStatus);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      await this.taskModel.updateOne(
        { taskId },
        {
          status: 'FAILED',
          $push: {
            statusLog: {
              status: 'FAILED',
              message: errorMessage.split('\n')[0],
              timestamp: new Date(),
            },
          },
        }
      );
      await this.sendImportNotification(taskId, 'FAILED');
      throw err;
    } finally {
      // No need to close dbConnection when using MongoConnectionService
    }
  }

  async getImportAuditLog(taskId: string): Promise<any> {
    try {
      // Get task details first
      const task = await this.taskModel.findOne({ taskId, type: 'import' });
      if (!task) {
        throw new Error(`Import task ${taskId} not found`);
      }

      // If audit log URL exists, fetch from GCS
      if (task.auditLogUrl) {
        try {
          const auditBuffer = await this.gcsService.downloadBuffer(task.auditLogUrl);
          const auditData = JSON.parse(auditBuffer.toString('utf-8'));
          return {
            success: true,
            data: auditData,
            source: 'gcs',
            url: task.auditLogUrl
          };
        } catch (gcsError) {
          console.warn(`Failed to fetch audit log from GCS: ${gcsError}`);
        }
      }

      // Fallback: get import logs from database
      const importLogCollection = await this.mongoConnectionService.getCollectionByOrgId('import_logs', task.orgId || 'asp');
      const importLogs = await importLogCollection.find({ taskId }).toArray();

      return {
        success: true,
        data: {
          taskId,
          timestamp: task.createdAt,
          orgId: task.orgId,
          createdBy: task.createdBy,
          collectionName: task.collectionName,
          summary: {
            totalCreated: task.insertedCount || 0,
            totalUpdated: task.updatedCount || 0,
            totalUnchanged: task.unchangedCount || 0,
            totalFailed: task.failedCount || 0,
            totalProcessed: importLogs.length
          },
          importLogEntries: importLogs,
          affected: task.affected || {},
          failedRows: task.failedRows || []
        },
        source: 'database'
      };
    } catch (error) {
      console.error(`Error fetching audit log for task ${taskId}:`, error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    result.push(current.trim());
    return result;
  }

  // Send notification when import task completes or fails


  async logImportAuditEvent(
    taskId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    beforeData: any,
    afterData: any,
    userId: string,
    userEmail: string
  ) {
    console.log(`🚀 Import audit logging called for task ${taskId}:`, {
      action,
      userId,
      userEmail: userEmail ? userEmail.substring(0, 10) + '...' : 'N/A'
    });

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Import audit logging timeout')), 3000); // 3 second timeout
    });

    try {
      await Promise.race([
        this.logImportAuditEventInternal(taskId, action, beforeData, afterData, userId, userEmail),
        timeoutPromise
      ]);
      console.log(`🎉 Import audit logging completed for task ${taskId}: ${action}`);
    } catch (error) {
      console.error(`❌ Import audit logging failed for task ${taskId}:`, error);
      // Don't throw here - audit failure shouldn't crash the import process
    }
  }

  private async logImportAuditEventInternal(
    taskId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    beforeData: any,
    afterData: any,
    userId: string,
    userEmail: string
  ) {
    try {
      console.log(`🔍 Starting import audit logging for task ${taskId}:`, {
        action,
        userId,
        userEmail,
        beforeData,
        afterData
      });

      const auditData = {
        eventType: action,
        recordType: 'IMPORT_TASK',
        beforeData,
        afterData,
        authoredInfo: {
          id: userId,
          name: userEmail || 'System User',
          email: userEmail || 'System User'
        }
      };

      console.log(`📝 Import audit data prepared:`, auditData);

      await this.auditHelperService.logAuditEvent(auditData);

      console.log(`✅ Import audit event logged successfully for task ${taskId}: ${action}`);
    } catch (error) {
      console.error(`❌ Failed to log import audit event for task ${taskId}:`, error);
      console.error(`❌ Error details:`, error.message, error.stack);
      throw error;
    }
  }

  async sendImportNotification(taskId: string, status: string) {
    console.log(`🚀 DEBUG: Starting notification process for task ${taskId} with status ${status}`);

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Import notification timeout')), 10000); // 10 second timeout
    });

    try {
      console.log(`⏱️ DEBUG: Setting up notification timeout (10 seconds) for task ${taskId}`);

      await Promise.race([
        this.sendImportNotificationInternal(taskId, status),
        timeoutPromise
      ]);

      console.log(`📢 DEBUG: Import notification processing completed successfully for task ${taskId}: ${status}`);
    } catch (error) {
      console.error(`❌ DEBUG: Failed to send import notification for task ${taskId}:`, {
        error: error.message,
        stack: error.stack,
        status,
        taskId
      });
      // Don't throw here - notification failure shouldn't crash the import process
    }
  }

  private async sendImportNotificationInternal(taskId: string, status: string) {
    console.log(`🔍 DEBUG: Starting internal notification for task ${taskId}, status: ${status}`);

    const task = await this.taskModel.findOne({ taskId }).lean();
    if (!task) {
      console.warn(`❌ DEBUG: Task not found for notification: ${taskId}`);
      return;
    }

    console.log(`✅ DEBUG: Task found for notification:`, {
      taskId: task.taskId,
      collectionName: task.collectionName,
      createdBy: task.createdBy,
      status: task.status,
      orgId: task.orgId
    });

    // Skip notification if no user ID
    if (!task.createdBy) {
      console.warn(`❌ DEBUG: No user ID found for task notification: ${taskId}`);
      return;
    }

    console.log(`🔍 DEBUG: Getting device token for user: ${task.createdBy}`);

    // Get device tokens and user email
    const deviceTokens = await this.getDeviceTokenForUser(task.createdBy);
    const email = task.createdByEmail;

    console.log(`📱 DEBUG: Device token result:`, {
      userId: task.createdBy,
      hasDeviceToken: !!deviceTokens,
      deviceTokenLength: deviceTokens?.length || 0,
      email: email || 'No email'
    });

    // Prepare notification content based on status
    let title = '';
    let message = '';
    let priority = NotificationPriority.MEDIUM;
    let type: NotificationType;

    switch (status) {
      case 'CREATED':
        title = 'Import Task Created';
        message = `Import task for ${task.collectionName} has been created and scheduled.`;
        priority = NotificationPriority.LOW;
        type = NotificationType.TASK_STARTED;
        break;

      case 'STARTED':
      case 'IN_PROGRESS':
        title = 'Import Processing Started';
        message = `Import processing for ${task.collectionName} has started.`;
        priority = NotificationPriority.MEDIUM;
        type = NotificationType.TASK_PROGRESS;
        break;

      case 'COMPLETED':
        title = 'Import Completed';
        message = `Your import task for ${task.collectionName} has been completed successfully.`;
        priority = NotificationPriority.HIGH;
        type = NotificationType.IMPORT_COMPLETED;
        break;

      case 'PARTIAL_COMPLETED':
        title = 'Import Partially Completed';
        message = `Your import task for ${task.collectionName} has been partially completed. Some records failed.`;
        priority = NotificationPriority.HIGH;
        type = NotificationType.IMPORT_FAILED;
        break;

      case 'FAILED':
        title = 'Import Failed';
        message = `Your import task for ${task.collectionName} has failed. Please try again.`;
        priority = NotificationPriority.HIGH;
        type = NotificationType.IMPORT_FAILED;
        break;

      default:
        console.warn(`Unknown import status: ${status}`);
        return;
    }

    // Construct the payload
    const payload: NotificationPayload = {
      userId: task.createdBy,
      senderId: task.createdBy,
      type,
      title,
      message,
      priority,
      channels: [NotificationChannel.IN_APP],
      isRouted: true,
      data: {
        taskId: task.taskId,
        collectionName: task.collectionName,
        importMode: task.importMode,
        status,
        downloadUrl: task.downloadUrl,
        backupUrl: task.backupUrl,
        totalRecords: task.totalRecords || 0,
        insertedCount: task.insertedCount || 0,
        updatedCount: task.updatedCount || 0,
        failedCount: task.failedCount || 0,
        notificationId: new ObjectId().toString(),
        deviceTokens: Array.isArray(deviceTokens) ? deviceTokens : [deviceTokens],
      },
      metadata: {
        taskId: task.taskId,
        collectionName: task.collectionName,
        importMode: task.importMode,
        orgId: task.orgId,
        status,
        userEmail: email,
      },
    };

    console.log(`📋 DEBUG: Notification payload created:`, {
      userId: payload.userId,
      title: payload.title,
      message: payload.message,
      taskId: payload.data?.taskId,
      hasDeviceTokens: payload.data?.deviceTokens?.length > 0,
      deviceTokenCount: payload.data?.deviceTokens?.length || 0
    });

    // Check if Kafka notifications are enabled
    const useKafka = process.env.ENABLE_KAFKA_NOTIFICATIONS !== 'false';
    console.log(`🔧 DEBUG: Kafka configuration - useKafka: ${useKafka}, env: ${process.env.ENABLE_KAFKA_NOTIFICATIONS}`);

    if (useKafka) {
      try {
        console.log(`📨 DEBUG: Attempting to publish to Kafka topic 'notification' for task ${taskId}`);
        await this.kafkaService.publishToKafka(payload, 'notification');
        console.log(`✅ DEBUG: Kafka notification sent successfully for import task ${taskId}: ${status}`);
        return; // Success, no need for fallback
      } catch (kafkaError) {
        console.error(`❌ DEBUG: Kafka notification failed for task ${taskId}:`, {
          error: kafkaError.message,
          code: kafkaError.code,
          stack: kafkaError.stack
        });
        console.log(`🔄 DEBUG: Falling back to direct notification service for task ${taskId}`);
      }
    } else {
      console.log(`⚠️ DEBUG: Kafka notifications disabled, using direct notification service for task ${taskId}`);
    }

    // Fallback to direct notification service (either Kafka failed or disabled)
    try {
      console.log(`🔄 DEBUG: Using direct notification service for task ${taskId}`);

      // Send Firebase push notification if device token is available
      if (deviceTokens) {
        console.log(`🔥 DEBUG: Attempting Firebase push notification for task ${taskId}:`, {
          deviceToken: deviceTokens,
          title,
          message,
          userId: task.createdBy
        });

        try {
          await this.notificationService.notifyUserWithFirebasePush(
            deviceTokens,
            title,
            message,
            {
              taskId: task.taskId,
              collectionName: task.collectionName || 'unknown',
              status,
              importMode: task.importMode || 'upsert',
              totalRecords: (task.totalRecords || 0).toString(),
              insertedCount: (task.insertedCount || 0).toString(),
              updatedCount: (task.updatedCount || 0).toString(),
              failedCount: (task.failedCount || 0).toString()
            }
          );
          console.log(`✅ DEBUG: Firebase push notification sent successfully for task ${taskId}`);
        } catch (pushError) {
          console.error(`❌ DEBUG: Firebase push notification failed for task ${taskId}:`, {
            error: pushError.message,
            code: pushError.code,
            deviceToken: deviceTokens,
            userId: task.createdBy
          });
        }
      } else {
        console.log(`⚠️ DEBUG: No device token available for user ${task.createdBy}, skipping push notification`);
      }

      // Store notification in database
      try {
        console.log(`💾 DEBUG: Attempting to store notification in database for task ${taskId}:`, {
          orgId: task.orgId || 'asp',
          notificationId: payload.data?.notificationId,
          userId: payload.userId
        });

        await this.notificationService.saveNotificationToDb(
          payload,
          payload.data?.notificationId || new ObjectId().toString(),
          task.orgId || 'asp'
        );
        console.log(`✅ DEBUG: Notification stored successfully in database for task ${taskId}`);
      } catch (dbError) {
        console.error(`❌ DEBUG: Failed to store notification in database for task ${taskId}:`, {
          error: dbError.message,
          code: dbError.code,
          orgId: task.orgId || 'asp'
        });
      }

      console.log(`✅ DEBUG: Direct notification processing completed for import task ${taskId}: ${status}`);
    } catch (fallbackError) {
      console.error(`❌ DEBUG: Direct notification also failed for task ${taskId}:`, {
        error: fallbackError.message,
        stack: fallbackError.stack,
        taskId,
        status
      });
      // Don't throw here - notification failure shouldn't crash the import process
    }

    console.log(`📢 Notification processing completed for import task ${taskId}: ${status}`);
  }

  private async getDeviceTokenForUser(userId: string): Promise<string | null> {
    console.log(`🔍 DEBUG: Starting device token retrieval for user ${userId}`);

    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<any>((_, reject) => {
        setTimeout(() => reject(new Error('Device token fetch timeout')), 3000); // 3 second timeout
      });

      console.log(`⏱️ DEBUG: Setting up 3-second timeout for device token fetch`);

      // First try to get from user_notification_tokens collection (preferred method)
      const tokenCollectionPromise = (async () => {
        try {
          console.log(`🗄️ DEBUG: Querying user_notification_tokens collection for user ${userId}`);

          const collection = await this.mongoConnectionService.getCollectionByOrgId(
            'user_notification_tokens',
            'asp'
          );

          console.log(`📊 DEBUG: Collection obtained, searching for userId: ${userId}`);

          const tokenRecord = await collection.findOne({ userId });

          console.log(`🔎 DEBUG: Token record query result:`, {
            found: !!tokenRecord,
            hasToken: !!tokenRecord?.token,
            tokenType: Array.isArray(tokenRecord?.token) ? 'array' : typeof tokenRecord?.token,
            tokenLength: Array.isArray(tokenRecord?.token) ? tokenRecord.token.length : (tokenRecord?.token?.length || 0),
            platform: tokenRecord?.platform,
            createdAt: tokenRecord?.createdAt
          });

          if (tokenRecord?.token) {
            // Handle both array and string formats
            let deviceToken: string | null = null;

            if (Array.isArray(tokenRecord.token)) {
              // Token is an array - get the first valid token
              const validTokens = tokenRecord.token.filter(t => t && typeof t === 'string' && t.trim() !== '');
              deviceToken = validTokens.length > 0 ? validTokens[0] : null;
              console.log(`✅ DEBUG: Found device token array for user ${userId}, using first valid token:`, {
                totalTokens: tokenRecord.token.length,
                validTokens: validTokens.length,
                selectedToken: deviceToken ? `${deviceToken.substring(0, 10)}...` : 'none'
              });
            } else if (typeof tokenRecord.token === 'string' && tokenRecord.token.trim() !== '') {
              // Token is a string
              deviceToken = tokenRecord.token;
              console.log(`✅ DEBUG: Found device token string for user ${userId}: ${deviceToken.substring(0, 10)}...`);
            } else {
              console.log(`⚠️ DEBUG: Invalid token format for user ${userId}:`, typeof tokenRecord.token);
            }

            return deviceToken;
          }
        } catch (err) {
          console.warn(`⚠️ DEBUG: Failed to get token from user_notification_tokens for user ${userId}:`, {
            error: err.message,
            code: err.code
          });
        }
        return null;
      })();

      // Get token from collection with timeout
      console.log(`⏳ DEBUG: Racing token fetch against timeout for user ${userId}`);

      const deviceToken = await Promise.race([
        tokenCollectionPromise,
        timeoutPromise
      ]);

      console.log(`📱 DEBUG: Device token fetch result for user ${userId}:`, {
        hasToken: !!deviceToken,
        tokenLength: deviceToken?.length || 0,
        tokenPreview: deviceToken && typeof deviceToken === 'string' ? `${deviceToken.substring(0, 10)}...` : 'null'
      });

      if (!deviceToken) {
        console.warn(`⚠️ DEBUG: No device token found for user ${userId}`);
      }

      return deviceToken;
    } catch (error) {
      console.error(`❌ DEBUG: Failed to get device token for user ${userId}:`, {
        error: error.message,
        code: error.code,
        stack: error.stack
      });
      return null; // Return null on error - don't crash the notification process
    }
  }

  async getImportStatus(taskId: string) {
    const task = await this.taskModel.findOne({ taskId, type: 'import' }).lean();
    if (!task) throw new NotFoundException('Import task not found');
    return task;
  }

  async getAllImports(params: {
    page?: number;
    limit?: number;
    search?: string;
    filters?: Record<string, any>;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    selectedFields?: Record<string, number>;
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      filters = {},
      sortBy,
      sortOrder = 'asc',
      selectedFields
    } = params || {};
    const query: any = { type: 'import' };
    const sampleDoc = await this.taskModel.findOne().lean();
    const allFields = sampleDoc && typeof sampleDoc === 'object' ? Object.keys(sampleDoc) : [];
    let projection: Record<string, number> = {};
    let searchableFields: string[] = [];

    if (selectedFields && typeof selectedFields === 'object' && Object.keys(selectedFields).length > 0) {
      projection = selectedFields;
      searchableFields = ['collectionName', 'createdBy', 'status'];
    } else {
      const excluded = ['__v'];
      projection = (allFields || []).reduce((acc, field) => {
        if (!excluded.includes(field)) acc[field] = 1;
        return acc;
      }, {} as Record<string, number>);
      searchableFields = ['collectionName', 'createdBy', 'status'];
    }

    if (search?.trim()) {
      const searchTerm = search.trim();
      const regex = {
        $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
        $options: 'i'
      };
      query.$or = searchableFields.map(field => ({ [field]: regex }));
    }

    if (filters) {
      const parsed =
        typeof filters === 'string'
          ? JSON.parse(filters)?.filters || JSON.parse(filters)
          : filters.filters || filters;
      for (const [key, value] of Object.entries(parsed)) {
        if (value !== undefined && value !== '') {
          query[key] = value;
        }
      }
    }

    const sort: Record<string, 1 | -1> = {};
    if (sortBy) {
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    } else {
      sort.createdAt = -1;
    }

    const safePage = Math.max(1, page);
    const safeLimit = Math.min(Math.max(1, limit), 100);
    const skip = (safePage - 1) * safeLimit;

    const totalItems = await this.taskModel.countDocuments(query);
    const imports = await this.taskModel
      .find(query)
      .select(projection)
      .sort(sort)
      .skip(skip)
      .limit(safeLimit)
      .lean();

    // Patch createdBy with user email
    const importsWithUserEmail = await Promise.all(
      imports.map(async (importItem) => {
        if (importItem.createdBy) {
          try {
            const user = await this.userService.findOne(importItem.createdBy);
            if (user && user.email) {
              return {
                ...importItem,
                createdBy: user.email
              };
            }
          } catch (error) {
            // If user not found, keep the original createdBy value
            console.warn(`User not found for ID: ${importItem.createdBy}`);
          }
        }
        return importItem;
      })
    );

    const totalPages = Math.ceil(totalItems / safeLimit);

    return {
      items: importsWithUserEmail,
      pagination: {
        page: safePage,
        limit: safeLimit,
        total: totalItems,
        totalItems,
        totalPages,
        hasNext: safePage < totalPages,
        hasPrev: safePage > 1
      }
    };
  }

  async deleteImportTask(taskId: string): Promise<boolean> {
    const result = await this.taskModel.deleteOne({ taskId, type: 'import' });
    return result.deletedCount > 0;
  }

  async rollbackImport(taskId: string): Promise<{
    success: boolean;
    message: string;
    restoredCount: number;
    deletedCount: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    let restoredCount = 0;
    let deletedCount = 0;

    try {
      // Find the import task
      const task = await this.taskModel.findOne({ taskId, type: 'import' });
      if (!task) {
        return {
          success: false,
          message: 'Import task not found',
          restoredCount: 0,
          deletedCount: 0,
          errors: ['Task not found']
        };
      }

      if (task.status !== 'COMPLETED') {
        return {
          success: false,
          message: 'Can only rollback completed import tasks',
          restoredCount: 0,
          deletedCount: 0,
          errors: ['Task is not completed']
        };
      }

      // Step 1: Restore from JSON backups using new backup system
      if (task.backupUrls && Object.keys(task.backupUrls).length > 0) {
        for (const [collectionKey, backupInfo] of Object.entries(task.backupUrls)) {
          try {
            const backupUrl = (backupInfo as any).url;
            if (!backupUrl || backupUrl === '') {
              console.log(`⚠️ No backup URL for ${collectionKey}, skipping...`);
              continue;
            }

            // Extract file path from GCS URL
            const backupPath = backupUrl.replace('https://storage.googleapis.com/', '').split('/').slice(1).join('/');

            // Download JSON backup file
            const backupContent = await this.gcsService.downloadBuffer(backupPath);
            const backupData = JSON.parse(backupContent.toString('utf-8'));

            if (backupData.data && Array.isArray(backupData.data)) {
              // Get the collection to restore
              const targetCollection = await this.mongoConnectionService.getCollectionByOrgId(
                backupData.metadata.collectionName,
                task.orgId || 'asp'
              );

              // Clear current collection and restore from backup
              await targetCollection.deleteMany({});

              if (backupData.data.length > 0) {
                // Convert string IDs back to ObjectIds for restoration
                const restoredData = backupData.data.map((doc: any) => {
                  const restoredDoc = { ...doc };
                  if (restoredDoc._id && typeof restoredDoc._id === 'string') {
                    restoredDoc._id = new Types.ObjectId(restoredDoc._id);
                  }
                  // Convert other ObjectId fields back
                  Object.keys(restoredDoc).forEach(field => {
                    if (typeof restoredDoc[field] === 'string' && restoredDoc[field].match(/^[0-9a-fA-F]{24}$/)) {
                      try {
                        restoredDoc[field] = new Types.ObjectId(restoredDoc[field]);
                      } catch (e) {
                        // Keep as string if conversion fails
                      }
                    }
                  });
                  return restoredDoc;
                });

                await targetCollection.insertMany(restoredData);
                restoredCount += restoredData.length;
                console.log(`✅ Restored ${restoredData.length} records to ${backupData.metadata.collectionName}`);
              }
            }
          } catch (error) {
            const errorMsg = `Failed to restore ${collectionKey}: ${error instanceof Error ? error.message : String(error)}`;
            errors.push(errorMsg);
            console.error(errorMsg);
          }
        }
      } else {
        errors.push('No backup URLs found in task - cannot perform rollback');
      }

      // Step 2: Update task status to indicate rollback completion

      // Step 4: Update task status to indicate rollback
      const rollbackStatus = errors.length === 0 ? 'REVERTED' : 'REVERT_FAILED';
      const rollbackMessage = errors.length === 0
        ? `Rollback completed successfully - Restored: ${restoredCount}, Deleted: ${deletedCount}`
        : `Rollback completed with ${errors.length} errors - Restored: ${restoredCount}, Deleted: ${deletedCount}`;

      await this.taskModel.updateOne(
        { taskId },
        {
          status: rollbackStatus,
          rollbackDate: new Date(),
          rollbackStats: {
            restoredCount,
            deletedCount,
            errors: errors.length
          },
          $push: {
            statusLog: {
              status: rollbackStatus,
              message: rollbackMessage,
              timestamp: new Date(),
            },
          },
        }
      );

      const success = errors.length === 0;
      const message = success
        ? `Rollback completed successfully - Restored: ${restoredCount}, Deleted: ${deletedCount}`
        : `Rollback completed with ${errors.length} errors - Restored: ${restoredCount}, Deleted: ${deletedCount}`;

      console.log(message);
      return {
        success,
        message,
        restoredCount,
        deletedCount,
        errors
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Rollback failed:', errorMessage);
      return {
        success: false,
        message: `Rollback failed: ${errorMessage}`,
        restoredCount,
        deletedCount,
        errors: [...errors, errorMessage]
      };
    }
    // Defensive: should never reach here, but for type safety
    return {
      success: false,
      message: 'Unknown error in rollbackImport',
      restoredCount,
      deletedCount,
      errors: [...errors, 'Unknown error in rollbackImport']
    };
  }

  /**
   * Extract matching keys from nested object structure
   * Used to create values structure from imported data based on template fields
   */
  private extractMatchingKeys(
    obj: any[] | Record<string, any> | null,
    keysToExtract: string | string[],
    result: Record<string, any> = {}
  ): Record<string, any> {
    const keys = Array.isArray(keysToExtract) ? keysToExtract : [keysToExtract];

    const normalize = (str: string) => str.replace(/[^a-zA-Z0-9]/g, "").toLowerCase();
    const normalizedKeys = keys.map(normalize);

    if (Array.isArray(obj)) {
      obj.forEach((item) => this.extractMatchingKeys(item, keysToExtract, result));
    } else if (typeof obj === "object" && obj !== null) {
      for (const key in obj) {
        const normalizedKey = normalize(key);
        const index = normalizedKeys.indexOf(normalizedKey);

        if (index !== -1) {
          result[keys[index]] = obj[key]; // Use clean key from input array
        }

        this.extractMatchingKeys(obj[key], keysToExtract, result);
      }
    }

    return result;
  }

  /**
   * Convert flattened row data to values structure format based on template
   * Creates structure with section names as keys and their fields as nested objects
   * Handles grid fields as arrays and regular fields as flat properties
   * Stores the result as a JSON string in the 'values' property
   */
  private convertToValuesStructure(
    flattenedRow: any,
    configurationFields: any[]
  ): Record<string, any> {
    console.log('🚀 DEBUG: convertToValuesStructure CALLED!');
    console.log('🔍 DEBUG convertToValuesStructure - Input flattenedRow:', JSON.stringify(flattenedRow, null, 2));
    console.log('🔍 DEBUG convertToValuesStructure - Input configurationFields:', JSON.stringify(configurationFields, null, 2));

    if (!configurationFields || !Array.isArray(configurationFields)) {
      console.log('❌ DEBUG: No configuration fields provided, returning flattenedRow');
      return flattenedRow;
    }

    const valuesStructure: Record<string, any> = {};
    console.log('🏗️ DEBUG: Starting to build valuesStructure');

    // Process each step in the template
    configurationFields.forEach((step: any, stepIndex: number) => {
      console.log(`📋 DEBUG: Processing step ${stepIndex + 1}: ${step.name}`);
      if (step.sections && Array.isArray(step.sections)) {
        step.sections.forEach((section: any, sectionIndex: number) => {
          console.log(`📂 DEBUG: Processing section ${sectionIndex + 1}: ${section.name}`);
          if (section.fields && Array.isArray(section.fields)) {
            const sectionKey = this.convertToCamelCase(section.name || 'section');
            console.log(`🔑 DEBUG: Section key converted to: ${sectionKey}`);
            const sectionData: Record<string, any> = {};

            section.fields.forEach((field: any, fieldIndex: number) => {
              const fieldKey = this.convertToCamelCase(field.label);
              console.log(`🏷️ DEBUG: Processing field ${fieldIndex + 1}: "${field.label}" -> "${fieldKey}"`);

              if (field.field_type === 'grid') {
                // Handle grid fields - store as arrays
                const gridData = this.extractGridData(flattenedRow, field);
                console.log(`📊 DEBUG: Grid data for ${fieldKey}:`, gridData);
                if (gridData.length > 0) {
                  sectionData[fieldKey] = gridData;
                  console.log(`✅ DEBUG: Added grid data for ${fieldKey}`);
                }
              } else {
                // Handle regular fields
                const fieldValue = this.extractFieldValue(flattenedRow, field);
                console.log(`🔍 DEBUG: Field value for "${field.label}" (${fieldKey}):`, fieldValue);
                if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
                  sectionData[fieldKey] = fieldValue;
                  console.log(`✅ DEBUG: Added field ${fieldKey} = ${fieldValue}`);
                } else {
                  console.log(`❌ DEBUG: Skipped field ${fieldKey} (empty/null/undefined)`);
                }
              }
            });

            console.log(`📦 DEBUG: Section data for ${sectionKey}:`, JSON.stringify(sectionData, null, 2));
            // Add section data to values structure if it has content
            if (Object.keys(sectionData).length > 0) {
              valuesStructure[sectionKey] = sectionData;
              console.log(`✅ DEBUG: Added section ${sectionKey} to valuesStructure`);
            } else {
              console.log(`❌ DEBUG: Skipped empty section ${sectionKey}`);
            }
          }
        });
      }
    });

    console.log(`🏁 DEBUG: Final valuesStructure:`, JSON.stringify(valuesStructure, null, 2));

    // Always include all original fields as fallback to ensure no data is lost
    const result: Record<string, any> = {};

    // First, add all original fields (preserving all Excel data)
    Object.keys(flattenedRow).forEach(key => {
      if (key !== '__rowNumber') { // Exclude internal tracking fields
        result[key] = flattenedRow[key];
        console.log(`📋 DEBUG: Preserved original field: ${key} = ${flattenedRow[key]}`);
      }
    });

    // Then, overlay structured data if available (template-based organization)
    if (Object.keys(valuesStructure).length > 0) {
      Object.assign(result, valuesStructure);
      console.log(`✅ DEBUG: Merged structured data with ${Object.keys(valuesStructure).length} sections`);
    }

    console.log(`🎯 DEBUG: Final result with ${Object.keys(result).length} total fields:`, JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * Extract grid data from flattened row based on grid field configuration
   * For now, returns empty array - can be enhanced based on Excel grid data format
   */
  private extractGridData(flattenedRow: any, gridField: any): any[] {
    // Grid data handling can be implemented based on how grid data is provided in Excel
    // For example, if grid data comes as multiple rows or in a specific format

    // Check if there's any grid-related data in the flattened row
    const gridKey = this.convertToCamelCase(gridField.label);
    if (flattenedRow[gridKey] && Array.isArray(flattenedRow[gridKey])) {
      return flattenedRow[gridKey];
    }

    // For now, return empty array
    return [];
  }

  /**
   * Extract field value from flattened row based on field configuration
   */
  private extractFieldValue(flattenedRow: any, field: any): any {
    // Try to find the field value using different matching strategies
    const fieldLabel = field.label;
    const fieldId = field.id;

    console.log(`🔍 DEBUG extractFieldValue: Looking for field "${fieldLabel}" (id: ${fieldId})`);
    console.log(`🔍 DEBUG extractFieldValue: Available row keys:`, Object.keys(flattenedRow));

    // Direct match by label
    if (flattenedRow[fieldLabel] !== undefined) {
      console.log(`✅ DEBUG extractFieldValue: Found direct match by label: ${fieldLabel} = ${flattenedRow[fieldLabel]}`);
      return flattenedRow[fieldLabel];
    }

    // Direct match by id
    if (flattenedRow[fieldId] !== undefined) {
      console.log(`✅ DEBUG extractFieldValue: Found direct match by id: ${fieldId} = ${flattenedRow[fieldId]}`);
      return flattenedRow[fieldId];
    }

    // Try camelCase conversion of field label
    const camelCaseLabel = this.convertToCamelCase(fieldLabel);
    if (flattenedRow[camelCaseLabel] !== undefined) {
      console.log(`✅ DEBUG extractFieldValue: Found camelCase match: ${camelCaseLabel} = ${flattenedRow[camelCaseLabel]}`);
      return flattenedRow[camelCaseLabel];
    }

    // Try case-insensitive matching
    const rowKeys = Object.keys(flattenedRow);
    const matchingKey = rowKeys.find(key =>
      key.toLowerCase() === fieldLabel.toLowerCase() ||
      key.toLowerCase() === fieldId.toLowerCase() ||
      key.toLowerCase() === camelCaseLabel.toLowerCase()
    );

    if (matchingKey && flattenedRow[matchingKey] !== undefined) {
      console.log(`✅ DEBUG extractFieldValue: Found case-insensitive match: ${matchingKey} = ${flattenedRow[matchingKey]}`);
      return flattenedRow[matchingKey];
    }

    // Use extractMatchingKeys for flexible matching
    const extracted = this.extractMatchingKeys(flattenedRow, [fieldLabel, fieldId, camelCaseLabel]);
    const keys = Object.keys(extracted);
    if (keys.length > 0) {
      console.log(`✅ DEBUG extractFieldValue: Found flexible match: ${keys[0]} = ${extracted[keys[0]]}`);
      return extracted[keys[0]];
    }

    console.log(`❌ DEBUG extractFieldValue: No match found for field "${fieldLabel}"`);
    return undefined;
  }

  /**
   * Get template fields for a specific sub-collection by key
   * @param templateKey - The key to search for (e.g., 'cpt-code', 'diagnosis-code', etc.)
   * @param orgId - Organization ID
   * @returns Parsed template fields or empty array if not found
   */
  private async getTemplateFieldsByKey(templateKey: string, orgId: string): Promise<any[]> {
    try {
      console.log(`🔍 DEBUG: Getting template fields for key: ${templateKey}, orgId: ${orgId}`);

      const templateCollection = await this.mongoConnectionService.getCollectionByOrgId("templates", "asp");
      const template = await templateCollection.findOne({
        key: templateKey,
        isActive: true
      });

      if (!template) {
        console.log(`❌ DEBUG: No template found for key: ${templateKey}`);
        return [];
      }

      console.log(`✅ DEBUG: Template found for key: ${templateKey}`, JSON.stringify(template, null, 2));

      // Parse the fields if it's a JSON string
      let templateFields = template.fields;
      if (typeof templateFields === 'string') {
        try {
          templateFields = JSON.parse(templateFields);
          console.log(`✅ DEBUG: Successfully parsed template fields for ${templateKey}`);
        } catch (parseError) {
          console.error(`❌ DEBUG: Failed to parse template fields for ${templateKey}:`, parseError);
          return [];
        }
      }

      const fields = Array.isArray(templateFields) ? templateFields : [];
      console.log(`📋 DEBUG: Extracted ${fields.length} template fields for ${templateKey}`);
      return fields;

    } catch (error) {
      console.error(`❌ DEBUG: Error getting template fields for ${templateKey}:`, error);
      return [];
    }
  }

  /**
   * Convert a label string to camelCase while preserving original case
   * Example: "Add CPT Dictionary" -> "addCPTDictionary"
   * Example: "Create Action & Status Code" -> "createActionStatusCode"
   */
  private convertToCamelCase(label: string): string {
    return label
      // First, remove special characters and replace with spaces
      .replace(/[&\-_\(\)\/\\]/g, ' ')
      // Remove extra spaces
      .replace(/\s+/g, ' ')
      .trim()
      .split(' ')
      .map((word, index) => {
        if (index === 0) {
          // First word: lowercase only the first character, preserve rest
          return word.charAt(0).toLowerCase() + word.slice(1);
        }
        // Other words: uppercase first character, preserve rest
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join('');
  }
}