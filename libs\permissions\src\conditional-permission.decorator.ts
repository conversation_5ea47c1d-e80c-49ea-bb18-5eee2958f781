import { SetMetadata } from '@nestjs/common';

export interface ConditionalPermission {
  condition: (input: any) => boolean;
  permission: {
    module: string;
    subModule: string;
    permission: string;
  };
}

export const CONDITIONAL_PERMISSION_KEY = 'conditional_permissions';

/**
 * Decorator for conditional permissions based on input parameters
 * @param permissions Array of conditional permissions
 */
export const RequireConditionalPermission = (...permissions: ConditionalPermission[]) =>
  SetMetadata(CONDITIONAL_PERMISSION_KEY, permissions);

// Helper function to create conditional permissions
export const createConditionalPermission = (
  condition: (input: any) => boolean,
  module: string,
  subModule: string,
  permission: string
): ConditionalPermission => ({
  condition,
  permission: { module, subModule, permission }
});

// Predefined conditions for common scenarios
export const Conditions = {
  // For UserType enum (MAIN_CLIENT, SUB_CLIENT, etc.)
  isMainClient: (input: any) => input.type === 'MAIN_CLIENT',
  isSubClient: (input: any) => input.type === 'SUB_CLIENT',

  // For OrganisationType enum (MAIN_ORGANISATION, SUB_ORGANISATION)
  isMainOrganisation: (input: any) => input.type === 'MAIN_ORGANISATION',
  isSubOrganisation: (input: any) => input.type === 'SUB_ORGANISATION',

  // Generic condition creator for any type check
  hasType: (expectedType: string) => (input: any) => input.type === expectedType,
};
