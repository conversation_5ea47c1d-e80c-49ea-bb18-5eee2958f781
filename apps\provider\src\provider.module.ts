import { Module } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import {
  ApolloFederationDriver,
  ApolloFederationDriverConfig,
} from '@nestjs/apollo';
import { ProviderController } from './provider.controller';
import { ProviderService } from './services/provider.service';
import { ProviderResolver } from './resolvers/provider.resolver';
import { ProviderTicket, ProviderSchema, CounterSchema } from './entities/provider.entity';
import { DatabaseModule, MongoConnectionService } from '@app/db';
import { BaseResponseResolver } from './resolvers/base-response.resolver';
import { ProviderEmailTicketResolver } from './resolvers/provider_email_ticket.resolver';
import { ProviderEmailTicketService } from './services/provider_email_ticket.service';
import { ProviderHistoryService } from './services/provider_history.service';
import { ProviderHistoryResolver } from './resolvers/provider_history.resolver';
import { ProviderHistorySchema } from './entities/provider_history';
import { EmailTicketSchema } from './entities/provider_email_ticket';
import { EmailModule } from '@app/email';
import { NotificationModule } from '@app/notification';
import {
  PermissionsGuard,
  UserProcessAssignment,
  UserProcessAssignmentSchema,
  Role,
  RoleSchema,
  PermissionsModule,
  OrganisationRole,
  OrganisationRoleSchema,
  RedisCacheService
} from '@app/permissions';


import { Reflector, APP_GUARD } from '@nestjs/core';

const permissionsGuardFactory = {
  provide: PermissionsGuard,
  useFactory: (
    reflector: Reflector,
    UserProcessAssignmentModel: any,
    RoleModel: any,
    OrganisationRoleModel: any,
    cacheService: RedisCacheService,
    mongoConnectionService: MongoConnectionService
  ) => {


    return new PermissionsGuard(
      reflector,
      async (userId, orgId, subOrgId, processId) => {
        console.log(`🔍 [getAssignment-provider] Query params:`, { userId, orgId, subOrgId, processId });

        const result = await UserProcessAssignmentModel.findOne({
          userId,
          orgId,
          subOrganisationId: subOrgId,
          processId,
        }).populate('roleId', 'name permissions key category');

        console.log(`🔍 [getAssignment-provider] Query result:`, result ? {
          _id: result._id,
          userId: result.userId,
          roleId: result.roleId,
          orgId: result.orgId,
          subOrganisationId: result.subOrganisationId,
          processId: result.processId
        } : 'No assignment found');

        return result;
      },
      {
        getSystemRole: async (roleId) => {

          const role = await RoleModel.findById(roleId);

          return role;
        },
        getOrganisationRole: async (roleId) => {

          const orgRole = await OrganisationRoleModel.findById(roleId);

          return orgRole;
        },
      },
      process.env.JWT_SECRET ?? 'fallback-secret',
      cacheService,
      mongoConnectionService
    );
  },
  inject: [
    Reflector,
    getModelToken(UserProcessAssignment.name),
    getModelToken(Role.name),
    getModelToken(OrganisationRole.name),
    RedisCacheService,
    MongoConnectionService,
  ],
};

@Module({
  imports: [
    DatabaseModule,
    EmailModule,
    NotificationModule,
    PermissionsModule,
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/asp', {
      connectionFactory: (connection) => {
        connection.on('connected', () => {
          console.log('[Mongoose] ✅ Connected to MongoDB');
        });
        connection.on('error', (error) => {
          console.error('[Mongoose] ❌ Connection error:', error);
        });
        connection.on('disconnected', () => {
          console.log('[Mongoose] ⚠️ Disconnected from MongoDB');
        });
        return connection;
      },
    }),
    MongooseModule.forFeature([
      { name: ProviderTicket.name, schema: ProviderSchema },
      { name: 'EmailTicket', schema: EmailTicketSchema },
      { name: 'ProviderHistory', schema: ProviderHistorySchema },
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },
      { name: 'Counter', schema: CounterSchema }
    ]),
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req, res }) => {
        // Create base context that will be enhanced by guards
        const context = {
          req,
          res,
          query: req?.body?.query,
          orgId: req?.headers['orgid'],
          userId: req?.headers['userid'],
          subOrgId: req?.headers['suborgid'],
          processId: req?.headers['processid'],
          roleName: req?.headers['rolename'],
        };

        return context;
      }
    }),
  ],
  controllers: [ProviderController],
  providers: [
    permissionsGuardFactory,
    ProviderService,
    ProviderResolver,
    BaseResponseResolver,
    ProviderEmailTicketResolver,
    ProviderEmailTicketService,
    ProviderHistoryService,
    ProviderHistoryResolver,
    {
      provide: APP_GUARD,
      useExisting: PermissionsGuard,
    },
  ],
})
export class ProviderModule {
  constructor() {
    console.log('[ProviderModule] 🚀 Module loaded');
  }
}
