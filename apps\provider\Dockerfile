FROM node:22-alpine AS builder

WORKDIR /app

COPY package.json ./

RUN npm install --legacy-peer-deps

COPY . .

RUN npx nest build provider

# Stage 2:
FROM node:22-alpine AS runtime

WORKDIR /app

COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package*.json /app/
COPY --from=builder /app/apps/provider /app/apps/provider

RUN npm install --legacy-peer-deps

EXPOSE 4003


CMD ["node", "dist/apps/provider/main.js"]