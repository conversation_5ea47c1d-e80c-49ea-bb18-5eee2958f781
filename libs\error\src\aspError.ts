// error.ts
import { GraphQLError, GraphQLFormattedError } from 'graphql';
import { ErrorType, ResponseCode, HttpStatus } from './errorCodes';
import * as dotenv from 'dotenv';
dotenv.config();

const isDev = process.env.NODE_ENV === 'development';

type FormattedError = GraphQLFormattedError & {
  code: HttpStatus;
  type: string;
  message?: string;
  error?: string;
};


type FormattedSuccess = {
  code: HttpStatus;
  message: string;
  type: string;
  data?: Record<string, unknown>;
};

class GQLError extends GraphQLError {
  public code: HttpStatus;
  public type: ErrorType;
  public internalError?: string;

  constructor(
    code: HttpStatus,
    error: ResponseCode,
    type: ErrorType,
    message?: string,
  ) {
    super(isDev ? (message ?? 'An unexpected error occurred.') : 'An unexpected error occurred.', {
      extensions: {
        code,
        message: isDev ? message : undefined,
        type: ErrorType[type],
        error: isDev ? error : undefined,
      },
    });

    this.code = code;
    this.type = type;
    this.internalError = error;

    if (!isDev && error) {
      console.error('[GQLError]', { code, type, error });
    }
  }

  toJSON(): FormattedError {
    return {
      code: this.code,
      type: ErrorType[this.type],
      message: this.message,
      ...(isDev && this.internalError ? { error: this.internalError } : {}),
    };
  }
}

class Success {
  public code: HttpStatus;
  public message: ResponseCode;
  public type: string;
  public data?: Record<string, unknown>;

  constructor(
    code: HttpStatus,
    message: ResponseCode,
    type?: ErrorType | string,
    data?: Record<string, unknown>,
  ) {
    this.code = code;
    this.message = message;
    if (typeof type === 'string') {
      this.type = type;
    } else if (type !== undefined) {
      this.type = ErrorType[type];
    } else {
      this.type = 'GENERAL';
    }
    this.data = data;
  }

  toJSON(): FormattedSuccess {
    return {
      message: this.message,
      code: this.code,
      type: this.type,
      ...(this.data ? { data: this.data } : {}),
    };
  }
}

export { GQLError as Error, FormattedError, Success, FormattedSuccess };
