export enum NotificationType {
  IMPORT_COMPLETED = 1,
  IMPORT_FAILED = 2,
  EXPORT_COMPLETED = 3,
  EXPORT_FAILED = 4,
  TASK_STARTED = 5,
  TASK_PROGRESS = 6,
  SYSTEM_ALERT = 7,
  USER_MENTION = 8,
  CUSTOM = 9,
  SOURCE = 11,
  TICKET_ASSIGNMENT = 10,
  ALL_TICKETS = 12,

}


export enum NotificationChannel {
  WEBSOCKET = 'WEBSOCKET',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PUSH = 'PUSH',
  IN_APP = 'IN_APP',
  SLACK = 'SLACK',
  TEAMS = 'TEAMS'
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum NotificationStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  READ = 'READ'
}
