import { ErrorType, Error, ResponseCode, HttpStatus } from '@app/error';
import { IncomingHttpHeaders } from 'http';
import * as jwt from 'jsonwebtoken';
import { BEARER_TOKEN } from '../../domain/config/vars';

function getBearerToken(headers: IncomingHttpHeaders) {
  const bearerToken = Object.keys(headers).find(
    (header) => header.toLowerCase() === BEARER_TOKEN,
  );
  return bearerToken ? headers[bearerToken] : undefined;
}

// Function to decode and validate the token
export async function validateRequest(token: string) {
  try {
    // Decode the token without verifying (to get the `kid`)
    const decodedToken = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');

    if (!decodedToken) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOKEN_INVALID,
        ErrorType.LOGIN,
      );
    }

    // Extract and return the username
    const userId = typeof decodedToken === 'object' ? decodedToken['userId'] : null;
    const orgId = typeof decodedToken === 'object' ? decodedToken['orgId'] : null;  
    if (!userId) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.INTERNAL_ERROR,
        ErrorType.LOGIN,
      );
    }

    return {
      userId,
      orgId
      
    };
  } catch (error) {
    console.log(error);
    throw new Error(
      HttpStatus.UNAUTHORIZED,
      ResponseCode.INTERNAL_ERROR,
      ErrorType.LOGIN,
    );
  }
}

export async function decodeToken(bearerToken: string) {
  try {
    if (!bearerToken)
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOKEN_MISSING,
        ErrorType.LOGIN,
      );

    // Decode the token without verifying (to get the `kid`)
    const decodedToken = jwt.decode(bearerToken, { complete: true });
    if (!decodedToken) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.TOKEN_INVALID,
        ErrorType.LOGIN,
      );
    }
    const { payload } = decodedToken;

    // Extract and return the username
    const userId = payload['userId'];

    if (!userId) {
      throw new Error(
        HttpStatus.UNAUTHORIZED,
        ResponseCode.INTERNAL_ERROR,
        ErrorType.LOGIN,
      );
    }

    return {
      userId,
      

    };
  } catch (error) {
    console.log('the error isg iven as:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(
      HttpStatus.UNAUTHORIZED,
      ResponseCode.INTERNAL_ERROR,
      ErrorType.LOGIN,
    );
  }
}
