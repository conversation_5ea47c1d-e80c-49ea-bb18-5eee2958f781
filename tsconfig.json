{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "paths": {"@app/audit": ["libs/audit/src"], "@app/audit/*": ["libs/audit/src/*"], "@app/common": ["libs/common/src"], "@app/common/*": ["libs/common/src/*"], "@app/config": ["libs/config/src"], "@app/config/*": ["libs/config/src/*"], "@app/db": ["libs/db/src"], "@app/db/*": ["libs/db/src/*"], "@app/email": ["libs/email/src"], "@app/email/*": ["libs/email/src/*"], "@app/error": ["libs/error/src"], "@app/error/*": ["libs/error/src/*"], "@app/functions": ["libs/functions/src"], "@app/functions/*": ["libs/functions/src/*"], "@app/notification": ["libs/notification/src"], "@app/notification/*": ["libs/notification/src/*"], "@app/permissions": ["libs/permissions/src/index.ts"]}}}