import { Module } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { GraphQLModule } from '@nestjs/graphql';
import {
  ApolloFederationDriver,
  ApolloFederationDriverConfig,
} from '@nestjs/apollo';
import { IdentityController } from './identity.controller';
import { IdentityService } from './identity.service';
import { IdentityResolver } from './identity.resolver';
import { UserResolver } from './resolvers/user.resolver';
import { ProcessResolver } from './resolvers/process.resolver';
import { DatabaseModule, MongoConnectionService } from '@app/db';
import { OrganisationSchema, Organisation } from './entities/client.entities';
import { User, UserSchema } from './entities/user.entity';
import { UserModule } from './user.module';
import { ProcessModule } from './process.module';
import { AgentResolver } from './agent.resolver';
import { UserProcessAssignment, UserProcessAssignmentSchema } from './entities/user-process-assignment.entity';
import { Role, RoleSchema } from './entities/role.entity';
import {
  PermissionsGuard,
  PermissionsModule,
  RedisCacheService
} from '@app/permissions';
import { Reflector, APP_GUARD } from '@nestjs/core';
import { ProcessRoleHierarchy, ProcessRoleHierarchySchema } from './entities/process-role-hierarchy.entity';
import { OrganisationProcessMapping, OrganisationProcessMappingSchema } from './entities/organisation-process-mapping.entity';
import { Process, ProcessSchema } from './entities/process.entity';
import { OrganisationProcessMappingResolver } from './resolvers/organisation-process-mapping.resolver';
import { OrganisationProcessMappingService } from './services/organisation-process-mapping.service';
import { OrganisationSettings, OrganisationSettingsSchema } from './entities/organisation-settings.entity';
import { OrganisationSettingsService } from './services/organisation-settings.service';
import { OrganisationSettingsResolver } from './resolvers/organisation-settings.resolver';
import { OrganisationRole, OrganisationRoleSchema } from './entities/organisation-role.entity';
import { TaskQueue, TaskQueueSchema } from './entities/task.entity';
import { ExportService } from './services/export.service';
import { GcsService } from './services/gcs.service';
import { ExportResolver } from './resolvers/export.resolver';
import { BullModule } from '@nestjs/bullmq';
// import { ExportProcessor } from './services/export.processor';
import { ImportResolver } from './resolvers/import.resolver';
import { ImportService } from './services/import.service';
import { ImportConfigurationService } from './services/import-configuration.service';
import { ImportConfigurationResolver } from './resolvers/import-configuration.resolver';
import { ImportConfiguration, ImportConfigurationSchema } from './entities/import-configuration.entity';
import { NotificationModule } from '@app/notification';
import { NotificationGateway } from './gateways/notification.gateway';
import { AuditHelperService } from '@app/audit';
import { EmailModule } from '@app/email';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TaskProcessor } from './services/task.processor';
import { AuditModule } from 'libs/audit/audit.module';
import { ModuleUrl, ModuleUrlSchema } from './entities/module-url.entity';
import { ModuleUrlService } from './services/module-url.service';
import { ModuleUrlResolver } from './resolvers/module-url.resolver';
import { ModuleUrlSeeder } from './seeds/module-url.seed';
import { TaskExpirationService } from './services/task-expiration.service';
const enhancedPermissionsGuardFactory = {
  provide: PermissionsGuard,
  useFactory: (
    reflector: Reflector,
    UserProcessAssignmentModel: any,
    RoleModel: any,
    OrganisationRoleModel: any,
    cacheService: RedisCacheService,
    mongoConnectionService: MongoConnectionService
  ) => {
    return new PermissionsGuard(
      reflector,
      async (userId, orgId, subOrgId, processId) => {
        console.log(`🔍 [getAssignment] Querying for userId: ${userId}, orgId: ${orgId}, subOrgId: ${subOrgId}, processId: ${processId}`);

        const result = await UserProcessAssignmentModel.findOne({
          userId,
          organisationId: orgId,  // ✅ Fixed: use correct field name
          subOrganisationId: subOrgId,
          processId,
        }).populate('roleId', 'name permissions key category');

        console.log(`🔍 [getAssignment] Query result:`, result ? 'Found assignment' : 'No assignment found');
        return result;
      },
      {
        getSystemRole: async (roleId) => {
          const role = await RoleModel.findById(roleId);
          return role;
        },
        getOrganisationRole: async (roleId) => {
          const role = await OrganisationRoleModel.findById(roleId);
          return role;
        },
      },
      process.env.JWT_SECRET ?? 'fallback-secret',
      cacheService,
      mongoConnectionService
    );
  },
  inject: [
    Reflector,
    getModelToken(UserProcessAssignment.name),
    getModelToken(Role.name),
    getModelToken(OrganisationRole.name),
    RedisCacheService,
    MongoConnectionService,
  ],
};

@Module({
  imports: [
    DatabaseModule,
    PermissionsModule,
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/asp', {
      connectionFactory: (connection) => {
        connection.on('connected', () => {
          console.log('Mongoose connected to MongoDB');
        });
        connection.on('error', (error: Error) => {
          console.error('Mongoose connection error:', error);
        });
        connection.on('disconnected', () => {
          console.log('Mongoose disconnected from MongoDB');
        });
        return connection;
      },
    }),
    MongooseModule.forFeature([
      { name: Organisation.name, schema: OrganisationSchema },
      { name: User.name, schema: UserSchema },
      { name: UserProcessAssignment.name, schema: UserProcessAssignmentSchema },
      { name: Role.name, schema: RoleSchema },
      { name: OrganisationRole.name, schema: OrganisationRoleSchema },
      { name: ProcessRoleHierarchy.name, schema: ProcessRoleHierarchySchema },
      { name: OrganisationProcessMapping.name, schema: OrganisationProcessMappingSchema },
      { name: Process.name, schema: ProcessSchema },
      { name: OrganisationSettings.name, schema: OrganisationSettingsSchema },
      { name: TaskQueue.name, schema: TaskQueueSchema },
      { name: ImportConfiguration.name, schema: ImportConfigurationSchema },
      { name: ModuleUrl.name, schema: ModuleUrlSchema },
    ]),
    // Register BullMQ queue for import/export jobs
    BullModule.registerQueue({
      name: 'task-queue{identity}',
      connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: +(process.env.REDIS_PORT || 6379),
      },
    }),
    EventEmitterModule.forRoot(),
    NotificationModule,
    EmailModule,
    AuditModule,
    GraphQLModule.forRoot<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      autoSchemaFile: {
        federation: 2,
      },
      context: async ({ req, res }) => {
        // Create base context that will be enhanced by guards
        const context = {
          req,
          res,
          query: req?.body?.query,
          orgId: req?.headers['orgid'],
          userId: req?.headers['userid'],
          subOrgId: req?.headers['suborgid'],
          processId: req?.headers['processid'],
          roleName: req?.headers['rolename'],
        };

        return context;
      },
      playground: process.env.NODE_ENV !== 'production',
      introspection: process.env.NODE_ENV !== 'production',
    }),
    UserModule,
    ProcessModule,
  ],
  controllers: [IdentityController],
  providers: [
    IdentityService,
    IdentityResolver,
    UserResolver,
    ProcessResolver,
    AgentResolver,
    TaskExpirationService,
    OrganisationProcessMappingResolver,
    OrganisationProcessMappingService,
    enhancedPermissionsGuardFactory,
    {
      provide: APP_GUARD,
      useExisting: PermissionsGuard,
    },
    OrganisationSettingsService,
    OrganisationSettingsResolver,
    ExportService,
    GcsService,
    ExportResolver,
    ImportService,
    ImportResolver,
    ImportConfigurationService,
    ImportConfigurationResolver,
    NotificationGateway,
    TaskProcessor,
    ModuleUrlService,
    ModuleUrlResolver,
    ModuleUrlSeeder,
    AuditHelperService,
  ],
})
export class IdentityModule {}