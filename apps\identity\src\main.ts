import { NestFactory } from '@nestjs/core';
import { IdentityModule } from './identity.module';
import { Response } from 'express';
import mongoose from 'mongoose';
import { RoleSchema, seedDefaultRoles } from './entities/role.entity';
import { permissionTemplate } from 'roles-permission';
// Bull Board imports
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Queue } from 'bullmq';

async function bootstrap() {
  const app = await NestFactory.create(IdentityModule);
  app.enableCors({
    origin: '*',
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
  });
 
  // Add simple health check endpoint
  app.use('/health', (_, res: Response) => {
    res.status(200).json({ status: 'ok', uptime: process.uptime() });
  });

  // Bull Board setup for live queue monitoring
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/admin/queues');
  const exportQueue = new Queue('export-queue', {
    connection: {
      host: process.env.REDIS_HOST || 'localhost',
      port: +(process.env.REDIS_PORT || 6379),
    },
  });
  createBullBoard({
    queues: [new BullMQAdapter(exportQueue)],
    serverAdapter,
  });
  // Mount Bull Board on the same Express instance as Nest
  const expressApp = app.getHttpAdapter().getInstance();
  expressApp.use('/admin/queues', serverAdapter.getRouter());

  // Connect to MongoDB directly for seeding (if not already connected)
  // await mongoose.connect('mongodb+srv://karthikyoki999:<EMAIL>/asp');
  // const RoleModel = mongoose.model('Role', RoleSchema);
  // await seedDefaultRoles(RoleModel, permissionTemplate);
  await app.listen(process.env.PORT ?? 4001);
}
bootstrap();
