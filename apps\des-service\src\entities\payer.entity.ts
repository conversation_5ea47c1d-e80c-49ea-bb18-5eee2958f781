import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { GraphQLJSON } from 'graphql-type-json';



@ObjectType()
@Schema({
    timestamps: true,
    collection: 'payers',
    strict: false,

})
@Schema({ timestamps: true, strict: false })
export class Payer extends Document {
    @Field(() => ID)
    declare _id: Types.ObjectId;


    @Field(() => ID, { nullable: true })
    @Prop({ index: true })
    templateId?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @Prop({ type: Object })
    values: Record<string, any>;
}

export const PayerSchema = SchemaFactory.createForClass(Payer);

