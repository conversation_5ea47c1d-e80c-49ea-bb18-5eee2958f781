import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CptCode } from './entities/cpt-code.entity';
import {
  CreateCptCodeInput,
  UpdateCptCodeInput,
  PaginateCptCodeArgs,
  PaginatedCptCodeResponse,
} from './dto/cpt-code.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { Specialty } from './entities/specialty.entity';
import { instanceToPlain } from 'class-transformer';

type CptCodeDocument = CptCode & Document;
type SpecialtyDocument = Specialty & Document;

@Injectable()
export class CptCodeService {
  constructor(
    @InjectModel(CptCode.name)
    private cptCodeModel: Model<CptCodeDocument>,
    @InjectModel(Specialty.name)
    private specialtyModel: Model<SpecialtyDocument>,
  ) {}

  private transformDocument(doc: any): CptCodeDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        'Cannot transform null document',
      );
    }
    
    const plainDoc = {
      _id: doc._id || new Types.ObjectId(),
      code: doc.code || '',
      values: doc.values || {},
      specialtyId: doc.specialtyId ? new Types.ObjectId(doc.specialtyId.toString()) : new Types.ObjectId(),
      templateId: doc.templateId || '',
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || '',
      updatedBy: doc.updatedBy || '',
    };
    
    return plainDoc as CptCodeDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { code: searchRegex },
      { 'values.description': searchRegex },
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ specialtyId: objectId });
      orConditions.push({ templateId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ? 
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } : 
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }

  async findAll(input: PaginateCptCodeArgs): Promise<PaginatedCptCodeResponse> {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      // Always include _id in projection
      projection._id = 1;

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        Object.assign(projection, selectedFields);
        
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v'];
        projection = {
          _id: 1,
          code: 1,
          values: 1,
          specialtyId: 1,
          templateId: 1,
          isActive: 1,
          createdAt: 1,
          updatedAt: 1,
          createdBy: 1,
          updatedBy: 1
        };

        // Search in all available fields (except excluded ones)
        searchableFields = Object.keys(projection).filter(field => !excluded.includes(field));
      }

      // Search Handling
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }
      
      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.cptCodeModel.countDocuments(query);
      // === Fetch Data ===
      const cptCodes = await this.cptCodeModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: cptCodes,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to fetch CPT codes: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string): Promise<CptCode> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, 'Invalid ID format');
      }
      const doc = await this.cptCodeModel.findById(id)
        .populate('specialtyId')
        .lean()
        .exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT code not found');
      }
      return this.transformDocument(doc);
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to find CPT code: ${(err as globalThis.Error).message}`);
    }
  }

  async findByCode(code: string): Promise<CptCode | null> {
    try {
      const doc = await this.cptCodeModel.findOne({ code }).lean().exec();
      return doc ? this.transformDocument(doc) : null;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.findByCode:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to find CPT code by code: ${(err as globalThis.Error).message}`);
    }
  }

  async create(input: CreateCptCodeInput): Promise<Success> {
    try {
      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      // Handle flattenedValues if it's a string
      if (typeof input.flattenedValues === 'string') {
        try {
          const parsedValues = JSON.parse(input.flattenedValues);
          Object.assign(processedFlattenedValues, parsedValues);
        } catch (error) {
          console.error('Error parsing flattenedValues:', error);
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.CPT,
            'Invalid flattenedValues format'
          );
        }
      } else if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      // Handle values if it's a string
      let processedValues: Record<string, any> = {};
      if (typeof input.values === 'string') {
        try {
          processedValues = JSON.parse(input.values);
        } catch (error) {
          console.error('Error parsing values:', error);
          throw new Error(
            HttpStatus.BAD_REQUEST,
            ResponseCode.INVALID_PARAMETERS,
            ErrorType.CPT,
            'Invalid values format'
          );
        }
      } else if (typeof input.values === 'object') {
        processedValues = input.values;
      }

      // Check for existing CPT code with same code and specialtyId
      const existing = await this.cptCodeModel.findOne({
        code: input.code,
        specialtyId: input.specialtyId
      }).lean().exec();

      if (existing) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.ALREADY_EXISTS,
          ErrorType.CPT,
          'A CPT code with this code already exists in the specified specialty.'
        );
      }

      const cptCodeData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: processedValues,
      };

      delete cptCodeData.flattenedValues;

      const newCptCode = new this.cptCodeModel(cptCodeData);
      const savedDoc = await newCptCode.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { cptCode: this.transformDocument(savedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.create:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to create CPT code: ${(err as globalThis.Error).message}`);
    }
  }

  async update(input: UpdateCptCodeInput): Promise<Success> {
    try {
      const { id, specialtyId,flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, 'Invalid CPT code ID format for update.');
      }

      const existingCptCode = await this.cptCodeModel.findById(id).lean().exec();
      if (!existingCptCode) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT code not found for update.');
      }

      if (specialtyId) {
        if (!Types.ObjectId.isValid(specialtyId)) {
          throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.CPT, `Invalid Specialty ID format: ${specialtyId}`);
        }
        const specialty = await this.specialtyModel.findById(specialtyId).lean().exec();
        if (!specialty) {
          throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, `Specialty not found with ID: ${specialtyId}`);
        }
      }

      const updatePayload: any = { ...updateData, updatedAt: new Date() };
      if (specialtyId) updatePayload.specialtyId = new Types.ObjectId(specialtyId);
      if (input.isActive !== undefined) updatePayload.isActive = input.isActive;

const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.cptCodeModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      // const updatedDoc = await this.cptCodeModel
      //   .findByIdAndUpdate(id, { $set: updatePayload }, { new: true })
      //   .populate('specialtyId')
      //   .lean()
      //   .exec();

      if (!updatedDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.CPT, 'CPT code not found during update process, or optimistic lock failed.');
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { cptCode: this.transformDocument(updatedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.update:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.CPT, `Failed to update CPT code: ${(err as globalThis.Error).message}`);
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.CPT,
          'Invalid ID format'
        );
      }

      const deletedDoc = await this.cptCodeModel
        .findByIdAndDelete(id)
        .lean()
        .exec();

      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.CPT,
          'CPT code not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.CPT,
        { message: 'CPT code deleted successfully.' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in CptCodeService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        `Failed to delete CPT code: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.cptCodeModel.countDocuments().exec();
    } catch (err) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.CPT,
        `Failed to count CPT codes: ${(err as globalThis.Error).message}`,
      );
    }
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'code',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'specialtyId',
      'templateId'
    ];
  }

  getSearchableFields(): string[] {
    return ['code', 'values.description'];
  }
} 