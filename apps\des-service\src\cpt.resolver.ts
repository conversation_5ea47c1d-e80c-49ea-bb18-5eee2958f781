import { Resolver, Query, Mutation, Args, Context, ID } from '@nestjs/graphql';
import { MasterCptService } from './cpt.service';
import { Cpt } from './entities/cpt.entity';
import {
  CreateCptInput,
  UpdateCptInput,
  PaginateCptArgs,
  PaginatedCptResponse,
  GetCptInput,
  SuccessResponse,
} from './dto/cpt.dto';
import { BaseResponse } from './dto/base.response.dto';
import { HttpStatus, ResponseCode, ErrorType, Success, Error } from '@app/error';
import * as jwt from 'jsonwebtoken';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { GraphQLJSON } from 'graphql-type-json';
import { RequirePermission } from '@app/permissions';

@Resolver(() => Cpt)
export class CptResolver {
  constructor(private readonly cptService: MasterCptService) {}

  @Query(() => PaginatedCptResponse,{ name: 'cpts', nullable: true })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptsWithPagination(
    @Args() args: PaginateCptArgs,
    @Context() context: any,
  ): Promise<PaginatedCptResponse> {
    try {
      const userId = context.req.user?.sub;
      const filters = args.filters || {};
      if (userId) {
        filters.createdBy = userId;
      }
      return await this.cptService.findAll({
        ...args,
        filters,
      });
    } catch (error) {
      console.error('Error in getCptsWithPagination:', error);
      throw error;
    }
  }

  @Query(() => BaseResponse, { name: 'cpt', nullable: true })
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptById(
    @Args('id') id: string,
    @Context() context: any,
  ){
    try {
      const cpt = await this.cptService.findById(id);
      return {
        code: 200,
        message: 'CPT fetched successfully',
        type: 'success',
        data: cpt
      };
    } catch (error) {
      console.error('Error in getCptById:', error);
      throw error;
    }
  }

  // @Query(() => BaseResponse, { name: 'cpt', nullable: true })
  // async getMasterCpt(@Args('id', { type: () => ID }) id: string): Promise<BaseResponse> {
  //   try {
  //     const cptEntity = await this.cptService.findById(id);
  //     return {
  //       code: HttpStatus.OK,
  //       message: 'CPT fetched successfully.',
  //       type: ErrorType.CPT.toString() + '_FETCH_SUCCESS',
  //       data: cptEntity ? (typeof cptEntity.toJSON === 'function' ? cptEntity.toJSON() : { ...cptEntity }) : null,
  //     };
  //   } catch (error) {
  //     console.error('Error fetching CPT by ID:', id, error);
  //     const statusCode = error.httpStatus || error.status || HttpStatus.INTERNAL_SERVER_ERROR;
  //     const message = error.message || 'Failed to fetch CPT.';
  //     const typeSuffix = (error.httpStatus || error.status) === HttpStatus.NOT_FOUND ? '_NOT_FOUND' : '_FETCH_ERROR';
  //     const errorTypeStr = error.errorType?.toString() || error.type?.toString() || ErrorType.CPT.toString();
  //     return {
  //       code: statusCode,
  //       message: message,
  //       type: errorTypeStr + typeSuffix,
  //       data: null,
  //     };
  //   }
  // }

  // @Query(() => BaseResponse)
  // async cpts(
  //   @Args() args: PaginateCptArgs
  // ): Promise<BaseResponse> {
  //   try {
  //     const paginatedData = await this.cptService.findAll(args);
  //     const items = paginatedData.items.map(item => 
  //       item ? (typeof item.toJSON === 'function' ? item.toJSON() : { ...item }) : null
  //     );
  //     return {
  //       code: HttpStatus.OK,
  //       message: 'CPTs fetched successfully.',
  //       type: ErrorType.CPT.toString() + '_FETCH_SUCCESS',
  //       data: { 
  //         items: items.filter(Boolean) as any, 
  //         pagination: paginatedData.pagination 
  //       } as any,
  //     };
  //   } catch (error) {
  //     console.error('Error fetching CPTs with args:', args, error);
  //     const statusCode = error.httpStatus || error.status || HttpStatus.INTERNAL_SERVER_ERROR;
  //     const message = error.message || 'Failed to fetch CPTs.';
  //     const errorTypeStr = error.errorType?.toString() || error.type?.toString() || ErrorType.CPT.toString();
  //     return {
  //       code: statusCode,
  //       message: message,
  //       type: errorTypeStr + '_FETCH_ERROR',
  //       data: null,
  //     };
  //   }
  // }

  @Query(() => Number)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async countCpts(
    @Context() context: any,
  ): Promise<number> {
    try {
      return await this.cptService.count();
    } catch (error) {
      console.error('Error in countCpts:', error);
      throw error;
    }
  }

  @Query(() => [String])
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptSortFields(): Promise<string[]> {
    return this.cptService.getAvailableSortFields();
  }

  @Query(() => [String])
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'View' })
  async getCptSearchFields(): Promise<string[]> {
    return this.cptService.getSearchableFields();
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Add' })
  async createCpt(
    @Args('input') input: CreateCptInput,
    @Context() context: any,
  ): Promise<SuccessResponse> {
    try {
      const userId = context.req.user?.sub;
      if (userId) {
        input.createdBy = userId;
      }
      const result = await this.cptService.create(input);
      return {
        code: 200,
        message: 'CPT created successfully',
        type: 'success',
        data: result
      };
    } catch (error) {
      console.error('Error in createCpt:', error);
      throw error;
    }
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Edit' })
  async updateCpt(
    @Args('input') input: UpdateCptInput,
    @Context() context: any,
  ): Promise<SuccessResponse> {
    try {
      const userId = context.req.user?.sub;
      if (userId) {
        input.updatedBy = userId;
      }
      const result = await this.cptService.update(input);
      return {
        code: 200,
        message: 'CPT updated successfully',
        type: 'success',
        data: result
      };
    } catch (error) {
      console.error('Error in updateCpt:', error);
      throw error;
    }
  }

  @Mutation(() => SuccessResponse)
  @UseGuards(JwtAuthGuard)
  @RequirePermission({ module: 'Masters', subModule: 'CPT Dictionary', permission: 'Delete' })
  async deleteCpt(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<SuccessResponse> {
    try {
      await this.cptService.delete(id);
      return {
        code: 200,
        message: 'CPT deleted successfully',
        type: 'success'
      };
    } catch (error) {
      console.error('Error in deleteCpt:', error);
      throw error;
    }
  }

  // @ResolveField('specialtyName', () => String, { nullable: true })
  // async getSpecialtyName(@Parent() masterCpt: Cpt) {
  //   if (masterCpt.specialty && typeof masterCpt.specialty === 'object') {
  //     return masterCpt.specialty.name;
  //   }
  //   return null;
  // }
} 