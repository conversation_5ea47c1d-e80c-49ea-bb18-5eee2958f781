import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types, Document } from 'mongoose';
import { Icd } from './entities/icd.entity';
import {
  CreateIcdInput,
  UpdateIcdInput,
  PaginateIcdArgs,
  PaginatedIcdResponse,
  IcdFilterInput
} from './dto/icd.dto';
import {
  ErrorType,
  Error,
  ResponseCode,
  HttpStatus,
  Success,
} from '@app/error';
import { instanceToPlain } from 'class-transformer';

type IcdDocument = Icd & Document;

@Injectable()
export class IcdService {
  constructor(
    @InjectModel(Icd.name)
    private icdModel: Model<IcdDocument>,
  ) {}

  private transformDocument(doc: any): IcdDocument {
    if (!doc) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.ICD,
        'Cannot transform null document',
      );
    }
    
    const plainDoc = {
      _id: doc._id || new Types.ObjectId(),
      type: doc.type,
      code: doc.code || '',
      values: doc.values || {},
      templateId: doc.templateId || '',
      isActive: doc.isActive !== undefined ? doc.isActive : true,
      createdAt: doc.createdAt || new Date(),
      updatedAt: doc.updatedAt || new Date(),
      createdBy: doc.createdBy || '',
      updatedBy: doc.updatedBy || '',
    };
    
    return new this.icdModel(plainDoc) as IcdDocument;
  }

  private buildSearchQuery(searchTerm: string): any {
    if (!searchTerm) return {};
    const searchRegex = { $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' };
    const orConditions: any[] = [
      { code: searchRegex },
      { 'values.description': searchRegex },
    ];
    if (Types.ObjectId.isValid(searchTerm)) {
      const objectId = new Types.ObjectId(searchTerm);
      orConditions.push({ _id: objectId });
      orConditions.push({ templateId: objectId });
    }
    return { $or: orConditions };
  }

  private buildValuesFilter(fieldFilters: Array<{ path: string; value: any; filter?: boolean }>): any {
    if (!fieldFilters?.length) return {};
    const conditions = fieldFilters
      .filter(filter => filter.filter !== false)
      .map(filter => {
        const condition: any = {};
        condition[`values.${filter.path}`] = typeof filter.value === 'string' ? 
          { $regex: filter.value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), $options: 'i' } : 
          filter.value;
        return condition;
      });
    return conditions.length > 1 ? { $and: conditions } : conditions[0] || {};
  }

  async findAll(input: PaginateIcdArgs): Promise<PaginatedIcdResponse> {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        filters,
        sortBy,
        sortOrder = 'asc',
        selectedFields
      } = input;

      const query: any = {};

      // Field Selection Logic
      let projection: Record<string, number> = {};
      let searchableFields: string[] = [];

      // Always include _id in projection
      projection._id = 1;

      if (selectedFields && Object.keys(selectedFields).length > 0) {
        // Use client-provided projection
        Object.assign(projection, selectedFields);
        
        // Search only in selected fields (exclude non-searchable ones)
        const excludedFromSearch = [
          '__v', '_id', 'values', 'flattenedValues', 'createdAt', 'updatedAt'
        ];
        searchableFields = Object.keys(selectedFields)
          .filter(field => selectedFields[field] === 1 && !excludedFromSearch.includes(field));
      } else {
        // Dynamically include all fields except internal ones
        const excluded = ['__v'];
        projection = {
          _id: 1,
          code: 1,
          values: 1,
          templateId: 1,
          isActive: 1,
          createdAt: 1,
          updatedAt: 1,
          createdBy: 1,
          updatedBy: 1
        };

        // Search in all available fields (except excluded ones)
        searchableFields = Object.keys(projection).filter(field => !excluded.includes(field));
      }

      // Search Handling
      if (search?.trim()) {
        const searchTerm = search.trim();
        const regex = {
          $regex: searchTerm.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
          $options: 'i'
        };

        // Search only in the determined searchable fields
        query.$or = searchableFields.map(field => ({ [field]: regex }));

        // Allow search by _id if valid ObjectId
        if (/^[0-9a-fA-F]{24}$/.test(searchTerm)) {
          query.$or.push({ _id: searchTerm });
        }

        // Handle date search separately if the search term looks like a date
        if (searchTerm.match(/^\d{4}-\d{2}-\d{2}/) || searchTerm.match(/^\d{1,2}\/\d{1,2}\/\d{4}/)) {
          try {
            const searchDate = new Date(searchTerm);
            if (!isNaN(searchDate.getTime())) {
              // Search for dates within the same day
              const startOfDay = new Date(searchDate);
              startOfDay.setHours(0, 0, 0, 0);
              const endOfDay = new Date(searchDate);
              endOfDay.setHours(23, 59, 59, 999);

              query.$or.push(
                { createdAt: { $gte: startOfDay, $lte: endOfDay } },
                { updatedAt: { $gte: startOfDay, $lte: endOfDay } }
              );
            }
          } catch (error) {
            // Ignore invalid date formats
          }
        }
      }
      
      // === Filters Handling ===
      if (filters) {
        const parsed =
          typeof filters === 'string'
            ? JSON.parse(filters)?.filters || JSON.parse(filters)
            : filters.filters || filters;

        for (const [key, value] of Object.entries(parsed)) {
          if (value !== undefined && value !== '') {
            query[key] =
              typeof value === 'string'
                ? {
                  $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'),
                  $options: 'i'
                }
                : value;
          }
        }
      }

      const sort: Record<string, 1 | -1> = {};
      if (sortBy) {
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sort.createdAt = -1;
      }

      // === Pagination Calculations ===
      const safePage = Math.max(1, page);
      const safeLimit = Math.min(Math.max(1, limit), 100);
      const skip = (safePage - 1) * safeLimit;

      const totalItems = await this.icdModel.countDocuments(query);
      // === Fetch Data ===
      const icds = await this.icdModel
        .find(query)
        .select(projection)
        .sort(sort)
        .skip(skip)
        .limit(safeLimit)
        .lean();

      const totalPages = Math.ceil(totalItems / safeLimit);

      return {
        items: icds,
        pagination: {
          page: safePage,
          limit: safeLimit,
          total: totalItems,
          totalItems,
          totalPages,
          hasNext: safePage < totalPages,
          hasPrev: safePage > 1
        }
      };
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.findAll:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.ICD, `Failed to fetch ICDs: ${(err as globalThis.Error).message}`);
    }
  }

  async findById(id: string): Promise<IcdDocument> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.ICD, 'Invalid ID format');
      }
      const doc = await this.icdModel.findById(id).lean().exec();
      if (!doc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.ICD, 'ICD not found');
      }
      return this.transformDocument(doc);
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.findById:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.ICD, `Failed to find ICD: ${(err as globalThis.Error).message}`);
    }
  }

  async findByCode(code: string): Promise<IcdDocument | null> {
    try {
      const doc = await this.icdModel.findOne({ code }).lean().exec();
      return doc ? this.transformDocument(doc) : null;
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.findByCode:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.ICD, `Failed to find ICD by code: ${(err as globalThis.Error).message}`);
    }
  }

  async create(input: CreateIcdInput): Promise<Success> {
    try {
      const existing = await this.findByCode(input.code);
      if (existing) {
        throw new Error(
          HttpStatus.CONFLICT,
          ResponseCode.DUPLICATE_ENTRY,
          ErrorType.ICD,
          'ICD with this code already exists'
        );
      }

      const inputPlain = instanceToPlain(input) as Record<string, any>;
      const processedFlattenedValues: Record<string, any> = {};

      if (typeof input.flattenedValues === 'object') {
        Object.assign(processedFlattenedValues, input.flattenedValues);
      }

      const icdData: Record<string, any> = {
        ...inputPlain,
        ...processedFlattenedValues,
        values: input.values || {},
      };

      delete icdData.flattenedValues;

      const newIcd = new this.icdModel(icdData);
      const savedDoc = await newIcd.save();

      return new Success(
        HttpStatus.CREATED,
        ResponseCode.SUCCESS,
        ErrorType.ICD,
        { icd: this.transformDocument(savedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.create:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.ICD, `Failed to create ICD: ${(err as globalThis.Error).message}`);
    }
  }

  async update(input: UpdateIcdInput): Promise<Success> {
    try {
      const { id,flattenedValues, ...updateData } = input;

      if (!Types.ObjectId.isValid(id)) {
        throw new Error(HttpStatus.BAD_REQUEST, ResponseCode.INVALID_PARAMETERS, ErrorType.ICD, 'Invalid ICD ID format for update.');
      }

      const existingIcd = await this.icdModel.findById(id).lean().exec();
      if (!existingIcd) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.ICD, 'ICD not found for update.');
      }

      if (input.code) {
        const existingByCode = await this.findByCode(input.code);
        if (existingByCode && existingByCode._id.toString() !== id) {
          throw new Error(
            HttpStatus.CONFLICT,
            ResponseCode.DUPLICATE_ENTRY,
            ErrorType.ICD,
            'ICD with this code already exists'
          );
        }
      }

      const updatePayload: any = { ...updateData, updatedAt: new Date() };
      if (input.isActive !== undefined) updatePayload.isActive = input.isActive;

const processedFlattenedValues: Record<string, any> = {};
      if (typeof flattenedValues === 'object' && flattenedValues) {
        Object.assign(processedFlattenedValues, flattenedValues);
      }

      const processedUpdateData: any = {
        ...updateData,
        ...processedFlattenedValues,
        updatedAt: new Date()
      };

      const updatedDoc = await this.icdModel.findByIdAndUpdate(
        id,
        processedUpdateData,
        { new: true }
      );

      // const updatedDoc = await this.icdModel
      //   .findByIdAndUpdate(id, { $set: updatePayload }, { new: true })
      //   .lean()
      //   .exec();

      if (!updatedDoc) {
        throw new Error(HttpStatus.NOT_FOUND, ResponseCode.NOT_FOUND, ErrorType.ICD, 'ICD not found during update process, or optimistic lock failed.');
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.ICD,
        { icd: this.transformDocument(updatedDoc) }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.update:', err);
      throw new Error(HttpStatus.INTERNAL_SERVER_ERROR, ResponseCode.INTERNAL_SERVER_ERROR, ErrorType.ICD, `Failed to update ICD: ${(err as globalThis.Error).message}`);
    }
  }

  async delete(id: string): Promise<Success> {
    try {
      if (!Types.ObjectId.isValid(id)) {
        throw new Error(
          HttpStatus.BAD_REQUEST,
          ResponseCode.INVALID_PARAMETERS,
          ErrorType.ICD,
          'Invalid ID format'
        );
      }

      const deletedDoc = await this.icdModel
        .findByIdAndDelete(id)
        .lean()
        .exec();

      if (!deletedDoc) {
        throw new Error(
          HttpStatus.NOT_FOUND,
          ResponseCode.NOT_FOUND,
          ErrorType.ICD,
          'ICD not found'
        );
      }

      return new Success(
        HttpStatus.OK,
        ResponseCode.SUCCESS,
        ErrorType.ICD,
        { message: 'ICD deleted successfully.' }
      );
    } catch (err) {
      if (err instanceof Error) throw err;
      console.error('Unexpected error in IcdService.delete:', err);
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.ICD,
        `Failed to delete ICD: ${(err as globalThis.Error).message}`
      );
    }
  }

  async count(): Promise<number> {
    try {
      return await this.icdModel.countDocuments().exec();
    } catch (err) {
      throw new Error(
        HttpStatus.INTERNAL_SERVER_ERROR,
        ResponseCode.INTERNAL_SERVER_ERROR,
        ErrorType.ICD,
        `Failed to count ICDs: ${(err as globalThis.Error).message}`,
      );
    }
  }

  getAvailableSortFields(): string[] {
    return [
      '_id',
      'code',
      'type',
      'isActive',
      'createdAt',
      'updatedAt',
      'createdBy',
      'updatedBy',
      'templateId'
    ];
  }

  getSearchableFields(): string[] {
    return ['code', 'values.description'];
  }
} 