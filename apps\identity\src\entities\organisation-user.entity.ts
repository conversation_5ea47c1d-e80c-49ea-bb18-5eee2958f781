import { ObjectType, Field, ID } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { OrganisationRole, OrganisationType } from './organisation-role.entity';

@ObjectType()
@Schema({
  timestamps: true,
  collection: 'organisation_users',
})
export class OrganisationUser extends Document {
  @Field(() => ID)
  declare _id: Types.ObjectId;

  @Field()
  @Prop({ required: true })
  name: string;

  @Field()
  @Prop({ required: true, })
  email: string;

  @Field()
  @Prop({ required: true, })
  employeeId: string;

  @Field()
  @Prop({ required: true })
  organisationId: string;

  @Field({ nullable: true })
  @Prop()
  subOrganisationId?: string;  

  @Field({ nullable: true })
  @Prop()
  orgId?: string;

  @Field(() => OrganisationRole)
  @Prop({ type: Types.ObjectId, ref: 'OrganisationRole', required: true })
  roleId: Types.ObjectId;

  @Field(() => OrganisationType)
  @Prop({ type: String, enum: OrganisationType, required: true })
  type: OrganisationType;

  @Field({ nullable: true, defaultValue: false })
  @Prop()
  is2FAenabled?: boolean;

  @Field({ nullable: true, defaultValue: false })
  @Prop()
  bypass2FA?: boolean;

  @Field({ nullable: true })
  @Prop()
  secretKey?: string;

  @Field({ nullable: true })
  @Prop()
  qrcode?: string;

  @Field({ nullable: true })
  @Prop()
  attempt?: number;

  @Field({ nullable: true })
  @Prop()
  dailyOtpAttempts?: number;

  @Field({ nullable: true })
  @Prop()
  lastOtpAttemptDate?: Date;

  @Field()
  @Prop({ default: true })
  isActive: boolean;

  @Field({ nullable: true })
  createdAt?: Date;

  @Field({ nullable: true })
  updatedAt?: Date;
}

export const OrganisationUserSchema = SchemaFactory.createForClass(OrganisationUser);
OrganisationUserSchema.index({ email: 1, organisationId: 1 }, { unique: true });
OrganisationUserSchema.index({ employeeId: 1, organisationId: 1 }, { unique: true }); 