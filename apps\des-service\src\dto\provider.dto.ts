import { ArgsType, Field, InputType, ObjectType, Int, ID, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsInt, Min, Max, IsBoolean, IsMongoId, IsObject, IsEnum, IsEmail } from 'class-validator';
import { GraphQLJSON } from 'graphql-type-json';
import { Provider } from '../entities/provider.entity';
import { ClientType } from '../entities/template.entity';
import { SuccessResponse } from './base.response.dto';

@InputType()
export class GetProviderInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

@InputType()
export class ProviderFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  templateId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  fieldFilters?: Array<{
    path: string;
    value: any;
    label?: string;
    filter?: boolean;
  }>;
}

@InputType()
export class CreateProviderInput {
  // @Field(() => ClientType)
  // @IsEnum(ClientType)
  // type: ClientType;

  @Field(() => ID)
  @IsMongoId()
  templateId: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  subOrganisationId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field(() => GraphQLJSON)
  @IsObject()
  values: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdateProviderInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  // @Field(() => ClientType, { nullable: true })
  // @IsOptional()
  // @IsEnum(ClientType)
  // type?: ClientType;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  templateId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  token?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsMongoId()
  subOrganisationId?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@ObjectType()
export class ProviderPaginationMeta {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class PaginatedProviderResponse {
  @Field(() => [GraphQLJSON])
  providers: any[];

  @Field(() => ProviderPaginationMeta)
  pagination: ProviderPaginationMeta;
}

@ArgsType()
export class PaginateProviderArgs {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}


@ObjectType()
export class AuthPayload {
  @Field()
  accessToken: string;

  @Field(() => Provider)
  user: Provider;
}
 