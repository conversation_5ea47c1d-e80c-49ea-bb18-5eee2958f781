import { Injectable, Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { applicationDefault, initializeApp, cert, Credential } from 'firebase-admin/app';
import * as path from 'path';

@Injectable()
export class FirebasePushService {
  private readonly logger = new Logger(FirebasePushService.name);
  private initialized = false;

  constructor() {
    this.initFirebase();
  }

  private initFirebase() {
    if (this.initialized) return;

    try {
      // Use the Firebase web app configuration for Admin SDK
      const firebaseConfig = {
        projectId: process.env.FIREBASE_PROJECT_ID || 'asp-rcm',
        // For Admin SDK, we can initialize with just project ID and credentials
      };

      // Try different credential methods in order of preference
      let credential: Credential;
      const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS ||
                                 path.join(process.cwd(), 'firebase-service-account.json');

      // Method 1: Try service account file
      if (this.isValidServiceAccountFile(serviceAccountPath)) {
        try {
          credential = cert(serviceAccountPath);
          this.logger.log(`Using Firebase service account from: ${serviceAccountPath}`);
        } catch (serviceAccountError) {
          this.logger.warn(`Failed to load service account file: ${serviceAccountError.message}`);
          throw serviceAccountError;
        }
      } else {
        // Method 2: Try to create service account from environment variables
        const serviceAccountFromEnv = this.getServiceAccountFromEnv();
        if (serviceAccountFromEnv) {
          try {
            credential = cert(serviceAccountFromEnv);
            this.logger.log(`Using Firebase service account from environment variables`);
          } catch (envError) {
            this.logger.warn(`Failed to load service account from environment: ${envError.message}`);
            throw envError;
          }
        } else {
          // Method 3: Try application default credentials
          this.logger.warn(`No service account found, trying application default credentials`);
          credential = applicationDefault();
        }
      }

      initializeApp({
        credential,
        projectId: firebaseConfig.projectId,
      });

      this.initialized = true;
      this.logger.log(`✅ Firebase Admin initialized successfully with project ID: ${firebaseConfig.projectId}`);
    } catch (error) {
      this.logger.error(`❌ Firebase initialization failed: ${error.message}`);
      this.logger.warn(`🔥 Push notifications will be disabled. To enable Firebase push notifications:`);
      this.logger.warn(`   1. Download Firebase Admin SDK service account from Firebase Console`);
      this.logger.warn(`   2. Save it as firebase-service-account.json in project root`);
      this.logger.warn(`   3. Or set environment variables (see FIREBASE_SETUP_GUIDE.md)`);
      // Don't throw error, just log it so the service can still start
    }
  }

  private getServiceAccountFromEnv(): any {
    // Try to create service account object from environment variables
    const projectId = process.env.FIREBASE_PROJECT_ID || process.env.GOOGLE_CLOUD_PROJECT;
    const privateKey = process.env.FIREBASE_PRIVATE_KEY;
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;

    if (projectId && privateKey && clientEmail) {
      return {
        type: 'service_account',
        project_id: projectId,
        private_key: privateKey.replace(/\\n/g, '\n'), // Handle escaped newlines
        client_email: clientEmail,
      };
    }
    return null;
  }

  private isValidServiceAccountFile(filePath: string): boolean {
    try {
      const fs = require('fs');
      if (!fs.existsSync(filePath)) {
        return false;
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const serviceAccount = JSON.parse(content);

      // Check if it's a real service account (not placeholder)
      return serviceAccount.type === 'service_account' &&
             serviceAccount.project_id &&
             serviceAccount.private_key &&
             serviceAccount.client_email &&
             !serviceAccount.private_key.includes('Your Private Key Here') &&
             !serviceAccount.client_email.includes('xxxxx');
    } catch (error) {
      return false;
    }
  }

  async sendPushNotification(
    token: string,
    payload: admin.messaging.MessagingPayload,
  ): Promise<void> {
    if (!this.initialized) {
      this.logger.error('Firebase Admin SDK not initialized. Skipping push notification.');
      return;
    }

    if (!token || token.trim() === '') {
      this.logger.warn('Invalid or empty device token provided. Skipping push notification.');
      return;
    }
    // Validate that all data values are strings
    if (payload.data) {
      const nonStringValues = Object.entries(payload.data).filter(([, value]) => typeof value !== 'string');
      if (nonStringValues.length > 0) {
        console.error(`❌ DEBUG: Non-string values found in Firebase data:`, nonStringValues);
      } else {
        console.log(`✅ DEBUG: All Firebase data values are strings`);
      }
    }

    try {
      const message: admin.messaging.Message = {
        token,
        notification: payload.notification,
        data: payload.data || {},
      };

      console.log(`🚀 DEBUG: Sending Firebase message:`, {
        token: `${token.substring(0, 10)}...`,
        notification: message.notification,
        dataKeys: Object.keys(message.data || {}),
        dataTypes: Object.entries(message.data || {}).map(([k, v]) => `${k}: ${typeof v}`)
      });

      await admin.messaging().send(message);
      console.log(`✅ Push notification sent successfully to device token: ${token.substring(0, 10)}...`);
    } catch (error) {
      this.logger.error(`❌ Failed to send push notification: ${error.message}`);

      // Provide specific guidance based on error type
      if (error.message.includes('Project Id')) {
        this.logger.error(`🔥 Firebase Project ID issue. Please check your Firebase configuration.`);
      } else if (error.message.includes('credential')) {
        this.logger.error(`🔥 Firebase credentials issue. Please verify your service account file.`);
      } else if (error.message.includes('token')) {
        this.logger.error(`🔥 Invalid device token: ${token.substring(0, 10)}...`);
      }
    }
  }
}
