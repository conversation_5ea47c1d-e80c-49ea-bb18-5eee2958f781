import { Test, TestingModule } from '@nestjs/testing';
import { DesServiceResolver } from './des-service.resolver';
import { DesServiceService } from './des-service.service';
import { CreateFormInput, UpdateFormInput, GetFormVersionInput } from './dto/form.dto';
import { CreateTemplateInput, UpdateTemplateInput, FindAllTemplatesArgs } from './dto/template.dto';
import { Types } from 'mongoose';

describe('DesServiceResolver', () => {
  let resolver: DesServiceResolver;
  let service: DesServiceService;

  const mockObjectId = new Types.ObjectId();

  const mockSuccessResponse = new Success(
    HttpStatus.OK,
    ResponseCode.SUCCESS,
    ErrorType.FORM,
    { message: 'Success' }
  );

  const mockService = {
    findAllForms: jest.fn(),
    findFormById: jest.fn(),
    createForm: jest.fn(),
    updateForm: jest.fn(),
    deleteForm: jest.fn(),
    getForm: jest.fn(),
    findAllTemplates: jest.fn(),
    findTemplateById: jest.fn(),
    createTemplate: jest.fn(),
    updateTemplate: jest.fn(),
    deleteTemplate: jest.fn(),
    getTemplateCount: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DesServiceResolver,
        {
          provide: DesServiceService,
          useValue: mockService,
        },
      ],
    }).compile();

    resolver = module.get<DesServiceResolver>(DesServiceResolver);
    service = module.get<DesServiceService>(DesServiceService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('Form Operations', () => {
    describe('getForms', () => {
      it('should return forms with pagination', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          search: 'test',
        };

        mockService.findAllForms.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getForms(args);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.findAllForms).toHaveBeenCalledWith(args);
      });
    });

    describe('getForm', () => {
      it('should return a form by ID', async () => {
        const formId = mockObjectId.toString();

        mockService.findFormById.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getForm(formId);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.findFormById).toHaveBeenCalledWith(formId);
      });
    });

    describe('createForm', () => {
      it('should create a new form', async () => {
        const createFormInput: CreateFormInput = {
          name: 'Test Form',
          description: 'Test Description',
          templateId: 'test-template-001',
          fields: { type: 'text', label: 'Name' },
          createdBy: 'test-user',
        };

        mockService.createForm.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.createForm(createFormInput);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.createForm).toHaveBeenCalledWith(createFormInput);
      });
    });

    describe('updateForm', () => {
      it('should update a form', async () => {
        const updateFormInput: UpdateFormInput = {
          id: mockObjectId.toString(),
          name: 'Updated Form',
          value: { type: 'text', label: 'Updated Name' },
        };

        mockService.updateForm.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.updateForm(updateFormInput);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.updateForm).toHaveBeenCalledWith(updateFormInput);
      });
    });

    describe('deleteForm', () => {
      it('should delete a form', async () => {
        const formId = mockObjectId.toString();

        mockService.deleteForm.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.deleteForm(formId);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.deleteForm).toHaveBeenCalledWith(formId);
      });
    });

    describe('getFormVersion', () => {
      it('should get form version', async () => {
        const getFormVersionInput: GetFormVersionInput = {
          formId: mockObjectId.toString(),
          version: 1,
        };

        mockService.getForm.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getFormVersion(getFormVersionInput);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.getForm).toHaveBeenCalledWith(getFormVersionInput);
      });
    });

    describe('getFormVersions', () => {
      it('should get all form versions', async () => {
        const formId = mockObjectId.toString();

        // Note: This should be updated when the service method is fixed
        mockService.getAllForm = jest.fn().mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getFormVersions(formId);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.getAllForm).toHaveBeenCalledWith(formId);
      });
    });
  });

  describe('Template Operations', () => {
    describe('getTemplates', () => {
      it('should return templates with pagination', async () => {
        const args: FindAllTemplatesArgs = {
          page: 1,
          limit: 10,
          search: 'test',
          filters: { status: 'active' },
          sort: { field: 'name', ascending: true },
        };

        mockService.findAllTemplates.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getTemplates(args);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.findAllTemplates).toHaveBeenCalledWith(args);
      });
    });

    describe('getTemplate', () => {
      it('should return a template by ID', async () => {
        const templateId = mockObjectId.toString();

        mockService.findTemplateById.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getTemplate(templateId);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.findTemplateById).toHaveBeenCalledWith(templateId);
      });
    });

    describe('createTemplate', () => {
      it('should create a new template', async () => {
        const createTemplateInput: CreateTemplateInput = {
          name: 'Test Template',
          description: 'Test Template Description',
          templateId: 'test-template-001',
          value: [{ type: 'text', label: 'Name' }],
          status: 'active',
          createdBy: 'test-user',
        };

        mockService.createTemplate.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.createTemplate(createTemplateInput);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.createTemplate).toHaveBeenCalledWith(createTemplateInput);
      });
    });

    describe('updateTemplate', () => {
      it('should update a template', async () => {
        const updateTemplateInput: UpdateTemplateInput = {
          id: mockObjectId.toString(),
          name: 'Updated Template',
          description: 'Updated Description',
        };

        mockService.updateTemplate.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.updateTemplate(updateTemplateInput);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.updateTemplate).toHaveBeenCalledWith(updateTemplateInput);
      });
    });

    describe('deleteTemplate', () => {
      it('should delete a template', async () => {
        const templateId = mockObjectId.toString();

        mockService.deleteTemplate.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.deleteTemplate(templateId);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.deleteTemplate).toHaveBeenCalledWith(templateId);
      });
    });

    describe('getTemplateCount', () => {
      it('should get template count without filters', async () => {
        mockService.getTemplateCount.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getTemplateCount();

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.getTemplateCount).toHaveBeenCalledWith(undefined);
      });

      it('should get template count with filters', async () => {
        const filters = '{"status":"active","name":"test"}';
        const parsedFilters = { status: 'active', name: 'test' };

        mockService.getTemplateCount.mockResolvedValue(mockSuccessResponse);

        const result = await resolver.getTemplateCount(filters);

        expect(result).toBe(mockSuccessResponse);
        expect(mockService.getTemplateCount).toHaveBeenCalledWith(parsedFilters);
      });
    });
  });
});
