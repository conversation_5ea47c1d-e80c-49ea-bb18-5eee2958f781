import { InputType, Field, ID, Int, ArgsType, ObjectType, Directive } from '@nestjs/graphql';
import { IsOptional, IsString, IsBoolean, IsObject, IsMongoId, IsInt, IsEnum } from 'class-validator';
import { Graph<PERSON>JSON } from 'graphql-type-json';
import { Diagnosis } from '../entities/diagnosis.entity';
import { ClientType } from '../entities/template.entity';
import { SuccessResponse } from './base.response.dto';

@InputType()
export class GetDiagnosisInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}

@InputType()
export class DiagnosisFilterInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  type?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field({ nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;
}

@InputType()
export class CreateDiagnosisInput {
  @Field(() => ClientType)
  @IsEnum(ClientType)
  type: ClientType;

  @Field()
  @IsString()
  name: string;

  @Field()
  @IsString()
  icd: string;

  @Field(() => ID)
  @IsMongoId()
  templateId: string;

  @Field(() => GraphQLJSON)
  @IsObject()
  values: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@InputType()
export class UpdateDiagnosisInput {
  @Field(() => ID)
  @IsMongoId()
  id: string;

  // @Field(() => ClientType, { nullable: true })
  // @IsOptional()
  // @IsEnum(ClientType)
  // type?: ClientType;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  name?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  icd?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  values?: Record<string, any>;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  flattenedValues?: Record<string, any>;
}

@ObjectType()
export class DiagnosisPaginationInfo {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Boolean)
  hasNext: boolean;

  @Field(() => Boolean)
  hasPrev: boolean;
}

@ObjectType()
export class PaginatedDiagnosisResponse {
 @Field(() => [GraphQLJSON])
  items: any[];

  @Field(() => DiagnosisPaginationInfo)
  pagination: DiagnosisPaginationInfo;
}

@ArgsType()
export class PaginateDiagnosisArgs {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  filters?: Record<string, any>;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  @IsOptional()
  @IsInt()
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  @IsOptional()
  @IsInt()
  limit?: number;

  @Field(() => GraphQLJSON, { nullable: true })
  @IsOptional()
  @IsObject()
  selectedFields?: Record<string, any>;
}


export { SuccessResponse }; 