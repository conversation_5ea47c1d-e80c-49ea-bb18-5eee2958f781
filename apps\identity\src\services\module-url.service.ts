import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ModuleUrl, PaginationMetas } from '../entities/module-url.entity';
import {
  CreateModuleUrlInput,
  UpdateModuleUrlInput,
  GetModuleUrlsArgs,
  ModuleUrlPaginatedResponse
} from '../dto/module-url.dto';

@Injectable()
export class ModuleUrlService {
  constructor(
    @InjectModel(ModuleUrl.name) private moduleUrlModel: Model<ModuleUrl>
  ) {}

  async create(input: CreateModuleUrlInput): Promise<ModuleUrl> {
    try {
      // Validate URL time values are positive numbers
      if (input.importUrlTime && input.importUrlTime <= 0) {
        throw new BadRequestException('Import URL time must be a positive number of days');
      }
      if (input.backupUrlTime && input.backupUrlTime <= 0) {
        throw new BadRequestException('Backup URL time must be a positive number of days');
      }
      if (input.exportUrlTime && input.exportUrlTime <= 0) {
        throw new BadRequestException('Export URL time must be a positive number of days');
      }
      if (input.auditUrlTime && input.auditUrlTime <= 0) {
        throw new BadRequestException('Audit URL time must be a positive number of days');
      }

      const moduleUrl = new this.moduleUrlModel(input);
      const savedUrl = await moduleUrl.save();

      console.log('✅ Created module URL:', {
        id: savedUrl._id,
        module: savedUrl.moduleName,
        collectionName: savedUrl.collectionName,
        importUrlTime: savedUrl.importUrlTime,
        backupUrlTime: savedUrl.backupUrlTime,
        exportUrlTime: savedUrl.exportUrlTime,
        auditUrlTime: savedUrl.auditUrlTime,
        isImport: savedUrl.isImport,
        isExport: savedUrl.isExport
      });

      return savedUrl;
    } catch (error) {
      console.error('❌ Failed to create module URL:', error);
      throw error;
    }
  }

  async findAll(args: GetModuleUrlsArgs): Promise<ModuleUrlPaginatedResponse> {
    try {
      const query: any = {};
      let parsed: any = {};

      // Handle dynamic filtering
      if (args.filters) {
        try {
          parsed =
            typeof args.filters === 'string'
              ? JSON.parse(args.filters)?.filters || JSON.parse(args.filters)
              : args.filters?.filters || args.filters;

          for (const [key, value] of Object.entries(parsed)) {
            if (value !== undefined && value !== '') {
              query[key] =
                typeof value === 'string'
                  ? {
                    $regex: value.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$1'),
                    $options: 'i',
                  }
                  : value;
            }
          }
        } catch (err) {
          console.warn('Invalid filters provided. Skipping filters.', err);
        }
      }

      // Pagination setup
      const page = args.page || 1;
      const limit = args.limit || 10;
      const skip = (page - 1) * limit;

      // Sorting
      const sortBy = args.sortBy || 'createdAt';
      const sortOrder = args.sortOrder === 'asc' ? 1 : -1;
      const sort: any = { [sortBy]: sortOrder };

      // Execute query with pagination
      const [results, totalItems] = await Promise.all([
        this.moduleUrlModel
          .find(query)
          .sort(sort)
          .limit(limit)
          .skip(skip)
          .exec(),
        this.moduleUrlModel.countDocuments(query)
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalItems / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const pagination: PaginationMetas = {
        page,
        limit,
        total: results.length,
        totalItems,
        totalPages,
        hasNext,
        hasPrev
      };

      return {
        data: results,
        pagination
      };
    } catch (error) {
      console.error('❌ Failed to find module URLs:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<ModuleUrl> {
    try {
      const moduleUrl = await this.moduleUrlModel.findById(id);
      
      if (!moduleUrl) {
        throw new NotFoundException(`Module URL with ID ${id} not found`);
      }

      console.log('🔍 Found module URL by ID:', {
        id: moduleUrl._id,
        module: moduleUrl.moduleName,
        collectionName: moduleUrl.collectionName,
        importUrlTime: moduleUrl.importUrlTime,
        backupUrlTime: moduleUrl.backupUrlTime,
        exportUrlTime: moduleUrl.exportUrlTime,
        auditUrlTime: moduleUrl.auditUrlTime,
        isImport: moduleUrl.isImport,
        isExport: moduleUrl.isExport
      });

      return moduleUrl;
    } catch (error) {
      console.error('❌ Failed to find module URL by ID:', error);
      throw error;
    }
  }

  async update(id: string, input: UpdateModuleUrlInput): Promise<ModuleUrl> {
    try {
      // Validate expiration dates if provided
      // if (input.backupUrlTime && new Date(input.backupUrlTime) <= new Date()) {
      //   throw new BadRequestException('Backup URL expiration date must be in the future');
      // }
      // if (input.auditUrlTime && new Date(input.auditUrlTime) <= new Date()) {
      //   throw new BadRequestException('Audit URL expiration date must be in the future');
      // }

      const updatedUrl = await this.moduleUrlModel.findByIdAndUpdate(
        id,
        { ...input, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!updatedUrl) {
        throw new NotFoundException(`Module URL with ID ${id} not found`);
      }

      console.log('✅ Updated module URL:', {
        id: updatedUrl._id,
        changes: input,
        module: updatedUrl.moduleName,
        collectionName: updatedUrl.collectionName
      });

      return updatedUrl;
    } catch (error) {
      console.error('❌ Failed to update module URL:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const result = await this.moduleUrlModel.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundException(`Module URL with ID ${id} not found`);
      }

      console.log('🗑️ Deleted module URL:', {
        id: result._id,
        module: result.moduleName,
        collectionName: result.collectionName
      });

      return true;
    } catch (error) {
      console.error('❌ Failed to delete module URL:', error);
      throw error;
    }
  }

  async findByCollectionName(collectionName: string): Promise<ModuleUrl | null> {
    try {
      console.log(`🔍 Finding module URL config for collection: ${collectionName}`);

      const moduleUrl = await this.moduleUrlModel.findOne({
        collectionName,
        // isActive: true
      }).lean();

      if (moduleUrl) {
        console.log(`✅ Found module URL config:`, {
          id: moduleUrl._id,
          moduleName: moduleUrl.moduleName,
          collectionName: moduleUrl.collectionName,
          importUrlTime: moduleUrl.importUrlTime,
          backupUrlTime: moduleUrl.backupUrlTime,
          exportUrlTime: moduleUrl.exportUrlTime,
          auditUrlTime: moduleUrl.auditUrlTime,
          isImport: moduleUrl.isImport,
          isExport: moduleUrl.isExport
        });
      } else {
        console.warn(`⚠️ No module URL config found for collection: ${collectionName}`);
      }

      return moduleUrl;
    } catch (error) {
      console.error(`❌ Failed to find module URL config for ${collectionName}:`, error);
      throw error;
    }
  }

}
